<!--
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-10-12 09:24:03
 * @LastEditors: 悦悦 <EMAIL>
 * @LastEditTime: 2024-10-31 10:57:16
 * @FilePath: /隐患一张图/ehs-hazard/src/App.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <n-config-provider
    preflight-style-disabled
    :locale="zhCN"
    :date-locale="dateZhCN"
    :theme="isDark ? darkTheme : undefined"
    :theme-overrides="
      isDark ? theme.dark(primaryColor) : theme.light(primaryColor)
    "
    :component-options="componentOptions"
  >
    <n-message-provider>
      <router-view />
    </n-message-provider>
  </n-config-provider>
</template>

<script lang="ts" setup>
import {
  NConfigProvider,
  zhCN,
  dateZhCN,
  NSelect,
  NCascader,
  NDataTable,
  darkTheme,
  GlobalComponentConfig,
} from 'naive-ui';
import { themeStore } from '@/theme/ThemeStore';
import { naiveThemeOverrides } from '@/theme';
import EmptyComp from '@/components/empty/Empty.vue';
import { useCssVar } from '@vueuse/core';
import { computed, h, ref, watch } from 'vue';
import { useStore } from '@/store/index';

const { userInfo } = useStore();
const theme = naiveThemeOverrides();
const componentOptions = ref<GlobalComponentConfig>({
  Empty: {
    renderIcon: () => h(EmptyComp),
    description: '暂无数据',
  },
});
const primaryColor = useCssVar('--skin-c1', document.documentElement);
const isDark = computed(() => themeStore.themeRef.value.theme === 'dark');

// 全局修改NaiveUI组件默认属性
NSelect.props.consistentMenuWidth = { type: Boolean, default: false };
NCascader.props.virtualScroll = { type: Boolean, default: false };
NDataTable.props.striped = { type: Boolean, default: true };

const getLogo = () => {
  //  logo图片  测试环境图片还没有  先用生产环境的图片 后面有了替换下面注释代码即可
  const logo_url = userInfo.iconPicUrl;
  // 创建一个新的<link>元素来替换favicon
  let newLink = document.createElement('link');
  newLink.rel = 'icon';
  newLink.type = 'image/x-icon';
  newLink.href = logo_url;

  // 获取<head>元素
  let head = document.head;
  const iconEl = document.querySelector("link[rel='icon']");
  // 替换favicon
  iconEl && head.removeChild(iconEl);
  head.appendChild(newLink);
};

watch(userInfo, getLogo, { immediate: true, deep: true });
</script>

<style>
body {
  min-height: 100vh;
  min-width: 1920px;
}

@font-face {
  font-family: 'YouSheBiaoTiHei';
  src: url('@/assets/YouSheBiaoTiHei-Bold.TTF') format('truetype');
  font-weight: normal;
  font-style: normal;
}
</style>
