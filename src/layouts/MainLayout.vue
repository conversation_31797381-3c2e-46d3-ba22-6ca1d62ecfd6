<template>
  <div v-if="!isInIfm" :class="{ [$style['main-layout']]: true, [$style['row']]: true, [$style['row2']]: noHeader }">
    <!-- <div :class="{ [$style['main-layout']]: true, [$style['row']]: true, [$style['row2']]: noHeader }"> -->
    <Header :class="$style['header']" v-if="!noHeader" />
    <Menu :class="$style['sidebar']" />
    <router-view :class="$style['content']" v-slot="{ Component }">
      <keep-alive :include="keepAliveComps">
        <component :is="Component" />
      </keep-alive>
    </router-view>
  </div>
  <div v-else :class="[$style['main-layout'], $style['row-ifm']]">
    <router-view :class="$style['content']" v-slot="{ Component }">
      <component :is="Component" />
    </router-view>
  </div>
</template>

<script lang="ts" setup>
import Header from '@/views/header/index.vue';
import Menu from '@/views/menu/index.vue';
import { computed } from 'vue';
import { useRoute } from 'vue-router';

const isInIfm = window.__IFM_ENV__;

const route = useRoute();
// 需缓存组件
const keepAliveComps = [] as string[];
const noHeaderPage = ['/mis', '/home', '/mis2'];
const noHeader = computed(() => {
  return noHeaderPage.includes(route.path);
});

defineOptions({ name: 'MainLayoutComp' });
</script>

<style module lang="scss">
:root {
  --header-height: 64px;
  /* 设置header高度的CSS变量 */
  // --min-height: 1080px; /* 设置一个最小高度的CSS变量 */
}

// @media (max-height: 1080px) {
//   :root {
//     --min-height: 1080px; /* 当视口高度 <= 1080px 时，设定最小高度为1080px */
//   }
// }

.main-layout {
  display: grid;
  min-height: var(--min-height);
  /* 使用CSS变量设定最小高度 */
  min-width: 1920px;
  /* 使用既定的最小宽度 */

  &.row {
    grid-template-areas:
      'header header'
      'sidebar content';
    /* 定义grid区域 */
    grid-template-columns: auto 1fr;
    grid-template-rows: var(--header-height) calc(100vh - var(--header-height));
  }

  &.row2 {
    grid-template-areas: 'sidebar content';
    /* 定义grid区域 */
    grid-template-columns: auto 1fr;
    grid-template-rows: minmax(calc(100vh), var(--min-height));
    /* 无Header时，100vh或最小高度 */
  }
  &.row-ifm {
    grid-template-areas: 'content'; /* 定义grid区域 */
    grid-template-columns: 1fr;
    grid-template-rows: 100vh;
  }
}

.header {
  grid-area: header;
  z-index: 9;
}

.sidebar {
  grid-area: sidebar;
  /* min-width: 48px; */
}

.content {
  grid-area: content;
  overflow: auto;
  padding: 0 24px 24px;
}
</style>
