import elementPlus from 'element-plus';
import zhCn from 'element-plus/es/locale/lang/zh-cn';
import 'element-plus/dist/index.css';
import { createApp } from 'vue';
import App from './App.vue';
import { setupApi, setupAssets, setupRouter, setupStore } from '@/plugins';
import Header from '@/components/header/ComHeaderB.vue';
import ComDrawerA from '@/components/drawer/ComDrawerA.vue';
import UnitSelect from '@/components/unitSelect/index.vue';
import '@tanzerfe/ifm-child';

async function setupApp() {
  const app = createApp(App);
  app.component('AppHeader', Header);
  app.component('ComDrawerA', ComDrawerA);
  app.component('UnitSelect', UnitSelect);

  app.use(elementPlus, {
    size: '',
    locale: {
      ...zhCn,
      el: {
        pagination: {
          pagesize: '/页',
          goto: '跳至',
          pageClassifier: '',
          total: '共 {total} 条',
        },
      },
    },
  });

  // import assets: js、css
  setupAssets();

  // register store
  await setupStore(app);

  // api
  setupApi();

  // register module router
  await setupRouter(app);

  app.mount('#app');
}

setupApp().then();
