import { getWatermarkBizData } from '@/components/upload/fetchData';
import { useStore } from '@/store';

const isAbsoluteURL = (url: string) => {
  const absoluteURLRegex = /^https?:\/\//;
  return absoluteURLRegex.test(url);
};

// 检查原图路径是否以 ".jpg", ".png", ".jpeg" 等常见图片格式结尾
const imageExtension = /\.(jpeg|jpg|png|gif|bmp|tiff|webp)$/i;
const getFileURL = (filePath: string, preview = false) => {
  let fileBaseUrl; //文件基础路径
  if (!filePath) return '';
  // 如果是绝对路径，就直接使用
  if (isAbsoluteURL(filePath)) return filePath;
  const isImg = filePath.match(imageExtension);
  // 兼容本地路径拼接 默认测试环境
  if (location.origin.includes('localhost')) {
    fileBaseUrl = filePath.startsWith('/img1')
      ? 'https://test-bw.gsafetycloud.com'
      : 'https://test-bw.gsafetycloud.com/ehs/';
  } else {
    const tongming = localStorage.getItem('filePrefix').split('/').pop();
    const isTmStart = filePath.startsWith(tongming) || filePath.startsWith('/' + tongming);
    // 线上环境路径拼接
    fileBaseUrl =
      filePath.includes('/img1') || filePath.startsWith('/img1') || filePath.startsWith('img1') || isTmStart
        ? localStorage.getItem('filePrefix').slice(0, localStorage.getItem('filePrefix').lastIndexOf('/'))
        : localStorage.getItem('filePrefix') + '/';
  }
  return fileBaseUrl + (preview && isImg ? replaceWithThumbnailPath(filePath, '_160X160') : filePath);
};
window.getFileURL = getFileURL;
/**
 * 替换原图路径为缩略图路径
 * @param {string} originalImagePath - 原图的完整路径
 * @param {string} thumbnailSuffix - 缩略图路径的后缀或参数（例如 "_80X160.jpg" 或 "?thumbnail=true"）
 * @returns {string} - 缩略图的完整路径
 */
function replaceWithThumbnailPath(originalImagePath: string, thumbnailSuffix: string) {
  const originalExtension = originalImagePath.match(imageExtension);
  if (!originalExtension) {
    throw new Error('原图路径没有有效的图片扩展名');
  }

  // 如果缩略图后缀包含文件扩展名（例如 "_80X160.jpg"），则直接使用它替换原图的扩展名
  // 否则，将缩略图后缀添加到原图的扩展名之前
  if (thumbnailSuffix.includes('.')) {
    // 缩略图后缀包含点，表示它有自己的扩展名，直接替换原图的扩展名
    return originalImagePath.replace(imageExtension, thumbnailSuffix);
  } else {
    // 缩略图后缀不包含点，表示它是一个参数或简单的后缀，添加到原图的扩展名之前
    return originalImagePath.replace(imageExtension, `${thumbnailSuffix}${originalExtension[0]}`);
  }
}

// 获取水印信息
export const getWatermarkBizDataApi = () => {
  return new Promise(async (resolve, reject) => {
    const { data }: any = await getWatermarkBizData({
      orgCode: useStore().userInfo.unitId,
    });
    resolve(data);
  });
};

export default getFileURL;
