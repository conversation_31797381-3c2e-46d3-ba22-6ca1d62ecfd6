export const getDefaultDateRange = (days = 30) => {
  // 获取今天的日期
  const today = new Date(+new Date() + 8 * 3600 * 1000);

  // 获取30天前的日期
  const thirtyDaysAgo = new Date(today);
  thirtyDaysAgo.setDate(today.getDate() - days);

  // 将日期格式化为字符串，例如："2023-09-23"
  const formattedToday = today.toISOString().split('T')[0];
  const formattedThirtyDaysAgo = thirtyDaysAgo.toISOString().split('T')[0];

  // 输出结果
  console.log(`[${formattedThirtyDaysAgo}, ${formattedToday}]`);
  return [formattedThirtyDaysAgo, formattedToday];
};
