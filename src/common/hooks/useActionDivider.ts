import { h, VNode } from 'vue';
import { NDivider, NPopover, NButton } from 'naive-ui';

/**
 * 操作栏应用分隔线
 * @param actions
 */
export function useActionDivider(actions: [VNode, boolean?][]) {
  const ret: VNode[] = [];
  const dividerEl = h(NDivider, {
    vertical: true,
    themeOverrides: {
      color: 'var(--skin-divide)',
    },
  });

  const allPermActions = actions
    .filter((item) => {
      return item[1] || item[1] === undefined; // 默认有权限
    })
    .map((item) => item[0]);
  const len = allPermActions.length;

  if (len <= 3) {
    for (let i = 0, len = allPermActions.length; i < len; i++) {
      const item = allPermActions[i];

      ret.push(item);

      // 加分隔线
      if (len > 1 && i !== len - 1) {
        ret.push(dividerEl);
      }
    }

    return ret;
  } else {
    const moreBtn = allPermActions.slice(2);
    const more = h(
      NPopover,
      {
        trigger: 'hover',
        placement: 'bottom-end',
        contentClass: 'flex flex-col gap-2',
      },
      {
        trigger: () => h(NButton, { size: 'small', ghost: true, type: 'primary' }, '更多'),
        default: () => moreBtn,
      }
    );
    ret.push(allPermActions[0], dividerEl, allPermActions[1], dividerEl, more);

    return ret;
  }
}
