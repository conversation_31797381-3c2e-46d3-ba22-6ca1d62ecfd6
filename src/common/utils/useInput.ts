// 整数并限制输入十位数
export const handleIntegerInput = (value: string): string => {
  return value.replace(/[^\d]/g, '');
};
// 小数并限制输入十位数，保留两位小数
export const handleDecimalInput = (value: string): string => {
  return value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1');
};
// 仅允许输入字母和数字
export const handleNumberAndLetterInput = (value: string): string => {
  return value.replace(/[\W]/g, '');
};
// 标准化时间
export const formatDate = (date: Date): string => {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const hour = date.getHours().toString().padStart(2, '0');
  const minute = date.getMinutes().toString().padStart(2, '0');
  const second = date.getSeconds().toString().padStart(2, '0');
  return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
};
