/*
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-07-03 18:23:52
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-07-08 12:56:16
 * @FilePath: /angang-platform/src/common/utils.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

import { ref, Ref } from 'vue';

interface TempData {
  tempId?: string;
  eventType: string;
  [key: string]: string | number | undefined;
}

const temp_data: Ref<TempData | null> = ref(null);

export function isTibetUnit(unitId: string) {
  return unitId === '540102DZDA202206010001';
}

export const loading = ref(false);

/**
 * 延迟函数
 * @param t 毫秒
 */
export function sleep(t: number) {
  return new Promise((resolve) => setTimeout(resolve, t));
}

export function openMessage(item: any, router: any, isNotify = false) {
  // 1 火警 2 预警 3 故障 4 隐患 5 动作 6 离线 7 疑似真警 8 屏蔽 9 普通 101 催促 102 真警
  let url = '/monitor/eventManagement';
  const disposeId = item.disposeId || item.businessId;
  url = `${url}?orgCode=${item.orgCode}&unitId=${item.unitId}&eventType=${item.messageType}&disposeId=${disposeId}`;

  router.push(url);
  // PubSub.publish('UPDATE_MENU_ACTIVE', url);
}

export function nornalizefaultAddress(item: any) {
  if (!item.buildingName && !item.floorName && !item.faultAddress) {
    return '未知';
  } else {
    return (item.buildingName || '') + (item.floorName || '') + (item.faultAddress || '');
  }
}
export function setTempData(data: any) {
  temp_data.value = data;
}
/**
 * 主机回路点位
 * @param {(Object)} newInfo
 */
export function switchCode(newInfo: any) {
  // 如果三个都不存在，则显示二次码
  if (!newInfo.laMake && !newInfo.laLoop && !newInfo.laPoint) {
    return '--';
  } else {
    newInfo.laMake = newInfo.laMake ? newInfo.laMake : '';
    newInfo.laLoop = newInfo.laLoop ? newInfo.laLoop : '';
    newInfo.laPoint = newInfo.laPoint ? newInfo.laPoint : '';
    let pinjie = '';
    if (newInfo.laMake !== '') {
      pinjie = newInfo.laMake;
    }
    if (newInfo.laMake !== '' && newInfo.laLoop !== '') {
      pinjie = newInfo.laMake + '-' + newInfo.laLoop;
    }
    if (newInfo.laMake !== '' && newInfo.laLoop !== '' && newInfo.laPoint !== '') {
      pinjie = newInfo.laMake + '-' + newInfo.laLoop + '-' + newInfo.laPoint;
    }
    return pinjie;
    // return newInfo.laMake + '-' + newInfo.laLoop + '-' + newInfo.laPoint ;
  }
}

export function normalizeAddress(item: any) {
  let address =
    (item.buildingName || item.buildName || '') +
    '' +
    (item.floorName || item.floorName || '') +
    '' +
    (item.deviceAddress || item.faultAddress || '');

  if (item.unitType != 0 && item.unitType) {
    address = (item.houseNumber || '') + (item.deviceAddress || '');
  }

  return address.trim() === '' ? '未采集' : address;
}
