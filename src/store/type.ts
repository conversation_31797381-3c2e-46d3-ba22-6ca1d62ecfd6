export interface IAuthState {
  /** 用户信息 */
  userInfo: Partial<ILoginRes>;
}

/**
 * 登录接口返回的信息 todo
 */
export interface ILoginRes {
  id: string;
  loginName: string;
  sysCode: string;
  systemName: string;
  userName?: string;
  userTelphone: string;
  userToken: string;
  userType: string;
  superviseUnitId?: string;
  unitId?: string;
  userRoleCodes?: string;
  userId?: string | number;
  orgCode?: string | number;
}
