import pkg from '../../package.json';
import { defineStore } from 'pinia';
import { loginApi } from '@/api/login';
import { getFileConfig } from '@/views/hazard-management/fetchData';
import { getToken, setToken, removeToken } from '@/utils/auth';
import { useMessage } from 'naive-ui';
import { default as useCurrentUnit } from './currentUnit';
import { $Config } from '@tanzerfe/http';
import { checkUnitIdIsBusinessFlag, getOrgDetailInfo } from '@/views/data_statistics/fetchData';

const message = useMessage();
export const useStore = defineStore(`${pkg.name}-store`, {
  persist: true,
  state: (): any => ({
    userInfo: {
      token: getToken(), //用户token
      userName: null, //用户token
      unitId: null, //单位id
    },
  }),
  getters: {
    // 登录状态
    isLogin(state) {
      return Boolean(state.userInfo.userToken);
    },
  },
  actions: {
    reset() {
      this.$reset();
    },
    // 登录
    login(data: any) {
      return new Promise((resolve: any, reject) => {
        loginApi(data)
          .then((res: any) => {
            if (res.code == 200) {
              setToken(res.data.token);
              this.userInfo = res.data;
              resolve({ code: res.code, message: res.message });
              //刷新页面后会重新执行main.ts中的setupApi() 方法 ，从新设置请求头自定义参数   用于修复多部门账号登录后 请求头token未更新问题
              // location.href = location.href.split("?")[0];
              // 修复同上问题
              $Config.getCustomHeaders = {
                Token: res.data.token,
                Zhid: res.data.zhId,
                id: res.data.id,
                roleCodes: res.data.roleCodes,
              };
            } else {
              resolve({ code: res.code, message: res.message });
            }
          })
          .catch((error) => {
            message.error(error.message);
            removeToken();
            reject(error);
            const path = 'http://192.168.202.247:10122/ycsy-platform/#/staging';
            location.href = path;
          });
      });
    },
    // 获取文件路径前缀
    getFilePrefix() {
      return new Promise(async (resolve: any, reject) => {
        try {
          const {
            data: { fileUrlPrefix },
          }: any = await getFileConfig();
          localStorage.setItem('filePrefix', fileUrlPrefix);
          resolve();
        } catch (error) {
          reject(error);
        }
      });
    },
    // 校验当前单位是否存在下级业务单位
    checkUnitIdIsBusinessFlagApi() {
      return new Promise(async (resolve: any, reject) => {
        try {
          const { data }: any = await checkUnitIdIsBusinessFlag({
            unitId: this.userInfo.unitId,
          });
          localStorage.setItem('isBusinessFlag', data);
          console.log('=============>checkUnitIdIsBusinessFlag');
          resolve();
        } catch (error) {
          reject(error);
        }
      });
    },
    // 当前单位已经是最底级的业务单位  就获取当前的
    getOrgDetailInfoApi() {
      return new Promise(async (resolve: any, reject) => {
        try {
          const {
            data: { levelCode },
          }: any = await getOrgDetailInfo({
            orgCode: this.userInfo.unitId,
          });
          localStorage.setItem('levelCode', levelCode);
          console.log('=============>getOrgDetailInfo');
          resolve();
        } catch (error) {
          reject(error);
        }
      });
    },
  },
});

export { default as useCurrentUnit } from './currentUnit';
