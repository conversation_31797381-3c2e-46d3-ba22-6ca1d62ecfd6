import { getAllUnit } from '@/views/hazard-management/fetchData';
import { defineStore } from 'pinia';
import { useStore } from '.';
import { SelectOption } from 'naive-ui';

interface ISelectOptionsStore {
  _unitOptions: null | SelectOption[];
  //   get unitOptions(): SelectOption[];
}
export const useSelectOptionsStore = defineStore('selectOptionsStore', {
  state: (): ISelectOptionsStore => ({
    _unitOptions: null,
  }),
  getters: {
    unitOptions(state) {
      if (!this._unitOptions) {
        this._unitOptions = [];
        const store = useSelectOptionsStore();
        store.getUnitOptions();
      }
      return this._unitOptions;
    },
  },
  actions: {
    async getUnitOptions() {
      const userInfo = useStore().userInfo;
      const params = {
        pageNo: 1,
        pageSize: -1,
        orgCode: userInfo.orgCode,
      };
      const res = await getAllUnit(params);
      res.data.rows.forEach((el: any) => {
        el.disabled = false;
      });
      this._unitOptions = res.data.rows.map((item: any) => ({
        label: item.unitName,
        value: item.id,
        orgType: item.orgType,
      }));
    },
  },
});
