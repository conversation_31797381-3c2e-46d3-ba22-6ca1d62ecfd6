import {
  createRouter,
  createWebHashHistory,
  NavigationGuardNext,
  RouteLocationNormalized,
  RouteLocationNormalizedLoaded,
} from 'vue-router';

import routes from './routes';
import { useStore } from '@/store';

const Router = createRouter({
  scrollBehavior: () => ({
    left: 0,
    top: 0,
  }),
  routes,
  history: createWebHashHistory(),
});

Router.beforeEach(
  async (to: RouteLocationNormalized, from: RouteLocationNormalizedLoaded, next: NavigationGuardNext) => {
    // 检查用户是否登录
    if (to.query.token && to.query.sysCode) {
      const { token, sysCode } = to.query;
      const userStore = useStore();
      await userStore.login({ token, sysCode });
      await userStore.getFilePrefix();
      await userStore.checkUnitIdIsBusinessFlagApi();
      await userStore.getOrgDetailInfoApi();
      next();
      return;
    }
    const hasUnitId = JSON.parse(localStorage?.getItem('ehs-hazard-mgr-store') || '{}')?.userInfo?.unitId
      ? true
      : false;
    if (!hasUnitId) {
      const currentUrl = location.href; //获取当前url
      if (currentUrl.includes('223.247.156.85:9832')) {
        location.href = 'https://agjp.tanzervas.com/aqsc/v1/platform/index.html#/staging';
      } else if (currentUrl.includes('192.168.202.247')) {
        location.href = 'http://192.168.202.247:10122/ycsy-platform/#/staging';
      }
    }
    next();
  }
);
export default Router;
