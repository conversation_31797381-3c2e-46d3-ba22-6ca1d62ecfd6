import { RouteRecordRaw } from 'vue-router';
import MainLayout from '@/layouts/MainLayout.vue';

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'topRoute',
    component: MainLayout,
    redirect: '/mis',
    children: [
      {
        path: '/home',
        name: 'home',
        component: () => import('@/views/home/<USER>'),
      },
      {
        path: '/hazard-management',
        name: 'hazardManagement',
        component: () => import('@/views/hazard-management/index.vue'),
      },
      {
        path: '/mis',
        name: 'mis',
        component: () => import('@/views/mis/index.vue'),
      },
      {
        path: '/task-management',
        children: [
          {
            path: '',
            name: 'taskManagement',
            component: () => import('@/views/task-management/index.vue'),
          },
          {
            path: 'hazard-task-detail/:id',
            name: 'hazardTaskDetail',
            component: () => import('@/views/task-management/hazard-task-details.vue'),
          },
        ],
      },
      {
        path: '/inspection-planning',
        name: 'inspectionPlanning',
        children: [
          {
            path: 'inspection-task',
            name: 'inspectionTask',
            component: () => import('@/views/inspection-planning/inspection-task/index.vue'),
          },
          {
            path: 'planned-management',
            name: 'plannedManagement',
            children: [
              {
                path: 'index',
                name: 'plannedManagementIndex',
                component: () => import('@/views/inspection-planning/planned-management/index.vue'),
              },
              {
                path: 'add',
                name: 'plannedManagementAdd',
                component: () => import('@/views/inspection-planning/planned-management/Add/index.vue'),
              },
              {
                path: 'edit/:id',
                name: 'plannedManagementEdit',
                component: () => import('@/views/inspection-planning/planned-management/Add/index.vue'),
              },
              {
                path: 'details/:id',
                name: 'plannedManagementDetails',
                component: () => import('@/views/inspection-planning/planned-management/Details/index.vue'),
              },
            ],
          },
        ],
      },
      {
        path: 'hiddenDangersPhoto',
        name: 'hiddenDangersPhoto',
        component: () => import('@/views/hidden-dangers-photo/index.vue'),
      },
      {
        path: 'dangerScreen',
        name: 'dangerScreen',
        component: () => import('@/views/screen/index.vue'),
      },
      // 检查表
      {
        path: '/checkList',
        children: [
          {
            path: '',
            name: 'checkList',
            component: () => import('@/views/inspect-mgr/inspect/index.vue'),
          },
          {
            path: 'add',
            name: 'inspectPointAdd',
            component: () => import('@/views/inspect-mgr/inspect/comp/editModel.vue'),
          },
          {
            path: 'edit/:id',
            name: 'inspectPointEdit',
            component: () => import('@/views/inspect-mgr/inspect/comp/editModel.vue'),
          },
        ],
      },
      {
        path: '/params',
        name: 'params',
        children: [
          // 参数配置
          {
            path: 'paramsConf',
            name: 'paramsConf',
            component: () => import('@/views/paramsConf/index.vue'),
          },
        ],
      },
      // 巡查点位管理
      {
        path: '/inspection_point',
        name: 'inspectionPoint',
        component: () => import('@/views/inspection_point/index.vue'),
      },
      // 数据统计概况
      {
        path: '/data_statistics',
        children: [
          {
            path: '',
            name: 'dataStatistics',
            component: () => import('@/views/data_statistics/index.vue'),
          },
          {
            path: 'dataStatisticsDetail',
            name: 'dataStatisticsDetail',
            component: () => import('@/views/data_statistics/detail/index.vue'),
          },
        ],
      },
      // 隐患数据库
      {
        path: '/risk-database',
        children: [
          {
            path: '',
            name: 'riskDatabase',
            component: () => import('@/views/risk-database/risk-library/index.vue'),
          },
          // 隐患详情
          {
            path: 'risk-library-detail',
            name: 'riskLibraryDetail',
            component: () => import('@/views/risk-database/risk-library/comp/detail/index.vue'),
          },
        ],
      },
      {
        path: 'random-check',
        children: [
          {
            path: '',
            name: 'randomCheck',
            component: () => import('@/views/random-check/check-task-manage/index.vue'),
          },
          // 详情
          {
            path: 'checkTaskDetail',
            name: 'checkTaskDetail',
            component: () => import('@/views/random-check/check-task-manage/comp/detail/index.vue'),
          },
        ],
      },
      // 举一反三
      {
        path: 'takeOne-anitThree',
        children: [
          {
            path: '',
            name: 'takeOneAnitThree',
            component: () => import('@/views/takeOne-anitThree/index.vue'),
          },
          {
            path: 'detail',
            name: 'takeOneAnitThreeDetail',
            component: () => import('@/views/takeOne-anitThree/detail/index.vue'),
          },
          {
            path: 'add',
            name: 'takeOneAnitThreeAdd',
            component: () => import('@/views/takeOne-anitThree/add/index.vue'),
          },
          // 新增频发问题
          {
            path: 'addFrequently',
            name: 'takeOneAnitThreeAddFrequently',
            component: () => import('@/views/takeOne-anitThree/add/addFrequently.vue'),
          },
        ],
      },
      // 数据分析
      {
        path: 'data-analysis',
        children: [
          {
            path: '',
            name: 'dataAnalysis',
            component: () => import('@/views/dataAnalysis/index.vue'),
          },
        ],
      },
    ],
  },
];

export default routes;
