import { BRI_EVENT_TYPE, ISender } from './type';
import { DDebounce } from '@/utils/decorator.ts';
import { setUserInfo } from './SyncData';
import { Subject } from 'rxjs';
import { sySend } from '@/utils/sySend.ts';

export class BridgeRemoteService {
  private static _instance: BridgeRemoteService;
  public static getInstance() {
    if (!this._instance) {
      this._instance = new BridgeRemoteService();
    }

    return this._instance;
  }

  public static init() {
    this.getInstance();
  }

  public enableLog = true; // log开关

  // 暴露数据流给外部方便走自定义解析器
  public data$ = new Subject();

  private constructor() {
    // reg
    this.registerEvents();

    // on loaded
    this.notifyLoaded();
    window.onload = () => this.notifyLoaded();
  }

  private registerEvents() {
    sySend.on(BRI_EVENT_TYPE.LOGIN, (message, event) => {
      const { data } = (message || {}) as ISender;
      // 保存登录信息
      setUserInfo(data.data || {});
      this.data$.next(message);
    });

    // 接收消息
    sySend.on(BRI_EVENT_TYPE.MESSAGE, (message, event) => {
      this.data$.next(message);
      this.log(BRI_EVENT_TYPE.MESSAGE, message);
    });

    // 窗口关闭
    sySend.on(BRI_EVENT_TYPE.DESTROY, (message, event) => {
      window.self.close();
    });
  }

  @DDebounce(500)
  private notifyLoaded() {
    // 派发加载完成通知
    sySend.broadcast(BRI_EVENT_TYPE.LOADED, { type: BRI_EVENT_TYPE.LOADED });
    this.log(BRI_EVENT_TYPE.LOADED, { type: BRI_EVENT_TYPE.LOADED });
  }

  private log(name: string, data: any) {
    if (this.enableLog) {
      console.log(name + '::', data);
    }
  }
}
