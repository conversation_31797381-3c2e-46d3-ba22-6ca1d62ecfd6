import { BRI_EVENT_TYPE } from './type';
import { DDebounce } from '@/utils/decorator.ts';
import { getUserInfo } from './SyncData';
import { Subject } from 'rxjs';
import { sySend } from '@/utils/sySend.ts';
import { toRaw, watch } from 'vue';
import { useStore } from '@/store';

export class BridgeHostService {
  private static _instance: BridgeHostService;
  public static getInstance() {
    if (!this._instance) {
      this._instance = new BridgeHostService();
    }

    return this._instance;
  }

  public static init() {
    this.getInstance();
  }

  public enableLog = true; // log开关

  // 暴露数据流给外部方便走自定义解析器
  public data$ = new Subject();

  private constructor() {
    // reg
    this.registerEvents();

    // watcher
    this.startWatch();

    // on loaded
    this.notifyLoaded();
    window.onload = () => this.notifyLoaded();
  }

  private registerEvents() {
    sySend.on(BRI_EVENT_TYPE.LOADED, (message, event) => {
      // 同步数据
      this.syncAuth();

      this.data$.next(message);
      this.log(BRI_EVENT_TYPE.LOADED, message);
    });
  }

  /**
   * 同步用户信息
   */
  public syncAuth = () => {
    sySend.broadcast(BRI_EVENT_TYPE.LOGIN, toRaw(getUserInfo()));
  };

  @DDebounce(500)
  private notifyLoaded() {
    // 同步数据给remote
    this.syncAuth();
  }

  private startWatch() {
    const auth = useStore();

    watch(
      () => auth.isLogin,
      (nv, ov) => {
        const isLogin = nv && !ov;
        const isLogout = !nv && ov;

        // 登录登出 同步
        (isLogin || isLogout) && this.syncAuth();
      }
    );
  }

  private log(name: string, data: any) {
    if (this.enableLog) {
      console.log(name + '::', data);
    }
  }
}
