import { BridgeService } from './BridgeService';
// import PubSub from 'pubsub-js';
import { misToGisTypes } from './types';
import { useCurrentUnit } from '@/store';

const curProject = 'hazardMgr';
const onMessageKey: any = {
  setUnitId: curProject + '_' + 'screen_set_unit_Id',
};

export class misToGisBridgeService {
  private static _ins: BridgeService;
  static getIns() {
    if (!this._ins) {
      // 例如：ehsRiskMgr_channel
      this._ins = new BridgeService(curProject + '_channel', true); // 每个项目进来初始化自己项目名的通道
    }
    return this._ins;
  }

  static registerEvents() {}

  static sendEvents(type: any, data: any) {
    misToGisBridgeService._ins.sendMessage(curProject + '_' + type, data);
  }
  static onMessage() {
    Object.keys(onMessageKey).forEach((key) => {
      if (key == 'setUnitId') {
        misToGisBridgeService._ins.onMessage(onMessageKey[key], (data) => {
          console.log('🚀 ~ misToGisBridgeService ~ misToGisBridgeService._ins.onMessage ~ data:', data);
          useCurrentUnit().currentUnitId = data?.unitId || '';
          // PubSub.publish(onMessageKey[key], data);
        });
      }
    });
  }
  static init() {
    this.getIns();
    this.onMessage();
  }
  static clear() {
    misToGisBridgeService._ins.clearListeners();
  }
}
