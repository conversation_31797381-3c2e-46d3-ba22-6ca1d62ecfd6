<template>
  <n-breadcrumb
    :theme-overrides="breadcrumbTheme"
    :style="{ height: toVw(45), fontSize: toVw(18), 'line-height': toVw(45) }"
    class="flex items-center"
  >
    <n-breadcrumb-item
      v-for="item of data"
      :clickable="!!item.clickable"
      :key="item.name"
      @click="handleClick(item)"
      :style="{ fontSize: toVw(16) }"
    >
      {{ item.name }}
    </n-breadcrumb-item>
  </n-breadcrumb>
</template>

<script lang="ts" setup>
import breadcrumbTheme from './theme';
import { useRouter } from 'vue-router';
import type { IBreadData } from '@/components/breadcrumb/type.ts';
import { toVw, toVh } from '@/utils/fit';
interface Props {
  data: IBreadData[];
}

const props = withDefaults(defineProps<Props>(), {
  data: () => [],
});
const router = useRouter();

/**
 * 路由跳转
 * @param item
 */
function handleClick(item: IBreadData) {
  if (item.clickable && item.routeRaw) {
    router.push(item.routeRaw);
  }
}

defineOptions({ name: 'ComBread' }); // 面包屑组件
</script>

<style scoped>
:deep(.n-breadcrumb-item__link) {
  font-size: revert;
}
</style>
