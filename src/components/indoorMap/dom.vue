<template>
  <div class="h-full w-full">
    <div class="h-full w-full">
      <div class="rounded h-full w-full">
        <div class="w-full h-full relative">
          <floorMap
            :floor-info="floorData"
            :device-list="deviceList"
            :isAddMark="true"
            :pointer="pointer"
            @add-mark="addMark"
          ></floorMap>
        </div>
      </div>
    </div>
  </div>
</template>
<!--
  //在index.html 中引入 gis css js
    <link
      rel="stylesheet"
      href="https://www.tanzercloud.com/api/v2/gissetter-service/MapEnginePackage/mapEnginePackageCSS?mepName=IndoorThree_CSS&wgId=67"
    />
     -->
<!--
  <script
    type="text/javascript"
    src="https://www.tanzercloud.com/api/v2/gissetter-service/MapEnginePackage/mapEnginePackage?mepName=IndoorThree&wgId=67"
  ></script>
-->

<script setup lang="ts">
import { onMounted, ref, h, watch } from 'vue';
import floorMap from './index.vue';
import { pointerList, FxRes } from './data';
import { GISOBJ } from './gisGlobal';

const gisMap3DM = GISOBJ.getIndoorMap();

const deviceList = ref(pointerList.data);
const pointer = {
  x: 3028718.761922908,
  y: 909491.4322155592,
};
const floorData = ref({
  unitId: 'e3de3e5405664eacb434d8e2aafe6d37',
  buildingId: '110000DW1832753728612990976_009',
  floorId: '110000DW1832753728612990976_009_U001',
  floorAreaImg: '',
});

const colorFill = [
  {
    fill: { color: 'rgba(207, 38, 27, 0.7)' },
    stroke: { color: 'rgba(207, 38, 27, 1)', width: 1 },
  },
  {
    fill: { color: 'rgba(245, 154, 34, 0.7)' },
    stroke: { color: 'rgba(245, 154, 34, 1)', width: 1 },
  },
  {
    fill: { color: 'rgba(244, 244, 16, 0.7)' },
    stroke: { color: 'rgba(244, 244, 16, 1)', width: 1 },
  },
  {
    fill: { color: 'rgba(39, 101, 255, 0.7)' },
    stroke: { color: 'rgba(39, 101, 255, 1)', width: 1 },
  },
];

let data2 = {
  unitId: 'AHHF_QHHFY_20180408',
  buildingId: 'AHHF_QHHFY_20180408_002',
  floorId: 'AHHF_QHHFY_20180408_002_U001',
  floorAreaImg: '',
};

let data3 = {
  unitId: 'AHHF_QHHFY_20180408',
  buildingId: 'AHHF_QHHFY_20180408_006',
  floorId: 'AHHF_QHHFY_20180408_006_U001',
  floorAreaImg: '',
};

const addMark = (val: { x: number; y: number; text: string }) => {
  console.log('🚀 ~ addMark ~ val:', val);
};

// 接口查询楼层批量上色
const queryFloorBgColor = (floorId?: string) => {
  const useKey = 'currentRiskLevel';
  //  : 'riskLevel';
  const parms = {
    floorId: floorId,
  };
  const res = FxRes;
  if (res.data.length) {
    for (let index = 0; index < res.data.length; index++) {
      const element = res.data[index];
      if (element.locationGisId) {
        const level = element[useKey] && Number(element[useKey]);
        const pSymbol = gisMap3DM.InitStyleInfoValueItemByGSDataToSymbol(colorFill[level - 1], undefined, 'fill');
        const pElement = gisMap3DM.getCurrentGridAreaLayer().GetGeoByFieldNV('gridNo', element.locationGisId);
        pElement.setSymbol(pSymbol);
        pElement.GeoUpdate();
        gisMap3DM.render();
      }
    }
  }
};

onMounted(async () => {
  setTimeout(() => {
    gisMap3DM.options.gridLoad = true;
    gisMap3DM.setGridVisible(true);
    queryFloorBgColor();
  }, 3000);
});

defineOptions({ name: 'plannedManagementDetails' });
</script>
<style module lang="scss"></style>
