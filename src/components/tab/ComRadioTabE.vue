<template>
  <div :class="[$style.tabsCorner]">
    <div
      :class="[$style.tabsCornerItemWrap]"
      v-for="(item, i) in props.tabList"
      :key="i"
      :style="{
        'z-index': 11 - i,
        height: toVh(40),
        padding: `0 ${toVh(15)}`,
        'line-height': toVh(43),
        'font-size': toVh(16),
      }"
      @click="tabChange(item.name)"
    >
      <div :class="{ [$style.tabsCornerItem]: true, [$style.tabsCornerItemActive]: item.name === tabAct }">
        <p :class="[$style.tabsCornerItemText]">{{ item.label }}</p>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { toVh } from '@/utils/fit';
const emits = defineEmits(['change']);

type listT = {
  tabList: any;
  tab: any;
};
const props = defineProps<listT>();
const tabAct = ref(props.tab);
function tabChange(v: any) {
  tabAct.value = v;
  emits('change', v);
}

defineOptions({ name: 'ComRadioTabE' });
</script>

<style module lang="scss">
.tabsCorner {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  position: relative;
  box-sizing: border-box;
  overflow: hidden;
}

.tabsCornerItemWrap {
  min-width: 119px;
  height: 40px;
  padding: 0 15px;
  line-height: 43px;
  margin-left: -16px;
  position: relative;
  z-index: 10;
  cursor: pointer;

  .tabsCornerItem {
    position: relative;
    width: 100%;
    height: 100%;
    background-color: #eef7ff;
    border-radius: 8px 8px 0 0;
    text-align: center;

    //box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
    &::before,
    &::after {
      content: '';
      position: absolute;
      top: 0;
      width: 15px;
      height: 100%;
      background: #eef7ff;
    }

    &::before {
      border-top-left-radius: 6px;
      transform: skew(-15deg);
      left: -8px;
      background: #eef7ff;
    }

    &::after {
      border-top-right-radius: 6px;
      transform: skew(20deg);
      right: -8px;
      background: #eef7ff;
      box-shadow: 3px 18px 4px 0px rgba(0, 0, 0, 0.1);
    }

    &.tabsCornerItemActive {
      background-color: #527cff;
      color: #fff;

      &::before,
      &::after {
        background: #527cff;
      }
    }

    .tabsCornerItemText {
      padding: 0 10px;
    }
  }

  &:first-child {
    .tabsCornerItem {
      &::before {
        transform: skew(0deg);
      }
    }
  }
}
</style>
