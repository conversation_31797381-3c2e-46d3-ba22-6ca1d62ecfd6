<template>
  <n-modal
    ref="modalRef"
    v-model:show="showModal"
    :width="width"
    :height="height"
    preset="card"
    :title="title"
    :show-icon="false"
    :theme-overrides="overrideModalTheme()"
    :style="wrapStyle"
    header-style="padding:10px;  font-size: 18px;"
    :content-style="`padding: 0 10px; ${contentStyle.join(';')}`"
    :contentClass="contentClass"
    @after-enter="useDragModal($event)"
    @after-leave="handleLeave"
  >
    <template #header>
      <div class="text-[18px] py-">
        <slot name="header" />
      </div>
    </template>

    <div class="h-full p-5" :style="{ maxHeight: contentMaxHeight }">
      <slot></slot>
    </div>

    <template #action>
      <slot name="action" />
    </template>
  </n-modal>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue';
import { overrideModalTheme } from './modalTheme';
import { useDragModal } from '@/components/dialog/dragPlugin';
defineOptions({ name: 'ComDialog' }); // 基础弹窗组件支持 n-modal 所有属性
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: '',
  },
  contentClass: {
    type: String,
  },
  contentStyle: {
    type: Array,
    default: () => [],
  },
  width: {
    type: Number,
    default: 900,
  },
  height: {
    type: Number,
    default: 600,
  },
  showClose: {
    type: Boolean,
    default: true,
  },
  wrapStyle: {
    type: String,
  },
  modelColor: {
    type: String,
    default: 'rgba(255,255,255,1)',
  },
});
const showModal = ref(false);
watch(
  () => props.modelValue,
  (val) => {
    showModal.value = val;
  }
);
const emits = defineEmits(['handleClose', 'handleNegative', 'handlePositive']);
const modalRef = ref();

const minH = computed(() => Math.max(props.height, 120));
const wrapStyle = computed(() => {
  if (props.wrapStyle) {
    return props.wrapStyle + `--n-color-modal: ${props.modelColor}`;
  } else {
    return `width: ${props.width}px; height: ${minH.value}px;` + `--n-color-modal: ${props.modelColor}`;
  }
});
const modelStyle = computed(() => `--n-color-modal: ${props.modelColor}`);

// 内容区域最大高度，计算公式：弹窗高 - title(-action) - padding
const contentMaxHeight = computed(() => {
  let h = minH.value - 40 - 10;
  return `${h}px`;
});

function close() {
  console.log('🚀 ~ close ~ close:', close);
  emits('handleClose', false);
  // showModal.value = false;
}
function handleLeave() {
  console.log(111);

  emits('handleClose');
}
</script>

<style module lang="scss"></style>
