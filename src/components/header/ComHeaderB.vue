<!--
 * @Author: fangweiwei <EMAIL>
 * @Date: 2024-11-14 10:22:43
 * @LastEditors: fangweiwei <EMAIL>
 * @LastEditTime: 2024-12-05 11:13:20
 * @FilePath: \ehs-hazard-mgr\src\components\header\ComHeaderB.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="header flex justify-between items-center p-3" :style="{ height: toVh(44), width: '100%' }">
    <div class="flex justify-start items-center">
      <slot name="h-icon" v-if="hasIcon">
        <img :src="activeColor ? activeIcon : normalIcon" alt="" />
      </slot>
      <div class="title" :style="{ fontSize: toVh(17) }">{{ title }}</div>
      <slot name="content"> </slot>
    </div>
    <slot name="right"> </slot>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import activeIcon from './assets/icon-title-arrow2.png';
import arrow3Icon from './assets/icon-title-arrow3.png';
import titleArrowIcon from './assets/icon-title-arrow.png';
import { toVw, toVh } from '@/utils/fit';

interface Props {
  title: string;
  activeColor?: boolean;
  blueIcon?: boolean;
  hasIcon?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  activeColor: false,
  blueIcon: true,
  hasIcon: true,
});

const normalIcon = computed(() => {
  if (props.blueIcon) {
    return arrow3Icon;
  } else {
    return titleArrowIcon;
  }
});

defineOptions({ name: 'ComHeaderB' });
</script>

<style lang="scss" scoped>
.header {
  --bgc: rgba(82, 124, 255, 0.15);
}

::v-deep.header {
  background: rgba(82, 124, 255, 0.1);
  border-radius: 4px 4px 4px 4px;
  width: 100%;
  height: 44px;

  img {
    width: 17px;
    height: 12px;
    margin-right: 20px;
  }

  background: var(--bgc);
  border-radius: 4px 4px 4px 4px;

  .n-input,
  .n-input .n-input__input-el,
  .n-button {
    height: 32px;
    line-height: 32px;
  }
}

.title {
  font-weight: 700;
  font-size: 16px;
  color: #527cff;
  font-family:
    Alibaba PuHuiTi 2,
    Alibaba PuHuiTi 20;
}
</style>
