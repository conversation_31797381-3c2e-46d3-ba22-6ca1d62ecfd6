export interface IUpload {
  id: string;
  name: string;
  percentage: number;
  status: 'pending' | 'uploading' | 'error' | 'finished' | 'removed';
  url: null | string;
  file: File;
  thumbnailUrl: null | string;
  type: string;
  fullPath: string;
  batchId: string;
  res: IUploadRes | null;
}

export interface IUploadRes {
  fileName: string;
  saveType: string;
  saveName: string;
  fileType: string;
  fileUrl: string;
  [property: string]: any;
}
