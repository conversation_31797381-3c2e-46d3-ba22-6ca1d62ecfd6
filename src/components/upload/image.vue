<template>
  <div v-if="mode === 'detail'" class="w-full">
    <div v-if="fileList && fileList.length" class="flex justify-start items-center">
      <n-image
        class="w-[100px] rounded-[4px] mx-[10px]"
        v-for="file in fileList"
        :key="file.url"
        :src="getFileURL(file.url, true)"
        :preview-src="getFileURL(file.url)"
        object-fit="cover"
      />
    </div>
    <templae v-else>--</templae>
  </div>
  <div v-else class="w-full">
    <!-- {{ fileList }} -->
    <n-upload
      :action="actionUrl"
      response-type="json"
      name="files"
      :default-file-list="fileList"
      :show-file-list="false"
      :on-error="handleError"
      :on-before-upload="handleBeforeUpload"
      :accept="props.accept"
      :multiple="props.multiple"
      :custom-request="customRequest"
      abstract
    >
      <!-- 自定义展示图片列表 -->
      <div class="flex justify-start items-center" v-loading="loading">
        <div
          class="w-[100px] h-[100px] rounded-[4px] mx-[10px] relative"
          v-for="(src, index) of fileList"
          :key="src.id"
        >
          <n-image
            class="w-full h-full"
            :src="getFileURL(src.url, true)"
            :preview-src="getFileURL(src.url)"
            object-fit="cover"
          />
          <el-icon :size="25" color="#fff" class="del-img-btn" @click="handleDel(index)">
            <Close />
          </el-icon>
        </div>
        <!-- 自定义上传按钮 -->
        <n-upload-trigger #="{ handleClick }" abstract>
          <div
            class="upload-btn flex justify-center items-center rounded-[4px] mx-[10px] cursor-pointer"
            @click="handleClick"
            v-if="fileList.length < max"
          >
            <el-icon :size="20" color="#000">
              <Plus />
            </el-icon>
          </div>
        </n-upload-trigger>
      </div>
    </n-upload>
    <div class="mt-[10px] text-[#999999]" v-if="props.tips">
      {{ props.tips }}
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import { UploadFileInfo } from 'naive-ui';
import { Plus, Close } from '@element-plus/icons-vue';
import { $toast } from '@/common/shareContext/useToastCtx.ts';
import { api } from '@/api';
import { useMessage } from 'naive-ui';
import type { UploadCustomRequestOptions } from 'naive-ui';
import { $http } from '@tanzerfe/http';
// 导入图片压缩插件
import ImageCompressor from 'image-compressorionjs';
import getFileURL from '@/utils/getFileURL';
import { uuid } from 'vue-uuid';

const message = useMessage();
const loading = ref(false);

defineOptions({ name: 'ImgUpload' });

interface IItem {
  fileName: string;
  fileUrl: string;
  id: string;
}
interface Props {
  data: IItem[];
  mode?: string; // detail为展示图片 其他为上传图片
  max?: number; // 限制上传数量
  size?: number; // 限制文件大小
  accept?: string; // 文件类型
  multiple?: boolean; //是否批量上传
  tips?: string; // 上传提示
  type?: string; //文件列表的内建样式
  rowNumber?: number; //每行显示多少
  imgTitle?: string; //默认文字
  groupName?: string; //分组名
  width?: number; //图片宽度
  showBtn?: boolean; //是否显示上传按钮
  watermarkData?: any; //水印配置
}

const actionUrl = api.getUrl(api.type.hazard, api.common.file.watermarkUploads);
const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  data: () => [],
  max: 15,
  accept: '.jpg,.jpeg,.png',
  multiple: true,
  type: 'image',
  rowNumber: 4,
  imgTitle: '点击上传文件',
  groupName: '',
  showBtn: true,
  watermarkData: null,
  size: 15,
});
const emits = defineEmits(['update']);
// 默认文件列表(用于编辑时回显已上传文件)
const fileList = ref([]);

watch(
  () => props.data,
  (data: any) => {
    console.log(data, '监听data数据');
    if (data && data.length > 0) {
      fileList.value = data.map((item: any) => {
        return {
          id: uuid.v1(),
          name: item.fileName,
          url: item.fileUrl,
        };
      });
      console.log(fileList.value, '初始化数据11111111111111');
    }
  },
  {
    deep: true,
    immediate: true,
  }
);

// 上传前校验(文件大小、类型)
function handleBeforeUpload(options: { file: UploadFileInfo }) {
  const { file } = options;
  if (!file.file) return false;
  const fileExt = file.name.slice(file.name.lastIndexOf('.') + 1);
  if (!props.accept.includes(fileExt)) {
    // 仅支持JPG、PNG格式的图片
    const accept = props.accept.replace(/\./g, '').replace(',', '、').toUpperCase();
    $toast.error(`仅支持${accept}格式的图片!`);
    return false;
  } else if (props.size && file.file.size / 1024 / 1024 > props.size) {
    $toast.error(`文件大小不超过 ${props.size} MB!`);
    return false;
  }
  return true;
}

const customRequest = async ({
  file,
  data,
  headers,
  withCredentials,
  action,
  onFinish,
  onError,
  onProgress,
}: UploadCustomRequestOptions) => {
  console.log(file.file, '压缩前的文件');
  let newFile = await compressImage(file.file as File);
  console.log(newFile, '压缩后文件');
  const formData = new FormData();
  if (data) {
    Object.keys(data).forEach((key) => {
      formData.append(key, data[key as keyof UploadCustomRequestOptions['data']]);
    });
  }
  let { waterTemplateName, projectName, address, dateTime, logoImageUrl, unitName, userName, weather } =
    props.watermarkData;
  formData.append('groupName', 'files'); //模板类型:
  formData.append('projectName', 'hazard'); // 项目名
  formData.append('waterTemplateName', waterTemplateName); //模板类型:
  formData.append('watermarkProjectName', projectName); // 项目名
  formData.append('address', address); //地址
  formData.append('dateTime', dateTime); //时间
  formData.append('logoImageUrl', logoImageUrl); //LOGO图片地址
  formData.append('unitName', unitName);
  formData.append('userName', userName);
  formData.append('weather', weather);
  formData.append('workContent', '隐患排查');
  formData.append('files', newFile as File);
  loading.value = true;
  $http
    .post(action as string, {
      withCredentials,
      headers: headers as Record<string, string>,
      data: formData,
      onUploadProgress: ({ percent }) => {
        onProgress({ percent: Math.ceil(percent) });
      },
    })
    .then((res: any) => {
      loading.value = false;
      if (res.code === 'success') {
        uploadSuccess(res.data[0], onFinish);
      } else {
        message.error(res.message);
      }
    })
    .catch((error) => {
      loading.value = false;
      message.error(error.message);
      onError();
    });
};

function uploadSuccess(data: any, onFinish: any) {
  let { saveName, fileUrl } = data;
  let file = {
    id: uuid.v1(),
    name: saveName,
    url: fileUrl,
  };
  fileList.value = fileList.value.concat(file);
  emitData();
  console.log(fileList.value, '上传成功222222222');
  onFinish();
}

// 压缩图片
const compressImage = (file: File) => {
  if (!(file && file.size > 0)) {
    return;
  }
  return new Promise((resolve, reject) => {
    new ImageCompressor(file, {
      quality: 0.4,
      success(result: any) {
        resolve(result);
      },
      error(error: any) {
        reject(error);
      },
    });
  });
};

// 上传失败
function handleError(e: any) {
  console.log(e, 'e');
}

// 删除图片更新文件列表
function handleDel(index: number) {
  fileList.value.splice(index, 1);
  emitData();
}
function emitData() {
  let data = fileList.value.map((item: any) => {
    return {
      fileUrl: item.url,
      fileName: item.name,
      id: item.id,
      saveName: item.name,
    };
  });
  emits('update', data);
}
</script>

<style scoped lang="scss">
.upload-btn {
  width: 100px;
  height: 100px;
  border: 1px dashed #eee;

  &:hover {
    border: 1px dashed #18a058;
    background-color: rgb(250, 250, 252);
  }
}

.del-img-btn {
  cursor: pointer;
  padding: 5px;
  background-color: rgba($color: #000000, $alpha: 0.3);
  border-bottom-left-radius: 50%;
  position: absolute;
  top: 0;
  right: 0;
}

// 定义一个带参数的mixin
@mixin template-columns($size: 96px) {
  grid-template-columns: repeat(auto-fill, $size);

  .n-upload-file.n-upload-file--image-card-type,
  .n-upload-trigger {
    @if $size == '96px' {
      width: $size;
    } @else {
      width: 100%;
    }
  }
}

:deep(.layoutStyle_1) {
  @include template-columns(calc(100% - 8px));
}

:deep(.layoutStyle_2) {
  @include template-columns(calc(50% - 8px));
}

:deep(.layoutStyle_3) {
  @include template-columns(calc(33.33% - 8px));
}

:deep(.layoutStyle_4) {
  @include template-columns(calc(25% - 8px));
}

:deep(.layoutStyle_5) {
  @include template-columns(calc(20% - 8px));
}

:deep(.layoutStyle_6) {
  @include template-columns(calc(16.66% - 8px));
}

:deep(.layoutStyle_7) {
  @include template-columns(calc(14.28% - 8px));
}

:deep(.layoutStyle_8) {
  @include template-columns(calc(12.5% - 8px));
}

:deep(.layoutStyle_9) {
  @include template-columns(calc(11.11% - 8px));
}

:deep(.layoutStyle_10) {
  @include template-columns(calc(10% - 8px));
}
</style>
