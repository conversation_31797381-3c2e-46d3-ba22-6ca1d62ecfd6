<template>
  <div class="w-full pt-[15px]">
    <n-upload
      response-type="json"
      name="files"
      :action="actionUrl"
      :data="{
        // 分组名
        groupName: props.groupName,
        // 项目名
        projectName: 'hazard',
      }"
      :multiple="props.multiple"
      :default-file-list="defaultFileList"
      :on-finish="handleUploadFinish"
      :on-update:file-list="handleUpdate"
      :on-before-upload="handleBeforeUpload"
      :accept="props.accept"
      :max="max"
    >
      <n-button type="primary" size="small">点击上传</n-button>
      <div class="mt-[10px] text-[#999999]" v-if="props.tips">{{ props.tips }}</div>
    </n-upload>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { UploadFileInfo } from 'naive-ui';
import { IUploadRes } from '@/components/upload/type';
import { api } from '@/api';
import { $toast } from '@/common/shareContext/useToastCtx.ts';
import getFileURL from '@/utils/getFileURL';

defineOptions({ name: 'FileUpload' });

interface IItem {
  fjMc: string;
  fjCflj: string;
  fjTywysbm: string;
}
interface Props {
  data: IItem[];
  mode?: string; // detail为展示图片 其他为上传图片
  max?: number; // 限制上传数量
  size?: number; // 限制文件大小
  accept?: string; // 文件类型
  tips?: string; // 上传提示
  groupName?: string;
  multiple?: boolean;
}
//  groupName :image/excel/file/pdf/zip
const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  data: () => [],
  max: 6,
  accept: '',
  groupName: 'file',
  multiple: false,
});

// interface Props {
//   data: IItem[];
//   mode?: string; // detail为展示图片 其他为上传图片
//   max?: number; // 限制上传数量
//   size?: number; // 限制文件大小
//   accept?: string; // 文件类型
//   tips?: string; // 上传提示
//   type?: string; //文件列表的内建样式
//   rowNumber?: number; //每行显示多少
//   imgTitle?: string; //默认文字
//   groupName?: string; //分组名
// }

// const props = withDefaults(defineProps<Props>(), {
//   modelValue: '',
//   data: () => [],
//   max: 6,
//   accept: '.jpg,.jpeg,.png,.webp,.gif,.bmp,.svg',
//   type: 'image',
//   rowNumber: 4,
//   imgTitle: '点击上传文件',
//   groupName: '',
// });

const emits = defineEmits(['update']);
const valueRef = computed<any[]>(() => props.data);
// const actionURL = api.getUrl(api.type.file, api.name.file.uploadFile);
const actionUrl = api.getUrl(api.type.hazard, api.common.file.uploadFile);

// 默认文件列表(用于编辑时回显已上传文件)
const defaultFileList = computed<UploadFileInfo[]>(() => {
  const ret: UploadFileInfo[] = [];

  for (const item of valueRef.value) {
    ret.push({
      id: item.id,
      name: item.fileName + '.' + item.fileType,
      url: getFileURL(item.fileUrl),
      // thumbnailUrl: getFileURL(item.url),
      status: 'finished',
    });
    fileResMap.set(item.id, item);
  }

  return ret;
});
const fileResMap: Map<any, IUploadRes> = new Map(); // 上传结果map

// 上传前校验(文件大小、类型)
function handleBeforeUpload(options: { file: UploadFileInfo }) {
  const { file } = options;
  if (!file.file) return false;
  const fileExt = file.name.slice(file.name.lastIndexOf('.') + 1);
  if (props.accept && !props.accept.includes(fileExt)) {
    $toast.error(`请上传 ${props.accept} 类型的文件!`);
    return false;
  } else if (props.size && file.file.size / 1024 / 1024 > props.size) {
    $toast.error(`文件不能超过 ${props.size} MB，请重新上传!`);
    return false;
  }
  return true;
}

// 上传完成
function handleUploadFinish(options: { file: UploadFileInfo; event?: ProgressEvent }) {
  const { file, event } = options;
  console.log('🚀 ~ handleUploadFinish ~ 上传完成file:', file);

  const data = (<any>event?.target)?.response?.data || {};
  fileResMap.set(file.id, data);
}

// 当文件数组改变时触发的回调函数
function handleUpdate(val: UploadFileInfo[]) {
  const finishedList = val.filter((item) => item.status === 'finished');
  const res: any[] = [];
  const data: any[] = [];
  finishedList.forEach((item) => {
    const _res = fileResMap.get(item.id) || null;
    if (_res) {
      // res.push(_res);
      res.push(_res instanceof Array ? _res[0] : _res);
      data.push(Object.assign(item, { res: _res instanceof Array ? _res[0] : _res }));
    }
  });
  // console.log('res', res, 'data', data);

  return emits('update', res, data);
}
</script>

<style scoped></style>
