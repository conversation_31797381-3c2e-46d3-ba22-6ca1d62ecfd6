import { ITaskState } from '@/views/task-management/type';

import { defineComponent, h } from 'vue';

const bg = {
  [ITaskState.待开始]: '#E6A23C',
  [ITaskState.进行中]: '#527CFF',
  [ITaskState.已完成]: '#67C23A',
  [ITaskState.已停用]: '#F56C6C',
};

export const taskState = defineComponent({
  name: 'taskState',
  props: {
    row: {
      type: Object,
      default: () => ({}),
    },
    field: {
      type: String,
      default: 'taskState',
    },
  },
  setup(props) {
    const key = props.field;
    return () =>
      h(
        'div',
        {
          className: 'inline-block px-[10px] py-[4px] text-center rounded-[4px] text-white',
          style: `background-color: ${bg[props.row[key] as ITaskState]}`,
        },
        { default: () => ITaskState[props.row[key]] }
      );
  },
});
