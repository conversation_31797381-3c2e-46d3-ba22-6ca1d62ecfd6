import { defineComponent, h } from 'vue';

/** 计划状态 */
export enum IPlanState {
  草稿 = 1,
  进行中,
  已结束,
  已停用,
  待开始,
}

/** 计划状态 */
export const planStateOptions = [
  { label: IPlanState[1], value: 1 },
  { label: IPlanState[2], value: 2 },
  { label: IPlanState[3], value: 3 },
  { label: IPlanState[4], value: 4 },
  { label: IPlanState[5], value: 5 },
];

const bg = {
  [IPlanState.草稿]: '#E6A23C',
  [IPlanState.进行中]: '#527CFF',
  [IPlanState.已结束]: '#67C23A',
  [IPlanState.已停用]: '#F56C6C',
  [IPlanState.待开始]: '#ddbf22',
};

export const planState = defineComponent({
  name: 'planState',
  props: {
    row: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props) {
    return () =>
      h(
        'div',
        {
          className: 'inline-block px-[10px] py-[4px] text-center rounded-[4px] text-white min-w-[70px]',
          style: `background-color: ${bg[props.row.planState as IPlanState]}`,
        },
        { default: () => IPlanState[props.row.planState] }
      );
  },
});
