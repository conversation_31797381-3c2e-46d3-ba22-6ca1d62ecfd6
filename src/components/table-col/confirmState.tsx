import { defineComponent, h } from 'vue';

//

export enum IConfirmState {
  待确认 = 0,
  已确认,
  已拒绝,
}
const bg: any = {
  0: '#F39600',
  1: '#527CFF',
  2: '#FA5151',
};

/** 表格检查人列 */
export const confirmState = defineComponent({
  name: 'confirmState',
  props: {
    row: {
      type: Object,
      default: () => ({}),
    },
    field: {
      type: String,
      default: 'confirmState',
    },
  },
  setup(props) {
    return () =>
      h(
        'div',
        {
          className: 'inline-block px-[10px] py-[4px] text-center rounded-[4px] text-white',
          style: `background-color: ${bg[props.row.confirmState]}`,
        },
        { default: () => IConfirmState[props.row.confirmState] }
      );
  },
});
