import { defineComponent, h } from 'vue';

//

export enum IApproveState {
  待审核 = 0,
  已审核,
}
const bg: any = {
  0: '#979797',
  1: '#527CFF',
};

/** 表格检查人列 */
export const approveState = defineComponent({
  name: 'approveState',
  props: {
    row: {
      type: Object,
      default: () => ({}),
    },
    field: {
      type: String,
      default: 'approveState',
    },
  },
  setup(props) {
    return () =>
      h(
        'div',
        {
          className: 'inline-block px-[10px] py-[4px] text-center rounded-[4px] text-white',
          style: `background-color: ${bg[props.row.approveState]}`,
        },
        { default: () => IApproveState[props.row.approveState] }
      );
  },
});
