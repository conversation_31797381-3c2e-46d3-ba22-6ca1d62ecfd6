import { defineComponent, h } from 'vue';

//

export enum IplanTypeStatus {
  启用 = 1,
  停用 = 2,
}
const bg: any = {
  2: '#979797',
  1: '#527CFF',
};

/** 表格检查人列 */
export const planTypeStatus = defineComponent({
  name: 'planTypeStatus',
  props: {
    row: {
      type: Object,
      default: () => ({}),
    },
    field: {
      type: String,
      default: 'planTypeStatus',
    },
  },
  setup(props) {
    return () =>
      h(
        'div',
        {
          className: 'inline-block px-[10px] py-[4px] text-center rounded-[4px] text-white',
          style: `background-color: ${bg[props.row.planTypeStatus]}`,
        },
        { default: () => IplanTypeStatus[props.row.planTypeStatus] }
      );
  },
});
