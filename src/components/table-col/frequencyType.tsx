import { defineComponent } from 'vue';

export const frequencyText = (row: any) => {
  if (!row?.frequencyType) return;
  const typeObj: any = {
    '0': '时',
    '1': '日',
    '2': '周',
    '3': '月',
    '4': '季度',
    '5': '年',
    '99': '不重复',
  };
  if (row.frequencyType === '99') return '不重复';
  return `每${row.frequency}${typeObj[row.frequencyType]}`;
};

export const frequencyType = defineComponent({
  name: 'frequencyType',
  props: {
    row: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props) {
    return () => frequencyText(props.row);
  },
});
