import { defineComponent } from 'vue';

//

export enum ICheckUserType {
  检查负责人 = 1,
  检查参与人 = 2,
}

/** 表格检查人列 */
export const checkUserType = defineComponent({
  name: 'checkUserType',
  props: {
    row: {
      type: Object,
      default: () => ({}),
    },
    field: {
      type: String,
      default: 'checkUserType',
    },
  },
  setup(props) {
    console.log('123', props.row, props.field);
    return () => ICheckUserType[props.row[props.field]] || '--';
  },
});
