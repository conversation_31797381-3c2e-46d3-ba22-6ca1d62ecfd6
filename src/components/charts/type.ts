import * as echarts from 'echarts';

type EChartsOption = echarts.EChartsOption;

export interface IProps {
  height?: number;
  extendOption?: Partial<EChartsOption>;
}

export interface IBarProps extends IProps {
  chartData: {
    xData: Array<string>;
    yData: Array<string | number>;
  };
  graphicColor?: Array<string[]>;
}
export interface IPieProps extends IProps {
  chartData: Array<{
    name: string;
    value: number;
  }>;
}
export interface ILineProps extends IProps {
  chartData: {
    xData: Array<string>;
    yData: Array<any>;
  };
}
export interface IStackBarProps extends IProps {
  chartData: {
    category: Array<string>;
    yData: Array<any>;
  };
  barColor: Array<string>;
}
export interface IRadarRadarProps extends IProps {
  chartData: {
    indicator: Array<{
      name: string;
      max: string | number;
    }>;
    data: Array<{
      name: string;
      axisName?: string;
      value: Array<string | number>;
    }>;
  };
  compareMark: string;
}
