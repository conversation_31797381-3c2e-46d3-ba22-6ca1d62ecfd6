<template>
  <div v-if="!isEmpty" class="line-part w-full h-full" ref="barChart"></div>
  <Empty v-else />
</template>

<script lang="ts" setup>
import Empty from '@/components/empty/index.vue';
import { useEchartsResizeObserver } from '@/common/utils/useEchartsResizeObserver';
import { sleep } from '@/common/utils';
import * as echarts from 'echarts';
import { defineComponent, markRaw, onBeforeUnmount, onMounted, ref, watch } from 'vue';

const props = defineProps({
  echartsData: {
    type: Object,
    default: () => {
      return { label: [], data: [] };
    },
  },
  color: {
    type: Array,
    default: () => {
      return ['#00B49C', '#FF6320', '#4070FF'];
    },
  },
  levelIndex: {
    type: Number,
    default: 0,
  },
  levelColor: {
    type: Array,
    default: () => {
      return [
        ['#E00000', '#E00000'],
        ['#FF6A00', '#FF6A00'],
        ['#F7BA1E', '#F7BA1E'],
      ];
    },
  },
  graphicColor: {
    type: Array,
    default: () => {
      return [];
    },
  },
  barBorderRadius: {
    type: Array,
    default: () => {
      return [0, 0, 0, 0];
    },
  },
  extra: {
    type: Object,
    default: () => {
      return { smooth: false, legend: {}, tooltip: {}, yAxis: {}, xAxis: {} };
    },
  },
});
const emits = defineEmits(['clickItem']);
const isEmpty = ref(false);
const barChart = ref();
const myChart = ref<any>(null);
const observer = ref<ResizeObserver>();

function initEcharts(echartsData: any) {
  if (myChart.value) destroyEcharts();
  // 标记一个对象，使其永远不会再成为响应式对象
  myChart.value = markRaw(echarts.init(barChart.value));
  const color = props.color;
  const graphicColor: any[] = props.graphicColor || [[]];
  const levelColor: any[] = props.levelColor || [[]];
  let series: object[] = [];
  const data = echartsData.data || [];
  const label = echartsData.label || [];

  data.forEach((item: any, index: number) => {
    const part: any = {
      ...item,
      barGap: props.extra?.barGap,
      smooth: props.extra.smooth,
      barWidth: props.extra.barWidth || '40%',
      symbol: 'circle',
      symbolSize: 5,
      showSymbol: false,
      lineStyle: {
        width: 2,
      },
      itemStyle: {
        color: color[index],
      },
      label: {
        normal: {
          show: true,
          color: '#ffffff',
        },
        formatter: '{@value}',
      },
    };
    if (graphicColor.length) {
      part.itemStyle = {
        normal: {
          color: function (params: any) {
            if (!props.levelIndex || params.dataIndex > props.levelIndex) {
              return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: graphicColor[index][0],
                },
                {
                  offset: 0.8,
                  color: graphicColor[index][1],
                },
              ]);
            } else {
              return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: levelColor[params.dataIndex][0],
                },
                {
                  offset: 0.8,
                  color: levelColor[params.dataIndex][1],
                },
              ]);
            }
          },
          barBorderRadius: props.barBorderRadius,
          label: {
            show: true, //开启显示
            textStyle: {
              //数值样式
              color: '#333',
              fontSize: 14,
            },
          },
        },
      };
    }
    series.push(part);
  });
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        lineStyle: {
          color: '#57617B',
        },
      },
      backgroundColor: 'rgba(0,150,236,0.6)',
      textStyle: {
        color: '#fff',
      },
      ...props.extra?.tooltip,
    },
    legend: {
      itemHeight: 6,
      itemGap: 10,
      data: data.map((item: any) => item.name),
      bottom: -6,
      textStyle: {
        fontSize: 14,
        color: 'rgba(102, 102, 102, 1)',
      },
      ...props.extra?.legend,
    },
    grid: {
      top: '15',
      left: '8',
      right: '8',
      bottom: '30',
      containLabel: true,
      ...props.extra?.grid,
    },
    xAxis: [
      {
        type: 'category',
        triggerEvent: true,
        axisTick: {
          show: false,
        },
        axisLabel: {
          //x轴文字的配置
          show: true,
          interval: 0, //使x轴文字显示全
          fontSize: 12,
          color: '#6E7079',
          formatter: function (params: string) {
            let newParamsName = '';
            const paramsNameNumber = params.length;
            const provideNumber = 5; //一行显示几个字
            if (paramsNameNumber > provideNumber) {
              newParamsName = params.substring(0, provideNumber) + '...';
            } else {
              newParamsName = params;
            }
            return newParamsName;
          },
          ...props.extra?.xAxis,
        },
        axisLine: {
          lineStyle: {
            fontSize: 12,
            color: '#E0E0E6',
          },
        },
        data: label,
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '',
        minInterval: 1,
        axisTick: {
          show: false,
        },
        axisLabel: {
          fontSize: 10,
          color: '#6E7079',
        },
        axisLine: {
          show: false,
          lineStyle: {
            fontSize: 12,
            color: '#526D85',
          },
        },
        splitLine: {
          show: true,
        },
        ...props.extra?.yAxis,
      },
      {
        type: 'value',
        min: 0,
        max: 100,
        position: 'right',
        axisLine: {
          lineStyle: {
            fontSize: 12,
            color: '#526D85',
          },
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          show: true,
          formatter: '{value} %', //右侧Y轴文字显示
          textStyle: {
            fontSize: 10,
            color: '#6E7079',
          },
        },
      },
    ],
    series,
    ...props.extra?.series,
  };
  myChart.value.setOption(option);
  myChart.value.on('click', function (params: any) {
    emits('clickItem', params.dataIndex);
  });

  observer.value = useEchartsResizeObserver(myChart, barChart).observer;
}

function destroyEcharts() {
  if (myChart.value) {
    myChart.value.dispose();
    myChart.value = null;
  }
}

function getImgUrl() {
  return myChart.value?.getDataURL({ type: 'png' });
}

onMounted(() => {
  watch(
    () => props.echartsData,
    async (val: any) => {
      isEmpty.value = !val.data.length;
      await sleep(500);
      if (!isEmpty.value && barChart.value) initEcharts(val);
    },
    { immediate: true, deep: true }
  );
});

onBeforeUnmount(() => {
  destroyEcharts();
  // 在组件卸载时停止监听，避免内存泄漏
  observer.value?.disconnect();
});

defineExpose({ getImgUrl });

defineComponent({ name: 'BarLineChart' });
</script>

<style scoped lang="scss"></style>
