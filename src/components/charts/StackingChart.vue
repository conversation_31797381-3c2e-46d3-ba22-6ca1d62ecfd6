<!--堆叠条形图-->
<template>
  <div v-if="!isEmpty" class="line-part w-full h-full" ref="barChart"></div>
  <Empty v-else />
</template>

<script lang="ts" setup>
import Empty from '@/components/empty/index.vue';
import { useEchartsResizeObserver } from '@/common/utils/useEchartsResizeObserver';
import { sleep } from '@/common/utils';
import * as echarts from 'echarts';
import { defineComponent, markRaw, onBeforeUnmount, onMounted, ref, watch } from 'vue';

const props = defineProps({
  echartsData: {
    type: Object,
    default: () => {
      return { label: [], data: [] };
    },
  },
  color: {
    type: Array,
    default: () => {
      return ['#00B49C', '#FF6320', '#4070FF'];
    },
  },
  extra: {
    type: Object,
    default: () => {
      return { smooth: false, legend: {}, tooltip: {}, yAxis: {}, xAxis: {} };
    },
  },
});
const emits = defineEmits(['clickItem']);
const isEmpty = ref(false);
const barChart = ref();
const myChart = ref<any>(null);
const observer = ref<ResizeObserver>();

function initEcharts(echartsData: any) {
  if (myChart.value) destroyEcharts();
  // 标记一个对象，使其永远不会再成为响应式对象
  myChart.value = markRaw(echarts.init(barChart.value));
  const color = props.color;
  let series: object[] = [];
  const data = echartsData.data || [];
  const label = echartsData.label || [];

  data.forEach((item: any, index: number) => {
    const part: any = {
      ...item,
      barGap: props.extra?.barGap,
      smooth: props.extra.smooth,
      barWidth: props.extra.barWidth || '40%',
      symbol: 'circle',
      stack: '总量',
      symbolSize: 5,
      showSymbol: false,
      lineStyle: {
        width: 2,
      },
      itemStyle: {
        color: color[index],
      },
      label: {
        normal: {
          distance: 10,
          align: 'center',
          verticalAlign: 'middle',
          show: true,
          fontSize: 12,
          position: 'bottom',
          color: '#666666',
          formatter: function (params: any) {
            if (params.data) {
              return `{insideTop|▲}
              {insideBottom|${item.name}: ${params.data}}`;
            }
            return '';
          },
          rich: {
            insideBottom: {
              padding: [3, 0, 0, -50],
            },
            insideTop: {
              color: color[index],
              width: 0,
              verticalAlign: 'bottom',
              lineHeight: 35,
              fontSize: '18',
            },
          },
        },
      },
    };
    series.push(part);
  });

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        lineStyle: {
          color: '#57617B',
        },
      },
      backgroundColor: 'rgba(0,150,236,0.6)',
      textStyle: {
        color: '#fff',
      },
      ...props.extra?.tooltip,
    },
    legend: {
      itemHeight: 6,
      itemGap: 3,
      data: data.map((item: any) => item.name),
      bottom: -6,
      textStyle: {
        fontSize: 14,
        color: 'rgba(102, 102, 102, 1)',
      },
      ...props.extra?.legend,
    },
    xAxis: [
      {
        type: 'value',
        axisTick: {
          show: false,
        },
        axisLine: {
          show: false,
          lineStyle: {
            width: 1,
            color: '#6E7079',
          },
        },
        axisLabel: {
          textStyle: {
            fontSize: 12,
            color: '#6E7079',
          },
        },
        splitLine: {
          show: false,
        },
      },
    ],
    grid: {
      top: '15',
      left: '0',
      right: '10',
      bottom: '30',
      //是否包含坐标轴
      containLabel: true,
      //鼠标滑过是否显示信息栏，目前来看最好在grid中配置tooltip鼠标滑过信息栏
      ...props.extra?.grid,
    },
    yAxis: [
      {
        inverse: true,
        splitNumber: 1, // 坐标轴的分割段数，是一个预估值，实际显示会根据数据稍作调整。
        interval: 1, // 强制设置坐标轴分割间隔。
        type: 'category',
        data: label,
        axisTick: {
          show: false,
        },
        axisLine: {
          show: true,
          lineStyle: {
            width: 1,
            color: '#E0E0E6',
          },
        },
        axisLabel: {
          textStyle: {
            fontSize: 12,
            color: '#6E7079',
          },
        },
      },
    ],
    series,
    ...props.extra?.series,
  };
  myChart.value.setOption(option);
  myChart.value.on('click', function (params: any) {
    emits('clickItem', params.dataIndex);
  });

  observer.value = useEchartsResizeObserver(myChart, barChart).observer;
}

function destroyEcharts() {
  if (myChart.value) {
    myChart.value.dispose();
    myChart.value = null;
  }
}

function getImgUrl() {
  return myChart.value?.getDataURL({ type: 'png' });
}

onMounted(() => {
  watch(
    () => props.echartsData,
    async (val: any) => {
      isEmpty.value = !val.data.length;
      await sleep(500);
      if (!isEmpty.value && barChart.value) initEcharts(val);
    },
    { immediate: true, deep: true }
  );
});

onBeforeUnmount(() => {
  destroyEcharts();
  // 在组件卸载时停止监听，避免内存泄漏
  observer.value?.disconnect();
});

defineExpose({ getImgUrl });

defineComponent({ name: 'StackingChart' });
</script>

<style scoped lang="scss"></style>
