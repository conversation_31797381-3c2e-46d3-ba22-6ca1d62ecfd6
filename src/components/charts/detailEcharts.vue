<template>
  <div v-if="!isEmpty" class="line-part w-full h-full" ref="radarChartRef"></div>
  <Empty v-else />
</template>
<script lang="ts" setup>
import * as echarts from 'echarts';
import { IRadarRadarProps } from './type';
import upIcon from './assets/up_icon.png';
import downIcon from './assets/down_icon.png';
import Empty from '@/components/empty/index.vue';
import { defineComponent, markRaw, onBeforeUnmount, onMounted, ref, watch, nextTick } from 'vue';
import { sleep } from '@/common/utils.ts';
import { useEchartsResizeObserver } from '@/common/utils/useEchartsResizeObserver.ts';

defineOptions({ name: 'RadarChart' });

const props = withDefaults(defineProps<IRadarRadarProps>(), {
  height: 500,
  chartData: () => ({
    indicator: [],
    data: [],
  }),
  compareMark: '',
  extendOption: () => ({}),
});
const emits = defineEmits(['clickItem']);
const isEmpty = ref(false);
const radarChartRef = ref();
const observer = ref<ResizeObserver>();
const myChart = ref<any>(null);

const option = {
  grid: {
    left: '10%',
    right: '10%',
    bottom: '15%',
    containLabel: true,
  },
  legend: {
    bottom: '0',
    left: 'center',
    itemWidth: 8,
    itemHeight: 8,
    itemGap: 10,
    data: [],
    textStyle: {
      fontSize: 14,
      color: 'rgba(102, 102, 102, 1)',
    },
  },
  radar: [
    {
      shape: 'circle',
      center: ['50%', '50%'],
      radius: 90,
      indicator: [],
      axisName: {
        fontSize: 16,
        fontWeight: 700,
        color: '#333',
      },
    },
    {
      radius: 75,
      indicator: [],
      startAngle: -125,
      splitArea: {
        show: false,
      },
      axisLine: {
        show: false,
      },
      splitLine: {
        show: false,
      },
      axisName: {
        formatter: function (value) {
          const _indicator = props.chartData.indicator.map((item) => item.name);
          const _data = props.chartData.data;
          const _axisName = [];
          const _i = _indicator.indexOf(value);
          let compareData = null;
          if (props.compareMark) {
            const filterList = _data.filter((item) => item.name === props.compareMark);
            compareData = filterList[0].value;
          }
          _data.forEach((item) => {
            const _value = item.value[_i];
            let _text = `{legend|${item.axisName}: ${_value}}`;
            if (props.compareMark && props.compareMark !== item.name) {
              if (_value > compareData[_i]) _text += ' {upIcon|}';
              if (_value < compareData[_i]) _text += ' {downIcon|}';
            }
            _axisName.push(_text);
          });
          return _axisName.join('\n');
        },
        rich: {
          legend: {
            align: 'left',
          },
          upIcon: {
            height: 12,
            align: 'left',
            backgroundColor: {
              image: upIcon,
            },
          },
          downIcon: {
            height: 12,
            align: 'left',
            backgroundColor: {
              image: downIcon,
            },
          },
        },
        color: '#333',
        fontSize: 14,
        lineHeight: 18,
        backgroundColor: '#F2F4F7',
        padding: [5, 2],
        borderRadius: 4,
      },
    },
  ],
  series: [
    {
      name: '',
      type: 'radar',
      areaStyle: {
        opacity: 0.3,
      },
      data: [],
    },
  ],
};

function initEcharts(chartData: any) {
  if (myChart.value) destroyEcharts();
  // 标记一个对象，使其永远不会再成为响应式对象
  myChart.value = markRaw(echarts.init(radarChartRef.value));

  option.radar[0].indicator = chartData.indicator;
  option.radar[1].indicator = chartData.indicator;
  option.legend.data = chartData.data.map((item) => item.name);
  option.series[0].data = chartData.data;

  myChart.value.setOption({
    ...option,
    ...props.extendOption,
  });
  myChart.value.on('click', function (params: any) {
    emits('clickItem', params.dataIndex);
  });
  observer.value = useEchartsResizeObserver(myChart, radarChartRef).observer;
}

function destroyEcharts() {
  if (myChart.value) {
    myChart.value.dispose();
    myChart.value = null;
  }
}

onMounted(() => {
  watch(
    () => props.chartData,
    async (val: any) => {
      isEmpty.value = !val.data.length;
      await sleep(500);
      if (!isEmpty.value && radarChartRef.value) initEcharts(val);
    },
    { immediate: true, deep: true }
  );
});

onBeforeUnmount(() => {
  destroyEcharts();
  // 在组件卸载时停止监听，避免内存泄漏
  observer.value?.disconnect();
});
</script>

<style lang="scss" scoped></style>
