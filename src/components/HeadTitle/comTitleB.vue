<template>
  <div class="com-title-a">
    <slot>{{ props.title }}</slot>
    <slot name="left-extra"></slot>
    <div class="ml-auto">
      <slot name="right"></slot>
    </div>
  </div>
</template>
<script setup lang="ts">
const props = defineProps({
  title: {
    type: String,
    default: '',
  },
});
defineOptions({ name: 'ComTitleB' });
</script>

<style scoped lang="scss">
.com-title-a {
  display: flex;
  align-items: center;
  font-weight: 500;
  font-size: 14px;
  color: #000000;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
</style>
