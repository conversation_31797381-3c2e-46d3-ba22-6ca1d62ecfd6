<template>
  <n-tree-select
    :style="props.style"
    :options="treeData"
    :value="treeValue"
    label-field="unitName"
    key-field="id"
    children-field="children"
    :on-update:value="handleUpdateValue"
    clearable
  />
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { useStore } from '@/store/index';
import { getAllUnit } from './fetchData';
const { unitId } = useStore().$state.userInfo;
const treeData = ref([]);
const treeValue = ref(null);
const $emits = defineEmits(['updateVal']);

interface Props {
  style?: string;
}
const props = withDefaults(defineProps<Props>(), {
  style: () => 'width: 230px',
});

// 用于清除选中值
function clearVal() {
  treeValue.value = null;
}

function getIds() {
  // 获取treeData以及子级的id
  let ids: any[] = [];
  function getChildrenId(treeData: any) {
    treeData.forEach((item: any) => {
      ids.push(item.id);
      if (item.children && item.children.length > 0) {
        getChildrenId(item.children);
      }
    });
  }
  treeData.value.length && getChildrenId(treeData.value);
  return ids.length ? ids.join(',') : [];
}

function handleUpdateValue(value: any) {
  console.log(value);
  treeValue.value = value;
  $emits('updateVal', treeValue.value);
}

function getTreeData() {
  getAllUnit({ orgCode: unitId, pageNo: 1, pageSize: -1 }).then((res: any) => {
    treeData.value = res.data.rows;
  });
}
getTreeData();
defineOptions({ name: 'ComEmpty' });
</script>

<style lang="scss"></style>
