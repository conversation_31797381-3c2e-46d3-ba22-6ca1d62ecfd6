<!-- eslint-disable  -->
<template>
  <!-- 只有监管单位显示树 -->
  <div
    class="flex flex-col justify-center justify-items-center"
    v-if="userInfo.unitOrgType == '2'"
  >
    <div class="flex justify-start items-center mb-4">
      <img src="./icon.png" :style="`width: 18px;`" alt="" />
      <div
        :style="`font-size: 16px; font-weight: 600; margin-left: ${toVw(10)}`"
      >
        所属单位
      </div>
    </div>
    <n-input v-model:value="filterText" clearable placeholder="请输入组织名称">
      <template #suffix>
        <BsSearch />
      </template>
    </n-input>
    <el-scrollbar>
      <el-tree
        class="tree-scroll notVw"
        empty-text="暂无数据"
        default-expand-all
        highlight-current
        :expand-on-click-node="false"
        @node-click="handleNodeClick"
        node-key="id"
        ref="treeRef"
        :data="treeData"
        :default-checked-keys="[currentNodeKey]"
        :current-node-key="currentNodeKey"
        :filter-node-method="filterNode"
      >
        <template #default="{ data }">
          <span class="custom-tree-node">
            <img
              :style="`width: 18px;`"
              :src="getTreeItemIco(data)"
              :alt="data.treeName"
            />
            <span
              :style="`margin-left: 10px; font-size: 14px`"
              :title="data.treeName"
              >{{
                data.treeName.length > 12
                  ? data.treeName.slice(0, 12) + '...'
                  : data.treeName
              }}</span
            >
          </span>
        </template>
        <template #empty>
          <n-empty description="暂无数据" />
        </template>
      </el-tree>
    </el-scrollbar>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import { ElTree } from 'element-plus';
import { getOrgTree } from '@/views/mis/fetchData.ts';
import { BsSearch } from '@kalimahapps/vue-icons';
import business from './business.png'; //业务单位
import section from './section.png'; //部门
import supervise from './supervise.png'; //监管单位
import { toVw, toVh } from '@/utils/fit';
import { useStore } from '@/store';
import { useRoute } from 'vue-router';
const route = useRoute();
const userInfo = useStore()?.userInfo;

const filterText = ref('');
const treeRef = ref();
const treeData = ref([]);
const emits = defineEmits(['updateVal', 'getTopUnitName']);
const currentNodeKey = ref('');

interface Tree {
  [key: string]: any;
}

const handleNodeClick = (val: any, isInit = false) => {
  // currentNodeKey.value = val.id;
  let isBusinessUnit = val.attributes.orgType == '1';
  emits('updateVal', val.id, isBusinessUnit, val, isInit);
};

//  logo图片  测试环境图片还没有  先用生产环境的图片 后面有了替换下面注释代码即可
const logo_url = userInfo.logoPicUrl;

const getTreeItemIco = (row: any) => {
  if (row.parentId == -1) return logo_url;
  if (row.attributes.orgType == '0') {
    return section;
  } else if (row.attributes.orgType == '1') {
    return business;
  } else {
    return supervise;
  }
};

watch(filterText, (val: any) => {
  treeRef.value!.filter(val);
});

const filterNode = (value: string, data: Tree) => {
  if (!value) return true;
  return data.treeName.includes(value);
};

// 获取组织机构树
const getOrgUnitTree = async () => {
  const res: any = await getOrgTree({
    orgCode: userInfo.unitId,
    type: 2,
    needChildUnit: 1,
    unitStatus: 1,
  });

  if (res.data && res.data.length > 0) {
    treeData.value = res.data || [];
    emits('getTopUnitName', treeData.value[0].treeName);
    // 初始化调用
    handleNodeClick(treeData.value[0], true);
    currentNodeKey.value =
      Object.keys(route.query).length > 0
        ? route.query.unitId
        : userInfo.unitId;
  }
};

function findItemById(data: any, id: any) {
  // 遍历数据源中的每一项
  for (let item of data) {
    // 如果当前项的 id 匹配传入的 id，则返回该项
    if (item.id === id) {
      return item;
    }
    // 如果当前项有子项，递归查找子项
    if (item.children && item.children.length > 0) {
      const found: any = findItemById(item.children, id);
      // 如果在子项中找到了匹配的项，则返回该项
      if (found) {
        return found;
      }
    }
  }
  // 如果没有找到匹配的项，返回 null
  return null;
}

function changeCurrentNodeKey(unitId: string) {
  currentNodeKey.value = unitId;
  // 触发点击事件
  let item = findItemById(treeData.value, unitId);
  // 执行选中事件
  item && handleNodeClick(item);
}
defineExpose({
  changeCurrentNodeKey,
});
getOrgUnitTree();
</script>
<style lang="scss" scoped>
.tree-scroll {
  margin-top: 20px;
  height: 100%;
  /* overflow-y: scroll; */
  background: transparent;
}

.custom-tree-node {
  width: 200px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  font-size: 14px;
}

.notVw :deep(.el-tree-node__content) {
  /* padding: 20px 0; */
  height: 44px;
}

:deep(
  .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content
) {
  background: rgba(82, 124, 255, 0.1);
}
</style>
