:root {
  color-scheme: light;

  --header-height: 64px;

  --com-primary-color: #527cff;
  --com-border: unset;
  --com-border-radius: 10px;
  // --com-container-bg: #f4f9ff;
  --com-container-bg: rgba(238, 247, 255, 1);
  --com-container-shadow: unset;
  --com-box-bg: rgba(238, 247, 255, 1);
  --com-box-shadow: unset;
}

/* style */

body {
  background: #c8d5ff;
  color: #000;

  .n-dialog .n-dialog__title {
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }

  .n-menu-item-content--selected::after {
    content: "";
    position: absolute;
    right: 0;
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-right: 8px solid #c8d5ff;
  }

  .n-base-close {
    svg {
      // border: 1px dashed;
      padding: 2px;
    }
  }

  // .n-base-icon svg {
  //   height: 1.2em;
  //   width: 1.2em;
  // }
  .n-breadcrumb-item__link {
    font-size: 14px;
  }

  .n-card-header__main,
  .n-drawer-header__main {
    padding-left: 16px;
    font-size: 16px;
    position: relative;
  }

  .n-card > .n-card__content,
  .n-card > .n-card__footer,
  .n-card > .n-card-header {
    background: #fff;
    padding: 12px 24px;
  }

  .n-card > .n-card__footer {
    border-top: 1px solid rgb(237, 240, 246);
  }

  .n-card > .n-card-header {
    border-bottom: 1px solid rgb(237, 240, 246);
  }

  // models 隐患选择弹窗
  .models {
    .n-card-header__main,
    .n-drawer-header__main {
      padding-left: 0;
      &::before {
        content: "";
        width: 0px;
      }
    }
    .n-card__content {
      padding: 0 0;
    }
  }
}

.com-header {
  background: var(--com-primary-color);
  color: #fff;
  width: 100%;
  height: var(--header-height);
  padding: 0 22px 0 14px;
}

/* 容器背景 */
.com-container-bg {
  background: var(--com-container-bg);
}

.com-container-border {
  border: var(--com-border);
  border-radius: var(--com-border-radius);
  box-shadow: var(--com-container-shadow);
}

/* 通用容器 */
.com-container {
  @extend .com-container-bg;
  @extend .com-container-border;
}

/* 基础容器尺寸、颜色 private */
._container-base {
  position: relative;
  height: calc(100vh - var(--header-height) - 24px - 24px);
  margin: 24px 24px 0;
  color: #000;
  overflow: hidden;
}

.com-table {
  //
  --n-th-color: #dde5f9 !important;
  --n-th-text-color: #222222 !important;
  --n-td-text-color: #222222 !important;
  --n-item-text-color: #606266 !important;
  --n-td-color-striped: rgba(223, 238, 252, 0.99) !important;
  --n-td-color: #eef7ff !important;
  --n-border-color: #c5cbd6 !important;
  --n-td-color-hover: #eef7f8 !important;

  .n-pagination {
    color: #606266;
  }
}

/* 表格容器外层 */
.com-table-wrap {
  @extend ._container-base;
  display: grid;
  grid-template-rows: auto 1fr;
}

/* 表格容器 */
.com-table-container {
  @extend .com-container-bg;
  @extend .com-container-border;
  padding: 24px 24px 16px;
}

.com-table-filter {
  @extend .com-container-bg;
  @extend .com-container-border;
  padding: 24px;
}

.com-table-filter-nb {
  @extend .com-container-bg;
  @extend .com-container-border;
  border-radius: unset;
  padding: 24px;
}

// 只有底部带圆角 borer-left-right,
.com-table-filter-blr {
  @extend .com-container-bg;
  @extend .com-container-border;
  border-top-left-radius: unset;
  border-top-right-radius: unset;
  padding: 24px;
}

/* Box盒子（比container小的模块） */

.com-box-bg {
  background: var(--com-box-bg);
}

.com-box-border {
  border: var(--com-border);
  border-radius: var(--com-border-radius);
  box-shadow: var(--com-box-shadow);
}

.com-box {
  @extend .com-box-bg;
  @extend .com-box-border;
}

.com-action-button {
  --n-text-color: var(--com-primary-color) !important;
}

.com-empty {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  justify-items: center;
}

/* overwrite naiveUI style -> */

/* 悬浮提示框宽度 */
.n-popover {
  @apply max-w-[600px];
}

/* 侧边弹窗背景虚化 */
.n-drawer {
  backdrop-filter: blur(3px);
}

/* <- */
//左侧菜单
.com-menu {
  --n-color: #363d64 !important;
  --n-item-text-color: #fff !important;
  --n-item-icon-color: #fff !important;
  --n-item-icon-color-child-active: #fff !important;
  --n-item-text-color-child-active-hover: #fff !important;
  --n-item-text-color-child-active: #fff !important;
  --n-item-icon-color-hover: #fff !important;
  --n-arrow-color: #fff !important;
  --n-arrow-color-child-active: #fff !important;
  --n-item-text-color-hover: #fff !important;
  --n-arrow-color-hover: #fff !important;
  --n-item-icon-color-child-active-hover: #fff !important;
  --n-arrow-color-child-active-hover: #fff !important;
}

// element-plus ui
.el-dialog,
.el-drawer {
  --el-dialog-padding-primary: 0px;
  --el-drawer-padding-primary: 0;
  .el-dialog__header,
  .el-drawer__header {
    padding: 5px 14px 5px 28px;
    margin-bottom: 0;
  }
  .el-dialog__body,
  .el-drawer__body {
    background-color: #f3f4f5;
    padding: 20px;
  }
  .el-dialog__footer,
  .el-drawer__footer {
    padding: 13px 24px 16px 0;
  }
}

.el-table {
  background: #eef7ff;
  // 表格变量修改
  --el-table-header-bg-color: #dde5f9;
  --el-table-header-text-color: #222222;
  --el-fill-color-lighter: #e1effd;

  // 按钮变量修改
  --el-color-primary: #527dff;
  --el-button-bg-color: #527dff;
  --el-button-border-color: #527dff;
  --el-td-padding: 10px 0;
  .el-table__cell {
    padding: var(--el-td-padding);
  }

  .el-pagination.is-background .btn-next,
  .el-pagination.is-background .btn-prev,
  .el-pagination.is-background .el-pager li {
    background-color: transparent;
  }
}
.el-table--enable-row-transition .el-table__body td.el-table__cell {
  background-color: #eef7ff;
  padding: var(--el-td-padding);
}

.hazard-class {
  width: 150px;
  padding: 6px;
  margin-right: 16px;
  background: #f59a2357;
  color: #f59a23;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.n-image-preview-container {
  z-index: 9999 !important;
}
