/**
 * 公共类
 */

body {
    font-size: 14px;
}

@mixin autofill($color, $theme) {
    input:-webkit-autofill,
    input:-webkit-autofill:hover,
    input:-webkit-autofill:focus,
    textarea:-webkit-autofill,
    textarea:-webkit-autofill:hover,
    textarea:-webkit-autofill:focus,
    select:-webkit-autofill,
    select:-webkit-autofill:hover,
    select:-webkit-autofill:focus {
        -webkit-background-clip: text;
        -webkit-text-fill-color: $color;
        color-scheme: $theme;
    }
}

.com-autofill-dark-none {
    @include autofill(rgba(255, 255, 255, 0.9), dark);
}

.com-autofill-light-none {
    @include autofill(rgba(0, 0, 0, 0.9), light);
}

/* com-g-x-x -> */

.com-g-row-aa {
    display: grid;
    grid-template-rows: auto auto;
    grid-template-columns: minmax(0, 1fr);
}

.com-g-row-a1 {
    display: grid;
    grid-template-rows: auto 1fr;
    grid-template-columns: minmax(0, 1fr);
}

.com-g-row-aa1 {
    display: grid;
    grid-template-rows: auto auto 1fr;
    grid-template-columns: minmax(0, 1fr);
}

.com-g-row-aaa1 {
    display: grid;
    grid-template-rows: auto auto auto 1fr;
    grid-template-columns: minmax(0, 1fr);
}

.com-g-row-full {
    grid-template-rows: 100%;
}

.com-g-col-a1 {
    display: grid;
    grid-template-columns: auto 1fr;
}

.com-g-col-1a {
    display: grid;
    grid-template-columns: 1fr auto;
}

.com-g-col-full {
    grid-template-columns: 100%;
}

/* com-g-x-x  <- */

.com-empty {
    position: absolute;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    justify-items: center;
}

.search-form {
    flex-wrap: wrap;
    .n-button {
        margin-left: 10px;
    }
    .n-select {
        width: 230px;
    }
    .n-date-picker--range {
        width: 360px;
    }

    .n-input,
    .n-input .n-input__input-el,
    .n-button {
        height: 32px;
        line-height: 32px;
    }
    .n-input {
        width: 230px;
    }
    .n-form.n-form--inline {
        flex-wrap: wrap;
    }
    .n-button {
        margin-left: 10px;
    }
}

.item_content {
    height: 100%;
    display: flex;
    height: calc(100% - 45px);

    .unit-tree {
        // flex: 1;
        width: 323px;
        padding: 20px;
        margin-right: 20px;
        background-color: #eef7ff;
        border-radius: var(--com-border-radius);
    }

    .right-table {
        display: flex;
        flex-direction: column;
        flex: 6;
        background-color: #eef7ff;
        border-radius: var(--com-border-radius);
        padding: 20px;
        position: relative;
    }

    .select-tree {
        cursor: pointer;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        z-index: 10;
    }

    .main_search {
        flex: 8;
        .btn-content {
            float: right;
            margin-left: 1dvb;
        }
    }
}

.notVw .unit-tree {
    width: 323px;
    padding: 20px;
    margin-right: 20px;
}

/* 滚动条整体宽度 */
::-webkit-scrollbar {
    width: 6px;
    /* 对于垂直滚动条 */
    height: 100%;
    /* 对于水平滚动条 */
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
    background: transparent;
    /* 轨道的颜色 */
    border-radius: 10px;
    /* 轨道的圆角 */
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
    background: #ddecff;
    /* 滑块的颜色 */
    border-radius: 10px;
    /* 滑块的圆角 */
}

/* 当鼠标悬停在滑块上时 */
::-webkit-scrollbar-thumb:hover {
    background: #ddecff;
    cursor: pointer;
    /* 滑块悬停时的颜色 */
}

/* 滚动条按钮（上下箭头）的样式，不过大部分浏览器不支持直接修改这些按钮的样式 */
/* ::-webkit-scrollbar-button { ... } */

/* 滚动条两端的小块 */
::-webkit-scrollbar-corner {
    background: #ddecff;
    /* 角落的颜色 */
}

/* 定义过渡动画 */
.slide-fade-enter-active {
    transition: all 0.2s ease;
}
.slide-fade-leave-active {
    transition: all 0.2s ease;
}
.slide-fade-enter-from,
.slide-fade-leave-to {
    transform: translateX(-10px);
    opacity: 0;
}
// <n-modal>
.n-card.w_500{
    width: 500px;
}

.n-image img {
    width: 100%;
    height: 100%;
    max-height: 100%;
}
