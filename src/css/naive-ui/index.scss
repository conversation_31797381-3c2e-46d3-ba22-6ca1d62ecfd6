/**
 * naive-ui 样式定制
 */

// 定制全局（n__开头）类名或属性名覆盖组件内部样式
.n__checkbox-icon-circle .n-checkbox-box {
    --n-border-radius: 50%;
}

.n__base-select-menu__empty .n-base-select-menu__empty {
    padding: 12px 10px;
}

.n__com-action-button {
    color: var(--skin-c1);
    font-size: 16px;
}


/* overwrite naiveUI style  */
.dark {
    .n-data-table {
        .n-data-table-thead {
            background: linear-gradient(0deg, rgba(13,56,91,1) 0%, rgba(21,79,121,1) 100%) !important;
            font-size: 16px;

            .n-data-table-tr {
                background: unset !important;
            }
        }

        //.n-data-table-tr:not(.n-data-table-tr--summary):hover > .n-data-table-td {}
    }
    .n-date-panel, .n-time-picker-panel, .n-cascader-menu, .n-base-select-menu {
        border: 1px solid #16c5f8;
        box-shadow: 0 0 15px rgba(21, 132, 195, 0.75) !important;
    }
}

/* overwrite naive style 悬浮提示框宽度 */
.n-popover:not(.n-popover--raw) {
    @apply max-w-[600px];
}

/* overwrite data-table empty position */
.n-data-table-base-table:has(> .n-scrollbar) {
  &:has(> .n-data-table-empty) {
    position: relative;
    height: 100%;
    min-height: 260px;

    > .n-data-table-empty {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, calc(-50% + 24px));
    }
  }
}

.ifm-child .n-breadcrumb {
  display: none !important;
}