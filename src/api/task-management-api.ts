export default {
  hazardTaskList: '/hazardTask/page', // 隐患任务列表
  exportTaskList: '/hazardTask/exportTaskList', // 隐患任务列表导出
  getHazardPlanDetail: '/hazardTaskDetail/getHazardPlanDetai',
  hazardPlanTaskEventList: '/hazardPlanTaskEvent/page',
  getTaskClockInList: '/hazardTask/getTaskClockInList',
  getHazardPageList: '/hazardRecord/getHazardPageList',
  getHazardInventory: '/hazardRecord/pageEvent', // 任务详情-隐患清单列表
  importRisk: '/hazardCheckArea/essentialFactor/importRisk', // 风险点-导入
  riskIdentPage: '/riskIdentificationControl/riskIdentPage', // 风险点-列表
  selectCheckItemList: '/riskCheckItem/selectCheckItemList', // 风险点检查事项
  selectHazardDetail: '/riskCheckItemHazard/selectHazardDetail',
  queryDictDataList: '/dictData/queryDictDataList',
  queryEventNumGroupByUser: '/hazardTaskDetail/queryEventNumGroupByUser', //taskId
  delTask: '/hazardTask/delTask', // 删除隐患任务
};
