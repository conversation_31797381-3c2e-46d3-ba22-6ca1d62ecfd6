/*
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-10-17 19:56:27
 * @LastEditors: 悦悦 <EMAIL>
 * @LastEditTime: 2024-10-17 20:02:50
 * @FilePath: /隐患一张图/ehs-hazard/src/api/hazard-management-api.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
export default {
  hazardGradeList: '/hazardGrade/list', // 隐患等级列表
  hazardEssentialFactorClassList: '/hazardEssentialFactorClass/treeList', // 隐患分级类别列表

  // 已完成的隐患信息
  levelStatisticsMeger: '/hazardMeger/levelStatistics', // 隐患统计
  statisticsMeger: '/hazardMeger/statistics',
  pageEventMeger: '/hazardMeger/pageEvent',

  // 未完成的隐患信息
  getHazardPageList: '/hazardPlanTaskEvent/page',

  // qeuryEventDetail: '/hazardRecord/qeuryEventDetail', // 隐患处置详情
  qeuryEventDetail: '/hazardMeger/queryDetail', // 隐患处置详情

  hazardDetails: '/hazardRecord/hazardDetails', // 隐患处置详情
  hazardUrge: '/dispose/saveUrgeRecord', // 隐患催促
  getDispsoseNodeRecord: '/dispose/record', // 隐患处置详情
  hazardOverdueList: '/hazardOverdueSetting/page', // 获取隐患超期时间列表-查询
  // hazardOverdueList: '/hazardOverdueSetting/queryList', // 获取隐患超期时间列表-查询
  // exportEventList: '/hazardMeger/exportEventList', // 导出隐患表格
  exportEventList: '/hazardMeger/exporthazardEventList', // 导出隐患表格
  getAllUnit: '/ehsUpms/getAllUnit', // 获取隐患单位列表
  getUserTaskUnitList: '/hazardTask/getUserTaskUnitList', // 获取用户相关任务单位列表

  // mergePageEvent: '/hazardMeger/pageEvent', // 任务详情-隐患清单列表
  mergePageEvent: '/hazardMeger/pageHazardEvent', // 任务详情-隐患清单列表
  addRelation: '/hazardEssentialFactorClass/addRelation',
  getFileConfig: '/file/getFileConfig',
  getHazardMisSetting: '/hazardMisSetting/list',
  addHazardMisSetting: '/hazardMisSetting/add',
  deleteHazardMisSetting: '/hazardMisSetting/delete',
};
