/*
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-10-14 11:46:47
 * @LastEditors: 悦悦 <EMAIL>
 * @LastEditTime: 2024-10-15 09:23:47
 * @FilePath: /隐患一张图/ehs-hazard/src/api/dyb.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AEge t
 */
/**
 * 此文件接口为示例代码需要，仅供参考，后期需删除
 */
export default {
  // 参数配置
  hazardClassify: {
    // 隐患分类
    getTreeList: 'hazardEssentialFactorClass/treeList', // 隐患分类-列表
    getDeleteTreeList: '/hazardEssentialFactorClass/delete', // 隐患分类-删除
    getAddTreeList: '/hazardEssentialFactorClass/saveBatch', // 隐患分类-新增
    getEditTreeList: '/hazardEssentialFactorClass/updateBatch', // 隐患分类-编辑
    getMoveTreeList: '/hazardEssentialFactorClass/move', // 隐患分类-移动
  },
};
