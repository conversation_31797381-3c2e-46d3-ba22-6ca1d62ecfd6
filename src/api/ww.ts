/*
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-10-12 09:24:03
 * @LastEditors: fangweiwei <EMAIL>
 * @LastEditTime: 2025-02-24 11:06:48
 * @FilePath: /隐患一张图/ehs-hazard/src/api/ww.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 * 此文件接口为示例代码需要，仅供参考，后期需删除
 */
export default {
  // 参数配置
  paramsConf: {
    // 检查类型设定
    getCheckSettingList: '/hazardPlanType/getPlanTypeList', // 分页列表
    addCheckSetting: '/hazardPlanType/addPlanType', // 新增
    deleteCheckSetting: '/hazardPlanType/deletePlanType', // 删除
    updateCheckSetting: '/hazardPlanType/updatePlanType', // 修改
    stopCheckSetting: '/hazardPlanType/updatePlanTypeStatus', // 停用
    // 隐患级别设置
    getGradeList: 'hazardGrade/listPage', // 分页列表
    updateGrade: 'hazardGrade/saveOrUpdate', // 新增 + 修改
    deleteGrade: 'hazardGrade/delete', // 删除
    // 隐患超期时间设置
    getOverdueSettingList: 'hazardOverdueSetting/page', // 分页列表
    addOverdueSetting: 'hazardOverdueSetting/addOverdueSetting', // 新增
    deleteOverdueSetting: 'hazardOverdueSetting/deleteOverdueSetting', // 删除
    updateOverdueSetting: 'hazardOverdueSetting/updateOverdueSetting', // 修改

    //物联网信号设置
    iotSignalPageList: '/hazardEssentialFactorClassItem/iotSignalPage', //物联网信号配置列表
    addIotSignalCode: '/hazardEssentialFactorClassItem/addIotSignalCode', //物联网信号配置列新增
    removeIotSignalCode: '/hazardEssentialFactorClassItem/removeIotSignalCode', //物联网信号配置列删除
    monitorEventConfigList: '/eventConfig/monitorEventConfigList', //物联网信号选择列表

    // 默认检查项配置
    getDefaultConfigList: 'hazardCheckDefaultConfig/listPage', // 分页列表
    saveOrUpdate: 'hazardCheckDefaultConfig/saveOrUpdate', // 新增 + 修改
    deleteDefaultConfig: 'hazardCheckDefaultConfig/delete', // 删除

    // 随机检查
    getRandomCheckList: 'hazardRandomCheck/page', // 分页列表
    getRandomCheckDetail: 'hazardRandomCheck/detail', // 详情
    exportHazardRandomCheck: 'hazardRandomCheck/exportHazardRandomCheck', // 导出
    hazardRandomCheckDetail: 'hazardRandomCheck/detailWeb', // 随机检查-检查信息

    // 隐患清单
    getHazardInventory: 'hazardRandomCheck/getHazardInventory', // 隐患事件统计
    exportEventList: '/hazardMeger/exportEventList', // 导出隐患表格
    getHazardInventoryDetail: '/hazardRandomCheck/getHazardInventoryDetail', // 详情

    // 隐患库
    getEssentialFactor: 'hazardEssentialFactor/listPage', // 分页列表
    saveOrUpdateHazard: 'hazardEssentialFactor/saveOrUpdateHazard', // 新增
    deleteEssentialFactor: 'hazardEssentialFactor/delete', // 删除

    // 隐患分类
    getTreeList: 'hazardEssentialFactorClass/treeList', // 隐患分类-列表
    saveBatch: 'hazardEssentialFactorClass/saveBatch', // 隐患分类-新增
    updateBatch: 'hazardEssentialFactorClass/updateBatch', // 隐患分类-编辑
    deleteEssentialFactorClass: 'hazardEssentialFactorClass/delete', // 隐患分类-删除
    saveFullName: 'hazardEssentialFactorClass/fullName', // 隐患分类-全量名
    moveFactorClass: 'hazardEssentialFactorClass/move', // 隐患分类-移动

    // 隐患库
    hazardEssentialFactorReportData: 'hazardEssentialFactor/reportData', // 隐患数据库导入
    hazardEssentialFactorTemplate: 'hazardEssentialFactor/template', // 隐患库导入模板
    // 隐患库检查项
    hazardEssentialFactorClassItemListPage: 'hazardEssentialFactorClassItem/listComPage', // 隐患库检查项列表
    hazardEssentialFactorClassItemDetail: 'hazardEssentialFactorClassItem/comDetail', // 隐患库检查项-详情
    hazardEssentialFactorClassItemDetailOther: 'hazardEssentialFactorClassItem/detail', // 隐患库检查项-详情2
    hazardEssentialFactorClassItemSaveOrUpdateHazard: 'hazardEssentialFactorClassItem/saveOrUpdateHazard', // 隐患库-新增/编辑
    hazardEssentialFactorClassItemUploadAdd: 'hazardEssentialFactorClassItem/uploadAdd', // 隐患库检查项-导入
    hazardEssentialFactorClassItemEexportTemplate: 'hazardEssentialFactorClassItem/exportTemplate', // 隐患库检查项-模板

    // 打卡设置
    hazardCheckRangeGetOrgTree: 'hazardCheckRange/getOrgTree', // 获取单位列表
    hazardCheckRangeSaveOrUpdate: 'hazardCheckRange/saveOrUpdate', // 新增编辑打卡
    hazardCheckRangeList: '/hazardCheckRange/page', // 打卡分页
    hazardCheckRangeDetail: '/hazardCheckRange/detail', // 获取单个打卡详情
    hazardCheckRangeDelete: '/hazardCheckRange/delete', // 删除单个打卡

    // 组织机构相关
    getAllUnit: 'ehsUpms/getAllUnit', // 获取组织机构树

    // 登录
    loginApi: 'login/getTokenLoginInfoByToken', // 获取组织机构树
    getRoleList: '/ehsUpms/getRoleList', // 获取角色

    // 获取设备列表
    getUnitBuildingInfoList: 'hazardCheckDevice/getUnitBuildingInfoList', // 获取组织机构树

    // 获取楼栋楼层树
    getBuildingTreeByUnitId: 'eBuilding/getBuildingTreeByUnitId', // 获取组织机构树

    // 巡查点位相关接口
    getCheckPointList: '/hazardCheckPoint/listPage', // 巡查点位列表
    getCheckPointDetail: '/hazardCheckPoint/detail', // 巡查点位详情
    deleteCheckPointById: '/hazardCheckPoint/delete', // 巡查点位详情
    saveOrUpdateCheckPoint: '/hazardCheckPoint/saveOrUpdate', // 巡查点位-新增/编辑
    getOrgCodeUpList: '/ehsUpms/getOrgCodeUpList', // 巡查点位-新增/编辑
    getCheckTableDetail: '/hazardCheckTable/getCheckTableDetail', // 检查表-检查表详情
    exportBatchQrCode: '/hazardCheckPoint/exportBatchQrCode', // 批量下载二维码
    bindCheckTableByBatch: '/hazardCheckPoint/bindCheckTableByBatch', // 批量关联检查表
    importCheckPointByBatch: '/hazardCheckPoint/importCheckPointByBatch', // 巡查点位-从设备/重点部位导入
    exportTemplateForYjy: '/hazardCheckPoint/exportTemplate', // 巡查点位-下载导入模板
    importForYjy: '/hazardCheckPoint/importCheckPoint', // 巡查点位-导入数据
    exportPointManage: '/hazardCheckPoint/exportPointManage', // 巡查点位-导入数据
    deleteBatch: '/hazardCheckPoint/deleteBatch', // 巡查点位-批删
    getPlanUnitByPland: '/hazardPlan/getPlanUnitByPlanId', // 检查计划-查询检查对象
    queryFloorAreaImage: '/record/queryFloorAreaImage',

    validateCheckTableVersionNum: '/hazardPlan/validateCheckTableVersionNum', // 检查【检查表】版本
    validateCheckTableVersionNumByPlanId: '/hazardPlan/validateCheckTableVersionNumByPlanId', // 检查计划-校验所关联的【检查表】版本
  },
};
