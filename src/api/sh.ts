/**
 * 此文件接口为示例代码需要，仅供参考，后期需删除
 */
export default {
  hazardPlan: {
    // 隐患计划
    shjurisdictionPageList: '/hazardEventIssuedUser/page', // 管辖范围-列表
    getList: '/hazardPlan/getPlanPageList', //列表
    addPlan: '/hazardPlan/addPlan', // 新增计划
    detailPlan: '/hazardPlan/detailPlan', // 计划详情
    updatePlan: '/hazardPlan/updatePlan', // ​/hazardPlan​/updatePlan
    startPlan: '/hazardPlan/startPlan', // 启用
    stopPlan: '/hazardPlan/stopPlan', // 启用
    deletePlan: '/hazardPlan/deletePlan', //删除
    // listPage: '/hazardCheckTable/listPage',
    getCheckTypeData: '/hazardPlanType/getPlanTypeList', //列表
  },
  checkList: {
    // 检查表
    listPage: '/hazardCheckTable/listPage',
  },
  file: {
    exportPlanList: '/hazardPlan/exportPlanList',
  },
  planTask: {
    getPlanTaskCount: '/hazardPlanTask/getPlanTaskCount', // 获取检查计划任务数量
    getPalnTaskPageList: '/hazardPlanTask/getPlanTaskPageList', // 检查计划任务分页
    exportPlanTaskList: '/hazardPlanTask/exportPlanTaskList',
  },
  paramsConf: {
    addPlanType: '/hazardPlanType/addPlanType',
    getPalnTypeList: '/hazardPlanType/getPlanTypeList',
    updatePlanType: '/hazardPlanType/updatePlanType',
    deletePlanType: '/hazardPlanType/deletePlanType',
    updatePlanTypeStatus: '/hazardPlanType/updatePlanTypeStatus',
  },
};
