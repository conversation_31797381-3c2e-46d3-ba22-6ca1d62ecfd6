/**
 * 此文件接口为示例代码需要，仅供参考，后期需删除
 */
export default {
  demoRequest: {
    getDataList: '/hazardPhotoCasually/page', // 隐患随手拍-列表
    getStatisticsData: '/hazardPhotoCasually/count', // 隐患随手拍-统计
    getDangerDetail: '/hazardPhotoCasually/detail', // 隐患随手拍-详情
    getDangerGrade: '/hazardGrade/list', // 隐患等级筛选列表
    getDangerLibrary: '/hazardEssentialFactorClass/treeList', // 隐患库分类
    getDangerCheckList: '/hazardEssentialFactorClassItem/listComPage', // 隐患库-检查项列表
    getDangerCheckListOther: '/hazardEssentialFactorClassItem/listComPage', // 隐患库-检查项列表2
    getClassItemAndCommonByFactorId: '/hazardEssentialFactor/getClassItemAndCommonByFactorId', // 隐患库-根据隐患库id获取隐患库常见隐患
    checkDangerConfirm: '/hazardPhotoCasually/update', // 隐患库-隐患审核/hazardEssentialFactor/listPage
    getListPage: '/hazardEssentialFactor/listPage', // 隐患库-隐患审核
    getListPageDetail: '/hazardEssentialFactorClassItem/comDetail', // 隐患库-检查列表详情
    getListPageDetailFile: '/hazardEssentialFactorClassItem/getClassItemFile', // 隐患库-检查列表详情附件
    detailPlan: '/hazardPlan/detailPlan', // 计划详情
    getUsersList: '/ehsUpms/getOrgUser', // 获取用户列表
    getOrgList: '/ehsUpms/getAllUnit', // 获取机构列表
  },
};
