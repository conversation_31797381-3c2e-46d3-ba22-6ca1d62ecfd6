/*
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-11-27 15:52:03
 * @LastEditors: 悦悦 <EMAIL>
 * @LastEditTime: 2024-11-27 21:19:53
 * @FilePath: /ehs-hazard/src/api/zyl.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
export default {
  // 参数配置
  checklist: {
    // 隐患分类
    // /hazardCheckTable/saveOrUpdate
    getChecklist: '/hazardCheckTable/listPage', // 检查表列表-列表
    hazardCheckTableSaveUpdate: '/hazardCheckTable/saveOrUpdate', // 检查表-新增-编辑
    // /hazardCheckTable/listPage
    // /hazardCheckTable/stopOrStartCheckTable  停用、启用
    getstopOrStartCheckTable: '/hazardCheckTable/stopOrStartCheckTable',
    // /hazardCheckTable/delete  //删除
    getdelete: '/hazardCheckTable/delete',
    // getChecklist: '/hazardCheckTable/listPage', // 隐患分类-列表
    getOrgCodeUpList: '/ehsUpms/getOrgCodeUpList',
    // /hazardCheckTable/saveOrUpdate
    // 新增/修改检查内容
    saveOrUpdate: '/hazardCheckArea/saveOrUpdate', // 新增、修改
    getCheckTableDetail: '/hazardCheckTable/getCheckTableDetail', //检查表详情
    getCheckAreaDetail: '/hazardCheckTable/getCheckAreaDetail',

    hazardCheckAreaImport: '/hazardCheckArea/excel/import', //模板导入
    hazardCheckAreaExportTempalte: '/hazardCheckArea/exportTemplate', //导入模板导出

    hazardEssentialFactorClassItemListPage: '/hazardEssentialFactorClassItem/listComPage', //隐患库列表-列表
    // /hazardEssentialFactorClassItem/listComPage
    // /hazardCheckArea/essentialFactor/import
    hazardCheckAreaEssentImport: '/hazardCheckArea/essentialFactor/import', //从隐患库中导入
    hazardEssentialFactorList: '/hazardEssentialFactor/listPage', //隐患库列表
    checkAreaDelete: '/hazardCheckArea/checkAreaDelete', //检查内容删除
    hazardGradeListNew: '/hazardGrade/listPage', // 隐患等级列表
  },
};
