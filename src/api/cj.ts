export default {
  // 检查表
  inspectMgr: {
    hazardCheckTableList: '/hazardCheckTable/listPage', // 检查表-列表
    hazardCheckTableSaveUpdate: '/hazardCheckTable/saveOrUpdate', // 检查表-新增-编辑
    hazardCheckTableDelete: '/hazardCheckTable/delete', // 检查表-删除
    hazardCheckTableIsEnable: '/hazardCheckTable//isEnable', // 检查表-是否被引用
    // 设备检查表
    hazardCheckDeviceList: '/hazardCheckDevice/listPage', //列表
    hazardCheckDeviceTypeList: '/hazardCheckDevice/getDeviceTypes', //设备类型列表
    hazardCheckSaveUpdate: '/hazardCheckDevice/saveOrUpdate', //新增编辑
    hazardCheckDeviceDelete: '/hazardCheckDevice/delete', //删除
    hazardCheckDeviceImport: '/hazardCheckDevice/import', //导入
    hazardCheckDeviceExportTemplate: '/hazardCheckDevice/exportTemplate', //导入模板导出
    // 点位检查表
    hazardCheckPointList: '/hazardCheckPoint/listPage', //列表
    hazardCheckPointDetail: '/hazardCheckPoint/detail', //详情
    hazardCheckPointFloorList: '/hazardCheckPoint/buildingFloor/list', //楼栋楼层list
    hazardCheckPointSaveUpdate: '/hazardCheckPoint/saveOrUpdate', //新增编辑
    hazardCheckPointDelete: '/hazardCheckPoint/delete', //删除
    hazardCheckPointImport: '/hazardCheckPoint/import', //导入
    hazardCheckPointExport: '/hazardCheckPoint/export', //导出
    hazardCheckPointExportQrCode: '/hazardCheckPoint/exportBatchQrCode', //导出二维码
    hazardCheckPointeExportTemplate: '/hazardCheckPoint/exportTemplate', //导入模板导出
    getUnitBuildingInfoList: '/eBuilding/getUnitBuildingInfoList', //根据单位获取楼栋号
    getFloorListByUnitIdAndBuilding: '/eBuilding/getFloorListByUnitIdAndBuilding', //根据单位id楼栋id获取楼层
    queryErecordBuildingFloorList: '/eBuilding/queryErecordBuildingFloorList', // 多条件楼层信息列表查询
    queryRegionalManagementList: '/hazardPlan/queryRegionalManagementList', // 查询区域管理列表

    // 区域检查表
    hazardCheckAreaList: '/hazardCheckArea/listTree', //内容树
    hazardCheckAreaDetail: '/hazardCheckArea/detail', //详情
    hazardCheckAreaDetailSave: '/hazardCheckArea/detail/save', //详情保存
    hazardCheckAreaBatchSave: '/hazardCheckArea/batchSave', //批量保存
    hazardCheckAreaBatchDelete: '/hazardCheckArea/batchDelete', //批量删除
    hazardCheckAreaImport: '/hazardCheckArea/excel/import', //模板导入
    hazardCheckAreaEssentImport: '/hazardCheckArea/essentialFactor/import', //从隐患库中导入
    hazardCheckAreaExportTempalte: '/hazardCheckArea/exportTemplate', //导入模板导出
    // 隐患库列表
    hazardEssentialFactorList: '/hazardEssentialFactor/listPage',
  },

  // 举一反三
  oneThree: {
    oneToThreeStatistics: '/hazardHaveOneFindThree/oneToThreeStatistics', //类比排查任务-数据统计
    getOneToThreePage: '/hazardHaveOneFindThree/getOneToThreePage', //类比排查任务-分页查询
    getOneToThreeDetail: '/hazardHaveOneFindThree/getOneToThreeDetail', //类比排查任务-详情
    exportOneToThree: '/hazardHaveOneFindThree/exportList', //类比排查任务-导出
    addOneToThree: '/hazardHaveOneFindThree/addOneToThree', //类比排查任务-新增
    deleteOneToThree: '/hazardHaveOneFindThree/deleteOneToThree', //类比排查任务-删除
    getUnitListByTaskId: '/hazardHaveOneFindThree/getUnitListByTaskId', //获取类比排查单位列表
    pageHaveOneFindThreeEvent: '/hazardRecord/pageHaveOneFindThreeEvent', //频发典型问题
    // 频发导出
    exportHighFrequencyEventList: '/hazardHaveOneFindThree/exportHighFrequencyEventList', // 频发典型问题-导出
  },
};
