/*
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-10-12 09:24:03
 * @LastEditors: xginger <EMAIL>
 * @LastEditTime: 2025-06-02 14:26:15
 * @FilePath: /隐患一张图/ehs-hazard/src/api/index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { stringify } from 'querystringify';
import { merge } from 'lodash-es';
// api file
import common from './common';
import zyx from './zyx';
import sh from './sh';
import cj from './cj';
import tt from './tt';
import ww from './ww';
import jjf from './jjf';
import dyb from './dyb';
import zyl from './zyl';
import lc from './lc';
import { mis } from './mis';
import weather from './weather';
import hazardManagementApi from './hazard-management-api';
import taskManagementApi from './task-management-api';
// http://***************:8702/hazardEventIssuedUser/page
export const api = {
  type: {
    nil: '',
    file: 'file-server', // 文件上传服务
    demo: 'resource-server', // demo服务-供参考用，新的服务请在下面接着写
    hazard: 'ehs-clnt-hazard-service', //隐患
    platform: 'ehs-clnt-platform-service', // 工作台
    rmc: 'ehs-clnt-rmc-service', // 风险分级管控
    tenant: 'ehs-clnt-tenant-service', // 承租方
    gisfv: 'bw-svc-indoor-gis-service', //gis服务
    weather: 'ehs-clnt-platform-service',
    eahs: 'ehs-api-hazard-service',
    // hazard: '',
  },
  public: {
    getAllUnit: '/ehsUpms/getAllUnit', //获取组织机构
    getOrgTree: '/ehsUpms/getOrgTree', //获取组织机构树
    getOrgUser: '/ehsUpms/getOrgUser', //获取机构用户列表
    getOrgUserPage: '/ehsUpms/getOrgUserPage', //获取机构用户列表
    getTenantTree: '/tenantInfo/getTenantryListByUnitId', //根据隐患单位获取承租方信息
    getTenantUserPage: '/tenantInfo/getTenantryUsersByTenantryId', //根据承租方id查询承租方下的人员列表
    getTenantInfo: '/tenantInfo/getTenantryByLocationInfo', //获取承租方id判断是否显示承租方字段-位置-根据楼栋楼层和区域id查承租方id
    getTenantTreeBuild: '/tenantInfo/getTenantryDetailByLocationInfo', //根据楼栋楼层和区域id查承租方id
    getRealWeatherInfoByDistinctCode: '/weather/getRealWeatherInfoByDistinctCode',
    getAreaDataByFid: '/tenantInfo/getTenantryAreaByBf',
    changeOrgCode: '/login/changeOrgCode',
    addHazardSourceConfig: '/hazardSourceConfig/addHazardSourceConfig', //新增隐患来源配置
    updateHazardSourceConfig: '/hazardSourceConfig/updateHazardSourceConfig', //修改隐患来源配置
    azardSourcegetInfo: '/hazardSourceConfig/getInfo', //获取隐患来源详情
    HazardSourcelist: '/hazardSourceConfig/list', //查询隐患来源配置
    deleteHazardSourceConfig: '/hazardSourceConfig/deleteHazardSourceConfig', //删除隐患来源配置
    getTypeCount: '/hazardPlan/getTypeCount',
  },
  hazardManagement: hazardManagementApi,
  taskManagement: taskManagementApi,
  name: merge(zyx, cj, tt, ww, jjf, weather, lc),
  sh: merge(sh),
  common: common,
  cj: cj,
  mis,
  ww,
  dyb,
  zyl,
  /**
   * 组装请求地址
   * @param serviceType
   * @param apiName
   * @param query
   */
  getUrl(serviceType: string, apiName: string, query?: any): string {
    // 内网接口基础路径
    const url = window.$SYS_CFG.INNER_BASE_URL;
    const paramsStr = query ? `?${stringify(query)}` : '';
    const _apiName = apiName.indexOf('/') === 0 ? apiName : '/' + apiName;
    const _serviceType = serviceType ? '/' + serviceType : '';
    return `${url}${_serviceType}${_apiName}${paramsStr}`;
  },
};
