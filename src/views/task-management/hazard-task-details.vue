<!--
 * @Author: fang<PERSON><PERSON> <EMAIL>
 * @Date: 2024-11-14 10:22:45
 * @LastEditors: fangweiwei <EMAIL>
 * @LastEditTime: 2024-11-27 17:43:46
 * @FilePath: \ehs-hazard-mgr\src\views\task-management\hazard-task-details.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="hazard-capture com-g-row-a1">
    <com-bread :data="breadData"></com-bread>
    <detailsVue :taskId="route.params.id" class="p-[20px] bg-[#EEF7FF] border border-white rounded" />
  </div>
</template>

<script lang="ts" setup>
import ComBread from '@/components/breadcrumb/ComBread.vue';
import { IBreadData } from '@/components/breadcrumb/type';
import { useRoute } from 'vue-router';

import detailsVue from './TaskDetails.vue';

const breadData: IBreadData[] = [
  { name: '隐患治理系统' },
  { name: '任务管理', routeRaw: { name: 'taskManagement' }, clickable: true },
  { name: '任务详情' },
];
const route = useRoute();
</script>

<style scoped>
.title {
  font-weight: bold;
  font-size: 1.5em;
  margin-bottom: 1em;
}

.hazard-capture :deep(.n-data-table) {
  --n-merged-th-color: #bbccf3;
}

.bg {
  background-image: url('@/assets/yhqktj-bj.png');
  background-size: cover;
  padding-left: 10px;
}

.item-border {
  position: relative;
}

.item-border::after {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border: 1px solid #ccc;
  /* 这里定义了边框的样式，你可以根据需要调整 */
  z-index: 1;
}
</style>
