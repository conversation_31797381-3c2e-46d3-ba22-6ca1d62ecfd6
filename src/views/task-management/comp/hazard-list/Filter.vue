<!--
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-10-30 22:38:47
 * @LastEditors: fangweiwei <EMAIL>
 * @LastEditTime: 2025-01-09 15:12:42
 * @FilePath: /ehs-hazard/src/views/task-management/comp/hazard-list/Filter.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <n-form :show-feedback="false" label-placement="left">
    <div class="flex justify-between items-center">
      <n-grid :x-gap="12" :y-gap="8" :cols="4">
        <!-- 监管级别的账号，包括集团顶级、二级等等这些监管类型的 -->
        <n-grid-item v-if="userInfo.unitOrgType == '2' && detailInfo.planUnitGroupStatus != '2'">
          <n-form-item label="检查对象:">
            <n-select
              v-model:value="filterForm.unitId"
              placeholder="请选择"
              :options="unitOptions"
              class="flex items-center"
            />
          </n-form-item>
        </n-grid-item>
        <n-grid-item>
          <n-form-item>
            <n-input :placeholder="filterForm.placeholder" v-model:value="filterForm.likeFieldValue" clearable>
              <template #suffix>
                <BsSearch />
              </template>
            </n-input>
          </n-form-item>
        </n-grid-item>
      </n-grid>
      <n-button type="primary" @click="exportEventListFn">导出</n-button>
    </div>
  </n-form>
</template>

<script setup lang="ts">
import { computed, ref, watch, watchEffect } from 'vue';
import { BsSearch } from '@kalimahapps/vue-icons';
import { exportEventList, getPlanUnitByPland } from '@/views/hazard-management/fetchData';
import { ACTION } from '../../type';
import { fileDownloader } from '@/utils/fileDownloader';
import { useStore } from '@/store';

const props = defineProps({
  value: {
    type: Object,
    default: () => {},
  },
  hazardReporters: {
    type: Object,
    default: () => {},
  },
  detailInfo: {
    type: Object,
    default: () => {},
  },
});

const { userInfo } = useStore();
const emits = defineEmits(['action']);
const unitOptions = ref<any[]>([]);
const filterForm = computed(() => props.value);

function doHandle(action: ACTION) {
  emits('action', {
    action: action,
    // data: trimObjNull(filterForm.value),
  });
}

const getPlanUnitByPlandApi = async () => {
  const { data } = await getPlanUnitByPland({ planId: props.detailInfo.planId });
  // unitOptions.value = data.map((item: any) => ({ label: item.unitName, value: item.unitId }));
  // 当创建分组时，检查对象没有下拉选择，只能显示该分组单位的信息，比如：有A和B两个分组，A单位分组页面，检查对象只能显示A单位，检查参与人和检查负责人信息，不支持下拉选择B单位，查看B单位，只能在分组2，页面显示
  console.log(data, filterForm.value.unitId, 'data----------data');
  unitOptions.value = data
    .filter((item: any) => item.unitId == filterForm.value.unitId)
    .map((item: any) => ({
      label: item.unitName,
      value: item.unitId,
    }));

  if (!unitOptions.value.length) {
    filterForm.value.unitId = null;
  }
};

watch(filterForm.value, () => {
  doHandle(ACTION.search);
});
watchEffect(() => {
  if (props.detailInfo.planId && userInfo.unitOrgType == '2') {
    getPlanUnitByPlandApi();
  }
});

const exportEventListFn = async () => {
  let data = {
    ...filterForm.value,
    flag: '02',
    fileName: '隐患清单',
    exportfields: [
      {
        key: 'hazardPosition',
        title: '隐患位置',
      },
      {
        key: 'hazardDesc',
        title: '隐患描述',
      },
      {
        key: 'hazardTypeName',
        title: '隐患分类',
      },
      {
        key: 'hazardLevelName',
        title: '隐患等级',
      },
      {
        key: 'eventTime',
        title: '上报时间',
      },
      {
        key: 'disposeStateName',
        title: '整改状态',
      },
      {
        key: 'createByName',
        title: '检查人',
      },
      {
        key: 'timeoutDays',
        title: '是否超期',
      },
      { key: 'eventFileUrlStr1', title: '隐患图片1' },
      { key: 'eventFileUrlStr2', title: '隐患图片2' },
      { key: 'eventFileUrlStr3', title: '隐患图片3' },
      { key: 'dealFileUrlStr1', title: '验收图片1' },
      { key: 'dealFileUrlStr2', title: '验收图片2' },
      { key: 'dealFileUrlStr3', title: '验收图片3' },
    ],
  };

  // userInfo.unitOrgType == '2' &&
  //   data.exportfields.splice(data.exportfields.length - 1, 0, {
  //     title: '检查人',
  //     key: 'createByName',
  //   });
  fileDownloader(exportEventList(), {
    method: 'POST',
    contentType: 'application/json',
    body: JSON.stringify(data),
    filename: '隐患清单' + Date.now(),
  });
};

const resetField = () => {
  filterForm.value.unitId = null;
  filterForm.value.likeFieldValue = '';
};

defineExpose({ resetField });
defineOptions({ name: 'checkTempFilterComp' });
</script>
<style module lang="scss"></style>
