<template>
  <n-data-table
    class="com-table"
    :scroll-x="800"
    remote
    max-height="500"
    striped
    :columns="columns"
    :data="tableData"
    :bordered="false"
    :loading="loading"
    :pagination="pagination"
    :render-cell="useEmptyCell"
  />
</template>

<script setup lang="ts">
import { h, ref, VNode, toRaw, onMounted, computed, ComputedRef } from 'vue';
import { DataTableColumn, DataTableColumns, NButton } from 'naive-ui';
import { cols } from './columns.ts';
import { useRoute, useRouter } from 'vue-router';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { IObj } from '@/types';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { pageEventMeger } from '@/views/hazard-management/fetchData.ts';
import { isTopUnit } from '@/views/inspection-planning/planned-management/Details/utils.ts';

import { debounce } from 'lodash-es';
const route = useRoute();
const emits = defineEmits(['action', 'showDetails']);

const [loading, search] = useAutoLoading(true);
const getTableData = debounce(() => {
  const params = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    ...filterData,
  };
  search(pageEventMeger(params)).then((res) => {
    tableData.value = res.data.rows || [];
    updateTotal(res.data?.total || 0);
  });
}, 200);
const { pagination, updateTotal } = useNaivePagination(getTableData);

const columns = computed(() => {
  let result: DataTableColumn[] = [];
  const filterCols = cols.filter((item: any) => {
    return item;
  });
  result.push(...filterCols);
  result.push({
    title: '操作',
    key: 'actions',
    align: 'left',
    fixed: 'right',
    width: 80,
    render(row) {
      return getActionBtn(row);
    },
  });
  // 只有集团才展示检查对象
  if (!isTopUnit()) result = result.filter((item: any) => item.key !== 'unitName');
  return result;
});
const tableData = ref<any[]>([]);

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          size: 'small',
          type: 'primary',
          ghost: true,
          class: 'com-action-button',
          onClick: () => emits('showDetails', row),
        },
        { default: () => '详情' }
      ),
    ],
  ];
  return useActionDivider(acList);
}

let filterData: IObj<any> = {}; // 搜索条件

loading.value = false;
function getTableDataWrap(data: IObj<any>) {
  filterData = Object.assign({}, data) || {};
  pagination.page = 1;
  getTableData();
}
defineOptions({ name: 'checkTampTableComp' });
defineExpose({ getTableDataWrap, getTableData });
</script>
<style module lang="scss"></style>
