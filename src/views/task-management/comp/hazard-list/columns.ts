/*
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-10-24 15:19:33
 * @LastEditors: 悦悦 <EMAIL>
 * @LastEditTime: 2024-11-07 20:33:03
 * @FilePath: /隐患一张图/ehs-hazard/src/views/task-management/comp/hazard-list/columns.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { disposeState } from '@/components/table-col/disposeState';
import { tagBgs } from '@/views/random-check/check-task-manage/comp/detail/columns';
import { DataTableColumn } from 'naive-ui';
import { h } from 'vue';
import { isTopUnit } from '@/views/inspection-planning/planned-management/Details/utils';

export const cols: DataTableColumn[] = [
  // {
  //   title: '检查对象',
  //   key: 'unitName',
  //   align: 'left',
  //   ellipsis: {
  //     tooltip: true,
  //   },
  // },
  {
    title: '隐患位置',
    key: 'hazardPosition',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '隐患描述',
    key: 'hazardDesc',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '隐患分类',
    key: 'hazardTypeName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '隐患等级',
    key: 'hazardLevelName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    // title: isTopUnit() ? '检查时间' : '上报时间',
    title: '上报时间',
    key: 'createTime',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '整改状态',
    key: 'disposeStateName',
    align: 'left',
    width: 120,
    render: (row) => h(disposeState, { row }),
  },
  {
    title: '检查人',
    key: 'createByName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '是否超期',
    width: 150,
    key: 'timeoutDays',
    align: 'left',
    render: (row: any) => {
      if (row.timeoutDays == 0 || !row.timeoutDays) {
        return '否';
      } else {
        return `超期${row.timeoutDays}天`;
      }
    },
  },
];
