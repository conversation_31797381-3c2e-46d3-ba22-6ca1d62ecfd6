<template>
  <n-drawer v-model:show="show" :width="502" placement="right">
    <n-drawer-content title="打卡记录" closable>
      <div class="com-g-row-a1 gap-y-[20px]">
        <n-input
          v-model:value="userName"
          @update:value="getTaskClockInListFn"
          type="text"
          placeholder="请输入姓名模糊搜索"
        />
        <n-descriptions label-placement="left" :title="item.userName" :column="1" v-for="item of list" :key="item.id">
          <n-descriptions-item label="打卡时间"> {{ item.clockInTime }} </n-descriptions-item>
          <n-descriptions-item label="打卡位置"> {{ item.clockInAddress }} </n-descriptions-item>
          <n-descriptions-item label="打卡照片">
            <n-image width="100" :src="getFileURL(item.filePath, true)" :preview-src="getFileURL(item.filePath)" />
          </n-descriptions-item>
        </n-descriptions>
        <Empty v-if="!list.length" />
      </div>
    </n-drawer-content>
  </n-drawer>
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue';
import { getTaskClockInList } from '../fetchData';
import { useRoute } from 'vue-router';
import { useStore } from '@/store';
import { throttle } from 'lodash-es';
import { IClockData } from '../type';
import getFileURL from '@/utils/getFileURL';

const { userInfo } = useStore();

const route = useRoute();
const taskId = ref('');
const list = ref<IClockData[]>([]);

const userName = ref('');
const getTaskClockInListFn = throttle(async () => {
  console.log(taskId.value);
  const params = { userName: userName.value, taskId: taskId.value };
  const res = await getTaskClockInList(params);
  list.value = res.data;
}, 500);

const show = ref(false);

const open = ({ id }: any) => {
  console.log(id);
  taskId.value = id;
  show.value = true;
  userName.value = '';
  getTaskClockInListFn();
};

defineExpose({ open });
</script>
