<template>
  <n-data-table
    class="h-full com-table"
    remote
    striped
    :columns="columns"
    :data="tableData"
    :bordered="false"
    :flex-height="true"
    :loading="loading"
    :pagination="pagination"
    :render-cell="useEmptyCell"
    :scroll-x="1500"
    :row-props="rowProps"
  />
</template>

<script setup lang="ts">
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { $dialog } from '@/common/shareContext/useDialogCtx.ts';
import { IObj } from '@/types';
import { debounce } from 'lodash-es';
import { DataTableColumns, NButton } from 'naive-ui';
import { useRoute, useRouter } from 'vue-router';
import { h, nextTick, ref, VNode } from 'vue';
import { delTask, hazardTaskList } from '../../fetchData';
import { cols } from './columns';

const props = defineProps({
  btnAuths: {
    type: Array,
    default: () => [],
  },
});

const router = useRouter();
const emits = defineEmits(['action']);
const route = useRoute();
const params = ref({});
const getTableData = debounce(() => {
  if (Object.keys(route.query).length > 0) {
    params.value = {
      ...filterData,
      planStartDate: route.query.starTime,
      planEndDate: route.query.endTime,
      unitId: route.query.unitId || filterData.unitId,
      pageNo: pagination.page,
      pageSize: pagination.pageSize,
    };
  } else {
    params.value = {
      pageNo: pagination.page,
      pageSize: pagination.pageSize,
      ...filterData,
    };
  }
  // const
  search(hazardTaskList(params.value))
    .then((res) => {
      tableData.value = res.data.rows || [];

      updateTotal(res.data.total || 0);
    })
    .catch(() => {
      tableData.value = [];
      updateTotal(0);
    });
}, 100);

const [loading, search] = useAutoLoading(true);
const { pagination, updateTotal } = useNaivePagination(getTableData);
// console.log(pagination.pageSize, 'pagination=====');
const rowProps = (row: any) => {
  return {
    onContextmenu: (e: MouseEvent) => {
      // e.preventDefault();
      nextTick().then(async () => {
        try {
          await navigator.clipboard.writeText(row.taskId);
          // message.success('ID已复制到剪贴板');
        } catch (err) {
          // message.error('复制失败');
        }
      });
    },
  };
};

const columns = ref<DataTableColumns>([]);
const tableData = ref<any[]>([]);
function setColumns() {
  columns.value.push(...cols);
  // 添加操作栏 action
  columns.value.push({
    title: '操作',
    key: 'actions',
    align: 'left',
    fixed: 'right',
    width: 180,
    render(row) {
      return getActionBtn(row);
    },
  });
}
setColumns();

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          size: 'small',
          type: 'primary',
          ghost: true,
          class: 'com-action-button',
          onClick: () =>
            router.push({
              name: 'hazardTaskDetail',
              params: { id: row.taskId },
            }),
        },
        { default: () => '详情' }
      ),
    ],
    [
      h(
        NButton,
        {
          size: 'small',
          type: 'primary',
          ghost: true,
          class: 'com-action-button',
          onClick: () => {
            $dialog.warning({
              title: '提示',
              positiveButtonProps: { type: 'primary', color: '#527cff' },
              content: '确定删除该任务，删除后相关人员将无法执行及上报隐患！',
              positiveText: '确定',
              negativeText: '取消',
              transformOrigin: 'center',
              onPositiveClick: async () => {
                delTask({ taskId: row.taskId }).then(() => {
                  getTableData();
                });
              },
            });
          },
        },
        { default: () => '删除' }
      ),
      props.btnAuths.includes('deleteTask') && +row.taskState !== 3,
    ],
  ];
  return useActionDivider(acList);
}

let filterData: IObj<any> = {}; // 搜索条件

function getTableDataWrap(data?: IObj<any>) {
  filterData = Object.assign({}, data) || {};
  pagination.page = 1;
  getTableData();
}
defineOptions({ name: 'checkTampTableComp' });
defineExpose({ getTableDataWrap, getTableData, pagination });
</script>
<style module lang="scss"></style>
