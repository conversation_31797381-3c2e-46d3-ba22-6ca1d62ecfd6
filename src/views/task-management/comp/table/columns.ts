import { DataTableColumn } from 'naive-ui';
import { IPlanType, ITaskState, ITimeState } from '../../type';
import { h } from 'vue';
import { taskState } from '@/components/table-col/taskStateTaskManage';
export const cols: DataTableColumn[] = [
  {
    title: '计划名称',
    key: 'planName',
    align: 'left',
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '检查类型',
    key: 'planTypeName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '检查对象',
    key: 'unitNames',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '任务起止时间',
    key: 'planStartTime',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
    render(row) {
      if (!row.planStartTime) {
        return '未开始';
      }
      if (row.planEndTime) {
        return `${row.planStartTime} - ${row.planEndTime}`;
      } else {
        return `${row.planStartTime} - 未完成`;
      }
    },
  },
  {
    title: '任务状态',
    key: 'taskStateName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
    render(row: any) {
      return h(taskState, { row });
    },
  },
  {
    title: '任务时效',
    key: 'timeStateName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '创建时间',
    key: 'createTime',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '计划创建人',
    key: 'createByName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
];
