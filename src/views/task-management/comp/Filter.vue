<template>
  <!-- new -->
  <div
    style="
      padding-left: 20px;
      padding-right: 20px;
      display: flex;
      justify-content: space-between;
    "
  >
    <el-tabs
      v-model="activeTab"
      style="width: 100%"
      class="demo-tabs"
      @tab-change="tabClick"
    >
      <el-tab-pane
        style="font-size: 17px; font-weight: bold"
        :label="`${item.planTypeName}(${item.countNum})`"
        :name="item.planTypeId"
        :key="item.planTypeId"
        v-for="item in contList"
      />
    </el-tabs>
    <div v-if="Object.keys(route.query).length > 0">
      <n-button type="primary" @click="router.back()">返回</n-button>
    </div>
  </div>
  <n-collapse
    :expanded-names="expanded"
    :accordion="true"
    :on-item-header-click="collapseClick"
    :trigger-areas="['main', 'arrow']"
    arrow-placement="right"
  >
    <div style="padding-left: 20px; padding-right: 20px; padding-bottom: 10px">
      <n-form
        :show-feedback="false"
        label-placement="left"
        label-width="100px"
        ref="formRef"
      >
        <n-collapse-item :disabled="!showCheckUnitId" name="112">
          <template #header>
            <n-button
              text
              :disabled="showCheckUnitId ? false : true"
              type="primary"
              width="120"
            >
              {{ collapse ? '收起' : '展开' }}
            </n-button>
          </template>
          <template #header-extra>
            <n-grid :x-gap="12" :y-gap="8" :cols="4">
              <n-gi>
                <n-form-item label="任务起止时间:">
                  <n-date-picker
                    v-model:value="daterange"
                    value-format="yyyy-MM-dd"
                    type="daterange"
                    @update:value="timeRangeValChange"
                    clearable
                  />
                </n-form-item>
              </n-gi>
              <n-gi>
                <n-form-item>
                  <n-input
                    placeholder="请输入计划名称/创建人模糊搜索"
                    v-model:value="filterForm.keyWords"
                    clearable
                  >
                    <template #suffix>
                      <BsSearch />
                    </template>
                  </n-input>
                </n-form-item>
              </n-gi>
              <n-gi v-if="showCheckUnitId">
                <n-form-item label="检查对象:">
                  <n-select
                    v-model:value="filterForm.checkUnitId"
                    :options="unitOptions"
                    clearable
                  />
                </n-form-item>
              </n-gi>
              <n-gi v-if="collapse || !showCheckUnitId">
                <n-form-item label="任务状态:">
                  <n-select
                    v-model:value="filterForm.taskState"
                    :options="taskStateOptions"
                    clearable
                  />
                </n-form-item>
              </n-gi>
              <n-gi v-if="!showCheckUnitId || !collapse">
                <div class="flex justify-end w-full">
                  <n-button type="primary" @click="doHandle(ACTION.export)">
                    导出
                  </n-button>
                </div>
              </n-gi>
            </n-grid>
          </template>
          <n-grid v-if="showCheckUnitId" :x-gap="12" :y-gap="8" :cols="4">
            <n-gi v-if="collapse" span="4">
              <div class="flex justify-end w-full">
                <n-button type="primary" @click="doHandle(ACTION.export)">
                  导出
                </n-button>
              </div>
            </n-gi>
          </n-grid>
        </n-collapse-item>
      </n-form>
    </div>
  </n-collapse>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import { trimObjNull } from '@/utils/obj.ts';
import {
  ACTION,
  IPlanType,
  ITaskFilter,
  ITaskState,
  taskStateOptions,
} from '../type';
import { BsSearch } from '@kalimahapps/vue-icons';
import { getTypeCount } from '../fetchData';
import { SelectOption } from 'naive-ui';
import { useRoute, useRouter } from 'vue-router';
import dayjs from 'dayjs';
import {
  getAllUnit,
  getUserTaskUnitList,
} from '@/views/hazard-management/fetchData';

import { useStore } from '@/store/index.ts';
import { getDefaultDateRange } from '@/utils/getDefaultDateRange';

const { userInfo } = useStore();
const route = useRoute();
const router = useRouter();
console.log(route, 'route');
const props = defineProps({
  tab: {
    type: String,
  },
  uid: {
    type: String,
  },
});
const tabClick = (name: any) => {
  console.log(name, '>>>>>>name');

  // if (activeTab.value == 1) {
  //   // 获取直属下级单位
  //   getOrgUnderlingApi();
  //   // 获取卡片数据
  //   getTaskAllStatisticApi();
  //   // 刷新列表
  filterForm.value.planTypeId = name;
  doHandle(ACTION.SEARCH);
  // }
  // emits('updateTab', activeTab.value);
};
const activeTab = ref<any>('');
const contList: any = ref([]);
const getType = async (type: any, id: any) => {
  const params = {
    topUnitId: userInfo.topUnitId,
    unitId: id || userInfo.unitId,
    taskType: type == 2 ? 1 : undefined,
    planUserId: userInfo.id,
  };
  let res = await getTypeCount(params);

  contList.value = res.data;
  activeTab.value = res.data[0].planTypeId;
};

const collapse = ref(false);
const emits = defineEmits(['action', 'changeCollapse']);
// 当空数组的时候，全部收齐
const expanded = ref('');
const collapseClick = (e: any) => {
  collapse.value = e.expanded;
  if (e.expanded) {
    expanded.value = e.name;
  } else {
    expanded.value = '';
  }
  emits('changeCollapse', e.expanded);
};

let btnAuths = userInfo.resourceVoList
  .find((item: any) => item.resUrl === 'taskManagement')
  ?.childrens.filter((item: any) => item.resType == '2')
  .map((item: any) => item.resUrl);

const defaultDateRange = getDefaultDateRange();

const formRef = ref();

// 默认显示，但在计划任务中，因为加了左侧的树，所以不展示
const showCheckUnitId = ref(false);

// 两个tab都有的情况下，默认选中检查计划tab 因为有左侧的树 不显示检查对象
showCheckUnitId.value =
  btnAuths?.includes('planTask') && btnAuths?.includes('myExecuteTask')
    ? true
    : false;

const filterForm = ref<any>({
  unitId: userInfo.unitId,
  planTypeId: '',
  planStartDate: 111,
  planEndDate: null,
});
const unitOptions = ref<SelectOption[]>([]);
const timeRangeValChange = (value: any, formatvalue: any) => {
  console.log(formatvalue, '>>>>>>');
  if (formatvalue) {
    filterForm.value.planStartDate = formatvalue[0];
    filterForm.value.planEndDate = formatvalue[1];
  } else {
    filterForm.value.planStartDate = '';
    filterForm.value.planEndDate = '';
    router.push({
      name: 'taskManagement',
      query: {},
    });
  }
};
console.log(route.query, 'route query');

// if (route.query?.startTime) {
//   filterForm.value.planStartDate = route.query?.startTime;
//   filterForm.value.planEndDate = route.query?.endTime;
// }

const daterange = ref(null);

function doHandle(action: ACTION) {
  emits('action', {
    action: action,
    data: trimObjNull(filterForm.value),
  });
}

watch(filterForm.value, () => {
  doHandle(ACTION.search);
});

onMounted(() => {
  getType(props.tab, props.uid);
  getUserTaskUnitList({ userId: userInfo.id }).then((res) => {
    unitOptions.value = res.data?.rows?.map((item: any) => ({
      label: item.unitName,
      value: item.id,
    }));
  });
});

const resetField = (data: any) => {
  if (Object.keys(route.query).length === 0) {
    daterange.value = null;
  }
  Object.keys(filterForm.value).forEach((key) => {
    filterForm.value[key as keyof ITaskFilter] = null;
  });
  console.log('resetField', showCheckUnitId.value);
  // 【计划任务】不能使用这个 filter 里面的 UnitId;我执行的任务是要
  if (showCheckUnitId.value) {
    filterForm.value.unitId = userInfo.unitId;
  } else {
    filterForm.value.unitId = data.unitId;
  }
  console.log('resetField======', filterForm.value);
};
function init() {
  if (Object.keys(route.query).length > 0) {
    daterange.value = [
      dayjs(route.query?.starTime as string).valueOf(),
      dayjs(route.query?.endTime as string).valueOf(),
    ];
    console.log(daterange.value, 'daterange.value');
  }
}
init();
defineExpose({ resetField, showCheckUnitId, collapse, expanded, getType });
defineOptions({ name: 'checkTempFilterComp' });
</script>
<style scoped lang="scss">
:deep(.n-collapse-item__header-extra) {
  width: calc(100% - 60px);
}

:deep(.n-collapse-item__content-inner) {
  padding-left: 60px;
}

.n-date-picker {
  width: 100%;
}
</style>
