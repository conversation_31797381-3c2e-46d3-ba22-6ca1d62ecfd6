<template>
  <div :class="{ 'detail-info': !isComponents }" class="rounded" :style="{ height: isComponents ? '83vh' : '87vh' }">
    <Header :title="detailInfo.planName" />
    <el-row class="py-[.625rem]">
      <el-col :span="8" v-for="item in basicInfo" :key="item.label">
        <el-form-item :label="item.label" label-position="left">
          <span v-if="item.key === 'timeStateName'" :class="{ 'text-[red]': detailInfo.timeState == 2 }">
            {{ detailInfo.timeStateName || '--' }}
          </span>
          <span v-else-if="item.key === 'planStartDate'">
            {{ detailInfo.planStartDate ? detailInfo.planStartDate + ' ~ ' + detailInfo.planEndDate : '--' }}
          </span>
          <span v-else-if="item.key === 'planStartTime'">
            {{ detailInfo.planStartTime ? detailInfo.planStartTime + ' ~ ' + detailInfo.planEndTime : '--' }}
          </span>
          <span v-else-if="item.key === 'beginTime'">
            {{ `${detailInfo.planStartTime} ~ ${detailInfo.planEndTime}` }}
          </span>
          <span v-else-if="item.key === 'frequency'">
            <frequencyType :row="detailInfo" />
          </span>
          <span v-else>{{ detailInfo[item.key] ? detailInfo[item.key] : '--' }}</span>
        </el-form-item>
      </el-col>
    </el-row>
    <n-grid :x-gap="12" :y-gap="14" :cols="5" class="mb-4">
      <n-grid-item :span="3" class="com-g-row-a1">
        <Header title="任务进度" :hasIcon="false"></Header>
        <div class="flex items-center my-5">
          <n-space class="w-full" vertical>
            <n-steps>
              <n-step
                title="计划下发"
                :description="detailInfo.createTime || '--'"
                :status="detailInfo.createTime ? 'process' : 'wait'"
              ></n-step>
              <n-step
                title="任务执行"
                :description="detailInfo.beginTime || '--'"
                :status="detailInfo.beginTime ? 'process' : 'wait'"
              ></n-step>
              <n-step
                title="任务完成"
                :description="detailInfo.finishTime || '--'"
                :status="detailInfo.finishTime ? 'process' : 'wait'"
              ></n-step>
            </n-steps>
          </n-space>
        </div>
      </n-grid-item>
      <!-- 12470 【任务管理】督察计划勾选不需要现场打卡，则生成的任务，详情页面需去掉打卡记录模块 -->
      <!-- 只有选择现场打卡的任务才展示打卡记录 -->
      <n-grid-item :span="2">
        <template v-if="detailInfo.isNeedClock == '1'">
          <div class="relative">
            <Header title="打卡记录" :hasIcon="false"></Header>
            <span class="absolute right-[20px] top-[50%] translate-y-[-50%]">
              <n-button text @click="showRecordList">详情</n-button>
            </span>
          </div>
          <div class="mt-4">
            <div class="rounded bg-white h-[80px] w-[240px] relative flex justify-center items-end">
              <span class="left-[19px] top-[15px] absolute">打卡</span>
              <span class="text-[#4081FF] text-[36px]">
                {{ detailInfo.clockNum }}
                <span class="text-[24px]">次</span>
              </span>
            </div>
          </div>
        </template>
      </n-grid-item>
      <!--检查范围区域不显示检察人员 checkRange  检查范围(1:区域;2:设备;3:点位) -->
      <n-grid-item :span="2" v-if="detailInfo.checkRange != '2'">
        <Header title="检查人员" :blueIcon="true"></Header>
        <el-form :model="detailInfo" label-width="auto" class="mt-3">
          <el-form-item label="检查负责人:">
            {{ detailInfo.fzrs }}
          </el-form-item>
          <el-form-item label="检查参与人:">
            {{ detailInfo.cyrs }}
          </el-form-item>
        </el-form>
      </n-grid-item>
      <n-grid-item v-if="detailInfo.checkRange == '1'" :span="detailInfo.planType != '3' ? 3 : 5" class="flex flex-col">
        <Header title="检查表" :hasIcon="false"></Header>
        <div class="flex justify-between mt-4 flex-1 items-center">
          <span class="left">{{ detailInfo.checkTable }}</span>
          <n-button text @click="checkTableDetail(detailInfo?.checkTableId)">
            检查内容:{{ detailInfo.checkItemNum }}项目>
          </n-button>
        </div>
      </n-grid-item>

      <n-grid-item v-if="detailInfo.checkRange == '3'" :span="detailInfo.planType != '3' ? 3 : 5" class="flex flex-col">
        <Header title="巡查点位" :hasIcon="false" class="mb-[10px]">
          <template #right>
            <div
              v-if="detailInfo?.planCheckPointList.length && detailInfo?.planCheckPointList.length > 3"
              class="text-[#527cff] cursor-pointer"
              @click="showPoint = true"
            >
              {{ `共${detailInfo?.planCheckPointList.length}条 >` }}
            </div>
          </template>
        </Header>
        <div class="point-box" v-for="(item, index) of detailInfo?.planCheckPointList.slice(0, 3)" :key="item.id">
          <div class="w-[80px]">{{ index + 1 }}</div>
          <div class="w-[300px]">
            <div v-if="item.pointFullName && item.pointFullName.length < 20">
              {{ item.pointFullName }}
            </div>
            <n-tooltip v-else trigger="hover">
              <template #trigger>
                {{ item.pointFullName && item.pointFullName.slice(0, 20) + '...' }}
              </template>
              {{ item.pointFullName }}
            </n-tooltip>
          </div>
          <div
            class="w-[300px] text-[#527cff] cursor-pointer flex items-center"
            @click="checkTableDetail(item.checkTableId)"
          >
            <div class="w-[170px]" v-if="!item.checkTableName">
              {{ '--' }}
            </div>
            <div class="w-[170px]" v-if="item.checkTableName && item.checkTableName.length < 10">
              {{ item.checkTableName }}
            </div>
            <n-tooltip class="w-[170px]" v-if="item.checkTableName && item.checkTableName.length >= 10" trigger="hover">
              <template #trigger>
                <div class="w-[170px]">
                  {{ item.checkTableName.slice(0, 10) + '...' }}
                </div>
              </template>
              {{ item.checkTableName }}
            </n-tooltip>
            <div class="contentNum" v-if="item.checkTableId">{{ item.checkTableContentNum }} 项检查内容></div>
            <div class="contentNum" v-else>{{ defaultPointTotal }} 项默认检查内容></div>
          </div>
        </div>
      </n-grid-item>
    </n-grid>

    <n-grid :x-gap="12" :y-gap="8" :cols="1" class="mb-4">
      <n-grid-item>
        <Header title="隐患检查情况" :blueIcon="true"></Header>
        <span style="color: #f3a33c"
          >*仅统计任务检查过程中上报的准隐患数据，最终以隐患清单中任务结束后正式上报的数据为准</span
        >
        <div class="flex gap-[8px] mb-4 mt-4">
          <div class="w-[182px] h-[68px] bg flex flex-col">
            <span :style="{ fontSize: toVh(16) }">上报的隐患总数</span>
            <n-statistic tabular-nums>
              <n-number-animation :from="0" :to="statisticsData?.total || 0" />
            </n-statistic>
          </div>

          <!-- <div class="w-[182px] h-[68px] bg flex flex-col">
            <span :style="{ fontSize: toVh(16) }">已整改</span>
            <n-statistic tabular-nums style="--n-value-text-color: #00b578">
              <n-number-animation
                :from="0"
                :to="statisticsData?.disposedNum || 0"
              />
            </n-statistic>
          </div>
          <div class="w-[182px] h-[68px] bg flex flex-col">
            <span :style="{ fontSize: toVh(16) }">待整改</span>
            <n-statistic tabular-nums style="--n-value-text-color: #fa5151">
              <n-number-animation :from="0" :to="statisticsData._num || 0" />
            </n-statistic>
          </div>
          <div class="w-[182px] h-[68px] bg flex flex-col">
            <span :style="{ fontSize: toVh(16) }">超期数量</span>
            <n-statistic tabular-nums style="--n-value-text-color: #f78c10">
              <n-number-animation
                :from="0"
                :to="statisticsData?.timeoutNum || 0"
              />
            </n-statistic>
          </div> -->
          <div
            class="other-card flex items-center bg3"
            style="min-width: 570px"
            v-if="levelStatisticsData && levelStatisticsData.length"
          >
            <!-- <img src="./bg3.png" alt="" style="position: absolute; top: 0; left: 0" /> -->
            <div style="z-index: 2; position: relative" class="flex justify-between items-center w-full pl-[15px]">
              <div v-for="(item, idx) in levelStatisticsData" :key="idx" :style="{ fontSize: toVh(16), width: '25%' }">
                <div :title="item.hazardLevelName.length > 4 ? item.hazardLevelName : ''" class="whitespace-nowrap">
                  {{
                    item.hazardLevelName.length > 4 ? `${item.hazardLevelName.slice(0, 4)}...` : item.hazardLevelName
                  }}
                </div>
                <n-statistic tabular-nums :style="{ '--n-value-text-color': colorList[idx] }">
                  <n-number-animation :from="0" :to="item.total" />
                </n-statistic>
              </div>
            </div>
          </div>
        </div>
      </n-grid-item>
      <n-grid-item>
        <div class="mb-[14px] flex" v-if="detailInfo.checkExecuteMethod == '2'">
          <span class="text-[#909399] mr-2 flex-shrink-0">检查情况:</span>
          <div class="flex-grow">
            <div class="flex justify-between mb-2">
              <span class="text-nowrap mr-2"> 已查 ({{ detailInfo.checkItemedNum }}/{{ detailInfo.totalNum }}) </span>
              <n-progress
                class="flex-grow"
                type="line"
                :percentage="getPercentage(detailInfo.checkItemedNum, detailInfo.totalNum)"
                :show-indicator="false"
                color="#527cff"
                :height="20"
                :border-radius="4"
                :fill-border-radius="0"
              />
            </div>
            <div class="flex justify-between">
              <span class="text-nowrap mr-2"> 未查 ({{ detailInfo.uncheckedItemNum }}/{{ detailInfo.totalNum }}) </span>
              <n-progress
                type="line"
                class="flex-grow"
                :percentage="getPercentage(detailInfo.uncheckedItemNum, detailInfo.totalNum)"
                :show-indicator="false"
                color="#527cff"
                :height="20"
                :border-radius="4"
                :fill-border-radius="0"
              />
            </div>
          </div>
        </div>
        <div class="flex">
          <span class="text-[#909399] mr-2">人员隐患上报情况:</span>
          <div class="flex-1 flex flex-wrap">
            <div
              class="item hover:text-[#3e62eb] cursor-pointer"
              :class="{
                'text-[#3e62eb]': filterData.createBy == item.checkUserId,
              }"
              @click="selectHazardReporterFn(item.checkUserId)"
              v-for="(item, index) of hazardReporters"
              :key="index"
            >
              <div class="item-border py-[12px] px-[16px]">
                {{ item.checkUserName }}
              </div>
              <div class="item-border py-[12px] px-[16px]">
                {{ item.totalNum || '--' }}
              </div>
            </div>
          </div>
        </div>
      </n-grid-item>
    </n-grid>
    <n-grid :x-gap="12" :y-gap="8" :cols="1">
      <n-grid-item>
        <Header title="隐患清单" :blueIcon="true"></Header>
        <div class="gap-y-[8px] w-full">
          <Filter
            ref="filterRef"
            class="com-table-filter"
            v-model:value="filterData"
            :detailInfo="detailInfo"
            @action="actionFn"
            :hazardReporters="hazardReporters"
          />
          <hazardListTable ref="hazardListTableRef" @showDetails="showHazardDetails" />
        </div>
      </n-grid-item>
    </n-grid>
    <!-- 打卡列表抽屉 -->
    <punchCardRecord ref="punchCardRecordRef" />
    <hazardDetails ref="hazardDetailsRef" :showMap="false" />

    <!-- 巡查点位 -->
    <n-modal
      :show="showPoint"
      :mask-closable="false"
      :show-icon="false"
      preset="dialog"
      title="巡查点位"
      style="width: 800px"
      :autoFocus="false"
      :on-close="handelClosePoint"
    >
      <template #header>
        <div class="header">巡查点位</div>
      </template>
      <div class="my-[20px] h-[500px]">
        <div class="point-box" v-for="(item, index) of detailInfo?.planCheckPointList" :key="item.id">
          <div class="w-[80px]">{{ index + 1 }}</div>
          <div class="w-[300px]">
            <div v-if="item.pointFullName && item.pointFullName.length < 20">
              {{ item.pointFullName }}
            </div>
            <n-tooltip v-else trigger="hover">
              <template #trigger>
                {{ item.pointFullName && item.pointFullName.slice(0, 20) + '...' }}
              </template>
              {{ item.pointFullName }}
            </n-tooltip>
          </div>
          <div
            class="w-[300px] text-[#527cff] cursor-pointer flex items-center"
            @click="checkTableDetail(item.checkTableId)"
          >
            <div class="w-[170px]" v-if="!item.checkTableName">
              {{ '--' }}
            </div>
            <div class="w-[170px]" v-if="item.checkTableName && item.checkTableName.length < 10">
              {{ item.checkTableName }}
            </div>
            <n-tooltip class="w-[170px]" v-if="item.checkTableName && item.checkTableName.length >= 10" trigger="hover">
              <template #trigger>
                <div class="w-[170px]">
                  {{ item.checkTableName.slice(0, 10) + '...' }}
                </div>
              </template>
              {{ item.checkTableName }}
            </n-tooltip>
            <div class="contentNum" v-if="item.checkTableId">{{ item.checkTableContentNum }} 项检查内容></div>
            <div class="contentNum" v-else>{{ defaultPointTotal }} 项默认检查内容></div>
          </div>
        </div>
      </div>
    </n-modal>

    <!-- 检查表详情 -->
    <CheckTableDetail :show="showCheckTable" :row="checkDataDetail" @close="showCheckTable = false" />
    <!-- 默认点位检查项 -->
    <defalutPointCheckTable v-model:show="showDefaultPointCheckTable" />
  </div>
</template>

<script lang="ts" setup>
import Header from '@/components/header/ComHeaderB.vue';
import defalutPointCheckTable from '@/views/inspection-planning/planned-management/Details/comp/defalutPointCheckTable.vue';
import { getDefaultConfigList } from '@/views/paramsConf/comp/fetchData';
import { computed, provide, ref } from 'vue';
import { useRouter } from 'vue-router';
import { getStatisticsMeger, levelStatisticsMeger } from '../hazard-management/fetchData';
import hazardDetails from '../hazard-management/hazard-details.vue';
import Filter from './comp/hazard-list/Filter.vue';
import hazardListTable from './comp/hazard-list/hazard-list-table.vue';
import punchCardRecord from './comp/punch-card-record.vue';
import { getHazardPlanDetail, queryEventNumGroupByUser } from './fetchData';

import { frequencyType } from '@/components/table-col/frequencyType';
import { useStore } from '@/store';
import { toVh } from '@/utils/fit';
import CheckTableDetail from '@/views/inspect-mgr/inspect/comp/CheckTableDetail.vue';
import { getCheckTableDetail } from '@/views/inspection_point/fetchData';
import { watch } from 'vue';
import { IHazardStatistic } from '../hazard-management/type';
import { isTopUnit } from '../inspection-planning/planned-management/Details/utils';
import { ACTION, IHazardTaskDetailsData, IHzaardReporter } from './type';
const { userInfo } = useStore();

interface IProps {
  taskId: string;
  isComponents?: boolean;
}

const props = withDefaults(defineProps<IProps>(), {});

const router = useRouter();
const filterRef = ref();
const hazardListTableRef = ref();
const levelStatisticsData = ref<any[]>([]);
const colorList = ['#e23b50', '#f59a23', '#bfbf00', '#76b90e'];
const getStatisticsData = () => {
  levelStatisticsMeger({
    businessId: props.taskId,
    unitId: detailInfo.value.unitIds,
  }).then((res) => {
    levelStatisticsData.value = res.data;
  });
};
// 巡查点位
const showPoint = ref(false);
const handelClosePoint = () => {
  showPoint.value = false;
};
// 检查表详情
const showCheckTable = ref(false);
const checkDataDetail = ref<any>({});
// 默认检查项详情
const showDefaultPointCheckTable = ref(false);
const checkTableClose = () => {
  showCheckTable.value = false;
  showDefaultPointCheckTable.value = false;
  checkDataDetail.value = {};
};

provide('closeDetail', checkTableClose);

const checkTableDetail = (id: string) => {
  if (!id) return (showDefaultPointCheckTable.value = true);
  getCheckTableDetail(id).then((res) => {
    checkDataDetail.value = res.data;
    showCheckTable.value = true;
  });
};

/** 隐患上报人员信息 */
const hazardReporters = ref<IHzaardReporter[]>([]);

const selectHazardReporterFn = (id: string) => {
  filterData.value.createBy = filterData.value.createBy === id ? '' : id;
  actionFn({ action: ACTION.search });
};

const punchCardRecordRef = ref();
const hazardDetailsRef = ref();
const detailInfo = ref<IHazardTaskDetailsData>({ planName: '' });
const statisticsData = ref<IHazardStatistic>({});

/** 任务状态 */
const taskState = computed(() => detailInfo.value.taskState); // 1:待开始;2:进行中;3:已完成

const showHazardDetails = (row: any) => {
  hazardDetailsRef.value.open(row);
};

const getPercentage = (checkItemedNum: number | undefined, totalNum: number | undefined) => {
  if (typeof checkItemedNum === 'number' && typeof totalNum === 'number') return (checkItemedNum / totalNum) * 100;
  return 0;
};
const filterData = ref({
  businessId: props.taskId,
  likeFieldValue: '',
  likeFields: '',
  placeholder: '',
  createBy: null,
  unitId: '',
});
if (isTopUnit()) {
  filterData.value.likeFields = 'hazardDesc,createByName';
  filterData.value.placeholder = '请输入检查人/隐患描述模糊搜索';
} else {
  filterData.value.likeFields = 'hazardDesc,createByName';
  filterData.value.placeholder = '请输入检查人/隐患描述模糊搜索';
}
const actionFn = (data: any) => {
  if (data.action === ACTION.search) {
    hazardListTableRef.value.getTableDataWrap(filterData.value);
  }
};

const getHazardPlanDetailFn = async () => {
  const res = await getHazardPlanDetail({ taskId: props.taskId });
  detailInfo.value = res.data;
  const taskStates = ['待开始', '进行中', '已完成', '已停用'];
  detailInfo.value.taskStateName = taskStates[res.data.taskState - 1];
  filterData.value.unitId = res.data.unitIds;
};
const defaultPointTotal = ref(0);
// 获取默认巡查点位配置
const getDefaultConfigListFn = async () => {
  let data = {
    pageNo: 1,
    pageSize: 9999,
    checkTableType: 'point',
    unitId: userInfo.topUnitId,
  };
  const res: any = await getDefaultConfigList(data);
  defaultPointTotal.value = res.data.total;
  frequencyType.value = res.data;
};

const queryEventNumGroupByUserFn = async () => {
  const res: any = await queryEventNumGroupByUser({ taskId: props.taskId });
  hazardReporters.value = res.data || [];
};

const getHazardStatisticsFn = async () => {
  const res = await getStatisticsMeger({ businessId: props.taskId });
  statisticsData.value = res.data;
  statisticsData.value._num = Number(statisticsData.value.unDisposedNum) + Number(statisticsData.value.disposingNum);
};

/** 打开记录抽屉 */
const showRecordList = () => {
  punchCardRecordRef.value.open({ id: props.taskId });
};

const basicInfo = computed(() => {
  const result = [
    { label: '检查类型:', key: 'planTypeName' },
    { label: '创建人:', key: 'createByName' },
    { label: '任务状态:', key: 'taskStateName' },
    { label: '创建时间:', key: 'createTime' },
    { label: '检查频次:', key: 'frequency' },
    { label: '计划起止时间:', key: 'planStartDate' },
  ];
  if (isTopUnit()) result.push({ label: '检查对象:', key: 'unitNames' });
  result.push({ label: '任务起止时间:', key: 'planStartTime' });
  result.push({ label: '任务时效:', key: 'timeStateName' });
  return result;
});

watch(
  props,
  async () => {
    if (!props.taskId) return;
    filterData.value.businessId = props.taskId;
    queryEventNumGroupByUserFn();
    await getHazardPlanDetailFn();
    getDefaultConfigListFn();
    getHazardStatisticsFn();
    getStatisticsData();
    actionFn({ action: ACTION.search });
  },
  {
    immediate: true,
  }
);

defineOptions({ name: 'hazard-details' });
</script>

<style scoped lang="scss">
.detail-info {
  height: 83vh;
  overflow-y: scroll;
  padding: 1.25rem;
  background-color: rgb(*********** / var(--tw-bg-opacity, 1));
}

.title {
  font-weight: bold;
  font-size: 1.5em;
  margin-bottom: 1em;
}

.hazard-capture :deep(.n-data-table) {
  --n-merged-th-color: #bbccf3;
}

.bg {
  background-image: url('@/assets/yhqktj-bj.png');
  background-size: cover;
  padding-left: 10px;
}

.item-border {
  position: relative;
}

.item-border::after {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border: 1px solid #ccc;
  /* 这里定义了边框的样式，你可以根据需要调整 */
  z-index: 1;
}

.other-card {
  position: relative;
  height: 68px;
}

.bg3 {
  background-size: 100% 100%;
  background-image: url('@/views/hazard-management/comp/bg3.png');
}

.point-box {
  display: flex;
  justify-content: space-around;
  width: 100%;
  height: 40px;
  line-height: 40px;
  background-color: #fff;
  border-bottom: 1.5px solid #e3e3e3;
  padding-left: 10px;
}

.contentNum {
  font-size: 12px;
  padding: 0 5px;
  height: 24px;
  line-height: 24px;
  margin-left: 4px;
  color: #fff;
  /* padding: 3px 5px; */
  border-radius: 5px;
  background-color: #1890ff;
}
</style>
