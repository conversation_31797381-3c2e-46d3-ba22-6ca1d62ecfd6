<template>
  <div class="flex flex-col">
    <com-bread :data="breadData"></com-bread>
    <div class="item_content notVw flex-1">
      <transition name="slide-fade">
        <UnitSelect
          v-show="unitVisible && showTree"
          class="unit-tree"
          @updateVal="updateVal"
        />
      </transition>
      <div
        style="flex: 5; position: relative"
        :style="{ width: showTree ? '62vw' : '100%' }"
      >
        <img
          @click="showTree = !showTree"
          v-if="userInfo.unitOrgType == '2' && unitVisible"
          src="@/assets/open.png"
          style="
            cursor: pointer;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            z-index: 10;
          "
          :style="{ width: toVw(30), left: `-${toVw(15)}` }"
        />
        <radio-tab
          class="!pt-[0]"
          :tab-list="tabList"
          :tab="tabType"
          @change="handleChange"
        ></radio-tab>
        <div
          class="right-table !p-[0]"
          :style="{
            height: btnAuths.includes('addPointPlan')
              ? `calc(100% - ${toVw(34)})`
              : '96%',
          }"
        >
          <Filter
            style="border-radius: 5px 5px 0 0; padding: 16px"
            ref="filterRef"
            @action="actionFn"
            :tab="tabType"
            :uid="uid"
            @change-collapse="changeCollapse"
          />
          <TableList
            class="com-table-container !pt-[0]"
            style="border-radius: 0 0 5px 5px"
            ref="tableCompRef"
            @action="actionFn"
            :btnAuths="btnAuths"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import ComBread from '@/components/breadcrumb/ComBread.vue';
import { IBreadData } from '@/components/breadcrumb/type.ts';
import RadioTab from '@/components/tab/ComRadioTabE.vue';
import UnitSelect from '@/components/unitSelect/index.vue';
import { useStore } from '@/store';
import { fileDownloader } from '@/utils/fileDownloader';
import { toVw } from '@/utils/fit';
import { computed, ref, nextTick } from 'vue';
import Filter from './comp/Filter.vue';
import TableList from './comp/table/Table.vue';
import { exportTaskListUrl } from './fetchData';
import { ACTION } from './type';
import { useRoute } from 'vue-router';
const { userInfo } = useStore();
const route = useRoute();
const showTree = ref(true);

let btnAuths = userInfo.resourceVoList
  .find((item: any) => item.resUrl === 'taskManagement')
  ?.childrens.filter((item: any) => item.resType == '2')
  .map((item: any) => item.resUrl);
const tabType = ref(2);
tabType.value = btnAuths.includes('planTask') ? 2 : 1;
const tabList = computed(() => {
  let tabList: any = [];
  if (btnAuths.includes('planTask') && btnAuths.includes('myExecuteTask')) {
    tabList = [
      { name: 2, label: '我执行的任务' },
      { name: 1, label: '计划任务跟踪' },
    ];
  } else if (
    btnAuths.includes('planTask') &&
    !btnAuths.includes('myExecuteTask')
  ) {
    tabList = [{ name: 1, label: '计划任务跟踪' }];
  } else if (
    !btnAuths.includes('planTask') &&
    btnAuths.includes('myExecuteTask')
  ) {
    tabList = [{ name: 2, label: '我执行的任务' }];
  }
  return tabList;
});

const defaultTableParams: { planUserId: string; taskType?: any } = {
  planUserId: userInfo.id,
  taskType: tabType.value == 2 ? 1 : undefined,
};

const breadData: IBreadData[] = [
  { name: '隐患治理系统' },
  { name: '任务管理' },
];
const filterRef = ref();
const tableCompRef = ref();
const uid = ref('');
const unitVisible = ref(false);
// 组织结构切换
function updateVal(
  unitId: string,
  isBusinessUnit: any,
  val: any,
  isInit: boolean
) {
  // if (！isInit) filterRef.value.resetField();
  if (isInit) filterRef.value.resetField({ unitId });
  uid.value = unitId;
  filterRef.value.getType(tabType.value, unitId);
  tableCompRef.value.pagination.page = 1;
  // tableCompRef.value.pagination.pageSize = 10;
  tableCompRef.value.getTableDataWrap({ ...defaultTableParams, unitId });
}

const handleChange = (tab: any) => {
  filterRef.value.getType(tab, uid.value);
  if (tab == tabType.value) return;
  if (tab == 2) {
    unitVisible.value = false;
    filterRef.value.showCheckUnitId = true;
  } else {
    unitVisible.value = true;
    filterRef.value.showCheckUnitId = false;
    filterRef.value.collapse = false;
    filterRef.value.expanded = '';
    collapse.value = false;
  }
  tableCompRef.value.pagination.page = 1;
  // tableCompRef.value.pagination.pageSize = 10;
  defaultTableParams.taskType = tab == 2 ? 1 : undefined;
  tabType.value = tab;
  filterRef.value.resetField({ unitId: userInfo.unitId });
  tableCompRef.value.getTableDataWrap(defaultTableParams);
};

const collapse = ref(true);
const changeCollapse = (inCollapse: boolean) => {
  collapse.value = inCollapse;
};

const actionFn = (data: any) => {
  if (data.action === ACTION.search) {
    if (Object.keys(route.query).length > 0) {
      nextTick(() => {
        tableCompRef.value.getTableDataWrap({
          ...defaultTableParams,
          ...data.data,
        });
      });
    } else {
      tableCompRef.value.getTableDataWrap({
        ...defaultTableParams,
        ...data.data,
      });
    }
  } else if (data.action === ACTION.export) {
    fileDownloader(exportTaskListUrl(), {
      // filename: '测试导出',
      method: 'POST',
      contentType: 'application/json',
      body: JSON.stringify({ ...defaultTableParams, ...data.data }),
    });
  }
};
function init() {
  if (Object.keys(route.query).length > 0) {
    if (Number(route.query.tab) === 1) {
      showTree.value = unitVisible.value = true;
    }
    tabType.value = Number(route.query.tab);
    // console.log(route.query, 'route.query');
    nextTick(() => {
      filterRef.value.getType(tabType.value, uid.value);
      defaultTableParams.taskType = Number(tabType.value) == 2 ? 1 : undefined;
    });
  }
}
init();
defineOptions({ name: 'taskManagement' });
</script>

<style scoped lang="scss">
.com-table-filter {
  height: calc(100vh - 160px);
  border-top-left-radius: 0;

  .select-tree {
    cursor: pointer;
    position: absolute;
    top: 50%;
    // left: -20%;
    transform: translateY(-50%);
    z-index: 10;
    margin-left: -16px;
  }

  .unit-tree {
    padding: 0px;

    :deep(.el-scrollbar) {
      height: calc(100vh - 280px);
    }
  }

  .table-content {
    height: calc(100vh - 255px);

    :deep(.n-collapse-item__content-inner) {
      padding-top: 0px;
    }
  }

  .table-short-content {
    height: calc(100vh - 320px);

    :deep(.n-collapse-item__content-inner) {
      padding-top: 16px;
    }
  }
}
</style>
