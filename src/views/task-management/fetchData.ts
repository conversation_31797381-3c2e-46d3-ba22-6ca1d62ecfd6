/*
 * @Author: xginger <EMAIL>
 * @Date: 2025-06-02 14:01:44
 * @LastEditors: xginger <EMAIL>
 * @LastEditTime: 2025-06-02 15:33:10
 * @FilePath: \ehs-hazard-mgr\src\views\task-management\fetchData.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { api } from '@/api';
import { IObj, IPageRes } from '@/types';
import { $http } from '@tanzerfe/http';
import { IClockData, IHazardTask, IHazardTaskDetailsData } from './type.ts';

// const actionURL = api.getUrl(api.type.file, api.name.file.uploadFile);

/** 隐患任务列表 */
export function hazardTaskList(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.taskManagement.hazardTaskList);
  return $http.post<IPageRes<IHazardTask>>(url, {
    data: { _cfg: { showTip: true }, ...query },
  });
}
export function exportTaskListUrl() {
  const url = api.getUrl(api.type.hazard, api.taskManagement.exportTaskList);
  return url;
}

export function getHazardPlanDetail(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.taskManagement.getHazardPlanDetail, query);
  return $http.post<IHazardTaskDetailsData>(url, {
    data: { _cfg: { showTip: true } },
  });
}

/** 隐患任务表格 */
export function hazardPlanTaskEventList(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.taskManagement.hazardPlanTaskEventList);
  return $http.post<IPageRes<IHazardTask>>(url, {
    data: { _cfg: { showTip: true }, ...query },
  });
}

/** 获取打卡列表 */
export function getTaskClockInList(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.taskManagement.getTaskClockInList, query);
  return $http.post<IClockData[]>(url, { data: { _cfg: { showTip: true } } });
}

export function queryEventNumGroupByUser(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.taskManagement.queryEventNumGroupByUser, query);
  return $http.post<IClockData[]>(url, { data: { _cfg: { showTip: true } } });
}

export function delTask(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.taskManagement.delTask, query);
  return $http.post<any>(url, { data: { _cfg: { showTip: true } } });
}
export function getTypeCount(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, '/hazardTask/getTypeCount', {
    ...query,
  });
  return $http.get<any[]>(url, {
    data: { _cfg: { showTip: true } },
  });
}
