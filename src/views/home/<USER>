<template>
  <div class="w-full h-full !p-[0]">
    <!-- <p class="text-4xl text-skin-c1 dark:text-skin-c4/80">隐患治理一张图</p> -->
    <img class="w-full h-full" src="../../assets/hazard.png" alt="" />
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import { useStore } from '@/store/index';

onMounted(() => {
  // 第一次进入时打开gis
  if (sessionStorage.getItem('isOpenGis')) return;
  openGis();
});

const openGis = () => {
  const base_url = window.$SYS_CFG.base_url;
  const gisWindow = window.open(`${base_url}/ehs-gis/index.html#/?project=hazardMgr`, 'hazardMgr-gisWindow');
  gisWindow?.blur();
  sessionStorage.setItem('isOpenGis', 'true');
};

defineOptions({ name: 'HomeIndex' });
</script>

<style scoped lang="scss"></style>
