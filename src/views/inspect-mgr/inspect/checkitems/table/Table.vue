<template>
  <div class="flex justify-between items-center my-[15px]">
    <div>添加检查项:</div>
    <div class="btns">
      <n-button type="primary" @click="showExportGetinfo">导入</n-button>
      <n-button
        type="primary"
        v-if="checkAreaFormGrade == '2'"
        @click="handlePAdd"
        >新增检查项</n-button
      >
      <n-button
        type="primary"
        v-if="checkAreaFormGrade == '1'"
        @click="handleAdd"
        >新增</n-button
      >
    </div>
  </div>
  <n-data-table
    class="mt-[15px]"
    :striped="false"
    :columns="columns"
    :data="tableData"
    :bordered="true"
    :single-line="true"
    :single-column="false"
    :min-height="560"
    :max-height="560"
    :scroll-x="1500"
    :row-class-name="changeClass"
  ></n-data-table>
  <CheckItemDetail
    :showModal="show"
    @closeModal="closeModal"
    @getDetailInfo="getDetailInfo"
    :formData="formData"
    :isParent="isParent"
    :modalTitle="modalTitle"
    :checkTabelID="_checkTabelID"
    :type="type"
  />

  <n-modal
    v-model:show="showExport"
    title="导入"
    style="width: 400px"
    @negative-click="showExport = false"
    preset="card"
    :auto-focus="false"
  >
    <div style="line-height: 80px; cursor: pointer">
      <div @click="() => riskPointSelectListRef.open(formData)">
        从风险分级管控导入
      </div>
      <riskPointSelectList
        ref="riskPointSelectListRef"
        @importSuccess="importSuccess"
        @close="showExport = false"
      />
      <div @click="showHiddenImport = true">从隐患库导入</div>
      <hiddenDangerImport
        :showModal="showHiddenImport"
        @closeModal="showHiddenImport = false"
        @getDetailInfo="getDetailInfo"
        :formData="formData"
        @importSuccess="importSuccess"
      />
      <div @click="showTemplateImport = true">模板导入</div>
      <templateImport
        :showModal="showTemplateImport"
        :formData="formData"
        @closeModal="showTemplateImport = false"
        @importSuccess="importSuccess"
      />
    </div>
  </n-modal>
</template>

<script setup lang="ts">
import { h, ref, VNode, nextTick } from "vue";
import {
  DataTableColumn,
  NButton,
  NFormItem,
  NInput,
  useMessage,
} from "naive-ui";
import { useActionDivider } from "@/common/hooks/useActionDivider.ts";
import CheckItemDetail from "./CheckItemDetail.vue";
import {
  getCheckAreaDetail,
  getCheckTableDetail,
} from "../../checklist/tabel/fetchData";
import templateImport from "../../importhtml/templateImport.vue";
import riskPointSelectList from "../../importhtml/riskPointSelectList.vue";
import hiddenDangerImport from "../../importhtml/hiddenDangerImport.vue";

import GradeTag from "./GradeTag.vue";
import {
  checkAreaDelete,
  getsaveOrUpdateApi,
  saveUpdInspect,
} from "../../fetchData";
import { flatData } from "../../comp/utils";
import { $dialog } from "@/common/shareContext/useDialogCtx.ts";

// 导入
let showExport = ref<boolean>(false);
// 风险库导入
const riskPointSelectListRef = ref();
// 隐患库导入
let showHiddenImport = ref<boolean>(false);
// 模板
let showTemplateImport = ref<boolean>(false);
const message = useMessage();

const prop = defineProps({
  checkAreaFormGrade: {
    type: String,
    default: () => "",
  },
  data: {
    type: Array,
    default: () => [],
  },
  _checkTabelID: {
    type: String,
    default: () => "",
  },
});

const modalTitle = ref("");
const isParent = ref(1);
const formData = ref({});
const tableData = ref<any[]>([]);
const getDetailInfo = async () => {
  const res: any = await getCheckTableDetail({
    id: prop._checkTabelID,
  });
  formData.value = res.data;
  if (res.data.checkTableType === "2") {
    isParent.value = 2;
  } else if (
    res.data.checkTableType === 1 &&
    res.data.checkAreaFormGrade === 1
  ) {
    //一级检查表
    tableData.value = res.data.checkAreaList;
  } else {
    //二级检查表
    isParent.value = 2;
    tableData.value = flatData(res.data.checkAreaList);
  }
};
getDetailInfo();
function importSuccess(type: string) {
  getDetailInfo();
  showHiddenImport.value = false;
  showTemplateImport.value = false;
  showExport.value = false;
}
const show = ref(false);
const columns: DataTableColumn[] = [
  {
    title: "序号",
    key: "serialNumber",
    width: 60,
    align: "left",
  },
  {
    title: "检查项",
    key: "checkContent",
    width: 500,
    align: "left",
    render: (rowData: any, index: number) => {
      if (rowData.parentId == "0") {
        if (prop.checkAreaFormGrade == "2") {
          return h(
            NFormItem,
            {
              path: `row.checkContent`,
              rule: {
                required: true,
                trigger: ["blur", "input"],
                validator(rule: any, value: number[]) {
                  return rowData.checkContent
                    ? true
                    : new Error("请输入检查项");
                },
              },
            },
            {
              default: () =>
                h(NInput, {
                  value: rowData.checkContent,
                  type: "textarea",
                  autosize: {
                    minRows: 1,
                    maxRows: 3,
                  },
                  maxlength: 50,
                  clearable: true,
                  showCount: true,
                  autofocus: true,
                  ref: "inputRef",
                  onFocus: () => {
                    // 获取焦点
                    // 初始化获取焦点
                    // nextTick(() => {
                    //   rowData.$refs.inputRef?.focus();
                    // });
                  },
                  onBlur: () => {
                    rowData.checkTableId = prop._checkTabelID;
                    if (rowData.checkContent) {
                      getsaveOrUpdateApi(rowData)
                        .then((res: any) => {
                          getDetailInfo();
                        })
                        .catch(() => {});
                    } else {
                    }
                  },
                  onUpdateValue(v) {
                    rowData.checkContent = v;
                  },
                  placeholder: "请输入检查项",
                }),
            }
          );
        } else {
          return h("span", rowData.checkContent);
        }
      } else {
        return h("span", rowData.checkDetail);
      }
    },
  },
  {
    width: 500,
    title: "常见隐患",
    key: "areaEssentialFactorList",
    align: "left",
    render: (rowData: any, index: number) => {
      return h(GradeTag, {
        yhlistTotal: rowData?.areaEssentialFactorList?.length,
        yhlist:
          rowData?.areaEssentialFactorList &&
          rowData?.areaEssentialFactorList.length > 3
            ? rowData?.areaEssentialFactorList.slice(0, 3)
            : rowData?.areaEssentialFactorList,
        checkAreaFormGrade: prop.checkAreaFormGrade,
        parentId: rowData.parentId,
        id: rowData.id,
        dataList: tableData.value,
      });
    },
  },
  {
    width: 300,
    title: "操作",
    key: "action",
    align: "left",
    render: (rowData: any, index: number) => {
      return rowData.parentId == "0" && prop.checkAreaFormGrade == "2"
        ? getPActionBtn(rowData, index)
        : getActionBtn(rowData, index);
    },
  },
];

const type = ref(0);
function getActionBtn(row: any, index: number) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          size: "small",
          type: "primary",
          ghost: true,
          onClick: async () => {
            if (row.id) {
              const res: any = await getCheckAreaDetail({ id: row.id });
              formData.value = {
                ...res.data,
                rowIndex: index,
              };
            }
            type.value = 1;
            show.value = true;
            modalTitle.value = "检查内容详情";
          },
        },
        { default: () => "详情" }
      ),
    ],
    [
      h(
        NButton,
        {
          size: "small",
          type: "primary",
          ghost: true,
          onClick: async () => {
            if (row.rowData) {
              formData.value = {
                ...JSON.parse(row.rowData),
                rowIndex: index,
              };
            } else if (row.id) {
              const res: any = await getCheckAreaDetail({ id: row.id });
              formData.value = {
                ...res.data,
                rowIndex: index,
              };
            } else {
              formData.value = { ...row, rowIndex: index };
            }
            type.value = 2;
            show.value = true;
            modalTitle.value = "编辑检查内容";
          },
        },
        { default: () => "编辑" }
      ),
    ],
    [
      h(
        NButton,
        {
          size: "small",
          type: "primary",
          ghost: true,
          onClick: () => {
            $dialog.warning({
              title: "提示",
              content: "确定删除该检查项吗？删除后不可恢复",
              positiveButtonProps: { type: "primary", color: "#527cff" },
              positiveText: "确定",
              negativeText: "取消",
              transformOrigin: "center",
              onPositiveClick: async () => {
                if (row.id) {
                  checkAreaDelete({
                    id: row.id,
                    checkTableId: prop._checkTabelID,
                    checkTableType: 1,
                  }).then((res) => {
                    // tableData.value.splice(index, 1);
                    getDetailInfo();
                  });
                } else {
                  // tableData.value.splice(index, 1);
                }
              },
            });
          },
        },
        { default: () => "删除" }
      ),
    ],
  ];
  return useActionDivider(acList);
}

// 父级按钮
function getPActionBtn(row: any, index: number) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          size: "small",
          type: "primary",
          ghost: true,
          disabled: !row.id,
          onClick: async () => {
            formData.value = {
              ...row,
              rowId: row.id,
            };
            isParent.value = 0;
            type.value = 2;
            show.value = true;
            modalTitle.value = "新增检查内容";
          },
        },
        { default: () => "新增检查内容" }
      ),
    ],
    // showExport = true 导入
    [
      h(
        NButton,
        {
          size: "small",
          type: "primary",
          ghost: true,
          disabled: !row.id,
          onClick: async () => {
            formData.value = row;
            formData.value.checkAreaFormGrade = "2";
            showExport.value = true;
          },
        },
        { default: () => "导入" }
      ),
    ],
    [
      h(
        NButton,
        {
          size: "small",
          type: "primary",
          ghost: true,
          onClick: () => {
            // tableData.value.splice(index, 1);
            $dialog.warning({
              title: "提示",
              positiveButtonProps: { type: "primary", color: "#527cff" },
              content: `确定删除该检查项吗？删除后不可恢复`,
              positiveText: "确定",
              transformOrigin: "center",
              onPositiveClick: () => {
                if (row.id) {
                  checkAreaDelete({
                    id: row.id,
                    checkTableId: row.checkTableId,
                    checkTableType: 1,
                  }).then((res) => {
                    getDetailInfo();
                    // tableData.value.splice(index, 1);
                  });
                } else {
                  tableData.value.splice(index, 1);
                }
              },
              onNegativeClick: () => {
                // 取消删除
              },
            });
          },
        },
        { default: () => "删除" }
      ),
    ],
  ];
  return useActionDivider(acList);
}

// 点击新增检查项
function handlePAdd() {
  // 校验检查项是否填写
  const checkContent = tableData.value.find((item) => !item.checkContent);
  if (checkContent) {
    return message.warning("请先填写检查内容");
  }
  tableData.value.push({
    parentId: 0,
    checkContent: "",
    areaEssentialFactorList: [],
  });
}
// 点击新增
function handleAdd() {
  // router.push({ name: 'checklistConfAdd' });
  formData.value = {};
  type.value = 2;
  show.value = true;
}
function closeModal(value: boolean) {
  show.value = false;
}

// 点击导入时 获取当前检查表的详情
function showExportGetinfo() {
  getDetailInfo();
  nextTick(() => {
    showExport.value = true;
  });
}
const changeClass = (row: any, index: number) => {
  if (row.parentId === "0" && isParent.value === 2) {
    return "parent";
  }
  return "";
};
defineExpose({ closeModal, getDetailInfo, tableData });
defineOptions({ name: "myTable" });
</script>
<style lang="scss" scoped>
.btns .n-button {
  margin-left: 10px;
}

:deep(.n-form-item-blank) {
  display: flex;
  width: 100%;
  justify-content: flex-end;
  gap: 10px;
}

:deep(.parent td) {
  background-color: rgba(215, 241, 253, 1) !important;
}
</style>
