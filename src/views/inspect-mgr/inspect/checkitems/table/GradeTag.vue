<template>
  <div v-if="props.parentId != '0'" style="display: flex; flex-direction: column">
    <div style="margin: 8px 0px">共{{ yhlistTotal }}条</div>
    <div
      class="flex justify-start mt-2"
      style="align-items: flex-start"
      v-for="(item, index) in yhlist.slice(0, 3)"
      :key="index"
    >
      <div class="rounded hazard-class flex-1" v-if="item.essentialFactorGrade">{{ item.essentialFactorGrade }}</div>
      <div class="flex flex-col flex-1">
        <div class="text-gray-400">{{ item.essentialFactorClass }}</div>
        <div class="text-wrap">{{ index + 1 }}.{{ item.essentialFactorDescribe }}</div>
      </div>
    </div>
  </div>
  <div v-else>总计：{{ parentTotal() }} 条</div>
</template>
<script setup lang="ts">
const props = defineProps({
  yhlistTotal: {
    type: Number,
    default: () => 0,
  },
  yhlist: {
    type: Array,
    default: () => [],
  },
  checkAreaFormGrade: {
    type: String,
    default: () => '',
  },
  parentId: {
    type: String,
    default: () => '',
  },
  id: {
    type: String,
    default: () => '',
  },
  // 原先列表数据
  dataList: {
    type: Array,
    default: () => [],
  },
});

const parentTotal = () => {
  let total = 0;
  const dataList = props.dataList;

  const id = props.id;
  const checkAreaFormGradeList = dataList.filter((item: any) => item.parentId == id);

  if (checkAreaFormGradeList.length) {
    total = checkAreaFormGradeList.reduce((accumulator, currentValue: any) => {
      // 确保 areaEssentialFactorList 存在并且是一个数组
      if (currentValue.areaEssentialFactorList && Array.isArray(currentValue.areaEssentialFactorList)) {
        // 累加 areaEssentialFactorList 的长度
        return accumulator + currentValue.areaEssentialFactorList.length;
      } else {
        // 如果 areaEssentialFactorList 不存在或不是数组，返回累加器的值
        return accumulator;
      }
      // 初始化累加器的值为0
    }, 0) as number;
  }

  return total;
};

defineOptions({ name: 'checklistConfComp' });
</script>
