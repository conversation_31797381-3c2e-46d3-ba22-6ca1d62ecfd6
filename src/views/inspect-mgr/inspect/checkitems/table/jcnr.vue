<template>
  <view v-if="props.jcnrtxt"> {{ props.jcnrtxt }}</view>
  <view v-else>
    <n-input v-model:value="value" type="text" placeholder="检查内容" />
  </view>
</template>
<script setup lang="ts">
import { computed, ref, Ref } from 'vue';
const props = defineProps({
  jcnrtxt: {
    type: String,
  },
});
const value = ref('');
console.log(props.jcnrtxt, '=============props.jcnr');
defineOptions({ name: 'checklistConfComp' });
</script>

<style lang="scss" scoped></style>
