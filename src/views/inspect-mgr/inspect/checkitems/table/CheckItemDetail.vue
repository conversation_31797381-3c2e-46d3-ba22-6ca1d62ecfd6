<template>
  <el-dialog
    v-model="dialogVisible"
    destroy-on-close
    width="65vw"
    align-center
    :show-close="false"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    append-to-body
  >
    <template #header>
      <div class="flex justify-between items-center">
        <div class="flex justify-start items-center">
          <img src="@/assets/title_ico.png" style="width: 17px" alt="" />
          <div style="font-size: 16px" class="ml-2 text-black">
            {{ modalTitle }}
          </div>
        </div>
        <div class="flex justify-end items-center">
          <div
            v-if="!isEdit && isEnable && isMyUnit(route.query.checkUnitId)"
            style="display: flex; justify-content: flex-end"
          >
            <n-button style="margin-right: 20px" type="primary" @click="handleEdit"> 编辑 </n-button>
          </div>
          <img
            src="@/assets/dialog_close.png"
            style="width: 42px"
            class="cursor-pointer"
            alt=""
            @click="onNegativeClick"
          />
        </div>
      </div>
    </template>
    <div class="bg-white rounded p-4" style="max-height: 70vh; overflow-y: auto">
      <n-form
        class="form"
        ref="formRef"
        :model="model"
        :rules="isEdit ? rules : ''"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
      >
        <n-form-item label="检查项:" path="checkContent" v-if="isEdit || model.checkContent">
          <!-- 检查项长度50字，检查内容限制200个字 -->
          <n-input
            v-if="isEdit"
            maxlength="50"
            show-count
            v-model:value="model.checkContent"
            placeholder="请输入检查项"
            type="textarea"
            :autosize="{
              minRows: 3,
              maxRows: 5,
            }"
          />
          <div v-else>{{ model.checkContent || '--' }}</div>
        </n-form-item>
        <n-form-item label="检查内容:" path="checkDetail" v-if="isEdit || model.checkDetail">
          <n-input
            v-if="isEdit"
            maxlength="200"
            show-count
            v-model:value="model.checkDetail"
            placeholder="请输入检查内容"
            type="textarea"
            :autosize="{
              minRows: 3,
              maxRows: 5,
            }"
          />
          <div v-else>{{ model.checkDetail || '--' }}</div>
        </n-form-item>
        <n-form-item label="检查依据:" path="legalBasis" v-if="isEdit || model.legalBasis">
          <n-input
            v-if="isEdit"
            maxlength="200"
            show-count
            v-model:value="model.legalBasis"
            placeholder="请输入检查依据"
            type="textarea"
            :autosize="{
              minRows: 3,
              maxRows: 5,
            }"
          />
          <div v-else>{{ model.legalBasis || '--' }}</div>
        </n-form-item>
        <n-form-item label="检查标准:" path="complianceRequirements" v-if="isEdit || model.complianceRequirements">
          <n-input
            v-if="isEdit"
            maxlength="200"
            show-count
            v-model:value="model.complianceRequirements"
            placeholder="请输入检查标准"
            type="textarea"
            :autosize="{
              minRows: 3,
              maxRows: 5,
            }"
          />
          <div v-else>{{ model.complianceRequirements || '--' }}</div>
        </n-form-item>
        <n-form-item label="法律原文:" path="legalText" v-if="isEdit || model.legalText">
          <n-input
            v-if="isEdit"
            maxlength="200"
            show-count
            v-model:value="model.legalText"
            placeholder="请输入法律原文"
            type="textarea"
            :autosize="{
              minRows: 3,
              maxRows: 5,
            }"
          />
          <div v-else>{{ model.legalText || '--' }}</div>
        </n-form-item>
        <n-form-item label="法律责任:" path="legalLiability" v-if="isEdit || model.legalLiability">
          <n-input
            v-if="isEdit"
            maxlength="200"
            show-count
            v-model:value="model.legalLiability"
            placeholder="请输入法律责任"
            type="textarea"
            :autosize="{
              minRows: 3,
              maxRows: 5,
            }"
          />
          <div v-else>{{ model.legalLiability || '--' }}</div>
        </n-form-item>
        <n-form-item label="图例:" path="textareaValue" v-if="isEdit || model.checkAreaFileList?.length">
          <ImgUpload
            v-if="isEdit && watermarkData"
            :data="model.checkAreaFileList"
            @update="handleUpdate"
            :size="15"
            :max="6"
            :watermarkData="watermarkData"
            :type="'image-card'"
            :showBtn="false"
            class="com-upload-img"
          />
          <ImgUpload v-else mode="detail" :data="model.checkAreaFileList" :type="'image-card'" :showBtn="false" />
        </n-form-item>
        <ComHeaderB
          title="常见隐患"
          style="margin-bottom: 20px; margin-top: 50px"
          v-if="isEdit || model.areaEssentialFactorList?.length"
        />
        <el-table
          v-if="isEdit || model.areaEssentialFactorList?.length"
          :data="model.areaEssentialFactorList"
          empty-text="暂无数据"
          max-height="40vh"
          stripe
          style="min-height: 100px"
        >
          <el-table-column type="index" label="序号" width="70" />
          <el-table-column prop="essentialFactorClassId" label="隐患分类" width="170" show-overflow-tooltip>
            <template #default="{ row }">
              <el-tree-select
                v-model="row.essentialFactorClassId"
                :props="treeProps"
                node-key="id"
                :data="hiddenDangersType"
                v-if="isEdit"
                placeholder="请选择隐患分类"
                filterable
                :render-after-expand="false"
                clearable
                no-match-text="暂无数据"
              />
              <span v-else>{{ row.essentialFactorClass || '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="essentialFactorDescribe" label="隐患描述" width="300px" show-overflow-tooltip>
            <template #default="{ row }">
              <el-input
                v-if="isEdit"
                type="textarea"
                :autosize="{
                  minRows: 1,
                }"
                maxlength="200"
                show-word-limit
                v-model="row.essentialFactorDescribe"
                placeholder="请输入隐患描述"
              />
              <span v-else>{{ row.essentialFactorDescribe || '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="essentialFactorGrade" label="隐患等级" width="170" show-overflow-tooltip>
            <template #default="{ row }">
              <el-select
                v-model="row.essentialFactorGradeId"
                placeholder="请选择隐患等级"
                clearable
                filterable
                style="width: 100%"
                v-if="isEdit"
              >
                <el-option v-for="item in gradeList" :key="item.id" :label="item.gradeName" :value="item.id" />
              </el-select>
              <span v-else>{{
                (row.essentialFactorGradeId &&
                  gradeList.find((i: any) => i.id == row.essentialFactorGradeId).gradeName) ||
                '--'
              }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="sucFiles" label="正确图例" width="350">
            <template #default="{ row }">
              <ImgUpload
                :data="row.sucFiles"
                :max="3"
                :width="60"
                :rowNumber="3"
                type="image-card"
                :watermarkData="watermarkData"
                :mode="isEdit && watermarkData ? '' : 'detail'"
                :showBtn="false"
                @update="
                  (res: any) => {
                    row.sucFiles = res || [];
                  }
                "
              />
            </template>
          </el-table-column>
          <el-table-column prop="errFiles" label="错误图例" width="350">
            <template #default="{ row }">
              <ImgUpload
                :data="row.errFiles"
                :max="3"
                :width="60"
                :rowNumber="3"
                type="image-card"
                :watermarkData="watermarkData"
                :mode="isEdit && watermarkData ? '' : 'detail'"
                :showBtn="false"
                @update="
                  (res: any) => {
                    row.errFiles = res || [];
                  }
                "
              />
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" v-if="isEdit">
            <template #default="{ $index }">
              <n-button text type="primary" ghost @click="delRow($index)">删除</n-button>
            </template>
          </el-table-column>
        </el-table>
        <n-button v-if="isEdit" style="margin: 20px 0; width: 100%" dashed @click="addColumn">
          <template #icon>
            <IconAdd />
          </template>
          新增一行
        </n-button>
      </n-form>
    </div>
    <template #footer>
      <div class="flex justify-end items-center" v-if="isEdit">
        <el-button @click="onNegativeClick" plain>取消</el-button>
        <el-button type="primary" :disabled="submitLoading" :loading="submitLoading" @click="submit">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import ComHeaderB from '@/components/header/ComHeaderB.vue';
import { ref, defineProps, computed, watch } from 'vue';
import { NButton, NInput } from 'naive-ui';
import { ImgUpload } from '@/components/upload';
import { IUploadRes } from '@/components/upload/type';
import { FlFilledAdd as IconAdd } from '@kalimahapps/vue-icons';
const { userInfo } = useStore();
import { useRoute } from 'vue-router';
const route = useRoute();
import { useMessage } from 'naive-ui';
import { useStore } from '@/store';
import { isMyUnit } from '../../utils';
import { getDangerLibrary } from '@/views/hidden-dangers-photo/fetchData';
import { getsaveOrUpdateApi, postGetHazardGradeListAPI } from '../../fetchData';
import { $toast } from '@/common/shareContext/useToastCtx.ts';
import { ElMessageBox, ElSelect } from 'element-plus';
import { getWatermarkBizDataApi } from '@/utils/getWatermarkBizData';

const message = useMessage();
const gradeList = ref([]);
const emits = defineEmits(['closeModal', 'updateList', 'getDetailInfo']);
// 检查对象树参数配置
const treeProps = {
  label: 'className',
  children: 'children',
};
const props = defineProps({
  modalTitle: {
    type: String,
    default: '',
  },
  showModal: {
    type: Boolean,
    default: false,
  },
  formData: {
    type: Object,
    default: () => ({}),
  },
  //type 1 详情 2编辑详情
  type: {
    type: Number,
    default: 1,
  },
  // 是否被引用，引用不可编辑
  isEnable: {
    type: Boolean,
    default: false,
  },
  isParent: {
    type: Number,
    default: 1,
  },
  checkTabelID: {
    type: String,
    default: '',
  },
});

const dialogVisible = computed(() => {
  return props.showModal;
});

// 是否可以编辑
const isEdit = ref<boolean>(false);
watch(
  () => props.showModal,
  (newVal) => {
    isEdit.value = props.type == 2;
    if (JSON.stringify(props.formData) === '{}') {
      // 新增
      model.value = {
        essentialFactorClass: '', //隐患分类名称
        checkContent: '', //检查内容
        checkDetail: '', //检查详情
        legalBasis: '', //法规依据
        complianceRequirements: '', //合规要求
        legalText: '', //法律原文
        legalLiability: '', //法律责任
        checkAreaFileList: [], //正确图例/错误图例
        areaEssentialFactorList: [], //隐患分类列表
      };
    } else {
      if (isEdit.value) {
        // 编辑
        model.value = JSON.parse(JSON.stringify(props.formData));
        // 正确图例 错误图例
        model.value.areaEssentialFactorList = model.value.areaEssentialFactorList?.map((item: any) => {
          item.sucFiles = item.checkAreaEssentialFactorFileList.filter((item: any) => item.fileCategory == '1') || [];
          item.errFiles = item.checkAreaEssentialFactorFileList.filter((item: any) => item.fileCategory == '2') || [];
          return item;
        });
        if (props.isParent != 1) {
          if (props.formData.parentId != '0') {
            return;
          }
          model.value = {
            essentialFactorClass: '', //隐患分类名称
            checkContent: props.formData.checkContent, //检查内容
            checkDetail: '', //检查详情
            legalBasis: '', //法规依据
            complianceRequirements: '', //合规要求
            legalText: '', //法律原文
            legalLiability: '', //法律责任
            checkAreaFileList: [], //正确图例/错误图例
            areaEssentialFactorList: [], //隐患分类列表
            rowIndex: props.formData.rowIndex,
            rowId: props.formData.rowId,
          };
        } else {
        }
      } else {
        //展示详情
        model.value = JSON.parse(JSON.stringify(props.formData));
      }
      // 正确图例 错误图例
      model.value.areaEssentialFactorList = model.value.areaEssentialFactorList?.map((item: any) => {
        item.sucFiles = item.checkAreaEssentialFactorFileList.filter((item: any) => item.fileCategory == '1') || [];
        item.errFiles = item.checkAreaEssentialFactorFileList.filter((item: any) => item.fileCategory == '2') || [];
        return item;
      });
    }
  }
);
const model = ref<any>({
  essentialFactorClass: '', //隐患分类名称
  parentId: '',
  checkContent: '', //检查内容
  checkDetail: '', //检查详情
  legalBasis: '', //法规依据
  complianceRequirements: '', //合规要求
  legalText: '', //法律原文
  legalLiability: '', //法律责任
  checkAreaFileList: [],
  areaEssentialFactorList: [],
  sucFiles: [],
  errFiles: [],
});

// 隐患分类列表
const hiddenDangersType = ref<any[]>([]);
// 获取水印配置
const watermarkData = ref<any>();
getWatermarkBizDataApi().then((data: any) => {
  watermarkData.value = data;
});

function getLibrary() {
  let params = {
    unitId: userInfo.topUnitId,
    parentId: '0',
  };
  getDangerLibrary(params).then((res: any) => {
    hiddenDangersType.value = res.data;
  });
}
getLibrary();

const rules = ref({
  checkContent: {
    required: true,
    trigger: ['blur', 'input'],
    message: '请输入检查项',
  },
  checkDetail: {
    required: true,
    trigger: ['blur', 'input'],
    message: '请输入检查内容',
  },
});

function addColumn() {
  model.value.areaEssentialFactorList.push({
    isEdit: true, //是否可以编辑
    essentialFactorDescribe: '',
    essentialFactorGrade: null,
    essentialFactorGradeId: null,
    essentialFactorClass: null,
    essentialFactorClassId: null,
    checkAreaFileList: [],
    checkAreaEssentialFactorFileList: [],
    sucFiles: [],
    errFiles: [],
  });
}

const getGradeList = () => {
  postGetHazardGradeListAPI({
    pageNo: 0,
    pageSize: -1,
    unitId: userInfo.topUnitId,
  }).then((res: any) => {
    gradeList.value = res.data.rows;
  });
};
getGradeList();

function handleEdit() {
  isEdit.value = true;
}

const delRow = (index: number) => {
  ElMessageBox.confirm('确定删除该隐患吗？删除后不可恢复', '系统提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    model.value.areaEssentialFactorList.splice(index, 1);
  });
};

// 上传回调
function handleUpdate(res: any) {
  model.value.checkAreaFileList = res;
}

const formRef = ref<any>(null);
const submitLoading = ref<boolean>(false);
function submit(e: MouseEvent) {
  e.preventDefault();
  // 校验常见隐患
  const factorList: any = model.value.areaEssentialFactorList;
  if (factorList.length) {
    for (let index = 0; index < factorList.length; index++) {
      const element = factorList[index];
      if (!element.essentialFactorClassId) return $toast.error(`请选择隐患分类!`);
      if (!element.essentialFactorDescribe) return $toast.error(`请输入隐患描述!`);
      if (!element.essentialFactorGradeId) return $toast.error(`请选择隐患等级!`);
    }
  }

  formRef.value?.validate((errors: any) => {
    if (!errors) {
      model.value.areaEssentialFactorList.map((item: any) => {
        item.checkAreaEssentialFactorFileList = [
          ...(item.sucFiles || []).map((item: any) => {
            item.fileCategory = 1;
            item.id = null;
            return item;
          }),
          ...(item.errFiles || []).map((item: any) => {
            item.fileCategory = 2;
            item.id = null;
            return item;
          }),
        ];
        return item;
      });
      let dataForm = {
        ...model.value,
        checkAreaFileList: model.value.checkAreaFileList.map((item: any) => {
          return {
            ...item,
            id: null,
          };
        }),
        checkTableId: props.checkTabelID,
        parentId:
          JSON.stringify(props.formData) == '{}'
            ? '0'
            : props.formData.checkAreaFormGrade == '1'
              ? '0'
              : props.formData.parentId != '0'
                ? props.formData.parentId
                : props.formData.id,
      };
      getsaveOrUpdateApi(dataForm)
        .then((res: any) => {
          message.success('保存成功');
          emits('getDetailInfo');
          onNegativeClick();
        })
        .catch((err: any) => {
          message.error('保存失败');
        });
    }
  });
}

function onNegativeClick() {
  isEdit.value = false;
  model.value = {
    flagid: null,
    checkContent: '', //检查内容
    checkDetail: '', //检查详情
    legalBasis: '', //法规依据
    complianceRequirements: '', //合规要求
    legalText: '', //法律原文
    legalLiability: '', //法律责任
    checkAreaFileList: [], //正确图例/错误图例
    areaEssentialFactorList: [], //隐患分类列表
  };
  emits('closeModal', false);
}
</script>

<style lang="scss" scoped>
.com-upload-img :deep(.n-upload-file-list) {
  width: 100%;
}

// .com-upload-img :deep(.n-upload-file) {
//   width: 136px !important;
//   height: 96px !important;
// }

// .com-upload-img :deep(.n-upload-dragger) {
//   width: 136px !important;
//   height: 96px !important;
// }

// :deep(.n-upload-file-list) {
//   width: 200px !important;
// }

// :deep(.n-upload-file) {
//   width: 60px !important;
//   height: 36px !important;
// }

// :deep(.n-upload-dragger) {
//   width: 60px !important;
//   height: 36px !important;
// }

// :deep(.n-upload-trigger) {
//   height: 36px !important;
// }

.bds {
  border-top: 1px solid #ebeef5;
  background: rgb(244, 249, 255);
}
</style>
