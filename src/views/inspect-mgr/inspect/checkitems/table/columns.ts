import { formatDateRange } from '@/views/inspection-planning/planned-management/Details/utils';
import { DataTableColumn, NInput } from 'naive-ui';
// import { inspectType, planStatusList } from '../../constant';
import { h } from 'vue';
// import { planState } from '@/components/table-col/planState';
// import { formatDateRange } from '../../Details/utils';
import GradeTag from './GradeTag.vue';
import jcnr from './jcnr.vue';
// export const tagBgs = ['#F39600', '#89f12b', '#00B578', '#527CFF', '#f00'];
export const cols: DataTableColumn[] = [
  {
    title: '序号',
    key: 'index',
    width: 60,
    align: 'left',
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '检查内容',
    key: 'checkContent',
    width: 300,
    align: 'left',
    render: (rowData: any, index: number) => {
      if (rowData.checkContent == '') {
        return h(NInput, {
          value: rowData.parentId == '0' ? rowData.checkContent : rowData.checkDetail,
          type: 'textarea',
          autosize: {
            minRows: 1,
            maxRows: 3,
          },
          maxlength: 500,
          showCount: true,
          onUpdateValue(v) {
            if (rowData.parentId == '0') {
              rowData.checkContent = v;
              console.log(rowData);
            } else {
              rowData.checkDetail = v;
            }
          },
          placeholder: '请输入检查内容...',
          style: 'width:300px',
        });
      } else {
        return h('span', rowData.checkContent);
      }
    },
  },
  {
    width: 300,
    title: '常见隐患',
    key: 'areaEssentialFactorList',
    align: 'left',
    render: (rowData: any, index: number) => {
      return h(GradeTag, { yhlist: rowData?.areaEssentialFactorList });
    },
  },
];
