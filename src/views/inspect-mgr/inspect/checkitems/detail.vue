<template>
  <div class="com-g-row-a1">
    <div class="com-g-row-a1 h-full gap-y-[20px] com-table-container">
      <div class="item_content">
        <TableList class="table-list" ref="tableCompRef" />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { computed, ref, Ref } from 'vue';
import TableList from './table/Table.vue';

defineOptions({ name: 'checklistConfComp' });
</script>

<style module lang="scss"></style>
