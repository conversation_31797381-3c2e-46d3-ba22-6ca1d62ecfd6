import { formatDateRange } from '@/views/inspection-planning/planned-management/Details/utils';
// import { ro } from 'element-plus/es/locale/index.js';
import { DataTableColumn } from 'naive-ui';
// import { inspectType, planStatusList } from '../../constant';
import { h } from 'vue';
// import { planState } from '@/components/table-col/planState';
// import { formatDateRange } from '../../Details/utils';

// export const tagBgs = ['#F39600', '#89f12b', '#00B578', '#527CFF', '#f00'];

export const cols: DataTableColumn[] = [
  {
    title: '检查表名称',
    key: 'checkTableName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '类型',
    key: 'checkTableType',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
    render: (row) => {
      // 1: 综合检查，2: 巡查点位检查
      if (row.checkTableType == 1) {
        return h('span', '综合检查');
      } else {
        return h('span', '巡查点位检查');
      }
    },
  },
  {
    title: '状态',
    key: 'checkStatus',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
    render: (row) => {
      // 0 - 正常; 1 - 停用; 2 - 草稿
      if (row.checkStatus == 0) {
        return h(
          'span',
          {
            style: {
              color: '#1890FF',
            },
          },
          '正常'
        );
      } else if (row.checkStatus == 1) {
        return h(
          'span',
          {
            style: {
              color: 'red',
            },
          },
          '停用'
        );
      } else {
        return h('span', '--');
      }
    },
  },
  {
    title: '编制单位',
    key: 'unitName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '编制人',
    key: 'createByName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '编制时间',
    key: 'createTime',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
];
