import { api } from '@/api';
import { $http } from '@tanzerfe/http';
import { IObj, IPageRes } from '@/types';

export function getChecklist(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.zyl.checklist.getChecklist);
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}

export function getOrgCodeUpList(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.zyl.checklist.getOrgCodeUpList, { ...data });
  return $http.post(url, {
    data: { _cfg: { showTip: true } },
  });
}

export function getUpAndDownUnitInfo(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, '/hazardCheckTable/getUpAndDownUnitInfo', { ...data });
  return $http.post(url, {
    data: { _cfg: { showTip: true } },
  });
}

export function saveOrUpdate(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.zyl.checklist.saveOrUpdate, { ...data });
  return $http.post(url, {
    data: { _cfg: { showTip: true } },
  });
}

export function getCheckTableDetail(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.zyl.checklist.getCheckTableDetail, { ...data });
  return $http.post(url, {
    data: { _cfg: { showTip: true } },
  });
}

export function getCheckAreaDetail(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.zyl.checklist.getCheckAreaDetail, { ...data });
  return $http.post(url, {
    data: { _cfg: { showTip: true } },
  });
}
