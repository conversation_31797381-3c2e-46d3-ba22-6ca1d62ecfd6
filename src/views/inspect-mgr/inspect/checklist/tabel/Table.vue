<template>
  <n-data-table
    class="h-full com-table"
    remote
    :columns="columns"
    :data="tableData"
    :bordered="false"
    :pagination="pagination"
    :flex-height="true"
    :scroll-x="1500"
    :loading="loading"
    :render-cell="useEmptyCell"
    :row-key="(row: any) => row.id"
  />
  <!-- 检查表详情 -->
  <CheckTableDetail :show="detailVisibility" :row="rowData" @close="detailVisibility = false" />
</template>
<script setup lang="ts">
import { h, ref, VNode, toRaw, provide } from 'vue';
import { NButton, useMessage } from 'naive-ui';
import { useRouter } from 'vue-router';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { IObj } from '@/types';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { useStore } from '@/store';
import { getlistPage, exportCheckAreaByTableId, syncDataToCurrentUnit } from '@/views/inspect-mgr/inspect/fetchData.ts';
import CheckTableDetail from '@/views/inspect-mgr/inspect/comp/CheckTableDetail.vue';
import { fileDownloader } from '@/utils/fileDownloader';
import { $dialog } from '@/common/shareContext/useDialogCtx';

const message = useMessage();
const props = defineProps({
  btnAuths: {
    type: Array,
    default: () => [],
  },
});

const { userInfo } = useStore();
const rowData: any = ref({});
const router = useRouter();
const emits = defineEmits(['action']);
const [loading, search] = useAutoLoading(true);
const { pagination, updateTotal } = useNaivePagination(getTableData);
const tableData = ref<any[]>([]);

let columns = [
  {
    title: '检查表名称',
    key: 'checkTableName',
    align: 'left',
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '状态',
    key: 'checkStatus',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
    render: (row: any) => {
      // 0-正常;1-停用;2-草稿
      if (row.checkStatus == 0) {
        return h(
          'div',
          {
            style: {
              color: '#FFF',
              width: '66px',
              height: '30px',
              background: '#527CFF',
              borderRadius: '4px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            },
          },
          '正常'
        );
      } else if (row.checkStatus == 1) {
        return h(
          'span',
          {
            style: {
              color: '#FFF',
              width: '66px',
              height: '30px',
              background: '#CC2A2A',
              borderRadius: '4px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            },
          },
          '停用'
        );
      } else if (row.checkStatus == 2) {
        return h(
          'span',
          {
            style: {
              color: '#FFF',
              width: '66px',
              height: '30px',
              background: '#00B578',
              borderRadius: '4px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            },
          },
          '草稿'
        );
      } else {
        return '--';
      }
    },
  },
  {
    title: '编制单位',
    key: 'unitName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
    render: (row: any) => {
      // unitId
      return row.unitId === userInfo.unitId ? row.unitName + '(本单位)' : row.unitName;
    },
  },
  {
    title: '编制人',
    key: 'createByName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '编制时间',
    key: 'createTime',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
];
function setColumns() {
  columns.push({
    title: '操作',
    key: 'actions',
    align: 'left',
    fixed: 'right',
    width: 290,
    render(row: any) {
      return getActionBtn(row);
    },
  });
}
setColumns();

function getActionBtn(row: any) {
  let acList: [VNode, boolean?][] = [
    props.btnAuths.includes('detailCheckList') && [
      h(
        NButton,
        {
          size: 'small',
          type: 'primary',
          ghost: true,
          onClick: () => {
            rowData.value = row;
            todDetail();
          },
        },
        { default: () => '详情' }
      ),
    ],
    props.btnAuths.includes('updateCheckList') && [
      h(
        NButton,
        {
          size: 'small',
          type: 'primary',
          ghost: true,
          onClick: () => {
            router.push({
              name: 'inspectPointEdit',
              params: { id: toRaw(row).id },
            });
          },
        },
        { default: () => '编辑' }
      ),
      true,
    ],
    props.btnAuths.includes('stopCheckList') && [
      h(
        NButton,
        {
          size: 'small',
          type: 'warning',
          ghost: true,
          disabled: row.emptyFlag,
          onClick: () => {
            emits('action', {
              action: row.checkStatus == '1' || row.checkStatus == '2' ? 'strat' : 'shop',
              data: toRaw(row),
            });
          },
        },
        {
          default: () => (row.checkStatus == '1' || row.checkStatus == '2' ? '启用' : '停用'),
        }
      ),
    ],
    props.btnAuths.includes('delCheckList') && [
      h(
        NButton,
        {
          size: 'small',
          type: 'error',
          ghost: true,
          onClick: () => {
            emits('action', { action: 'del', data: toRaw(row) });
          },
        },
        { default: () => '删除' }
      ),
    ],
    props.btnAuths.includes('exportCheckList') && [
      h(
        NButton,
        {
          size: 'small',
          type: 'primary',
          loading: row.vloading,
          onClick: () => {
            toExport(row);
          },
          ghost: true,
        },
        { default: () => '导出' }
      ),
      true,
    ],
    row.isEnable && [
      h(
        NButton,
        {
          size: 'small',
          type: 'primary',
          disabled: row.syncFlag,
          onClick: () => {
            if (row.syncFlag) return;
            toAddUnit(row);
          },
          ghost: true,
        },
        { default: () => (row.syncFlag ? '已加至本单位' : '添加至本单位') }
      ),
      true,
    ],
  ];
  acList = acList.filter((item) => item);
  const acList1: [VNode, boolean?][] = [
    props.btnAuths.includes('detailCheckList') && [
      h(
        NButton,
        {
          size: 'small',
          type: 'primary',
          ghost: true,
          onClick: () => {
            rowData.value = row;
            todDetail();
          },
        },
        { default: () => '详情' }
      ),
    ],
    props.btnAuths.includes('exportCheckList') && [
      h(
        NButton,
        {
          size: 'small',
          type: 'primary',
          loading: row.vloading,
          onClick: () => {
            toExport(row);
          },
          ghost: true,
        },
        { default: () => '导出' }
      ),
      true,
    ],
    row.isEnable && [
      h(
        NButton,
        {
          size: 'small',
          type: 'primary',
          disabled: row.syncFlag,
          onClick: () => {
            if (row.syncFlag) return;
            toAddUnit(row);
          },
          ghost: true,
        },
        { default: () => (row.syncFlag ? '已加至本单位' : '添加至本单位') }
      ),
      true,
    ],
  ];
  const _acList1 = acList1.filter(Boolean);
  if (row.unitId === userInfo.unitId) {
    return useActionDivider(acList);
  } else {
    return useActionDivider(_acList1);
  }
}

let filterData: IObj<any> = {}; // 搜索条件
function getTableData() {
  const params: any = {
    unitId: userInfo.unitId,
    topUnitId: userInfo.topUnitId,
    loginUnitId: userInfo.unitId,
    ...filterData,
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    roleCodes: userInfo.roleCodes,
    flag: 1,
  };
  // 如果没选检查对象，默认登录用户自己的
  // if (!params.unitId) params.unitId = userInfo?.unitId;

  search(getlistPage(params)).then((res: any) => {
    if (res.data?.rows?.length) {
      for (let index = 0; index < res.data.rows.length; index++) {
        res.data.rows[index].vloading = false;
      }
    }
    tableData.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
}

loading.value = false;
function getTableDataWrap(data: IObj<any>) {
  filterData = Object.assign({}, data) || {};
  pagination.page = 1;
  getTableData();
}

//详情展示
const detailVisibility = ref(false);
const todDetail = () => {
  detailVisibility.value = true;
};
const closeDetail = () => {
  detailVisibility.value = false;
};

const toExport = async (row: any) => {
  console.log('🚀 ~ toExport ~ row:', row);
  row.vloading = true;
  await fileDownloader(exportCheckAreaByTableId() + `?checkTableId=${row.id}`, {
    filename: row.checkTableName,
    method: 'POST',
    contentType: 'application/json',
  });
  row.vloading = false;
};

const toAddUnit = async (row: any) => {
  $dialog.warning({
    title: '提示',
    content: '确定将该检查表同步至本单位？',
    positiveButtonProps: { type: 'primary', color: '#527cff' },
    positiveText: '确定',
    negativeText: '取消',
    transformOrigin: 'center',
    onPositiveClick: async () => {
      const _res: any = await syncDataToCurrentUnit({
        currentUnitId: userInfo.unitId,
        sourceCheckTableId: row.id,
        createBy: userInfo.id,
      });
      if (_res.code == 'success') {
        message.success(_res.message);
        getTableData();
      }
    },
  });
};
provide('closeDetail', closeDetail);

defineOptions({ name: 'checkTampTableComp' });
defineExpose({ getTableDataWrap, getTableData });
</script>
<style lang="scss">
.csBtn {
  position: absolute;
  left: 0;
  top: 0px;
  background-color: red;
  transform: translateX(-50%);
}
</style>
