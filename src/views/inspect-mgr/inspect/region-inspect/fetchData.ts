import { ICheckTempPageRes, checkForm } from './type';

import { $http } from '@tanzerfe/http';
import { api } from '@/api';
import { IObj } from '@/types';

// 获取分页
export function listPage(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.cj.inspectMgr.hazardCheckTableList);
  return $http.post<ICheckTempPageRes>(url, { data: { _cfg: { showTip: true }, ...data } });
}

// 新增 修改
export function saveUpdInspect(data: object) {
  const url = api.getUrl(api.type.hazard, api.cj.inspectMgr.hazardCheckTableSaveUpdate);
  return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: false }, ...data } });
}

// 删除
export function deleteInspect(param: object) {
  const url = api.getUrl(api.type.hazard, api.cj.inspectMgr.hazardCheckTableDelete, param);
  return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: true } } });
}

// 是否被引用
export function isEnableInspect(param: object) {
  const url = api.getUrl(api.type.hazard, api.cj.inspectMgr.hazardCheckTableIsEnable, param);
  return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: false }, ...param } });
}

/** 风险点列表 */
export function importRisk(param: object) {
  const url = api.getUrl(api.type.hazard, api.taskManagement.importRisk, param);
  return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: false }, ...param } });
}

export function riskIdentPage(param: object) {
  const url = api.getUrl(api.type.rmc, api.taskManagement.riskIdentPage, param);
  return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: false }, ...param } });
}

export function selectCheckItemList(param: object) {
  const url = api.getUrl(api.type.rmc, api.taskManagement.selectCheckItemList, param);
  return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: false }, ...param } });
}

export function selectHazardDetail(param: object) {
  const url = api.getUrl(api.type.rmc, api.taskManagement.selectHazardDetail, param);
  return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: false } } });
}

export function queryDictDataList() {
  const url = api.getUrl(api.type.rmc, api.taskManagement.queryDictDataList);
  return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: false } } });
}
