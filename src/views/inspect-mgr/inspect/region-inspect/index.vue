<template>
  <div class="com-g-row-a1">
    <RadioTab :tabList="tabList" :tab="curTab" @change="handleChange" />

    <div class="com-g-row-a1">
      <div class="com-table-filter" style="border-radius: 0 4px 0 0">
        <n-form :show-feedback="false" label-placement="left">
          <n-grid :x-gap="12" :y-gap="8" :cols="4">
            <n-grid-item>
              <n-form-item label="编制时间:">
                <!-- clearable -->
                <n-date-picker
                  style="width: 300px"
                  placeholder="请选择编制时间"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  v-model:value="dateTime"
                  type="daterange"
                  @change="dateChange"
                />
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item>
                <n-input
                  placeholder="请输入检查表名称模糊搜索"
                  maxlength="50"
                  v-model:value="filterForm.checkTableName"
                  :allow-input="(value: string) => !value.startsWith(' ') && !value.endsWith(' ')"
                  clearable
                >
                  <template #suffix>
                    <BsSearch style="cursor: pointer" />
                  </template>
                </n-input>
              </n-form-item>
            </n-grid-item>
            <n-grid-item span="2" class="text-right">
              <n-button type="primary" @click="doHandle(ACTION.ADD)">
                <IconAdd style="margin-right: 4px; font-size: 14px" />
                新建
              </n-button>
            </n-grid-item>
          </n-grid>
        </n-form>
      </div>
      <TableList
        style="padding-top: 0; border-radius: 0 0 4px 4px"
        class="com-table-container"
        ref="tableCompRef"
        @action="actionFn"
      />
    </div>

    <addModal
      :showModal="showModal"
      :formData="formData"
      :editType="editType"
      @closeModal="closeModal"
      @updateList="doHandle(ACTION.SEARCH)"
    />

    <!-- 导入model -->
    <n-modal
      v-model:show="showExport"
      title="导入"
      style="width: 400px"
      @negative-click="showExport = false"
      preset="card"
    >
      <div style="line-height: 80px">
        <div>从隐患库导入</div>
        <div>从风险库导入</div>
        <div>
          <n-upload
            abstract
            :action="actionUrl"
            accept=".doc,.docx,.xml,.xlsx"
            :data="{
              checkTableId: checkTableId,
            }"
            :is-error-state="isErrorState"
          >
            <n-button-group>
              <n-upload-trigger #="{ handleClick }" abstract>
                <n-button @click="handleClick">
                  <ByImport style="margin-right: 4px; font-size: 14px" />
                  模板导入
                </n-button>
              </n-upload-trigger>
            </n-button-group>
          </n-upload>
        </div>
      </div>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import RadioTab from '@/components/tab/ComRadioTabE.vue';
import TableList from './comp/table/Table.vue';
import addModal from './addModal.vue';

import { deleteInspect, saveUpdInspect, isEnableInspect } from './fetchData';
import { listPage } from './detail/fetchData';

import { ITabItem } from '@/components/tab/type';

import { FlFilledAdd as IconAdd, ByExport, BsSearch, ByImport } from '@kalimahapps/vue-icons';

import { $dialog } from '@/common/shareContext/useDialogCtx.ts';
import { ACTION } from '../../constant';
// import { deleteTemp } from './fetchData';
import { IActionData } from './type';
import { ICheckTempRow } from './type';
import { IObj } from '@/types';
import { trimObjNull } from '@/utils/obj.ts';
import dayjs from 'dayjs';
import { throttle } from 'lodash-es';

import { api } from '@/api';
import { useMessage } from 'naive-ui';
import { ref, reactive, onBeforeMount, onMounted, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { getDefaultDateRange } from '@/utils/getDefaultDateRange';

const defaultDateRange = getDefaultDateRange();
const message = useMessage();

const router = useRouter();
const route = useRoute();

// tabs
const tabList: Array<ITabItem & { comp: any }> = [
  { name: '1', label: '位置检查表', comp: '' },
  { name: '2', label: '设备检查表', comp: '' },
  { name: '3', label: '点位检查表', comp: '' },
];

const curTab = ref<string>((route.query?.checkTableType as string) || '1');
function handleChange(name: string) {
  curTab.value = name;
  resetSearch();
  tableCompRef.value?.getTableDataWrap({ checkTableType: name, ...filterForm.value });
  router.push({
    query: { checkTableType: name },
  });
}

const filterForm = ref(initForm());
function initForm() {
  return {
    checkTableName: null,
    startTime: defaultDateRange[0] + ' 00:00:00',
    endTime: defaultDateRange[1] + ' 23:59:59',
  };
}

const dateTime = ref<any[]>([
  dayjs(defaultDateRange[0] + ' 00:00:00').valueOf(),
  dayjs(defaultDateRange[1] + ' 23:59:59').valueOf(),
]);

const resetSearch = () => {
  // filterForm.value = initForm();
  filterForm.value.checkTableName = null;
  filterForm.value.startTime = defaultDateRange[0] + ' 00:00:00';
  filterForm.value.endTime = defaultDateRange[1] + ' 23:59:59';
  dateTime.value = [
    dayjs(defaultDateRange[0] + ' 00:00:00').valueOf(),
    dayjs(defaultDateRange[1] + ' 23:59:59').valueOf(),
  ];
};

// 编制时间
function dateChange(date: any) {
  console.log(date);
  if (date) {
    filterForm.value.startTime = dayjs(date[0]).format('YYYY-MM-DD 00:00:00');
    filterForm.value.endTime = dayjs(date[1]).format('YYYY-MM-DD 23:59:59');
  } else {
    filterForm.value.startTime = filterForm.value.endTime = '';
  }
  doHandle(ACTION.SEARCH);
}
const doHandle = throttle((action) => {
  // console.log(JSON.parse(JSON.stringify(filterForm.value)));
  actionFn({
    action: action,
    data: trimObjNull(filterForm.value),
  });
}, 1000);

const showModal = ref<boolean>(false);
const formData = ref<any>({});
const editType = ref<boolean>(false);

const currentAction = ref({ action: ACTION.ADD, data: {} });

async function actionFn(val: any) {
  // console.log(val);
  currentAction.value = val;
  // 新增
  if (val.action === ACTION.ADD) {
    editType.value = curTab.value == '1' ? true : false;
    return (showModal.value = true);
  }
  //修改
  if (val.action === ACTION.EDIT) {
    await handelIsEnable(val.data);
    if (curTab.value == '1') {
      //查询检查表内是否有内容，有不可修改单层双层
      await listPage({
        pageNo: 1,
        pageSize: 10,
        checkTableId: val.data.id,
      }).then((res: any) => {
        editType.value = res.data.length ? false : true;
      });
    }
    formData.value = JSON.parse(JSON.stringify(val.data));
    return (showModal.value = true);
  }
  // 查询
  if (val.action === ACTION.SEARCH) {
    return handleSearch({
      ...val.data,
      checkTableType: curTab.value,
    });
  }
  // 删除
  if (val.action === ACTION.DELETE) {
    await handelIsEnable(val.data);
    return handleDelete(val.data as ICheckTempRow);
  }
  // 导出
  if (val.action === ACTION.EXPORT) {
    return (exportIdList.value = val.data);
  }
  // 启用 停用
  if (val.action === ACTION.STATE) {
    await handelIsEnable(val.data);
    return handelUpdStatus(val.data);
  }
}

// 检查表是否被引用
function handelIsEnable(row: any) {
  return new Promise((resolve) => {
    resolve({});
    // isEnableInspect({ id: row.id }).then((res) => {
    //   if (!res.data) return resolve(res);
    //   $dialog.warning({
    //     title: '提示',
    //     content: `当前检查表已经被引用，不予许当前操作！`,
    //     positiveText: '确定',
    //     transformOrigin: 'center',
    //   });
    // });
  });
}

const tableCompRef = ref();
function handleSearch(data?: IObj<any>) {
  if (data) {
    tableCompRef.value?.getTableDataWrap(data);
  } else {
    tableCompRef.value?.getTableData();
  }
}
function closeModal() {
  formData.value = {};
  showModal.value = false;
}
// 删除
function handleDelete(data: any) {
  const d = $dialog.warning({
    title: '删除检查表',
    content: '确定删除吗？',
    positiveButtonProps: { type: 'primary', color: '#527cff' },
    positiveText: '确定',
    negativeText: '取消',
    transformOrigin: 'center',
    onPositiveClick: async () => {
      d.loading = true;
      return new Promise((resolve) => {
        deleteInspect({ id: data.id })
          .then((res) => {
            handleSearch();
            resolve(res);
          })
          .catch(() => {
            d.loading = false;
          });
      });
    },
  });
}

// 启用 停用
function handelUpdStatus(data: any) {
  $dialog.warning({
    title: '提示',
    content: `是否${data.checkStatus == '0' ? '停用' : '启用'}检查表" ${data.checkTableName} "？`,
    positiveText: '确定',
    negativeText: '取消',
    // positiveButtonProps: { type: 'primary' },
    positiveButtonProps: { type: 'primary', color: '#527cff' },
    transformOrigin: 'center',
    onPositiveClick: async () => {
      saveUpdInspect({ id: data.id, checkStatus: data.checkStatus != '0' ? 0 : 1 }).then(() => {
        message.success(data.checkStatus == '0' ? '停用成功' : '启用成功');
        handleSearch();
      });
    },
  });
}

// 导入
let showExport = ref<boolean>(false);

const actionUrl = api.getUrl(api.type.hazard + '/api', api.cj.inspectMgr.hazardCheckDeviceImport);
const checkTableId = '';

// (xhr: XMLHttpRequest) => boolean 判断请求是否为异常状态
function isErrorState(e: any) {
  // e xml
  let data = JSON.parse(e.response);
  if (data.code === 'success') {
    message.success('导入成功');
    showExport.value = false;
    doHandle(ACTION.SEARCH);
  } else {
    message.error(data.message);
  }
}

// 导出
const exportIdList = ref<number[]>([]);

function exportFun() {}

watch(filterForm.value, () => doHandle(ACTION.SEARCH));

onMounted(() => {
  doHandle(ACTION.SEARCH);
});
defineOptions({ name: 'checkTemplateComp' });
</script>
<style module lang="scss"></style>
