<template>
  <ComDrawerA
    :title="formData.id ? '编辑' : '新增'"
    :autoFocus="false"
    :footerPaddingBottom="25"
    :maskNeedClosable="false"
    :show="props.showModal"
    show-action
    @handleNegative="handleClose"
    class="!w-[430px]"
  >
    <!-- label-placement="left" -->
    <n-form
      style="padding: 24px"
      ref="formRef"
      :model="model"
      :rules="rules"
      label-width="auto"
      require-mark-placement="right-hanging"
      :style="{
        maxWidth: '820px',
      }"
    >
      <n-form-item label="检查表名称" path="checkTableName">
        <n-input v-model:value="model.checkTableName" :maxlength="50" placeholder="请输入检查表名称" />
      </n-form-item>
    </n-form>
    <template #footer>
      <div style="display: flex; justify-content: flex-end; padding: 0 24px">
        <n-button style="margin-right: 20px" type="info" @click="handleClose"> 取消 </n-button>
        <n-button type="primary" :disabled="submitLoading" :loading="submitLoading" @click="submit"> 保存 </n-button>
      </div>
    </template>
  </ComDrawerA>
</template>

<script setup lang="ts">
import ComDrawerA from '@/components/drawer/ComDrawerA.vue';
import { ref, defineProps, computed, watch, reactive } from 'vue';
import { saveUpdInspect } from './fetchData';
import { useMessage } from 'naive-ui';
import { useRoute } from 'vue-router';
import { useStore } from '@/store/index';

const message = useMessage();
const route = useRoute();

const emits = defineEmits(['closeModal', 'updateList']);

const props = defineProps({
  showModal: {
    type: Boolean,
    default: false,
  },
  formData: {
    type: Object,
    default: () => ({}),
  },
  // 是否可以修改
  editType: {
    type: Boolean,
    default: true,
  },
});
const model = ref<any>({
  checkTableName: '',
  checkAreaFormGrade: null,
  checkTableType: Number(route.query?.checkTableType) || 1,
});

const resetForm = () => {
  model.value = {
    checkTableName: '',
    checkAreaFormGrade: null,
    checkTableType: Number(route.query?.checkTableType) || 1,
  };
};
const showCheckAreaFormGrade = ref<boolean>(false);
watch(
  () => props.showModal,
  (newVal) => {
    if (newVal) {
      showCheckAreaFormGrade.value = props.editType;
      if (props.formData.id) {
        model.value = props.formData;
      } else {
        resetForm();
      }
    } else {
      resetForm();
      showCheckAreaFormGrade.value = false;
    }
  }
);

const rules = {
  checkTableName: {
    required: true,
    trigger: ['blur', 'input'],
    message: '请输入检查表名称',
  },
};

const formRef = ref<any>(null);

const submitLoading = ref<boolean>(false);

function submit(e: MouseEvent) {
  console.log(model.value, 'model');
  e.preventDefault();
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      submitLoading.value = true;
      let data = {
        ...model.value,
      };
      //分管单位ID
      if (!data.id) data['unitId'] = useStore().userInfo.unitId;

      saveUpdInspect(data)
        .then(() => {
          message.success(model.value.id ? '修改成功' : '新增成功');
          submitLoading.value = false;
          emits('updateList');
          handleClose();
        })
        .catch(() => {
          submitLoading.value = false;
        });
    }
  });
}

function handleClose() {
  emits('closeModal', false);
  emits('update:show' as any, false);
}

defineOptions({ name: 'RegionInspectAddModal' });
</script>
