<template>
  <div class="com-g-row-a1">
    <div>
      <ComBread :data="breadData" />
      <RadioTab :tabList="tabList" :tab="curTab" @change="handleChange" />
    </div>
    <TableList class="com-table-container" ref="tableCompRef" />
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, reactive, watch, ref } from 'vue';
import ComBread from '@/components/breadcrumb/ComBread.vue';
import RadioTab from '@/components/tab/ComRadioTabE.vue';

import { ITabItem } from '@/components/tab/type';
import { useRoute, useRouter } from 'vue-router';

import TableList from './table/Table.vue';

const router = useRouter();
const route = useRoute();

// tabs
const tabList: Array<ITabItem & { comp: any }> = [
  { name: '1', label: '位置检查表', comp: '' },
  { name: '2', label: '设备检查表', comp: '' },
  { name: '3', label: '点位检查表', comp: '' },
];

const curTab = ref<string>((route.query?.checkTableType as string) || '1');
function handleChange(name: string) {
  console.log(123);
  curTab.value = name;
  router.push({
    name: 'checkList',
    query: { checkTableType: name },
  });
}

const breadData = computed(() => [
  { name: '隐患治理系统' },
  {
    name: '检查表',
    clickable: true,
    routeRaw: {
      name: 'checkList',
      query: {
        checkTableType: '1',
      },
    },
  },
  {
    name: '位置检查表详情',
  },
]);

const tableCompRef = ref();
function handleSearch() {
  tableCompRef.value?.getTableData();
}

onMounted(() => {
  handleSearch();
});

defineOptions({ name: 'inspectRegionDetail' });
</script>

<style module lang="scss"></style>

<style module lang="scss">
.radio-tab {
  @apply bg-[#F4F9FF] h-[48px] border-b-[1px] border-[#EBEEF5] rounded-t-[4px];

  :global(.n-tabs) {
    @apply pl-[36px];
  }

  :global(.n-tabs-nav-scroll-content) {
    @apply h-[48px];
  }

  :global(.n-tab-pane) {
    display: none;
  }
}
</style>
