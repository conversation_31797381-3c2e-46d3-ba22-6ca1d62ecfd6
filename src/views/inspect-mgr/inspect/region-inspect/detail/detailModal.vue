<template>
  <n-modal v-model:show="showModal" style="width: 1200px" @negative-click="onNegativeClick" preset="card">
    <template #header>
      <n-flex justify="space-between">
        <div>检查详情</div>
        <div
          v-if="!editType && isEnable && isMyUnit(route.query.checkUnitId)"
          style="display: flex; justify-content: flex-end"
        >
          <n-button style="margin-right: 20px" type="primary" @click="handleEdit"> 编辑 </n-button>
        </div>
      </n-flex>
    </template>
    <template #default>
      <n-scrollbar :style="{ maxHeight: '600px' }">
        <n-form
          class="form"
          ref="formRef"
          :model="model"
          :rules="editType ? rules : ''"
          label-placement="left"
          label-width="auto"
          require-mark-placement="right-hanging"
          :style="{
            maxWidth: '1140px',
          }"
        >
          <n-form-item v-if="model.parentId == '0' || !editType" label="检查内容" path="checkContent">
            <!-- disabled -->
            <n-input v-if="editType" maxlength="50" v-model:value="model.checkContent" placeholder="请输入" />
            <div v-else>{{ model.checkContent || '--' }}</div>
          </n-form-item>
          <n-form-item v-if="model.parentId != '0'" label="检查详情" path="checkDetail">
            <n-input
              v-if="editType"
              maxlength="500"
              show-count
              v-model:value="model.checkDetail"
              placeholder="请输入检查详情"
              type="textarea"
              :autosize="{
                minRows: 3,
                maxRows: 5,
              }"
            />
            <div v-else>{{ model.checkDetail || '--' }}</div>
          </n-form-item>
          <n-form-item label="法规依据" path="legalBasis">
            <n-input
              v-if="editType"
              maxlength="500"
              show-count
              v-model:value="model.legalBasis"
              placeholder="请输入法规依据"
              type="textarea"
              :autosize="{
                minRows: 3,
                maxRows: 5,
              }"
            />
            <div v-else>{{ model.legalBasis || '--' }}</div>
          </n-form-item>
          <n-form-item label="合规要求" path="complianceRequirements">
            <n-input
              v-if="editType"
              maxlength="500"
              show-count
              v-model:value="model.complianceRequirements"
              placeholder="请输入合规要求"
              type="textarea"
              :autosize="{
                minRows: 3,
                maxRows: 5,
              }"
            />
            <div v-else>{{ model.complianceRequirements || '--' }}</div>
          </n-form-item>
          <n-form-item label="法律原文" path="legalText">
            <n-input
              v-if="editType"
              maxlength="500"
              show-count
              v-model:value="model.legalText"
              placeholder="请输入法律原文"
              type="textarea"
              :autosize="{
                minRows: 3,
                maxRows: 5,
              }"
            />
            <div v-else>{{ model.legalText || '--' }}</div>
          </n-form-item>
          <n-form-item label="法律责任" path="legalLiability">
            <n-input
              v-if="editType"
              maxlength="500"
              show-count
              v-model:value="model.legalLiability"
              placeholder="请输入法律责任"
              type="textarea"
              :autosize="{
                minRows: 3,
                maxRows: 5,
              }"
            />
            <div v-else>{{ model.legalLiability || '--' }}</div>
          </n-form-item>
          <n-form-item label="图例" path="textareaValue">
            <ImgUpload
              v-if="editType"
              :data="model.checkAreaFileList"
              multiple
              @update="handleUpdate"
              :size="15"
              :max="6"
            />
            <ImgUpload v-else mode="detail" :data="model.checkAreaFileList" />
          </n-form-item>

          <ComHeaderB title="常见隐患" style="margin-bottom: 20px" />

          <n-data-table scroll-x="820" class="com-table" :columns="editType ? columns2 : columns1" :data="data" />

          <n-button v-if="editType" style="margin: 20px 0; width: 100%" dashed @click="addd">
            <template #icon>
              <IconAdd />
            </template>
            新增一行
          </n-button>
        </n-form>
      </n-scrollbar>
    </template>

    <template #footer>
      <div v-if="editType" style="display: flex; justify-content: flex-end">
        <n-button style="margin-right: 20px" round type="info" @click="onNegativeClick"> 取消 </n-button>
        <n-button round type="primary" :disabled="submitLoading" :loading="submitLoading" @click="submit">
          确认
        </n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import ComHeaderB from '@/components/header/ComHeaderB.vue';

import { h, VNode, ref, defineProps, computed, watch } from 'vue';
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import {
  DataTableColumns,
  NButton,
  NInput,
  NSelect,
  NTreeSelect,
  NImageGroup,
  NImage,
  NU,
  NTreeSelectpload,
} from 'naive-ui';
import { saveUpdCheckArea } from './fetchData';
import { ImgUpload } from '@/components/upload';
import { IUploadRes } from '@/components/upload/type';
import { $dialog } from '@/common/shareContext/useDialogCtx.ts';
import { FlFilledAdd as IconAdd } from '@kalimahapps/vue-icons';
const { userInfo } = useStore();
import { useRoute } from 'vue-router';
const route = useRoute();
import { useMessage } from 'naive-ui';
import { getHazardGradeList } from '@/views/hazard-management/fetchData';
import { useStore } from '@/store';
import { isMyUnit } from '../../utils';
import { TreeNode } from '@/views/hidden-dangers-photo/type';
import { getDangerLibrary } from '@/views/hidden-dangers-photo/fetchData';

const message = useMessage();

const emits = defineEmits(['closeModal', 'updateList']);

const props = defineProps({
  showModal: {
    type: Boolean,
    default: false,
  },
  formData: {
    type: Object,
    default: () => ({}),
  },
  //type 1 详情 2编辑详情
  type: {
    type: Number,
    default: 1,
  },
  // 是否被引用，引用不可编辑
  isEnable: {
    type: Boolean,
    default: false,
  },
});

const showModal = computed({
  get: () => props.showModal,
  set: () => {
    emits('closeModal', false);
  },
});

// 是否可以编辑
const editType = ref<boolean>(false);
watch(
  () => props.showModal,
  (newVal) => {
    if (newVal && props.formData.id) {
      model.value = props.formData;
      data.value = props.formData.essentialFactorList || [];
      data.value.forEach((item) => {
        if (!item.checkAreaFileList) item.checkAreaFileList = [];
        item.correctFiles = item.checkAreaFileList.filter((i) => i.fileCategory == 1);
        item.errorFiles = item.checkAreaFileList.filter((i) => i.fileCategory == 2);
      });

      if (props.type == 2) {
        editType.value = true;
      } else {
        editType.value = false;
      }
    }
  }
);
const model = ref<any>({
  checkContent: '', //检查内容
  checkDetail: '', //检查详情
  legalBasis: '', //法规依据
  complianceRequirements: '', //合规要求
  legalText: '', //法律原文
  legalLiability: '', //法律责任
});

// 隐患分类列表
const hiddenDangersType = ref<any[]>([]);

function getLibrary() {
  let params = {
    unitId: userInfo.unitId,
    parentId: '0',
  };
  getDangerLibrary(params).then((res: any) => {
    hiddenDangersType.value = res.data;
  });
}
getLibrary();

const rules = ref({
  checkContent: {
    required: true,
    trigger: ['blur', 'input'],
    message: '请输入检查内容',
  },
  checkDetail: {
    required: true,
    trigger: ['blur', 'input'],
    message: '请输入检查详情',
  },
});

interface RowData {
  editType: boolean;
  essentialFactorDescribe: string;
  essentialFactorGrade: null;
  essentialFactorGradeId: null;
  essentialFactorClass: null;
  checkAreaFileList: any[];
  correctFiles: any[];
  errorFiles: any[];
}

const data = ref<RowData[]>([]);

function addd() {
  data.value.push({
    editType: true, //是否可以编辑
    essentialFactorDescribe: '',
    essentialFactorGrade: null,
    essentialFactorGradeId: null,
    essentialFactorClass: null,
    checkAreaFileList: [],
    correctFiles: [],
    errorFiles: [],
  });
}

const columns1 = [
  {
    title: '序号',
    key: 'index',
    width: 65,
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '隐患描述',
    key: 'essentialFactorDescribe',
    ellipsis: {
      tooltip: true,
    },
    // width: 200,
    render(row, index) {
      if (editType.value && row.editType) {
        return h(NInput, {
          maxlength: 50,
          value: row.essentialFactorDescribe,
          onUpdateValue(v) {
            data.value[index].essentialFactorDescribe = v;
          },
          style: 'width:300px',
        });
      } else {
        return row.essentialFactorDescribe;
      }
    },
  },
  {
    title: '隐患分类',
    key: 'essentialFactorClass',
    ellipsis: {
      tooltip: true,
    },
    // width: 80,
    // 15669 【测试环境web】位置检查表，检查详情，增加常见隐患，隐患分类可以随意输入，  需要下拉选择，从隐患分类的总组织树中选择
    render(row: any, index: number) {
      if (editType.value && row.editType) {
        return h(NTreeSelect, {
          style: 'width:160px',
          clearable: true,
          placeholder: '请选择隐患分类',
          options: hiddenDangersType.value,
          value: row.essentialFactorClass,
          keyField: 'className',
          labelField: 'className',
          valueFiled: 'className',
          childrenFiled: 'children',
          onUpdateValue(v) {
            data.value[index].essentialFactorClass = v;
          },
        });
      } else {
        return row.essentialFactorClass;
      }
      // render(row, index) {
      //   if (editType.value && row.editType) {
      //     return h(NInput, {
      //       maxlength: 50,
      //       value: row.essentialFactorClass,
      //       onUpdateValue(v) {
      //         data.value[index].essentialFactorClass = v;
      //       },
      //     });
      //   } else {
      //     return row.essentialFactorClass;
      //   }
    },
  },
  {
    title: '隐患等级',
    key: 'essentialFactorGrade',
    // width: 80,
    render(row, index) {
      if (editType.value && row.editType) {
        // return h(NInput, {
        //   maxlength: 50,
        //   value: row.essentialFactorGrade,
        //   onUpdateValue(v) {
        //     data.value[index].essentialFactorGrade = v;
        //   },
        // });

        return h(NSelect, {
          value: row.essentialFactorGradeId,
          placeholder: '请选择隐患等级',
          options: gradeList.value,
          labelField: 'gradeName',
          valueField: 'id',
          onUpdateValue(rowKey, rowData) {
            console.log(rowKey, rowData, 'rowKey, rowData');
            data.value[index].essentialFactorGradeId = rowKey;
            data.value[index].essentialFactorGrade = rowData['gradeName'];
          },
        });
      } else {
        return row.essentialFactorGrade;
      }
    },
  },
  {
    title: '正确图例',
    key: 'correctFiles',
    width: 200,
    render(row, index) {
      const correctFiles = row.correctFiles;
      if (editType.value && row.editType) {
        return h(ImgUpload, {
          data: correctFiles,
          max: 3,
          height: 30,
          type: 'image-card',
          showBtn: false,
          onUpdate: (res: IUploadRes[]) => {
            console.log(res, data);
            res.forEach((item) => (item.fileCategory = 1));
            data.value[index].correctFiles = res || [];
          },
        });
      } else {
        return h(ImgUpload, { data: correctFiles, width: 30, mode: 'detail' });
      }
    },
  },
  {
    title: '错误图例',
    key: 'errorFiles',
    width: 200,
    render(row, index) {
      const errorFiles = row.errorFiles;
      if (editType.value && row.editType) {
        return h(ImgUpload, {
          data: errorFiles,
          max: 3,
          height: 30,
          showBtn: false,
          type: 'image-card',
          onUpdate: (res: IUploadRes[]) => {
            console.log(res, data);
            res.forEach((item) => (item.fileCategory = 2));
            data.value[index].errorFiles = res || [];
          },
        });
      } else {
        return h(ImgUpload, { data: errorFiles, width: 30, mode: 'detail' });
      }
    },
  },
];

const gradeList = ref([]);

const getGradeList = () => {
  getHazardGradeList({ delFlag: 0, unitId: userInfo.topUnitId }).then((res: any) => {
    console.log(res, 'res');
    gradeList.value = res.data;
  });
};

getGradeList();

const columns2 = [
  ...columns1,
  {
    title: '操作',
    key: 'actions',
    align: 'center',
    width: 120,
    fixed: 'right',
    render(row, index) {
      return getActionBtn(index);
    },
  },
];

function handleEdit() {
  editType.value = true;
}

function getActionBtn(index: number) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-action-button',
          onClick: () => {
            data.value[index]['editType'] = true;
          },
        },
        { default: () => '编辑' }
      ),
    ],
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-action-button',
          onClick: () => {
            $dialog.error({
              title: '删除隐患',
              content: '确定删除该隐患吗？',
              positiveText: '确定',
              negativeText: '取消',
              transformOrigin: 'center',
              onPositiveClick: () => {
                data.value.splice(index, 1);
              },
            });
          },
        },
        { default: () => '删除' }
      ),
    ],
  ];
  return useActionDivider(acList);
}

// 上传图片
const imgData = ref([]);

// 上传回调
function handleUpdate(res: IUploadRes[], data: any) {
  console.log(res, data);
  model.value.checkAreaFileList = res;
}

const formRef = ref<any>(null);
const submitLoading = ref<boolean>(false);

function submit(e: MouseEvent) {
  console.log('submit');
  e.preventDefault();
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      data.value.forEach((item) => {
        item.checkAreaFileList = [...item.correctFiles, ...item.errorFiles];
      });
      submitLoading.value = true;
      let dataForm = {
        ...model.value,
        essentialFactorList: data.value || [],
      };
      saveUpdCheckArea(
        {
          checkAreaFormGrade: route.query?.checkAreaFormGrade,
        },
        dataForm
      )
        .then(() => {
          message.success('修改成功');
          submitLoading.value = false;
          emits('updateList');
          onNegativeClick();
        })
        .catch(() => {
          message.error('操作失败，请稍后重试');
          submitLoading.value = false;
        });
    }
  });
}

function onNegativeClick() {
  editType.value = false;
  emits('closeModal', false);
}
</script>

<style lang="scss" scoped>
.form :deep(.n-form-item .n-form-item-blank) {
  align-items: unset;
  padding-top: 0.4em;
}

.com-table :deep(.n-upload-trigger.n-upload-trigger--image-card) {
  height: 34px;
}

.com-table :deep(.n-upload-file-list .n-upload-file.n-upload-file--image-card-type) {
  height: 34px;
}
</style>
