import { DataTableColumn, NInput } from 'naive-ui';
import { h } from 'vue';

export const cols: DataTableColumn[] = [
  // {
  //   title: '#',
  //   key: 'index',
  //   width: 65,
  //   align: 'center',
  //   render: (_: any, index: number) => {
  //     return index + 1;
  //   },
  // },
  {
    title: '#',
    key: 'serialNumber',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
    width: 80,
  },
  {
    title: '检查内容',
    key: 'checkContent',
    align: 'left',
    // ellipsis: {
    //   tooltip: true,
    // },
    render: (row: any, index: any) => {
      if (!row.id) {
        return h(NInput, {
          value: row.parentId == '0' ? row.checkContent : row.checkDetail,
          type: 'textarea',
          autosize: {
            minRows: 1,
            maxRows: 3,
          },
          maxlength: 500,
          showCount: true,
          onUpdateValue(v) {
            if (row.parentId == '0') {
              row.checkContent = v;
            } else {
              row.checkDetail = v;
            }
          },
          placeholder: '请输入检查内容...',
          style: 'width:700px',
        });
      } else {
        return row.parentId == '0' ? row.checkContent : row.checkDetail;
      }
    },
  },
  {
    title: '常见隐患',
    key: 'aggregate',
    align: 'left',
    // ellipsis: {
    //   tooltip: true,
    // },
    width: 100,
    render: (row: any) => {
      console.log(row.parentId, '======================row.parentId');
      return row.parentId == '0'
        ? row.aggregate
          ? '总计：' + row.aggregate
          : '总计：' + 0
        : row.aggregate
          ? row.aggregate
          : 0;
    },
  },
];
