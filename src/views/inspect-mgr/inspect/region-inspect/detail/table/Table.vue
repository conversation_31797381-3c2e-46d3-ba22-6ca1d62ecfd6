<template>
  <div class="warp">
    <ComHeaderB title="检查内容" style="margin-bottom: 20px" />
    <div class="" style="display: flex; justify-content: flex-end; margin-bottom: 20px">
      <div v-if="!editType && isEnable && isMyUnit(route.query.checkUnitId)">
        <n-button type="primary" @click="handelEdit">
          <AnOutlinedEdit style="margin-right: 4px" />
          编辑
        </n-button>
        <n-button @click="showExport = true" style="margin-left: 20px">
          <ByImport style="margin-right: 4px; font-size: 14px" />
          导入
        </n-button>
      </div>
      <div v-if="editType" style="display: flex; justify-content: flex-end">
        <n-button style="margin-right: 20px" type="primary" @click="handelSumbit"> 保存 </n-button>
        <n-button type="info" @click="handelCancel"> 取消 </n-button>
      </div>
    </div>

    <n-button v-if="editType" type="primary" @click="handelTableAdd({})" style="margin-bottom: 20px">
      <IconAdd style="margin-right: 4px" />
      新增父级
    </n-button>
    <!-- class="h-full" -->
    <n-data-table
      style="height: 560px"
      striped
      remote
      :columns="columns"
      :data="tableData"
      :bordered="false"
      :flex-height="true"
      :loading="loading"
      :pagination="false"
      :row-key="(row: any) => row.id"
      :row-class-name="getRowClassName"
      :render-cell="useEmptyCell"
      :row-props="rowProps"
      default-expand-all
    />

    <n-dropdown
      class="dropdown-region"
      placement="bottom-start"
      trigger="manual"
      size="small"
      :x="x"
      :y="y"
      :options="dropdownOptions"
      :show="showDropdown"
      :on-clickoutside="onClickoutside"
      @select="handleSelect"
    />

    <n-modal
      v-model:show="showExport"
      title="导入"
      style="width: 400px"
      @negative-click="showExport = false"
      preset="card"
    >
      <div style="line-height: 80px; cursor: pointer">
        <div @click="() => riskPointSelectListRef.open(route.query?.id)" v-if="checkAreaFormGrade == '2'">
          从风险分级管控导入
        </div>
        <riskPointSelectList ref="riskPointSelectListRef" @close="closeRiskPointSelectList" />
        <div @click="showHiddenImport = true">从隐患库导入</div>
        <hiddenDangerImport
          :showModal="showHiddenImport"
          @closeModal="showHiddenImport = false"
          @importSuccess="
            showHiddenImport = false;
            showExport = false;
            getTableData();
          "
        />
        <div @click="showTemplateImport = true">模板导入</div>
        <templateImport
          :showModal="showTemplateImport"
          @closeModal="showTemplateImport = false"
          @importSuccess="importSuccess"
        />
      </div>
    </n-modal>

    <detailModal
      :showModal="showModal"
      :formData="formData"
      :isEnable="isEnable"
      :type="showModalType"
      @closeModal="closeModal"
      @updateList="getTableData"
    />
  </div>
</template>

<script setup lang="ts">
import { h, ref, VNode, toRaw, reactive, nextTick } from 'vue';
import { DataTableColumns, NButton, DropdownOption } from 'naive-ui';
import { cols } from './columns';
import { useRouter, useRoute } from 'vue-router';
import { listPage, checkedAreaDetail, saveUpdCheckAreaBatch, deleteCheckAreaBatch } from '../fetchData';
import { isEnableInspect } from '../../fetchData';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { $dialog } from '@/common/shareContext/useDialogCtx.ts';
import { AnOutlinedEdit, ByImport, CaAddLarge, AdCopy, FlDelete } from '@kalimahapps/vue-icons';
import templateImport from './templateImport.vue';
import hiddenDangerImport from './hiddenDangerImport.vue';
import ComHeaderB from '@/components/header/ComHeaderB.vue';
import riskPointSelectList from './riskPointSelectList.vue';
// import { findLastIndex } from 'lodash-es';

import type { Component } from 'vue';
import { NIcon, useMessage } from 'naive-ui';
const message = useMessage();
import { api } from '@/api';

import { saveUpdInspect } from '@/views/inspect-mgr/inspect/region-inspect/fetchData';

function renderIcon(icon: Component) {
  return () => {
    return h(NIcon, null, {
      default: () => h(icon),
    });
  };
}
import detailModal from '../detailModal.vue';
import { RowData } from 'naive-ui/es/data-table/src/interface';
import { isMyUnit } from '../../../utils';

const router = useRouter();
const route = useRoute();
const riskPointSelectListRef = ref();

const emits = defineEmits(['action']);

const [loading, search] = useAutoLoading(false);
const { pagination, updateTotal } = useNaivePagination(getTableData);

// 级别 1单级 2多级
const checkAreaFormGrade = ref(route.query?.checkAreaFormGrade);

const columns = ref<DataTableColumns>([]);
const tableData = ref<any[]>([]);
function setColumns() {
  columns.value.push(...cols);
  // 添加操作栏 action
  columns.value.push({
    title: '操作',
    key: 'actions',
    align: 'center',
    width: 120,
    render(row, index) {
      // 多级
      let actionTwo = checkAreaFormGrade.value == '2' && row.parentId != '0' ? getActionBtn(row, index) : '';
      // return row.type != 1 && !editType.value ? getActionBtn(row) : '';
      return !editType.value ? (checkAreaFormGrade.value == '1' ? getActionBtn(row, index) : actionTwo) : '';
    },
  });
}
setColumns();

// 新增 修改
const showModal = ref<boolean>(false);
const formData = ref<any>({});
// showModalType 1 详情 2 编辑详情
const showModalType = ref<number>(1);

function closeModal() {
  showModal.value = false;
}

function actionFn(type: string, data: any, index: number) {
  console.log(data, 'val');
  if (type === 'DETAIL') {
    showModalType.value = 1;
    checkedAreaDetail({ id: data.id }).then((res) => {
      // let arr = tableData.value.slice(0, index).findIndex((item: any) => item.parentId == '0');
      let arr = tableData.value.slice(0, index);
      let idx = findLastIndex(arr);
      console.log(idx, 'idx');
      formData.value = res.data;
      if (checkAreaFormGrade.value == '2') formData.value.checkContent = tableData.value[idx]['checkContent'];
      return (showModal.value = true);
    });
  }
  if (type === 'EDIT') {
    showModalType.value = 2;
    formData.value = JSON.parse(JSON.stringify(data));
    return (showModal.value = true);
  }
}

function getActionBtn(row: any, index: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-action-button',
          onClick: () => actionFn('DETAIL', toRaw(row), index),
        },
        { default: () => '详情' }
      ),
    ],
  ];
  return useActionDivider(acList);
}

function getActionBtnTwo(row: any, index: number) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          // text: true,
          class: 'com-action-button',
          onClick: (e) => {
            e.stopPropagation();
            actionFn('EDIT', toRaw(row), index);
          },
        },
        { default: () => '编辑详情' }
      ),
    ],
  ];
  return useActionDivider(acList);
}

interface TreeNode {
  checkContent: string;
  parentId: string;
  serialNumber: string;
  children?: TreeNode[];
}

function flattenTree(nodes: TreeNode[]) {
  let result = ref<TreeNode[]>([]);

  function flatten(node: TreeNode) {
    // 将当前节点添加到结果数组中
    result.value.push({ ...node, children: undefined }); // 移除children属性

    // 如果节点有children，则递归处理每个子节点
    if (node.children && node.children.length > 0) {
      node.children.forEach((child) => flatten(child));
    }
  }

  // 对输入数组的每个节点进行展开
  nodes.forEach((node) => flatten(node));

  return result.value;
}

const closeRiskPointSelectList = () => {
  showExport.value = false;
  getTableData();
};
// 排序列号
function sortData(list: TreeNode[]) {
  let result = ref<TreeNode[]>([]);
  let index = 1;
  let idx = 1;
  list.forEach((item) => {
    if (item.parentId == '0') idx = 1;
    result.value.push({
      ...item,
      serialNumber: item.parentId == '0' ? `${index++}` : index - 1 + '.' + idx++,
    });
  });
  return result.value;
}
const isEnable = ref<boolean>(false);
function getTableData() {
  isEnableInspect({ id: route.query?.id }).then((res: any) => {
    isEnable.value = !res.data;
  });
  const params = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    checkTableId: route.query?.id,
  };
  search(listPage(params)).then((res: any) => {
    // console.log(sortData(flattenTree(res.data)), '123');
    tableData.value = sortData(flattenTree(res.data)) || [];
    // updateTotal(res.data.total || 0);
  });
}

// 指定行上的颜色
function getRowClassName(row: any) {
  // console.log(row);
  // 一级
  if (rowObject.value.serialNumber == row.serialNumber && row.parentId == '0') {
    return 'inspectRegionDetail-active-background-color1';
  }
  // 二级
  if (rowObject.value.serialNumber == row.serialNumber) {
    return 'inspectRegionDetail-active-background-color2';
  }
  if (row.parentId == '0') {
    return 'inspectRegionDetail-background-color';
  }
}

const dropdownOptions: DropdownOption[] = [
  {
    // label: '',
    key: 'add',
    icon: renderIcon(CaAddLarge),
  },
  {
    // label: '复制',
    key: 'copy',
    icon: renderIcon(AdCopy),
  },
  {
    // label: () => h('span', { style: { color: 'red' } }, '删除'),
    key: 'delete',
    icon: renderIcon(FlDelete),
  },
];

const showDropdown = ref(false);
const x = ref(0);
const y = ref(0);

function onClickoutside() {
  showDropdown.value = false;
}

const actionHeight = ref<number>(0);

const rowIndex = ref<number>(0);
const rowObject = ref<any>({});
const rowProps = (row: any, idx: number) => {
  return {
    style: 'cursor: pointer;',
    onClick: (e: MouseEvent) => {
      if (!editType.value) return;
      // window.srcElement = e.srcElement;
      // console.log(row);
      rowIndex.value = idx;
      rowObject.value = row;
      // message.info(JSON.stringify(row, null, 2));
      e.preventDefault();
      showDropdown.value = false;
      nextTick().then(() => {
        showDropdown.value = true;
        // x.value = e.clientX;
        x.value = 234;
        y.value = e.clientY - 30;
      });
    },
  };
};

const rowData = reactive({
  // 新增标识
  type: 'add',
  checkTableId: route.query?.id,
  parentId: '0',
  checkContent: '',
  checkDetail: '',
});

function getNextSerialNumber(currentSerialNumber: string) {
  const parts = (currentSerialNumber + '').split('.');
  if (parts.length === 1) return `${parts}.1`;
  if (parts.length !== 2) {
    throw new Error('Invalid serial number format');
  }

  const major = parseInt(parts[0], 10);
  const minor = parseInt(parts[1], 10);

  if (minor < 999) {
    // 假设次版本号不超过999
    return `${major}.${minor + 1}`;
  } else {
    return `${major + 1}.001`; // 重置次版本号为"001"，保持三位数
  }
}
// 获取插入位置的索引
function getInsertPosition(index: number) {
  console.log(tableData.value.slice(index + 1, tableData.value.length), 'arr');
  // 从当前节点寻找下级父节点位置，在下级父节点前插入
  let idx = tableData.value.slice(index + 1, tableData.value.length).findIndex((item) => item.parentId == '0');

  if (idx === -1) {
    return tableData.value.length;
  } else {
    console.log(index + idx, idx, 'getInsertPosition');
    return index + idx + 1;
  }
}

function handleSelect(key: string | number) {
  // console.log(key, 'key');
  // 新增
  if (key === 'add') {
    // getInsertPosition(rowIndex.value);
    // console.log(tableData.value[rowIndex.value]['serialNumber']);
    // 单层
    if (checkAreaFormGrade.value == '1') return handelTableAdd();
    let index = getInsertPosition(rowIndex.value);
    tableData.value.splice(index, 0, {
      ...rowData,
      parentId: rowObject.value.parentId === '0' ? rowObject.value.id || '' : rowObject.value.parentId,
      serialNumber: getNextSerialNumber(tableData.value[index - 1]['serialNumber']),
    });
  }
  if (key === 'copy') {
    let index = getInsertPosition(rowIndex.value);

    let data = {
      ...rowObject.value,
      id: '',
      type: 'add',
      aggregate: 0,
    };
    // 单层
    if (checkAreaFormGrade.value == '1') return handelTableAdd(data);
    tableData.value.splice(
      index,
      0,
      JSON.parse(
        JSON.stringify({
          ...data,
          checkDetail: rowObject.value.parentId === '0' ? rowObject.value.checkContent : rowObject.value.checkDetail,
          parentId: rowObject.value.parentId === '0' ? rowObject.value.id || '' : rowObject.value.parentId,
          serialNumber: getNextSerialNumber(tableData.value[index - 1]['serialNumber']),
        })
      )
    );
    console.log(tableData.value, 'tableData');
  }
  if (key === 'delete') {
    let message =
      rowObject.value.parentId == '0'
        ? `您确认要删除该检查内容吗？删除后该检查内容下的检查详情也同步删除？`
        : `是否删除检查内容为："${rowObject.value.checkDetail}" 的数据吗？`;
    $dialog.warning({
      title: '提示',
      content: message,
      positiveText: '确定',
      negativeText: '取消',
      transformOrigin: 'center',
      positiveButtonProps: { type: 'primary', color: '#527cff' },
      onPositiveClick: async () => {
        // 当前位置到下个父节点位置
        let index = tableData.value
          .slice(rowIndex.value + 1, tableData.value.length)
          .findIndex((item) => item.parentId == '0');

        console.log(index, 'index');
        if (rowObject.value.id) deleteIdList.value.push(rowObject.value);
        // 是否父级
        let isLevel = rowObject.value.parentId == '0' ? (index != -1 ? index + 1 : tableData.value.length - index) : 1;
        tableData.value.splice(rowIndex.value, isLevel);
      },
    });
  }
  showDropdown.value = false;
}

function findLastIndex(arr: any) {
  return arr.findLastIndex((item: { parentId: string }) => item.parentId === '0');
}
// 添加父级
function handelTableAdd(data = {}) {
  tableData.value.push({
    checkContent: '',
    ...data,
    type: 'add',
    checkTableId: route.query?.id,
    parentId: '0',
    serialNumber: tableData.value.length
      ? Number(tableData.value[findLastIndex(tableData.value)]['serialNumber']) + 1
      : 1,
  });
  console.log(tableData.value, 'data');
}

const editType = ref<boolean>(false);

function handelEdit() {
  editType.value = true;
}

const deleteIdList = ref<RowData[]>([]);

function handelCancel() {
  let addList = tableData.value?.filter((item) => item.type == 'add');
  console.log(deleteIdList.value, addList);
  if (!deleteIdList.value.length && !addList.length) {
    rowIndex.value = 0;
    rowObject.value = {};
    showModalType.value = 1;
    return (editType.value = false);
  }

  $dialog.warning({
    title: '提示',
    content: `有修改未保存，确认离开吗？`,
    positiveText: '确定',
    negativeText: '取消',
    positiveButtonProps: { type: 'primary', color: '#527cff' },
    transformOrigin: 'center',
    onPositiveClick: async () => {
      showModalType.value = 1;
      editType.value = false;
      deleteIdList.value = [];
      getTableData();
    },
  });
}

interface TreeItem {
  type: string;
  checkTableId: string;
  parentId: string;
  checkContent: string;
  checkDetail: string;
  serialNumber: string;
  id?: string;
  children?: TreeItem[]; // 孩子们是可选的，因为不是所有项都有子项
}

function buildTreeSimplified(items: TreeItem[]): TreeItem[] {
  const result: TreeItem[] = [];
  const topLevelItems: { [key: string]: TreeItem } = {}; // 用于存储顶级项及其子项

  items.forEach((item) => {
    // 如果parentId为"0"，则它是顶级项
    if (item.parentId === '0' || item.parentId) {
      topLevelItems[item.serialNumber] = { ...item, children: [] };
      result.push(topLevelItems[item.serialNumber]);
    }

    // 尝试将当前项添加到其父项的children中
    const parts = (item.serialNumber + '').split('.');
    if (parts.length > 1 && topLevelItems[parts[0]]) {
      // 注意：这里我们不更新parentId，因为parentId应该反映原始数据
      topLevelItems[parts[0]].children?.push(item);
    }
  });

  // 注意：我们不需要过滤 result，因为顶级项已经正确地被添加到了 result 中
  return result;
}

function handelSumbit() {
  // 新增
  let addList = tableData.value?.filter((item) => item.type == 'add');

  if (!deleteIdList.value.length && !addList.length) return message.warning('当前数据未被修改');

  if (!addList?.every((item) => (item.parentId == '0' ? item.checkContent : item.checkDetail)))
    return message.warning('数据输入不完整，请输入完整后再进行保存');

  // console.log(buildTreeSimplified(tableData.value?.filter((item) => item.type == 'add')), 'add');
  const d = $dialog.warning({
    title: '提示',
    content: `是否保存当前已修改的数据？`,
    positiveButtonProps: { type: 'primary', color: '#527cff' },
    positiveText: '确定',
    transformOrigin: 'center',
    onPositiveClick: () => {
      d.loading = true;
      return new Promise(async (resolve) => {
        try {
          console.log(checkAreaFormGrade.value);
          // 修改主表级别类型
          if (route.query?.checkAreaFormGrade == '0') {
            await saveUpdInspect({
              id: route.query?.id,
              checkAreaFormGrade: checkAreaFormGrade.value,
            });
          }
          // 删除
          if (deleteIdList.value.length) {
            let arr = ref<any>([]);
            deleteIdList.value.map((item) => {
              arr.value.push({
                id: item.id,
                parentId: item.parentId,
              });
            });
            await deleteCheckAreaBatch(
              {
                checkAreaFormGrade: checkAreaFormGrade.value,
              },
              { formList: arr.value }
            );
          }

          if (addList.length) {
            interface Thing {
              serialNumber: string;
              type?: string;
              parentId: string;
              children?: Thing[];
            }

            console.log(buildTreeSimplified(addList));
            let arr = buildTreeSimplified(addList).map((item: Thing) => {
              if (item.parentId != '0') {
                item.serialNumber = item.serialNumber.split('.')[1];
              }
              delete item.type;
              item.children?.map((it) => {
                delete it.type;
                it.serialNumber = it.serialNumber.split('.')[1];
                return it;
              });
              return item;
              // arr.value.push({
              //   checkContent: item.checkContent,
              //   checkDetail: item.checkDetail,
              //   checkTableId: item.checkTableId,
              //   parentId: item.parentId,
              //   serialNumber: item.parentId != '0' ? item.serialNumber.split('.')[1] : item.serialNumber,
              //   children: item.children,
              // });
            });
            await saveUpdCheckAreaBatch(
              {
                checkAreaFormGrade: checkAreaFormGrade.value,
              },
              { formList: arr }
            );
          }

          message.success('操作成功');

          if (route.query?.checkAreaFormGrade == '0') {
            setTimeout(() => {
              router.push({
                name: 'inspectRegionDetail',
                query: {
                  id: route.query?.id,
                  checkAreaFormGrade: checkAreaFormGrade.value,
                },
              });
            }, 100);
          }

          rowIndex.value = 0;
          rowObject.value = {};
          editType.value = false;
          deleteIdList.value = [];
          getTableData();
          resolve({});
        } catch (error) {
          d.loading = false;
        }
      });
    },
  });
}

// 导入
let showExport = ref<boolean>(false);
// 隐患库导入
let showHiddenImport = ref<boolean>(false);
// 模板
let showTemplateImport = ref<boolean>(false);

const actionUrl = api.getUrl(api.type.hazard, api.cj.inspectMgr.hazardCheckAreaImport);
const checkTableId = route.query?.id;

// (xhr: XMLHttpRequest) => boolean 判断请求是否为异常状态
function isErrorState(e: any) {
  // e xml
  let data = JSON.parse(e.response);
  if (data.code === 'success') {
    message.success('导入成功');
    showExport.value = false;
    getTableData();
  } else {
    message.error(data.message);
  }
}

async function importSuccess(type: string) {
  if (checkAreaFormGrade.value == '0') {
    await saveUpdInspect({
      id: route.query?.id,
      checkAreaFormGrade: checkAreaFormGrade.value,
    });
  }
  if (route.query?.checkAreaFormGrade == '0') {
    setTimeout(() => {
      router.push({
        name: 'inspectRegionDetail',
        query: {
          id: route.query?.id,
          checkAreaFormGrade: type,
        },
      });
    }, 100);
  }

  showTemplateImport.value = false;
  showExport.value = false;
  getTableData();
}

defineOptions({ name: 'checkTampTableComp' });
defineExpose({ getTableData });
</script>

<style scoped>
::v-deep .n-data-table-thead {
  background-color: rgb(123, 188, 253) !important;
}

::v-deep .n-data-table-th {
  background-color: transparent !important;
}
</style>

<style lang="scss">
.inspectRegionDetail-background-color .n-data-table-td {
  background-color: rgb(199, 226, 254) !important;
}

.inspectRegionDetail-active-background-color1 .n-data-table-td {
  background-color: rgb(123, 188, 253) !important;
}

.inspectRegionDetail-active-background-color2 .n-data-table-td {
  background-color: rgb(248, 255, 212) !important;
}

.n-data-table .n-data-table-tr:not(.n-data-table-tr--summary):hover {
  background-color: rgb(123, 188, 253) !important;
}

.dropdown-region .n-dropdown-option .n-dropdown-option-body .n-dropdown-option-body__suffix {
  padding: 0;
  min-width: 0;
}
</style>
