<template>
  <n-modal
    v-model:show="showModal"
    title="隐患库导入"
    style="width: 800px"
    @negative-click="onNegativeClick"
    preset="card"
  >
    <div class="flex justify-between mt-[20px]">
      <n-button :disabled="!essentialFactorId" :loading="submitLoading" type="primary" @click="handleSubmit">
        <ByImport style="font-size: 14px; margin-right: 4px" />
        导入
      </n-button>
    </div>
    <div>
      <n-data-table
        class="com-table"
        style="height: 400px"
        remote
        striped
        :columns="columns"
        :data="tableData"
        :bordered="false"
        :flex-height="true"
        :loading="loading"
        :pagination="pagination"
        :row-key="(row: any) => row.id"
        @update:checked-row-keys="handleChecked"
      />
    </div>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, defineProps, computed, watch, reactive } from 'vue';
import { ByImport } from '@kalimahapps/vue-icons';
import { useMessage, DataTableColumns } from 'naive-ui';
import type { DataTableRowKey } from 'naive-ui';
import { factorList } from '../fetchData';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { checkAreaEssentImport } from '../fetchData';
import { useRoute } from 'vue-router';
import { useStore } from '@/store';
const route = useRoute();
const { userInfo } = useStore();

const [loading, search] = useAutoLoading(true);
const { pagination, updateTotal } = useNaivePagination(getTableData);

const columns = ref<DataTableColumns>([
  {
    type: 'selection',
    multiple: false,
  },
  // {
  //   title: '序号',
  //   key: 'index',
  //   width: 65,
  //   align: 'center',
  //   render: (_: any, index: number) => {
  //     return index + 1;
  //   },
  // },
  {
    title: '隐患库名称',
    key: 'elementName',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '编制时间',
    key: 'createTime',
    align: 'center',
  },
]);

const tableData = ref<any[]>([]);

const message = useMessage();

const emits = defineEmits(['closeModal', 'importSuccess']);

const props = defineProps({
  showModal: {
    type: Boolean,
    default: false,
  },
  formData: {
    type: Object,
    default: () => ({}),
  },
});

const showModal = computed({
  get: () => props.showModal,
  set: () => {
    emits('closeModal', false);
  },
});

watch(
  () => props.showModal,
  (newVal) => {
    if (newVal) {
      getTableData();
    }
  }
);

const checkTableId = route.query?.id;
// 隐患库id
const essentialFactorId = ref<string | number>('');

function handleChecked(rowKeys: DataTableRowKey[]) {
  essentialFactorId.value = rowKeys[0];
  // console.log(rowKeys);
}
function getTableData() {
  const params = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    unitId: userInfo.unitId,
    status: '0', //未禁用
  };

  search(factorList(params)).then((res: any) => {
    tableData.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
}

const submitLoading = ref<boolean>(false);

function handleSubmit() {
  submitLoading.value = true;
  checkAreaEssentImport({
    checkTableId: checkTableId,
    essentialFactorId: essentialFactorId.value,
  })
    .then(() => {
      message.success('导入成功');
      emits('importSuccess');
      submitLoading.value = false;
    })
    .catch((error) => {
      console.log(error, 'error');
      submitLoading.value = false;
    });
}

function onNegativeClick() {
  emits('closeModal', false);
}
</script>
