<template>
  <n-modal
    v-model:show="showModal"
    title="模板导入"
    style="width: 600px"
    @negative-click="onNegativeClick"
    preset="card"
  >
    <!-- <n-space>
      <n-spin :show="showLoading">
        <n-radio :checked="checkedValue === '2'" value="2" name="basic-demo" @change="handleRadioChange">
          双层检查表
        </n-radio>
        <n-radio :checked="checkedValue === '1'" value="1" name="basic-demo" @change="handleRadioChange">
          单级检查表
        </n-radio>

        <div style="margin-top: 20px">
          <n-upload
            abstract
            :action="actionUrl"
            accept=".doc,.docx,.xml,.xlsx"
            :data="{
              checkTableId: checkTableId,
              checkAreaFormGrade: checkedValue,
            }"
            :on-before-upload="onBeforeUpload"
            :is-error-state="isErrorState"
          >
            <n-button-group>
              <n-upload-trigger #="{ handleClick }" abstract>
                <n-button @click="handleClick">
                  <ByImport style="margin-right: 4px; font-size: 14px" />
                  模板导入
                </n-button>
              </n-upload-trigger>
            </n-button-group>
        </div>
      </n-spin>

      <template #description> 导入中... </template>
</n-space> -->

    <n-upload
      ref="uploadRef"
      :show-file-list="false"
      directory-dnd
      :action="actionUrl"
      :data="{
        checkTableId: checkTableId,
        checkAreaFormGrade: checkAreaFormGrade,
      }"
      :max="1"
      :accept="accept"
      :is-error-state="isErrorState"
      :on-before-upload="handleBeforeUpload"
    >
      <n-upload-dragger>
        <div style="margin-bottom: 12px">
          <n-icon size="48" :depth="3">
            <ArchiveIcon />
          </n-icon>
        </div>
        <n-text style="font-size: 16px"> 点击或者拖动文件到该区域来上传 </n-text>
      </n-upload-dragger>
    </n-upload>
    <div class="flex justify-end download" @click="downloadTemplate">下载导入模版</div>

    <!-- <template #footer>
      <div style="display: flex; justify-content: flex-end">
        <n-button style="margin-right: 20px" round type="info" @click="onNegativeClick"> 关闭 </n-button>
      </div>
    </template> -->
  </n-modal>
</template>

<script setup lang="ts">
import { ref, defineProps, computed, watch, reactive } from 'vue';
import { ByImport } from '@kalimahapps/vue-icons';
import { ArchiveOutline as ArchiveIcon } from '@vicons/ionicons5';
import { fileDownloader } from '@/utils/fileDownloader';
import { checkAreaExportTempalte } from '../fetchData';
import { useMessage, UploadFileInfo } from 'naive-ui';
import { $toast } from '@/common/shareContext/useToastCtx.ts';
import { useStore } from '@/store';
import { api } from '@/api';
import { throttle } from 'lodash-es';

import { useRoute } from 'vue-router';
const route = useRoute();
const auth = useStore();

const message = useMessage();

const emits = defineEmits(['closeModal', 'importSuccess']);

const props = defineProps({
  showModal: {
    type: Boolean,
    default: false,
  },
  formData: {
    type: Object,
    default: () => ({}),
  },
});

const showModal = computed({
  get: () => props.showModal,
  set: () => {
    emits('closeModal', false);
  },
});
// 导入loading
const showLoading = ref<boolean>(false);
// 当前导入级别
const checkAreaFormGrade = route.query?.checkAreaFormGrade || '1';
const { topUnitId } = useStore()?.$state.userInfo.topUnitId;
const checkedValue = ref<string>('2');

function handleRadioChange(e: Event) {
  checkedValue.value = (e.target as HTMLInputElement).value;
  console.log(checkedValue.value, 'checkedValue');
}

const actionUrl = api.getUrl(api.type.hazard, api.cj.inspectMgr.hazardCheckAreaImport);
const checkTableId = route.query?.id;

// 上传之前回调
function onBeforeUpload() {
  showLoading.value = true;
  //   if (checkAreaFormGrade != '0' && checkAreaFormGrade != checkedValue.value) {
  //     message.error('导入级别有误，请重新选择');
  //     return false;
  //   }
}
const uploadRef = ref();
// (xhr: XMLHttpRequest) => boolean 判断请求是否为异常状态
function isErrorState(e: any) {
  // e xml
  let data = JSON.parse(e.response);
  if (data.code === 'success') {
    message.success('导入成功');
    emits('importSuccess', checkedValue.value);
  } else {
    showLoading.value = false;
    message.error(data.message);
    if (uploadRef.value) uploadRef.value.clear(); // 清空上传列表
  }
}
const accept = '.xlsx,.xls';
// 上传前校验(文件大小、类型)
function handleBeforeUpload(options: { file: UploadFileInfo }) {
  const { file } = options;
  if (!file.file) return false;
  const fileExt = file.name.slice(file.name.lastIndexOf('.') + 1);
  if (accept && !accept.includes(fileExt)) {
    $toast.error(`请上传 ${accept} 类型的文件!`);
    return false;
  }
  return true;
}

const downloadTemplate = throttle(() => {
  fileDownloader(
    checkAreaExportTempalte() + `?checkAreaFormGrade=${checkAreaFormGrade}&unitId=${auth.$state.userInfo.topUnitId}`,
    {
      filename: '位置检查表导入模板.xlsx',
      method: 'POST',
      // body: JSON.stringify({ checkAreaFormGrade: checkAreaFormGrade }),
      contentType: 'application/json',
    }
  );
}, 5000);

// watch(
//   () => props.showModal,
//   (newVal) => {
//     if (newVal && props.formData.id) {
//       model.value = props.formData;
//       // editType.value = true;
//     } else {
//       model.value = {
//         checkTableName: '',
//         checkTableType: Number(route.query?.checkTableType) || 1,
//       };
//     }
//     console.log(model.value, 'newVal');
//   }
// );

function onNegativeClick() {
  emits('closeModal', false);
}
</script>

<style lang="scss" scoped>
.download {
  color: #3e62eb;
  text-decoration: underline;
  cursor: pointer;
}
</style>
