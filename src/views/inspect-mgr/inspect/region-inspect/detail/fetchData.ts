import { ICheckTempPageRes, checkForm } from '../type';

import { $http } from '@tanzerfe/http';
import { api } from '@/api';
import { IObj } from '@/types';

// 获取分页
export function listPage(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.cj.inspectMgr.hazardCheckAreaList, data);
  return $http.get(url, { data: { _cfg: { showTip: true } } });
}

export function checkedAreaDetail(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.cj.inspectMgr.hazardCheckAreaDetail, data);
  return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: false } } });
}
// 单独保存
export function saveUpdCheckArea(param: object, data: object) {
  const url = api.getUrl(api.type.hazard, api.cj.inspectMgr.hazardCheckAreaDetailSave, param);
  return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: false }, ...data } });
}
// 批量保存
export function saveUpdCheckAreaBatch(params: object, data: object) {
  const url = api.getUrl(api.type.hazard, api.cj.inspectMgr.hazardCheckAreaBatchSave, params);
  return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: false }, ...data } });
}

// 批量保存
export function deleteCheckAreaBatch(params: object, data: object) {
  const url = api.getUrl(api.type.hazard, api.cj.inspectMgr.hazardCheckAreaBatchDelete, params);
  return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: false }, ...data } });
}

// 隐患库列表
export function factorList(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.cj.inspectMgr.hazardEssentialFactorList);
  return $http.post(url, { data: { _cfg: { showTip: true }, ...data } });
}
// 从隐患库导入
export function checkAreaEssentImport(param: object) {
  const url = api.getUrl(api.type.hazard, api.cj.inspectMgr.hazardCheckAreaEssentImport, param);
  return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: false } } });
}

// 导入模板
export function checkAreaExportTempalte() {
  return api.getUrl(api.type.hazard, api.cj.inspectMgr.hazardCheckAreaExportTempalte);
}
// 隐患等级
export function getHazardGradeList() {
  const url = api.getUrl(api.type.hazard, api.hazardManagement.hazardGradeList);
  return $http.post(url, { data: { _cfg: { showTip: true } } });
}
