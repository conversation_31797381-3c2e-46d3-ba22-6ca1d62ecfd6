<template>
  <div>
    <n-form :show-feedback="false">
      <n-form-item label="隐患库导入:" style="margin-top: 10px">
        <n-button type="primary" @click="showModal = true"> 从隐患库导入 </n-button>
      </n-form-item>
      <div class="side-item" v-if="hiddenRowData.elementName">
        <div>{{ hiddenRowData.elementName }}</div>
        <div style="cursor: pointer" @click="hiddenRowData = {}">
          <AnOutlinedDelete />
        </div>
      </div>
      <n-form-item label="隐患库导入:" style="margin-top: 10px">
        <n-button type="primary"> 模板导入 </n-button>
      </n-form-item>

      <div style="margin-top: 10px">
        <n-button type="primary" @click="downloadTemplate(1)"> 一级模板下载</n-button>
      </div>
      <div style="margin-top: 10px">
        <n-button type="primary" @click="downloadTemplate(2)"> 二级模板下载</n-button>
      </div>
    </n-form>

    <n-modal
      v-model:show="showModal"
      title="隐患库导入"
      style="width: 800px"
      @negative-click="onNegativeClick"
      preset="card"
    >
      <div>
        <!-- class="h-full" -->
        <n-data-table
          class="com-table"
          style="height: 400px"
          remote
          striped
          :columns="columns"
          :data="tableData"
          :bordered="false"
          :flex-height="true"
          :loading="loading"
          :pagination="pagination"
          :row-key="(row: any) => row.id"
          @update:checked-row-keys="handleChecked"
        />
      </div>

      <template #footer>
        <div style="display: flex; justify-content: flex-end">
          <n-button style="margin-right: 20px" type="primary" @click="hiddenSubmit"> 确认 </n-button>
          <n-button @click="onNegativeClick"> 关闭 </n-button>
        </div>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useMessage, DataTableColumns, DataTableRowKey } from 'naive-ui';
import { AnOutlinedDelete } from '@kalimahapps/vue-icons';
import {
  factorList,
  checkAreaEssentImport,
  checkAreaExportTempalte,
} from '@/views/inspect-mgr/inspect/region-inspect/detail/fetchData';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { fileDownloader } from '@/utils/fileDownloader';
import { throttle } from 'lodash-es';
const { userInfo } = useStore();
import { useRoute } from 'vue-router';
import { useStore } from '@/store';
const route = useRoute();

const [loading, search] = useAutoLoading(true);
const { pagination, updateTotal } = useNaivePagination(getTableData);

const showModal = ref<boolean>(false);

const columns = ref<DataTableColumns>([
  {
    type: 'selection',
    multiple: false,
    width: 100,
  },
  {
    title: '隐患库名称',
    key: 'elementName',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '编制时间',
    key: 'createTime',
    align: 'center',
  },
]);

const tableData = ref<any[]>([]);

const message = useMessage();

const emits = defineEmits(['closeModal', 'importSuccess']);

const checkTableId = route.query?.id;
// 隐患库id
const essentialFactorId = ref<string | number>('');
const essentialFactorObj = ref({});
// 隐患库
const hiddenRowData = ref<any>({});

function handleChecked(rowKeys: DataTableRowKey[], rowData: any[]) {
  essentialFactorId.value = rowKeys[0];
  essentialFactorObj.value = rowData[0];
  console.log(rowKeys, rowData);
}
function getTableData() {
  const params = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    unitId: userInfo.unitId,
    status: 1,
  };

  search(factorList(params)).then((res: any) => {
    tableData.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
}

const submitLoading = ref<boolean>(false);

function handleSubmit() {
  submitLoading.value = true;
  checkAreaEssentImport({
    checkTableId: checkTableId,
    essentialFactorId: essentialFactorId.value,
  })
    .then(() => {
      message.success('导入成功');
      emits('importSuccess');
      submitLoading.value = false;
    })
    .catch((error) => {
      console.log(error, 'error');
      submitLoading.value = false;
    });
}

getTableData();
const onNegativeClick = () => {
  showModal.value = false;
};
const hiddenSubmit = () => {
  hiddenRowData.value = essentialFactorObj.value;
  showModal.value = false;
};

const downloadTemplate = throttle((type: number) => {
  fileDownloader(checkAreaExportTempalte() + `?checkAreaFormGrade=${type}`, {
    filename: `区域检查表导入${type == 1 ? '一级' : '二级'}模板.xlsx`,
    method: 'POST',
    // body: JSON.stringify({ checkAreaFormGrade: checkAreaFormGrade }),
    contentType: 'application/json',
  });
}, 5000);

defineOptions({ name: 'RegionHiddenImport' });
</script>

<style lang="scss" scoped>
.side-item {
  margin: 10px 0;
  padding: 10px;
  background-color: #e3e3e3;
  display: flex;
  justify-content: space-between;
}
</style>
