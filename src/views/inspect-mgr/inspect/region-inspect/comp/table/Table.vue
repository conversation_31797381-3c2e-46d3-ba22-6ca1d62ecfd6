<template>
  <n-data-table
    class="h-full com-table"
    remote
    :columns="columns"
    :data="tableData"
    :bordered="false"
    :flex-height="true"
    :loading="loading"
    :pagination="pagination"
    :render-cell="useEmptyCell"
    :row-key="rowKey"
    @update:checked-row-keys="handleCheck"
  />
</template>

<script setup lang="ts">
import { h, ref, VNode, toRaw, reactive } from 'vue';
import { DataTableColumns, NButton } from 'naive-ui';
import { cols } from './columns';
import { ACTION } from '@/views/inspect-mgr/constant';
import { useRoute, useRouter } from 'vue-router';
import { listPage } from '../../fetchData';

import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { IObj } from '@/types';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import type { DataTableRowKey } from 'naive-ui';
import { useStore } from '@/store/index';
import { isMyUnit } from '../../../utils';

const router = useRouter();
const route = useRoute();

const emits = defineEmits(['action']);

const [loading, search] = useAutoLoading(true);
const { pagination, updateTotal } = useNaivePagination(getTableData);

const columns = ref<DataTableColumns>([]);
const tableData = ref<any[]>([]);
function setColumns() {
  // columns.value.push(...cols);

  columns.value = [
    {
      title: '检查表名称',
      key: 'checkTableName',
      align: 'center',
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '编制时间',
      key: 'createTime',
      align: 'center',
    },
    {
      title: '操作',
      key: 'actions',
      align: 'center',
      width: 240,
      render(row) {
        return getActionBtn(row);
      },
    },
  ];

  if (filterData.checkTableType == '1') {
    columns.value.splice(1, 1, {
      title: '检查表层级',
      key: 'checkAreaFormGrade',
      align: 'center',
      ellipsis: {
        tooltip: true,
      },
      render: (row: any) => {
        return ['', '单层检查表', '双层检查表'][row.checkAreaFormGrade];
      },
    });
  }
  if (filterData.checkTableType == '3') {
    columns.value.splice(1, 1);
  }
}

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          // text: true,
          // class: 'com-action-button',
          size: 'small',
          type: 'primary',
          ghost: true,
          // style: 'background: rgba(245,154,35,0.1);color:#F59A23;border:1px solid #F59A23',
          onClick: () =>
            router.push({
              name: ['', 'inspectRegionDetail', 'inspectDeviceDetail', 'inspectPointDetail'][row.checkTableType],
              query: { id: row.id, checkAreaFormGrade: row.checkAreaFormGrade, checkUnitId: row.unitId },
            }),
        },
        { default: () => '详情' }
      ),
    ],
    [
      h(
        NButton,
        {
          // text: true,
          // class: 'com-action-button',
          size: 'small',
          type: 'primary',
          ghost: true,
          disabled: row.isEnable || !isMyUnit(row.unitId),
          // style: 'background: rgba(0,167,32,0.1);color:#00A720;border:1px solid #00A720',
          onClick: () => emits('action', { action: ACTION.EDIT, data: toRaw(row) }),
        },
        { default: () => '编辑' }
      ),
    ],
    [
      h(
        NButton,
        {
          // text: true,
          // class: 'com-action-button',
          size: 'small',
          type: 'warning',
          ghost: true,
          disabled: row.isEnable || !isMyUnit(row.unitId),
          onClick: () => emits('action', { action: ACTION.STATE, data: toRaw(row) }),
        },
        { default: () => (row.checkStatus == '0' ? '停用' : '启用') }
      ),
    ],
    // [
    //   h(
    //     NButton,
    //     {
    //       text: true,
    //       class: 'com-action-button',
    // size: 'small',
    // type: 'error',
    // ghost: true,
    //       onClick: () => emits('action', { action: ACTION.DELETE, data: toRaw(row) }),
    //     },
    //     { default: () => '删除' }
    //   ),
    // ],
  ];
  return useActionDivider(acList);
}

let filterData: IObj<any> = {}; // 搜索条件
function getTableData() {
  setColumns();
  tableData.value = [];
  const params = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    unitId: [useStore().userInfo.topUnitId, useStore().userInfo.unitId].join(','),
    ...filterData,
  };

  search(listPage(params)).then((res) => {
    tableData.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
}

function getTableDataWrap(data: IObj<any>) {
  filterData = Object.assign({}, data) || {};
  pagination.page = 1;
  getTableData();
}

const rowKey = (row: any) => row.id;

function handleCheck(rowKeys: DataTableRowKey[]) {
  console.log(rowKeys);
  emits('action', { action: ACTION.EXPORT, data: rowKeys });
}

defineOptions({ name: 'checkTampTableComp' });
defineExpose({ getTableDataWrap, getTableData });
</script>
<style module lang="scss"></style>
