import { ACTION } from '../../constant';
import { IObj, IPageRes } from '@/types';

export interface IActionData {
  action: ACTION;
  data: IObj<any>;
}

/**
 * 检查模板
 */
export interface ICheckTempRow {
  /**
   * 创建时间
   */
  createTime: string;
  /**
   * 创建人名称
   */
  createUser: string;
  /**
   * 消防监督检查模版
   */
  id: string;
  /**
   * 创建机构
   */
  orgCode: string;
  /**
   * 创建机构名称
   */
  orgName: string;
  /**
   * 模版名称
   */
  templateName: string;
  /**
   * 模版使用场景
   */
  templateScene: string;
  /**
   * 单位名称
   */
  unitId: string;
  /**
   * 单位名称
   */
  unitName: string;
  /**
   * 修改时间
   */
  updateTime: string;
  /**
   * 修改人
   */
  updateUser: string;
}

export type ICheckTempPageRes = IPageRes<ICheckTempRow>;

/**
 * 检查表
 */
export interface checkForm {
  /**
   * id
   */
  id?: number;
  //检查表名称
  checkTableName?: string;
  // 检查表类型
  checkTableType?: number;
  // 检查表状态
  checkStatus?: number;
}
