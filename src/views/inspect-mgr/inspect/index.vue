<template>
  <div>
    <ComBread :data="breadData" />
    <div class="com-g-row-a1 h-full gap-y-[20px] com-table-container">
      <Filter @action="actionFn" ref="filterRef" />
      <TableList class="table-list" ref="tableCompRef" @action="actionFn" :btnAuths="btnAuths" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { $dialog } from '@/common/shareContext/useDialogCtx';
import ComBread from '@/components/breadcrumb/ComBread.vue';
import { IBreadData } from '@/components/breadcrumb/type.ts';
import { useStore } from '@/store';
import { useMessage } from 'naive-ui';
import { computed, provide, ref, Ref } from 'vue';
import TableList from './checklist/tabel/Table.vue';
import Filter from './comp/Filter.vue';
import { getdeleteApi, getstopOrStartCheckTableApi } from './fetchData';

const message = useMessage();
const showModal = ref<boolean>(false);
const { userInfo } = useStore();
// 列表按钮权限
let btnAuths = userInfo.resourceVoList
  .find((item: any) => item.resUrl === 'checkList')
  ?.childrens.filter((item: any) => item.resType == '2')
  .map((item: any) => item.resUrl);
const actionFn = (val: any) => {
  console.log(filterRef.value, 'filterRef.value');
  if (val.action === 'strat') {
    handlStart(val.data.id, 0);
  } else if (val.action === 'shop') {
    handlStop(val.data.id, 1);
  } else if (val.action === 'del') {
    $dialog.warning({
      title: '提示',
      content: '确定删除该检查表吗？删除后不可恢复',
      positiveButtonProps: { type: 'primary', color: '#527cff' },
      positiveText: '确定',
      negativeText: '取消',
      transformOrigin: 'center',
      onPositiveClick: async () => {
        handlDel(val.data.id);
      },
    });
  } else if (val.action === 'add') {
    //新增
    showModal.value = true;
  } else if (val.action === 'edit') {
    //编辑
    openDialog();
    dialogType.value = 'EDIT';
    rowData.value = val.data;
  } else if (val.action === 'SEARCH') {
    handleChange(val.data);
  }
};
const dialogType = ref('ADD'); //新增还是编辑
const rowData = ref({});
const tableCompRef = ref();

const filterRef = ref<any>(null);

function handleChange(data: any = null) {
  tableCompRef.value?.getTableDataWrap(data);
}
const handlStop = async (id: string, checkStatus: number) => {
  $dialog.warning({
    title: '提示',
    positiveButtonProps: { type: 'primary', color: '#527cff' },
    content: '确定要停用该检查表吗？',
    positiveText: '确定',
    negativeText: '取消',
    transformOrigin: 'center',
    onPositiveClick: async () => {
      await getstopOrStartCheckTableApi({ id, checkStatus });
      handleChange(filterRef.value.filterForm);
      message.success('停用成功');
    },
  });
};
const handlStart = async (id: string, checkStatus: number) => {
  $dialog.warning({
    title: '提示',
    positiveButtonProps: { type: 'primary', color: '#527cff' },
    content: '确定要启用该检查表吗？',
    positiveText: '确定',
    negativeText: '取消',
    transformOrigin: 'center',
    onPositiveClick: async () => {
      await getstopOrStartCheckTableApi({ id, checkStatus });
      handleChange(filterRef.value.filterForm);
      console.log(filterRef.value.filterForm);
      message.success('启用成功');
    },
  });
};
const handlDel = async (id: string) => {
  await getdeleteApi({ id });
  handleChange(filterRef.value.filterForm);

  message.success('删除成功');
};
const breadData: Ref<IBreadData[]> = computed(() => [{ name: '隐患治理系统' }, { name: '检查表' }]);
const doHandle = () => {
  handleChange(filterRef.value.filterForm);
};

const dialogVisibility = ref(false);

const openDialog = () => {
  dialogVisibility.value = true;
};
const closeDialog = () => {
  dialogVisibility.value = false;
  dialogType.value = 'ADD';
  rowData.value = {};
};
function closeModal() {
  showModal.value = false;
}
provide('doHandle', doHandle);
provide('closeDialog', closeDialog);
provide('closeModal', closeModal);
defineOptions({ name: 'checklistConfComp' });
</script>

<style scoped lang="scss">
.item_content {
  height: calc(100% - 40px) !important;
}
</style>
