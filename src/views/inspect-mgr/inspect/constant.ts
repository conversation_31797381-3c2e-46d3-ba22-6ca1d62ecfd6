export const enum PROVIDE_KEY {
  currentAction = 'currentAction',
}

export const enum ACTION {
  SEARCH = 'SEARCH',
  DETAIL = 'DETAIL',
  EDIT = 'EDIT',
  DELETE = 'DELETE',
  CHECK = 'CHECK',
  UN_CHECK = 'UN_CHECK',
  EXPORT = 'EXPORT',
  // STARTORSTOP = 'STARTORSTOP',
  START = 'START',
  STOP = 'STOP',
}

export const ACTION_LABEL: { [key in ACTION]: string } = {
  [ACTION.SEARCH]: '搜索',
  [ACTION.DETAIL]: '详情',
  [ACTION.EDIT]: '编辑',
  [ACTION.DELETE]: '删除',
  [ACTION.CHECK]: '选择检查项',
  [ACTION.UN_CHECK]: '取消检查项',
  [ACTION.EXPORT]: '导出',
  [ACTION.START]: '启用',
  [ACTION.STOP]: '停用',
};

// 类型
export const inspectType: { label: string; value: string }[] = [
  // 1:综合检查;2:巡查点位检查;
  {
    label: '综合检查',
    value: '1',
  },
  {
    label: '巡查点位检查',
    value: '2',
  },
];
