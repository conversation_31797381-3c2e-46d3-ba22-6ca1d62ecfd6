<template>
  <n-modal
    v-model:show="showModal"
    title="隐患库导入"
    style="width: 80%"
    @negative-click="onNegativeClick"
    preset="card"
  >
    <el-form-item label="请选择隐患库:">
      <n-select
        v-model:value="essentialFactorId"
        :options="essentialFactorList"
        label-field="elementName"
        style="width: 300px"
        size="small"
        value-field="id"
        placeholder="请选择"
        clearable
        @update:value="selByessentialFactorId()"
      />
    </el-form-item>
    <el-row :gutter="20" style="height: 650px; overflow-y: auto">
      <el-col :span="6" class="com-table-filter">
        <n-button type="primary" disabled style="width: 100%">隐患分类</n-button>
        <n-input
          v-model:value="pattern"
          v-if="treeData.length"
          placeholder="请输入隐患分类"
          size="small"
          class="mt-5"
        />
        <n-tree
          class="tree-scroll mt-5"
          ref="treeRef"
          :pattern="pattern"
          block-line
          default-expand-all
          :data="treeData"
          show-line
          selectable
          key-field="id"
          label-field="label"
          children-field="children"
          :show-irrelevant-nodes="false"
          :render-prefix="renderPrefix"
          :on-update:selected-keys="handleUpdateSelectedKeys"
        />
      </el-col>
      <el-col :span="18">
        <!-- 全选按钮 -->
        <div class="flex justify-end" style="margin-bottom: 8px">
          <n-button type="primary" @click="checkboxAll">全选/取消全选</n-button>
        </div>
        <div class="table-scroll" v-if="tableData.length">
          <n-checkbox-group :value="selids" @update:value="handleUpdateValue">
            <div
              v-for="(item, index) in tableData"
              :key="item.id"
              class="mt-4 table-item"
              :style="{ 'margin-top': index == 0 ? index : '' }"
              @click.stop="clickbychecked(item)"
            >
              <n-flex
                justify="space-between"
                align="center"
                :class="selids.includes(item.id) ? 'selected-div_top p-2' : 'p-2 title1'"
              >
                <div class="">
                  <div class="font-bold mt-2 max-w-[1000px]">
                    {{ item.essentialFactorClassFullName }}
                  </div>
                  <div class="max-w-[1000px] line-clamp-2">
                    {{ item.inspectionItem }}
                  </div>
                </div>
                <div>
                  <n-checkbox :value="item.id" @click="clickbychecked(item)" />
                  <n-button
                    type="primary"
                    size="small"
                    @click.stop="editForm(item, 'detail', '检查项详情')"
                    style="margin-left: 10px"
                  >
                    查看详情
                  </n-button>
                </div>
              </n-flex>
              <!-- v1.0.4 -->
              <div :class="{ 'selected-div': selids.includes(item.id) }">
                <div class="flex flex-row border-solid border-[1px] border-[#dde0e6]">
                  <!-- 检查内容 -->
                  <div class="p-3" :class="item.classItemCommonList.length ? 'w-[50%]' : 'w-[100%]'">
                    <div class="flex justify-start items-center">
                      <div class="w-[2px] h-[13px] bg-[#527cff] rounded-[0px]"></div>
                      <div class="ml-3 text-[#7f7f7f]">检查内容：</div>
                    </div>
                    <div class="mt-[11px]">{{ item.inspectionDescribe }}</div>
                  </div>
                  <!-- 常见隐患 -->
                  <div
                    class="w-[50%] p-3 border-l-[1px] border-solid border-[#dde0e6]"
                    v-if="item.classItemCommonList.length"
                  >
                    <div class="flex justify-start items-center">
                      <div class="w-[2px] h-[13px] bg-[#527cff] rounded-[0px]"></div>
                      <div class="ml-3 text-[#7f7f7f]">
                        常见隐患（
                        <span class="text-[red]"> {{ item.classItemCommonList.length }}条 </span>
                        ）：
                      </div>
                    </div>
                    <div class="mt-[11px]">
                      <div class="flex flex-row pb-[8px]" v-for="(ks, index) in item.classItemCommonList" :key="index">
                        <div class="rounded level">
                          {{ ks.hazardGradeName }}
                        </div>
                        <div class="leading-[28px] pl-3">{{ index + 1 }}、{{ ks.hazardDesc }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </n-checkbox-group>
        </div>
        <n-empty style="position: relative" v-else description="暂无数据" />
        <div class="flex justify-end items-end mt-4" v-if="total">
          <div class="mr-3">共 {{ total }} 条</div>
          <n-pagination
            v-model:page="pagination.page"
            v-model:page-size="pagination.pageSize"
            :item-count="total"
            show-size-picker
            :page-sizes="[10, 20, 30, 40]"
            :on-update:page-size="(pageSize: number) => getTableData({ pageSize })"
            :on-update:page="(page: number) => getTableData({ page })"
          />
        </div>
      </el-col>
    </el-row>
    <template #action>
      <div style="display: flex; justify-content: flex-end">
        <n-button type="info" style="margin-right: 20px" @click="onNegativeClick"> 取消 </n-button>
        <n-button type="primary" style="margin-right: 20px" :loading="submitLoading" @click="handleSubmit">
          确定
        </n-button>
      </div>
    </template>
  </n-modal>
  <ComDrawerA
    title="隐患库详情"
    :autoFocus="false"
    :footerPaddingBottom="25"
    :maskNeedClosable="true"
    :show="modalShow"
    @handleNegative="onNegativeClickmodalShow"
    class="!w-[70%]"
  >
    <!-- <n-modal v-model:show="modalShow" title="隐患库详情" style="width: 1400px" @negative-click="onNegativeClickmodalShow"
    preset="card"> -->
    <CardDetail
      :watermarkData="watermarkData"
      :modalMode="modalMode"
      :treeData="treeData"
      :gradeOpt="gradeOpt"
      :rows="rowsData"
      :fromCheckList="true"
      @handleNegative="onNegativeClickmodalShow"
      @uploadData="getTreeData"
    />
    <!-- </n-modal> -->
  </ComDrawerA>
</template>

<script setup lang="ts">
import { ref, defineProps, computed, watch, reactive, h } from 'vue';
import parentIco from './parent.png';
import childrenIco from './children.png';
import { useMessage, DataTableColumns } from 'naive-ui';
import type { DataTableRowKey, TreeOption } from 'naive-ui';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { checkAreaEssentImport, factorList, getTreeList, hazardEssentialFactorClassItemListPage } from '../fetchData';
import { useRoute } from 'vue-router';
import { useStore } from '@/store';
import { hazardEssentialFactorClassItemDetail } from '@/views/risk-database/risk-library/fetchData';
import CardDetail from '../../../risk-database/risk-library/comp/detail/cardDetail.vue';
import { getGradeList } from '@/views/paramsConf/comp/fetchData';
import { getWatermarkBizDataApi } from '@/utils/getWatermarkBizData';
const { userInfo } = useStore();

const [loading, search] = useAutoLoading(true);
// const { pagination, updateTotal } = useNaivePagination(getTableData);

// const filterForm = reactive({
//   essentialFactorId: '',
// });

// 获取水印配置
const watermarkData = ref<any>();
getWatermarkBizDataApi().then((data: any) => {
  watermarkData.value = data;
});

const columns = ref<DataTableColumns>([
  {
    type: 'selection',
    multiple: false,
  },
  // {
  //   title: '序号',
  //   key: 'index',
  //   width: 65,
  //   align: 'center',
  //   render: (_: any, index: number) => {
  //     return index + 1;
  //   },
  // },
  {
    title: '隐患库名称',
    key: 'elementName',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '编制时间',
    key: 'createTime',
    align: 'center',
  },
]);

// const tableData = ref<any[]>([]);

const message = useMessage();
const essentialFactorList = ref<any[]>([]);
const emits = defineEmits(['closeModal', 'importSuccess', 'getDetailInfo']);

const props = defineProps({
  showModal: {
    type: Boolean,
    default: false,
  },
  formData: {
    type: Object,
    default: () => ({}),
  },
});

const showModal = computed({
  get: () => props.showModal,
  set: () => {
    emits('closeModal', false);
  },
});

watch(
  () => props.showModal,
  (newVal) => {
    if (newVal) {
      console.log(props.formData, '=====================propsformData');
      getessentialFactorData();
    }
  }
);
console.log(props.formData, '=====================propsformData');
const checkTableId = props.formData?.id;
// 隐患库id
const essentialFactorId = ref<string | number>('');

function getessentialFactorData() {
  const params = {
    pageNo: 1,
    pageSize: -1,
    unitId: userInfo.topUnitId,
    status: '0', //未禁用
    valid: 1, // 隐患数据库有效，非空表
  };

  search(factorList(params)).then((res: any) => {
    essentialFactorList.value = res.data.rows || [];
    essentialFactorId.value = res.data.rows[0].id;
    getTreeData();
    // getTableData();
  });
}

const treeData = ref([]);
const { pagination } = useNaivePagination(getTableData);
const tableData = ref<any[]>([]);
const childrenIds = ref<any[]>([]);
const total = ref();
const parentId = ref();
const defaultIds = ref<any[]>([]);
let defaultCheckedKeys: string[] = [];
const pattern = ref('');
// 根据隐患库切换分类和列表数据

function selByessentialFactorId() {
  selids.value = [];
  console.log(essentialFactorId.value, '===============隐患库id');
  getTreeData();
}
// 节点选中项发生变化时的回调函数
function handleUpdateSelectedKeys(keys: any) {
  if (keys.length == 0 || keys[0] != parentId.value) {
    childrenIds.value = [];
  }
  parentId.value = (keys.length && keys[0]) || '';
  // treeData是树状结构且多级 需要递归获取parentId对应的 item
  let node = findNodeById(treeData.value, parentId.value);
  getChildrenIds(node);
  console.log(childrenIds.value);
  pagination.page = 1;
  pagination.pageSize = 10;
  getTableData();
}
// 递归遍历树，并找到具有指定ID的节点
function findNodeById(tree: any, id: any) {
  // 遍历当前节点的所有子节点
  for (let i = 0; i < tree.length; i++) {
    // 如果找到匹配的ID，则返回该节点
    if (tree[i].id === id) {
      return tree[i];
    }
    // 如果当前节点有子节点，则递归查找
    if (tree[i].children && tree[i].children.length > 0) {
      const found: any = findNodeById(tree[i].children, id);
      if (found) {
        return found;
      }
    }
  }
  // 如果没有找到，则返回null
  return null;
}
// 获取当前id下的所有子id
function getChildrenIds(node: any) {
  // 递归获取子级id
  if (node && node.children && node.children.length) {
    node.children.forEach((item: any) => {
      childrenIds.value.push(item.id);
      item.children && item.children.length && getChildrenIds(item);
    });
  } else {
    childrenIds.value = [];
  }
}
function getTableData({ page, pageSize }: any = {}) {
  if (page) pagination.page = page;
  if (pageSize) {
    pagination.pageSize = pageSize;
    pagination.page = 1;
  }
  if (!page && !pageSize) pagination.page = 1;
  setTimeout(() => {
    console.log(pagination);
    const params = {
      pageNo: pagination.page,
      pageSize: pagination.pageSize,
      // hazardDescribe: hazardDescribe.value,
      essentialFactorClassId:
        parentId.value || childrenIds.value.length
          ? [parentId.value, ...childrenIds.value].join(',')
          : defaultIds.value.join(','),
      essentialFactorId: essentialFactorId.value,
    };
    hazardEssentialFactorClassItemListPage(params).then((res: any) => {
      tableData.value = res.data.rows || [];
      total.value = res.data.total || 0;
    });
  });
}

function getTreeData() {
  getTreeList({
    essentialFactorId: essentialFactorId.value,
    parentId: '0',
  }).then((res: any) => {
    res.data && res.data.length ? traverse(res.data) : [];
    defaultCheckedKeys = [];
    treeData.value = createData(res.data);
    getTableData();
  });
}
// getTreeData();
function traverse(items: any) {
  items.forEach((item: any) => {
    defaultIds.value.push(item.id); // 将当前项的id添加到ids数组中
    if (item.children && item.children.length > 0) {
      // 如果当前项有children，则递归遍历它们
      traverse(item.children);
    }
  });
  console.log(defaultIds.value);
}

function createData(data: any) {
  return data.map((item: any, index: any) => {
    defaultCheckedKeys.push(item.id);
    return {
      label: item.className,
      id: item.id,
      children: item.children && item.children.length ? createData(item.children) : null,
    };
  });
}

// const checkedvalue = ref(false);

const selids = ref<any>([]);
function clickbychecked(item) {
  if (selids.value.includes(item.id)) {
    selids.value = selids.value.filter((id) => id !== item.id);
  } else {
    selids.value.push(item.id);
  }
}
function handleUpdateValue(value: any) {
  // console.log(var);
  selids.value = value;
  console.log(selids.value);
}
function checkboxAll() {
  if (selids.value.length > 0) {
    // 取消全选
    selids.value = [];
  } else {
    // 全选
    selids.value = tableData.value.map((item: any) => item.id);
  }
}

function renderPrefix({ option }: { option: TreeOption }) {
  return h('img', {
    src: option.children ? parentIco : childrenIco,
    style: {
      width: '16px',
    },
  });
}

const modalShow = ref(false);
const modalTitle = ref();
const modalMode = ref<any>('detail');
const rowsData = ref({});
const form = ref<any>({
  id: null,
  hazardDescribe: null,
  essentialFactorClassId: null,
  hazardGradeId: null,
  inspectionItem: null,
  inspectionDescribe: null,
  inspectionItemBasis: null,
  inspectionAsk: null,
  legalText: null,
  legalLiability: null,
  files: [],
});
function onNegativeClickmodalShow() {
  modalShow.value = false;
}

const gradeOpt = ref<any>([]);
//获取隐患级别
function getGradeOpt() {
  const params = {
    pageNo: 1,
    pageSize: 9999,
    unitId: userInfo.topUnitId,
  };
  getGradeList(params).then((res: any) => {
    gradeOpt.value = res.data.rows || [];
  });
}
getGradeOpt();

// 检查项详情
function editForm(item: any, mode = 'detail', title = '隐患库详情') {
  modalTitle.value = title;
  if (!item) {
    modalShow.value = true;
    modalMode.value = mode;
    return;
  }
  console.log(item);
  hazardEssentialFactorClassItemDetail({ classItemId: item.id }).then((res) => {
    form.value = res.data || null;
    if (form.value) {
      modalShow.value = true;
      modalMode.value = mode;
      // 文件类别：1正确图例 2错误图例
      form.value.correctFiles = form.value.files.filter((item: any) => item.fileCategory == 1);
      form.value.errorFiles = form.value.files.filter((item: any) => item.fileCategory == 2);
      console.log(form.value, form.value.correctFiles.length);
      rowsData.value = form.value;
    }
  });
}
function handleChecked(rowKeys: DataTableRowKey[]) {
  essentialFactorId.value = rowKeys[0];
  // console.log(rowKeys);
}
const submitLoading = ref<boolean>(false);

function handleSubmit() {
  submitLoading.value = true;
  console.log(selids.value.join(','));
  // checkTableId
  if (selids.value.length == 0) {
    message.warning('至少选择一个检查项');
    submitLoading.value = false;
    return;
  }
  let data = {};
  if (props.formData.parentId) {
    // pattern.value = '1';
    data = {
      checkTableId: props.formData.checkTableId,
      classItemIds: selids.value.join(','),
      parentId: props.formData.id,
    };
  } else {
    data = {
      checkTableId: props.formData.id,
      classItemIds: selids.value.join(','),
      // parentId: props.formData.id,
    };
  }
  // console.log(props.formData);
  // console.log(data);
  // emits('importSuccess');
  // emits('getDetailInfo');
  checkAreaEssentImport(data)
    .then(() => {
      message.success('导入成功');
      onNegativeClick();
      emits('importSuccess');
      submitLoading.value = false;
    })
    .catch((error) => {
      console.log(error, 'error');
      submitLoading.value = false;
    });
}

function onNegativeClick() {
  emits('closeModal', false);
}
</script>

<style lang="scss" scoped>
::v-deep .n-tree-node--selected {
  background: rgba(82, 124, 255, 0.1);

  .n-tree-node-content__text {
    color: #527cff;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}

::v-deep {
  .n-tree-node-content__text {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}

::v-deep .n-tree .n-tree-node-content {
  height: 30px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.tree-scroll {
  height: 500px;
  overflow-y: scroll;
}

.table-scroll {
  height: 500px;
  overflow: auto;

  .table-item {
    border-radius: 6px;
    border-color: #e1e3e9;

    .title {
      background-color: #f5f7fa;
    }
  }
}

.download {
  color: #3e62eb;
  text-decoration: underline;
  cursor: pointer;
}

.selected-div {
  background-color: rgb(237 241 255);
}

.selected-div_top {
  background-color: rgb(189 231 252);
}

.title1 {
  color: #527cff;
  background-color: #edf1ff;
  border-radius: 4px 4px 0px 0px;
}

:deep(.el-tree-node__content) {
  padding: 20px 0;
}

:deep(.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content) {
  background: rgba(82, 124, 255, 0.1);
}

.level {
  min-width: 130px;
  padding: 6px 12px;
  margin-right: 16px;
  background: #f59a2357;
  color: #f59a23;
  text-align: center;
}
</style>
