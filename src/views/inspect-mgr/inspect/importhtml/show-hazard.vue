<template>
  <n-modal
    v-model:show="showModal"
    title="维护常见隐患"
    :mask-closable="false"
    style="width: 900px; height: 70vh"
    preset="card"
  >
    <n-scrollbar>
      <div class="rounded-lg border border-gray-300 p-4 mb-4" v-for="item of data" :key="item.id">
        <div class="flex">
          <span class="mr-2">隐患描述</span>
          <span>{{ item.hazardName }}</span>
        </div>
        <div class="flex">
          <span class="mr-2">正确图例</span>
          <n-image-group>
            <n-space>
              <n-image width="100" v-for="img of item.successImageUrls?.split(',')" :key="img" :src="img" />
            </n-space>
          </n-image-group>
        </div>
        <div class="flex">
          <span class="mr-2">错误图例</span>
          <n-image-group>
            <n-space>
              <n-image width="100" v-for="img of item.errorImageUrls?.split(',')" :key="img" :src="img" />
            </n-space>
          </n-image-group>
        </div>
      </div>
    </n-scrollbar>
    <!-- <div class="mt-10 flex w-full justify-center absolute bottom-[20px]">
      <n-button @click="showModal = false"> 关闭 </n-button>
    </div> -->
  </n-modal>
</template>

<script setup lang="ts">
import { ref, defineProps, computed, watch, reactive, h, render } from 'vue';
import { selectHazardDetail } from '../fetchData';
import { useStore } from '@/store';
import { NButton, NImageGroup, NImage } from 'naive-ui';

// const data = [
//   {
//     id: '0b986faf1f5a49b9a1d2b4ea57561bb3',
//     unitId: '10000',
//     zhId: 'ycsy',
//     riskCheckItemId: '9dd5b0299a314673ae26276d9cf437cc',
//     hazardName: '2313',
//     successImageUrls:
//       'https://agjp.tanzervas.com/aqsc/v1/ycsy/img1/images/ehs/null/bwFck/2024-10-17/e5f869b632d545379661daf2482a6ae8.png,https://agjp.tanzervas.com/aqsc/v1/ycsy/img1/images/ehs/null/bwFck/2024-10-17/b80c8939689941efa108fc5fc88c7f7e.png',
//     errorImageUrls:
//       'https://agjp.tanzervas.com/aqsc/v1/ycsy/img1/images/ehs/null/bwFck/2024-10-17/d31741a566fe4c11b216543425660e6f.png',
//     remark: '',
//     createUserId: 'test',
//     createTime: '2024-10-17 14:23:52',
//     updateUserId: '',
//     updateTime: '2024-10-17 14:23:52',
//   },
//   {
//     id: '9d95f7262bf642ac92ec78c347550e9f',
//     unitId: '10000',
//     zhId: 'ycsy',
//     riskCheckItemId: '9dd5b0299a314673ae26276d9cf437cc',
//     hazardName: '消防通道堵塞隐患',
//     successImageUrls: '',
//     errorImageUrls: '',
//     remark: '',
//     createUserId: 'test',
//     createTime: '2024-09-28 20:20:50',
//     updateUserId: '',
//     updateTime: '2024-09-28 20:20:50',
//   },
// ];

const data = ref<any[]>([]);
const getData = async (checkItemId: string) => {
  data.value = [];
  const res = await selectHazardDetail({ checkItemId });
  data.value = res.data as any[];
};

const showModal = ref(false);

const open = (data: any) => {
  showModal.value = true;
  getData(data.checkItemId);
};

defineExpose({ open });
</script>

<style lang="scss" scoped></style>
