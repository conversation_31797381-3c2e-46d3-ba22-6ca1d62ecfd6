<template>
  <n-modal
    v-model:show="showModal"
    title="模板导入"
    style="width: 600px; height: 350px"
    @negative-click="onNegativeClick"
    preset="card"
  >
    <div class="flex flex-col justify-center items-center h-full">
      <n-upload
        ref="uploadRef"
        :show-file-list="false"
        directory-dnd
        :action="actionUrl"
        :data="{
          checkTableId: checkTableId,
          checkAreaFormGrade: checkAreaFormGrade,
          parentId: parentId,
        }"
        :max="1"
        :accept="accept"
        :is-error-state="isErrorState"
        :on-before-upload="handleBeforeUpload"
      >
        <n-upload-dragger>
          <div class="mb-5">
            <n-icon size="48" :depth="3">
              <ArchiveIcon />
            </n-icon>
          </div>
          <n-text style="font-size: 16px"> 点击或者拖动文件到该区域来上传 </n-text>
        </n-upload-dragger>
      </n-upload>
      <div class="flex justify-end download w-full" @click="downloadTemplate">下载导入模版</div>
    </div>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, defineProps, computed, watch, reactive } from 'vue';
import { ByImport } from '@kalimahapps/vue-icons';
import { ArchiveOutline as ArchiveIcon } from '@vicons/ionicons5';
import { fileDownloader } from '@/utils/fileDownloader';
import { checkAreaExportTempalte } from '../fetchData';
import { useMessage, UploadFileInfo } from 'naive-ui';
import { $toast } from '@/common/shareContext/useToastCtx.ts';
import { useStore } from '@/store';
import { api } from '@/api';
import { throttle } from 'lodash-es';

import { useRoute } from 'vue-router';
const route = useRoute();
const auth = useStore();

const message = useMessage();

const emits = defineEmits(['closeModal', 'importSuccess']);

const props = defineProps({
  showModal: {
    type: Boolean,
    default: false,
  },
  formData: {
    type: Object,
    default: () => ({}),
  },
  fileName: {
    type: String,
    default: '综合检查表导入模板.xlsx',
  },
});

const showModal = computed({
  get: () => props.showModal,
  set: () => {
    emits('closeModal', false);
  },
});
// 导入loading
const showLoading = ref<boolean>(false);
// 当前导入级别
console.log(props.formData.value);
// const checkAreaFormGrade = route.query?.checkAreaFormGrade || '1';

const { topUnitId } = useStore()?.$state.userInfo.topUnitId;
const checkedValue = ref<string>('2');

function handleRadioChange(e: Event) {
  checkedValue.value = (e.target as HTMLInputElement).value;
  console.log(checkedValue.value, 'checkedValue');
}

const actionUrl = api.getUrl(api.type.hazard, api.zyl.checklist.hazardCheckAreaImport);
const checkAreaFormGrade = props.formData?.checkAreaFormGrade || '1';
const checkTableId = ref('');
const parentId = ref('');
if (props.formData.parentId) {
  // pattern.value = '1';
  checkTableId.value = props.formData.checkTableId;
  parentId.value = props.formData.id;
} else {
  checkTableId.value = props.formData.id;
  parentId.value = '0';
}
// if (checkAreaFormGrade === '1') {
//   parentId.value = '0';
// } else {
//   checkedValue.value = '1';
// }
console.log(props.formData);
// checkTableId
// 上传之前回调
function onBeforeUpload() {
  showLoading.value = true;
}
const uploadRef = ref();
// (xhr: XMLHttpRequest) => boolean 判断请求是否为异常状态
function isErrorState(e: any) {
  // e xml
  let data = JSON.parse(e.response);
  if (data.code === 'success') {
    message.success('导入成功');
    emits('importSuccess', checkedValue.value);
  } else {
    showLoading.value = false;
    message.error(data.message);
    if (uploadRef.value) uploadRef.value.clear(); // 清空上传列表
  }
}
const accept = '.xlsx,.xls';
// 上传前校验(文件大小、类型)
function handleBeforeUpload(options: { file: UploadFileInfo }) {
  const { file } = options;
  if (!file.file) return false;
  const fileExt = file.name.slice(file.name.lastIndexOf('.') + 1);
  if (accept && !accept.includes(fileExt)) {
    $toast.error(`请上传 ${accept} 类型的文件!`);
    return false;
  }
  return true;
}

const downloadTemplate = throttle(() => {
  fileDownloader(
    checkAreaExportTempalte() + `?checkAreaFormGrade=${checkAreaFormGrade}&unitId=${auth.$state.userInfo.topUnitId}`,
    {
      filename: props.fileName,
      method: 'POST',
      // body: JSON.stringify({ checkAreaFormGrade: checkAreaFormGrade }),
      contentType: 'application/json',
    }
  );
}, 5000);

function onNegativeClick() {
  emits('closeModal', false);
}
</script>

<style lang="scss" scoped>
.download {
  color: #3e62eb;
  cursor: pointer;
  margin-top: 15px;
  text-decoration: underline;
}
</style>
