<template>
  <n-modal v-model:show="showModal" title="检查事项" :mask-closable="false" style="width: 900px" preset="card">
    <n-data-table
      style="height: 560px"
      striped
      remote
      :columns="columns"
      :data="tableData"
      :bordered="false"
      :flex-height="true"
      :loading="loading"
      :pagination="pagination"
      :row-key="(row: any) => row.id"
    />
    <showHazard ref="showHazardRef" />
    <div class="mt-10 flex w-full justify-center">
      <n-button @click="showModal = false"> 关闭 </n-button>
    </div>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, defineProps, computed, watch, reactive, h, render } from 'vue';
import { selectCheckItemList } from '../fetchData';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination';
import { useStore } from '@/store';
import { NButton, NImageGroup, NImage } from 'naive-ui';
import showHazard from './show-hazard.vue';

const showHazardRef = ref();

const store = useStore();

const [loading, search] = useAutoLoading(false);

const tableData = ref([]);

const columns = [
  {
    title: '措施类型',
    key: 'measureName',
    rowSpan: (row) => {
      const measureName = row.measureName;
      let n = tableData.value.reduce((pre, cur) => {
        if (cur.measureName === measureName) pre++;
        return pre;
      }, 0);
      return n;
    },
  },
  {
    title: '检查事项',
    key: 'checkContent',
    render: (row: any) => row.checkContent.replace('["', '').replace('"]', ''),
  },
  {
    title: '图示',
    key: 'imageUrls',
    render: (row: any) => {
      const list = row.imageUrls.split(',');
      if (!list.length) return;
      return h(
        NImageGroup,
        {},
        list.map((item) => h(NImage, { src: item }))
      );
    },
  },
  {
    title: '常见隐患数',
    key: 'hazardCount',
    render: (row: any) => {
      if (!row.hazardCount) return 0;
      console.log(1231231);
      return h(
        'span',
        {
          class: 'p-1 text-[#0000ff] cursor-pointer',
          onClick: () => {
            showHazardRef.value.open(row);
            // checkItemListRef.value.open(row);
          },
        },
        row.hazardCount
      );
    },
  },
];

const getTableData = async () => {
  const params = {
    riskPointId,
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
  };
  tableData.value = [];
  const { data } = await search(selectCheckItemList(params));
  tableData.value = data.rows;
  console.log(data);
  updateTotal(data.total);
};
function getTableDataWrap() {
  pagination.page = 1;
  getTableData();
}
const { pagination, updateTotal } = useNaivePagination(getTableData);

const showModal = ref(false);

let riskPointId = '';
const open = (data: any) => {
  showModal.value = true;
  riskPointId = data.id;
  getTableDataWrap();
};

defineExpose({ open });
</script>

<style lang="scss" scoped></style>
