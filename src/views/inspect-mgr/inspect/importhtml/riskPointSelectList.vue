<template>
  <n-modal v-model:show="showModal" title="风险点库" :mask-closable="false" style="width: 900px" preset="card">
    <n-data-table
      style="height: 560px"
      striped
      remote
      v-model:checkedRowKeys="checkedRowKeys"
      :columns="columns"
      :data="tableData"
      :bordered="false"
      :flex-height="true"
      :loading="loading"
      :pagination="pagination"
      :row-key="(row: any) => row.id"
    />
    <checkItemList ref="checkItemListRef" />
    <div class="mt-10 flex w-full justify-center gap-2">
      <n-button @click="showModal = false"> 取消 </n-button>
      <n-button type="primary" :loading="submitLoading" @click="submit" :disabled="!checkedRowKeys.length">
        确定
      </n-button>
    </div>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, h } from 'vue';
import { queryDictDataList, riskIdentPage, importRisk } from '../fetchData';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination';
import { useStore } from '@/store';
import { NButton } from 'naive-ui';
import checkItemList from './checkItemList.vue';

const store = useStore();
const checkItemListRef = ref();

const [loading, search] = useAutoLoading(false);

const tableData = ref([]);
const labelMap = ref<any>({});
const checkedRowKeys = ref([]);

const emits = defineEmits(['close', 'importSuccess']);

const columns = [
  {
    type: 'selection',
  },
  {
    title: '风险点类型',
    key: 'riskPointType',
    render: (row: any) => {
      if (labelMap.value['dict_risk_point_type']) return labelMap.value['dict_risk_point_type'][row.riskPointType];
      return row.riskPointType;
    },
  },
  {
    title: '风险点名称',
    key: 'riskPointName',
  },
  {
    title: '辨识数据',
    key: 'bsData',
  },
  {
    title: '固有风险',
    key: 'riskLevel',
    render: (row: any) => {
      const bg = ['', '#D41111 ', '#E86608', '#E5A800', '#527CFF', '#527CFF'];
      return h(
        'div',
        {
          class: 'w-[100px] text-center h-[28px] rounded',
          style: `background: ${bg[row.riskLevel]}; line-height: 28px; color: #fff`,
        },
        {
          default: () => {
            if (labelMap.value['dict_risk_level']) return labelMap.value['dict_risk_level'][row.riskLevel];
            return row.riskLevel;
          },
        }
      );
    },
  },
  {
    title: '操作',
    key: 'actions',
    align: 'center',
    width: 120,
    render: (row: any) =>
      h(
        NButton,
        {
          onClick: () => checkItemListRef.value.open(row),
        },
        '检查事项'
      ),
  },
];

const queryDictDataListFn = async () => {
  const { data }: { data: any } = await queryDictDataList();
  data.forEach((item: any) => {
    if (!labelMap.value[item.dictType]) labelMap.value[item.dictType] = {};
    labelMap.value[item.dictType][item.dictValue] = item.dictLabel;
  });
};
const getTableData = async () => {
  const params = {
    unitId: store.userInfo.unitId,
    unitFlag: 1, //是否包含所有子级 0-否 1-是
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
  };
  const { data }: { data: any } = await search(riskIdentPage(params));
  tableData.value = data.rows;
  updateTotal(data.total);
};
function getTableDataWrap() {
  pagination.page = 1;
  getTableData();
}
const { pagination, updateTotal } = useNaivePagination(getTableData);

const showModal = ref(false);

let checkTableId = '';
let parentId = '';
const open = (formData: any) => {
  showModal.value = true;
  submitLoading.value = false;
  if (formData.parentId) {
    // pattern.value = '1';
    checkTableId = formData.checkTableId;
    parentId = formData.id;
  } else {
    checkTableId = formData.id;
    parentId = '0';
  }
  getTableDataWrap();
};
const submitLoading = ref(false);
const submit = async () => {
  const params = {
    checkTableId,
    parentId,
    riskPointIds: checkedRowKeys.value.join(','),
  };
  submitLoading.value = true;
  console.log(params);
  try {
    await importRisk(params);
    showModal.value = false;
    emits('importSuccess');
  } catch (error) {}
  submitLoading.value = false;
};

queryDictDataListFn();
defineExpose({ open });
</script>

<style lang="scss" scoped></style>
