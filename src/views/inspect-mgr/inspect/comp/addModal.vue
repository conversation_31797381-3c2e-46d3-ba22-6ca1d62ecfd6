<template>
  <ComDrawerA
    title="创建检查表"
    :autoFocus="false"
    :footerPaddingBottom="25"
    :maskNeedClosable="false"
    :show="props.showModal"
    show-action
    @handleNegative="handleClose"
    class="!w-[430px]"
  >
    <n-form
      style="padding: 24px"
      ref="formRef"
      :model="model"
      :rules="rules"
      label-width="auto"
      require-mark-placement="right-hanging"
      :style="{
        maxWidth: '820px',
      }"
    >
      <n-form-item label="检查表名称" path="checkTableName">
        <n-input v-model:value="model.checkTableName" :maxlength="50" placeholder="请输入检查表名称" />
      </n-form-item>
    </n-form>
    <template #footer>
      <div style="display: flex; justify-content: flex-end; padding: 0 24px">
        <n-button style="margin-right: 20px" type="info" @click="handleClose"> 取消 </n-button>
        <n-button type="primary" :disabled="submitLoading" :loading="submitLoading" @click="submit"> 保存 </n-button>
      </div>
    </template>
  </ComDrawerA>
</template>

<script setup lang="ts">
import ComDrawerA from '@/components/drawer/ComDrawerA.vue';
import { ref, defineProps, computed, watch, reactive, inject } from 'vue';
import { saveUpdInspect } from '../fetchData';
import { useMessage } from 'naive-ui';
import { useRoute } from 'vue-router';
import { useStore } from '@/store/index';
const { userInfo } = useStore();
const message = useMessage();

const emits = defineEmits(['closeModal']);
const props = defineProps({
  showModal: {
    type: Boolean,
    default: false,
  },
  formData: {
    type: Object,
    default: () => ({}),
  },
  // 是否可以修改
  editType: {
    type: Boolean,
    default: true,
  },
});

const showModal = computed({
  get: () => props.showModal,
  set: (val) => {
    emits('closeModal', false);
  },
});

const model = ref<any>({
  checkTableName: '',
  checkTableType: '1',
  checkAreaFormGrade: '2',
  createBy: userInfo?.id,
  createByName: userInfo?.userName,
  unitId: userInfo?.unitId,
  unitName: userInfo?.unitName,
  zhId: userInfo?.zhId,
});

const resetForm = () => {
  model.value = {
    checkTableName: '',
    checkAreaFormGrade: '2',
    checkTableType: '1',
  };
};
watch(
  () => props.showModal,
  (newVal) => {
    if (!newVal) {
      resetForm();
    }
  }
);
const rules = {
  checkTableName: {
    required: true,
    trigger: ['blur', 'input'],
    message: '请输入检查表名称',
  },
};

const formRef = ref<any>(null);

const submitLoading = ref<boolean>(false);
const doHandle = inject('doHandle');
function submit(e: MouseEvent) {
  console.log(model.value, 'model');
  e.preventDefault();
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      submitLoading.value = true;
      let data = {
        ...model.value,
        unitId: useStore().userInfo.unitId,
      };
      //分管单位ID
      // if (!data.id) data['unitId'] = useStore().userInfo.unitId;
      saveUpdInspect(data)
        .then(() => {
          message.success('新增成功');
          submitLoading.value = false;
          doHandle();
          handleClose();
        })
        .catch(() => {
          submitLoading.value = false;
        });
    }
  });
}

function handleClose() {
  emits('closeModal', false);
  emits('update:show' as any, false);
}

defineOptions({ name: 'RegionInspectAddModal' });
</script>
