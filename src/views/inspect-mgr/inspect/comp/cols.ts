import { h } from 'vue';
import GradeTag from '@/views/inspect-mgr/inspect/checkitems/table/GradeTag.vue';

export const poiontTableCols = [
  {
    title: '序号',
    key: 'serialNumber',
    width: '80',
    align: 'center',
  },
  {
    title: '检查内容',
    key: 'checkContent',
  },
];

export const singleTableCols = [
  {
    title: '序号',
    key: 'serialNumber',
    width: '80',
    align: 'center',
  },
  {
    title: '检查内容',
    key: 'checkDetail',
  },
  {
    title: '常见隐患',
    key: 'areaEssentialFactorList',
    render: (rowData: any) => {
      return h(GradeTag, {
        yhlist:
          rowData?.areaEssentialFactorList && rowData?.areaEssentialFactorList.length > 3
            ? rowData?.areaEssentialFactorList.slice(0, 3)
            : rowData?.areaEssentialFactorList,
        parentId: rowData.parentId,
        id: rowData.id,
        dataList: rowData?.areaEssentialFactorList,
      });
    },
  },
];

export const multiTableCols = [
  {
    title: '序号',
    key: 'serialNumber',
    width: '80',
    align: 'center',
  },
  {
    title: '检查项',
    key: 'checkContent',
  },
  {
    title: '常见隐患',
    key: 'serialNumber',
    render: (rowData: any) => {
      if (rowData.areaEssentialFactorList) {
        return h(GradeTag, {
          yhlist: rowData?.areaEssentialFactorList,
          parentId: rowData.parentId,
          id: rowData.id,
          dataList: rowData?.areaEssentialFactorList,
          yhlistTotal: rowData?.areaEssentialFactorList.length,
        });
      } else {
        return h(
          'span',
          `总计：${
            rowData.childCheckAreaList && rowData.childCheckAreaList.length ? rowData.childCheckAreaList.length : 0
          }条`
        );
      }
    },
  },
];
