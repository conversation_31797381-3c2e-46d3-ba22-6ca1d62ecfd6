<template>
  <n-modal
    :show="show"
    :show-icon="false"
    preset="card"
    class="models"
    :mask-closable="false"
    :on-close="closeDialog"
    style="width: 1200px"
    :autoFocus="false"
  >
    <template #header>
      <div class="flex flex-row items-center">
        <img class="w-[17px] h-[12px] mr-[20px]" src="@/components/header/assets/icon-title-arrow3.png" />
        <div class="text-[16px] text-[#222222] font-bold">检查表详情</div>
      </div>
    </template>
    <template #default>
      <div class="p-[24px]">
        <n-form label-placement="left" label-align="left" :model="detailInfo">
          <n-grid :x-gap="8" :cols="24">
            <n-gi :span="8">
              <n-form-item label="检查表名称:">
                <n-tooltip>
                  <template #trigger>
                    <div class="text-ellipsis text-nowrap overflow-hidden w-[200px]">
                      {{ detailInfo.checkTableName }}
                    </div>
                  </template>
                  {{ detailInfo.checkTableName }}
                </n-tooltip>
              </n-form-item>
            </n-gi>
            <n-gi :span="8">
              <n-form-item label="编制单位:"> {{ detailInfo.unitName || '--' }} </n-form-item>
            </n-gi>
            <n-gi :span="8">
              <n-form-item label="编制时间:"> {{ detailInfo.createTime || '--' }} </n-form-item>
            </n-gi>
          </n-grid>
        </n-form>
        <n-data-table
          class="mt-[20px]"
          :columns="columns"
          :data="tableData"
          :bordered="true"
          :min-height="500"
          :max-height="500"
          :single-line="false"
        ></n-data-table>
      </div>
    </template>
  </n-modal>
  <detailModal
    :showModal="showDetail"
    @closeModal="closeModal"
    :formData="formData"
    :type="1"
    modalTitle="检查项详情"
  />
</template>
<script setup lang="ts">
import { ref, watch, inject, h } from 'vue';
import { getCheckTableDetail, getCheckAreaDetail } from '../checklist/tabel/fetchData';
import { flatData } from './utils';
import detailModal from '@/views/inspect-mgr/inspect/point-inspect/detail/detailModal.vue';
import { NButton } from 'naive-ui';
import GradeTag from '@/views/inspect-mgr/inspect/checkitems/table/GradeTag.vue';

const cancel = inject('closeDetail');
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  row: {
    type: Object,
    default: () => {},
  },
});

const detailInfo: any = ref({});

const poiontTableCols = [
  {
    title: '序号',
    key: 'serialNumber',
    width: '80',
    align: 'center',
  },
  {
    title: '检查内容',
    key: 'checkContent',
  },
];

const multiTableCols = [
  {
    title: '序号',
    key: 'serialNumber',
    width: '80',
    align: 'center',
  },
  {
    title: '检查项',
    key: 'checkContent',
    render: (row: any) => {
      return row.parentId == '0' ? row.checkContent : row.checkDetail;
    },
  },
  {
    title: '常见隐患',
    key: 'serialNumber',
    render: (rowData: any, index: number) => {
      return h(GradeTag, {
        yhlistTotal: rowData?.areaEssentialFactorList?.length,
        yhlist:
          rowData?.areaEssentialFactorList && rowData?.areaEssentialFactorList.length > 3
            ? rowData?.areaEssentialFactorList.slice(0, 3)
            : rowData?.areaEssentialFactorList,
        parentId: rowData.parentId,
        id: rowData.id,
        dataList: tableData.value,
      });
    },
  },
];

let columns: any = [];
const setColumns = () => {
  if (props.row.checkAreaFormGrade === 2 && props.row.checkTableType === 1) {
    columns = [
      ...multiTableCols,
      {
        title: '操作',
        key: 'checkContent',
        width: '120',
        render(row: any) {
          if (row.parentId !== '0') {
            return h(
              NButton,
              {
                type: 'primary',
                ghost: true,
                size: 'small',
                class: 'com-action-button',
                onClick: async () => {
                  await getCheckContent(row.id);
                  showDetail.value = true;
                },
              },
              { default: () => '详情' }
            );
          }
        },
      },
    ];
  } else {
    //点位巡查表
    columns = poiontTableCols;
  }
};
const closeDialog = () => {
  detailInfo.value = {};
  tableData.value = [];
  cancel();
};

const tableData: any = ref([]);

const getDetailInfo = async () => {
  const res: any = await getCheckTableDetail({
    id: props.row.id,
  });
  detailInfo.value = res.data;
  tableData.value = flatData(res.data.checkAreaList);
};

const getCheckContent = async (id: string) => {
  const res: any = await getCheckAreaDetail({ id });
  formData.value = res.data;
};

const changeClass = (row: any, index: number) => {
  if (row.parentId === '0' && props.row.checkAreaFormGrade === 2) {
    return 'parent';
  }
  return '';
};

const showDetail = ref(false);
const formData = ref({});
function closeModal(value: boolean) {
  showDetail.value = false;
}

watch(
  () => props.show,
  async (newVal) => {
    if (newVal) {
      setColumns();
      await getDetailInfo();
    }
  }
);
</script>

<style lang="scss" scoped>
.header {
  position: relative;

  &::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 18px;
    background-color: #1890ff;
    position: absolute;
    top: 5px;
    left: -10%;
  }
}

:deep(.parent td) {
  background-color: rgba(215, 241, 253, 1) !important;
}
</style>
