<template>
  <n-form :model="filterForm" label-placement="left" inline :show-feedback="false">
    <n-grid :x-gap="12" :y-gap="8" :cols="5">
      <n-gi class="flex items-center" :span="2">
        <n-form-item label="编制单位:" class="flex-1">
          <n-select
            v-model:value="searchUnitId"
            :options="options"
            clearable
            class="flex items-center w-full"
            label-field="orgName"
            value-field="orgCode"
            placeholder="全部"
            @update:value="searchUnitIdChange"
            :disabled="standard"
          />
        </n-form-item>
        <n-checkbox v-model:checked="standard" :on-update:checked="handleCheckbox">仅看本单位</n-checkbox>
      </n-gi>
      <n-gi>
        <n-form-item label="状态:">
          <n-select
            v-model:value="filterForm.checkStatus"
            :options="checkStatusOptions"
            class="flex items-center"
            placeholder="全部"
            clearable
            @update:value="doHandle(ACTION.SEARCH)"
          />
        </n-form-item>
      </n-gi>
      <n-gi>
        <n-form-item label="">
          <n-input
            placeholder="请输入检查表名称模糊搜索"
            class="flex items-center"
            maxlength="50"
            show-count
            v-model:value="filterForm.checkTableName"
            clearable
            @update:value="doHandle(ACTION.SEARCH)"
          />
        </n-form-item>
      </n-gi>
      <n-gi>
        <div class="flex justify-end">
          <n-button type="primary" @click="addCheckTable" v-if="btnAuths.includes('addCheckList')">
            <IconAdd />
            创建检查表
          </n-button>
        </div>
      </n-gi>
    </n-grid>
  </n-form>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { ACTION, ACTION_LABEL } from '../constant';
import { FlFilledAdd as IconAdd } from '@kalimahapps/vue-icons';
import { useRouter } from 'vue-router';
import { useStore } from '@/store';
import { getUpAndDownUnitInfo } from '../checklist/tabel/fetchData';
const { userInfo } = useStore();
const router = useRouter();
const emits = defineEmits(['action']);
const filterForm = ref({
  unitId: null,
  checkTableType: null,
  checkTableName: '',
  checkStatus: null,
});

const searchUnitId = ref(null);
const standard = ref(false);
// 列表按钮权限
let btnAuths = userInfo.resourceVoList
  .find((item: any) => item.resUrl === 'checkList')
  ?.childrens.filter((item: any) => item.resType == '2')
  .map((item: any) => item.resUrl);

console.log('🚀 ~ btnA11uths:', btnAuths);
const searchUnitIdChange = (val: any) => {
  if (val) {
    filterForm.value.unitId = val;
  } else {
    filterForm.value.unitId = unitIds.value;
  }
  doHandle(ACTION.SEARCH);
};

const handleCheckbox = (val: any) => {
  standard.value = val;
  searchUnitId.value = null;
  if (val) {
    filterForm.value.unitId = userInfo.unitId;
  } else {
    filterForm.value.unitId = unitIds.value;
  }
  doHandle(ACTION.SEARCH);
};

const addCheckTable = async () => {
  router.push({ name: 'inspectPointAdd' });
};

const unitIds = ref('');

function doHandle(action: ACTION) {
  emits('action', {
    action: action,
    data: filterForm.value,
  });
}

const checkStatusOptions = [
  {
    label: '正常',
    value: 0,
  },
  {
    label: '停用',
    value: 1,
  },
  {
    label: '草稿',
    value: 2,
  },
];

//编制单位
const options: any = ref([]);
const getOptions = async () => {
  const res: any = await getUpAndDownUnitInfo({
    unitId: userInfo.unitId,
  });
  options.value = res.data;
  unitIds.value = (res.data && res.data.map((i: any) => i.orgCode).join(',')) || '';

  filterForm.value.unitId = unitIds.value;

  options.value.unshift({ orgCode: unitIds.value, orgName: '全部' });
  doHandle(ACTION.SEARCH);
};
getOptions();

defineOptions({ name: 'checkTempFilterComp' });

defineExpose({
  filterForm,
});
</script>
<style lang="scss" scoped></style>
