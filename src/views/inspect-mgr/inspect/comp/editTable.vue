<template>
  <n-form-item label="检查内容" class="action_list" label-width="100px">
    <n-button type="primary" @click="importExcel">导入</n-button>
  </n-form-item>

  <n-data-table
    class="mt-[15px] com-table"
    :columns="columns"
    :data="tableData"
    min-height="460px"
    max-height="460px"
  ></n-data-table>

  <n-button @click="handleAdd" style="width: 100%; margin: 20px 0">
    <template #icon>
      <IconAdd />
    </template>
    新增一行</n-button
  >
  <templateImport
    v-model:show="showDialog"
    :formData="row"
    fileName="巡查点位检查表导入模板.xlsx"
    v-if="showDialog"
    @importSuccess="importSuccess"
  />
</template>
<script setup>
import { reactive, onBeforeMount, onMounted, h, ref, computed } from 'vue';
import { NInput, NFormItem } from 'naive-ui';
import { $dialog } from '@/common/shareContext/useDialogCtx.ts';
import templateImport from 'src/views/inspect-mgr/inspect/importhtml/templateImport.vue';
import { FlFilledAdd as IconAdd } from '@kalimahapps/vue-icons';
import { getsaveOrUpdateApi, checkAreaDelete } from '../fetchData';
import { getCheckTableDetail } from '../checklist/tabel/fetchData';
import { useRoute } from 'vue-router';
import { useMessage } from 'naive-ui';
const props = defineProps({
  data: {
    type: Array,
    default: () => [],
  },
  checkTabelID: {
    type: String,
    default: () => '',
  },
});
const emit = defineEmits(['update:data']);
const message = useMessage();
const tableData = ref([]);

const route = useRoute();
const showDialog = ref(false);

const columns = [
  {
    title: '序号',
    key: 'index',
    align: 'center',
    width: '90',
    render(row, index) {
      return h(
        'div',
        {},
        {
          default: () => index + 1,
        }
      );
    },
  },
  {
    title: '检查内容',
    key: 'checkContent',
    render: (row, index) => {
      return h(NInput, {
        value: row.checkContent,
        maxlength: 200,
        showCount: true,
        placeholder: '请输入检查内容',
        // status: row.checkContent ? 'success' : 'error',
        onBlur: async () => {
          if (!row.checkContent) {
            return;
          }
          const res = await getsaveOrUpdateApi({
            checkContent: row.checkContent,
            checkTableId: props.checkTabelID,
            checkTableType: 2,
            id: row.id,
          });
        },
        onUpdateValue: (value) => {
          row.checkContent = value;
        },
      });
    },
  },
  {
    title: '操作',
    key: 'action',
    width: '100',
    align: 'center',
    render: (row, index) => {
      return h(
        'div',
        {
          style: {
            color: '#1890ff',
            cursor: 'pointer',
          },
          onClick: () => {
            $dialog.warning({
              title: '提示',
              content: '确定删除该检查项吗？删除后不可恢复',
              positiveButtonProps: { type: 'primary', color: '#527cff' },
              positiveText: '确定',
              negativeText: '取消',
              transformOrigin: 'center',
              onPositiveClick: async () => {
                if (row.id) {
                  checkAreaDelete({
                    id: row.id,
                    checkTableId: props.checkTabelID,
                    checkTableType: 2,
                  }).then((res) => {
                    getDetailInfo();
                  });
                } else {
                  tableData.value.splice(index, 1);
                }
              },
            });
          },
        },
        {
          default: () => ['删除'],
        }
      );
    },
  },
];

const handleAdd = () => {
  // 校验检查项是否填写
  const checkContent = tableData.value.find((item) => !item.checkContent);
  if (checkContent) {
    return message.warning('请先填写检查内容');
  }
  tableData.value.push({
    checkContent: '',
  });
};

let row = reactive({
  checkAreaFormGrade: '',
  id: '',
});

const importExcel = () => {
  row.checkAreaFormGrade = '3';
  row.id = route.params.id || props.checkTabelID;
  showDialog.value = true;
};
onMounted(() => {
  getDetailInfo();
});

const getDetailInfo = async () => {
  const res = await getCheckTableDetail({
    id: props.checkTabelID,
  });
  tableData.value = res.data.checkAreaList;
};

function importSuccess() {
  getDetailInfo();
  showDialog.value = false;
}

defineExpose({
  tableData,
});
</script>

<style lang="scss" scoped>
/* .action_list {
    display: flex; */

:deep(.n-form-item-blank) {
  display: flex;
  width: 100%;
  justify-content: flex-end;
  gap: 10px;
}

/* } */
</style>
