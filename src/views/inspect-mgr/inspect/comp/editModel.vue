<template>
  <div class="com-g-row-a1 h-full">
    <com-bread :data="breadData" />
    <div class="com-table-container">
      <n-steps v-model:current="current" :on-update:current="updatecurrent">
        <n-step title="创建检查表" />
        <n-step title="添加检查项" />
      </n-steps>
      <view v-if="current === 1">
        <!-- 添加检查表 -->
        <n-form
          style="padding: 24px; margin: auto; margin-top: 5%"
          ref="formRef"
          :model="model"
          :rules="rules"
          label-width="auto"
          require-mark-placement="right-hanging"
          :style="{
            maxWidth: '820px',
          }"
        >
          <n-form-item label="检查表名称" path="checkTableName">
            <n-input v-model:value="model.checkTableName" :maxlength="50" placeholder="请输入检查表名称" />
          </n-form-item>
        </n-form>
        <div style="display: flex; justify-content: center; padding: 0 24px">
          <n-button style="margin-right: 20px" type="info" @click="cancelSubmit"> 取消 </n-button>
          <n-button type="primary" :disabled="submitLoading" :loading="submitLoading" @click="submit">
            下一步
          </n-button>
        </div>
      </view>
      <view v-if="current === 2">
        <editTable v-if="model.checkTableType === '2'" ref="editTableRef" :checkTabelID="checkTabelID"> </editTable>
        <TableList
          v-if="model.checkTableType === '1'"
          :checkAreaFormGrade="model.checkAreaFormGrade"
          :_checkTabelID="checkTabelID"
          :row="model"
          :data="data"
          ref="tableListRef"
        />
        <div style="display: flex; justify-content: center; padding: 0 24px; margin-top: 8px">
          <n-button style="margin-right: 20px" type="info" @click="backStep"> 上一步 </n-button>
          <n-button v-if="checkStatus != 0" style="margin-right: 20px" type="warning" @click="confirm('2')">
            暂存为草稿
          </n-button>
          <n-button type="primary" :disabled="submitLoading" :loading="submitLoading" @click="confirm('0')">
            确认
          </n-button>
        </div>
      </view>
    </div>
  </div>
</template>
<script setup lang="ts">
import ComBread from '@/components/breadcrumb/ComBread.vue';
import { nextTick, onMounted, reactive, ref, watch } from 'vue';
import { getstopOrStartCheckTableApi, saveUpdInspect } from '../fetchData';
import editTable from './editTable.vue';
import TableList from '../checkitems/table/Table.vue';
import { useMessage } from 'naive-ui';
import { useRoute, useRouter } from 'vue-router';
import { useStore } from '@/store/index';
import { getCheckTableDetail } from '../checklist/tabel/fetchData';
import { $toast } from '@/common/shareContext/useToastCtx';
const { userInfo } = useStore();
const message = useMessage();
const route = useRoute();
const router = useRouter();
const editTableRef = ref();
const breadData = ref([
  { name: '隐患治理系统' },
  {
    name: '检查表',
    clickable: true,
    routeRaw: {
      name: 'checkList',
    },
  },
  { name: route.params?.id ? '编辑检查表' : '创建检查表' },
]);
const tableListRef = ref();
// 步骤
const current = ref<number>(1);
function updatecurrent(val: number) {}

const model = ref<any>({
  checkTableName: '',
  checkTableType: '1',
  checkAreaFormGrade: '2',
  createBy: userInfo?.id,
  createByName: userInfo?.userName,
  unitId: userInfo?.unitId,
  unitName: userInfo?.unitName,
  zhId: userInfo?.zhId,
});

onMounted(async () => {
  if (route.params?.id) getDetail();
});

const checkStatus = ref(-1); //0-正常;1-停用;2-草稿
const oldCheckTableName = ref('');

const getDetail = async () => {
  const res: any = await getCheckTableDetail({ id: route.params?.id });
  model.value.checkTableName = oldCheckTableName.value = res.data.checkTableName;
  model.value.checkAreaFormGrade = String(res.data.checkAreaFormGrade);
  model.value.checkTableType = String(res.data.checkTableType);
  checkTabelID.value = model.value.id = res.data.id;
  checkStatus.value = res.data.checkStatus;
};

const rules = {
  checkTableName: {
    required: true,
    trigger: ['blur', 'input'],
    message: '请输入检查表名称',
  },
};

const formRef = ref<any>(null);
const checkTabelID = ref('');
const submitLoading = ref<boolean>(false);
function submit(e: MouseEvent) {
  e.preventDefault();
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      submitLoading.value = true;
      let data = {
        ...model.value,
        unitId: useStore().userInfo.unitId,
      };
      // 判断检查表名称是否改变
      if (data.checkTableName != oldCheckTableName.value) {
        saveUpdInspect(data)
          .then((res: any) => {
            if (res.code == 'success') {
              nextTick(() => {
                oldCheckTableName.value = data.checkTableName;
                checkTabelID.value = res.data;
                current.value = 2;
              });
            }
            submitLoading.value = false;
          })
          .catch(() => {
            submitLoading.value = false;
          });
      } else {
        // $toast.warning('请修改检查表名称');
        current.value = 2;
        submitLoading.value = false;
      }
    }
  });
}

const backStep = () => {
  current.value = 1;
};

const cancelSubmit = () => {
  router.push({ name: 'checkList', query: {}, replace: true });
};

const confirm = async (val: any) => {
  let checkTableData = tableListRef.value.tableData;
  if (!checkTableData.length) {
    return $toast.warning('请添加检查表内容');
  }
  for (let i of checkTableData) {
    if (
      (i.parentId == '0' && i.childCheckAreaList == null) ||
      (i.childCheckAreaList && i.childCheckAreaList.length == 0) ||
      i.checkContent.length == 0
    ) {
      return $toast.warning('检查内容未添加');
    }
  }
  await getstopOrStartCheckTableApi({ id: checkTabelID.value, checkStatus: val });
  message.success(val == '2' ? '保存草稿成功' : '保存成功');
  setTimeout(() => {
    router.push({ name: 'checkList', query: {}, replace: true });
  }, 500);
};
</script>
<style lang="scss" scoped>
::v-deep {
  .n-steps {
    width: 40%;
    display: flex;
    margin: auto;
  }
}
</style>
