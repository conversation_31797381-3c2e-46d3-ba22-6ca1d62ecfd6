//数组对象扁平化处理方法
export const flatData = (data: any[]) => {
  return data.reduce((prev: any[], curr: any) => {
    if (curr.childCheckAreaList && curr.childCheckAreaList.length > 0) {
      prev.push(curr);
      prev = prev.concat(flatData(curr.childCheckAreaList));
      // curr.childCheckAreaList = curr.childCheckAreaList.length;
    } else {
      prev.push(curr);
    }
    return prev;
  }, []);
};

//将数组转为树形结构
export const convert = (list: any) => {
  const res = [];
  const map = list.reduce((res: any, v: any) => ((res[v.id] = v), (v.childCheckAreaList = []), res), {});
  for (const item of list) {
    if (item.parentId === '') {
      res.push(item);
      continue;
    }
    if (item.parentId in map) {
      const parent = map[item.parentId];
      parent.childCheckAreaList = parent.childCheckAreaList || [];
      parent.childCheckAreaList.push(item);
    }
  }
  return res;
};
