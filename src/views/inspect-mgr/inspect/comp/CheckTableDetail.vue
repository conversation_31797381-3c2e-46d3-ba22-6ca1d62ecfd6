<template>
  <el-dialog
    v-model="dialogVisible"
    destroy-on-close
    width="65vw"
    align-center
    :show-close="false"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    append-to-body
  >
    <template #header>
      <div class="flex justify-between items-center">
        <div class="flex justify-start items-center">
          <img src="@/assets/title_ico.png" style="width: 17px" alt="" />
          <div style="font-size: 16px" class="ml-2 text-black">检查表详情</div>
        </div>
        <img src="@/assets/dialog_close.png" style="width: 42px" class="cursor-pointer" alt="" @click="closeDialog" />
      </div>
    </template>
    <div class="bg-white rounded p-4">
      <el-row :gutter="15">
        <el-col :span="8">
          <el-form-item label="检查表名称:">
            {{ detailInfo.checkTableName || '--' }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="编制单位:">
            {{ detailInfo.unitName || '--' }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="编制时间:">
            {{ detailInfo.createTime || '--' }}
          </el-form-item>
        </el-col>
      </el-row>
      <n-data-table
        class="mt-[20px]"
        :columns="columns"
        :data="tableData"
        :bordered="true"
        min-height="45vh"
        max-height="45vh"
        :single-line="false"
      />
    </div>
  </el-dialog>
  <CheckItemDetail
    :showModal="showDetail"
    modalTitle="检查项详情"
    @closeModal="closeModal"
    :formData="formData"
    :type="1"
  />
</template>
<script setup lang="ts">
import { ref, watch, h, computed } from 'vue';
import { getCheckTableDetail, getCheckAreaDetail } from '../checklist/tabel/fetchData';
import { flatData } from './utils';
import CheckItemDetail from '../checkitems/table/CheckItemDetail.vue';
import { NButton } from 'naive-ui';
import GradeTag from '@/views/inspect-mgr/inspect/checkitems/table/GradeTag.vue';

const emits = defineEmits(['close']);
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  row: {
    type: Object,
    default: () => {},
  },
});

const dialogVisible = computed(() => {
  return props.show;
});

const detailInfo: any = ref({});

const poiontTableCols = [
  {
    title: '序号',
    key: 'serialNumber',
    width: '80',
    align: 'center',
  },
  {
    title: '检查内容',
    key: 'checkContent',
  },
];

const multiTableCols = [
  {
    title: '序号',
    key: 'serialNumber',
    width: '80',
    align: 'center',
  },
  {
    title: '检查项',
    key: 'checkContent',
    render: (rowData: any) => {
      return h('span', rowData.parentId == '0' ? rowData.checkContent : rowData.checkDetail);
    },
  },
  // {
  //   title: '常见隐患',
  //   key: 'serialNumber',
  //   render: (rowData: any, index: number) => {
  //     return h(GradeTag, {
  //       yhlistTotal: rowData?.areaEssentialFactorList?.length,
  //       yhlist:
  //         rowData?.areaEssentialFactorList && rowData?.areaEssentialFactorList.length > 3
  //           ? rowData?.areaEssentialFactorList.slice(0, 3)
  //           : rowData?.areaEssentialFactorList,
  //       parentId: rowData.parentId,
  //       id: rowData.id,
  //       dataList: tableData.value,
  //     });
  //   },
  // },
];

let columns: any = [];
const setColumns = () => {
  if (props.row.checkAreaFormGrade === 2 && props.row.checkTableType === 1) {
    columns = [
      ...multiTableCols,
      {
        title: '操作',
        key: 'checkContent',
        width: '120',
        render(row: any) {
          if (row.parentId !== '0') {
            return h(
              NButton,
              {
                type: 'primary',
                ghost: true,
                size: 'small',
                class: 'com-action-button',
                onClick: async () => {
                  await getCheckContent(row.id);
                  showDetail.value = true;
                },
              },
              { default: () => '详情' }
            );
          }
        },
      },
    ];
  } else {
    //点位巡查表
    columns = poiontTableCols;
  }
};
const closeDialog = () => {
  emits('close');
};

const tableData: any = ref([]);

const getDetailInfo = async () => {
  const res: any = await getCheckTableDetail({
    id: props.row.id,
  });
  detailInfo.value = res.data;
  tableData.value = flatData(res.data.checkAreaList);
  console.log(tableData.value);
};

const getCheckContent = async (id: string) => {
  const res: any = await getCheckAreaDetail({ id });
  formData.value = res.data;
};

const showDetail = ref(false);
const formData = ref({});
function closeModal(value: boolean) {
  showDetail.value = false;
}

watch(
  () => props.show,
  async (newVal) => {
    if (newVal) {
      setColumns();
      await getDetailInfo();
    }
  }
);
</script>

<style lang="scss" scoped>
.header {
  position: relative;

  &::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 18px;
    background-color: #1890ff;
    position: absolute;
    top: 5px;
    left: -10%;
  }
}

:deep(.parent td) {
  background-color: rgba(215, 241, 253, 1) !important;
}
</style>
