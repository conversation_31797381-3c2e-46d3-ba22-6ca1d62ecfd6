import { api } from '@/api';
import { IObj } from '@/types';
import { $http } from '@tanzerfe/http';

// 新增编辑检查表
export function saveUpdInspect(data: object) {
  const url = api.getUrl(api.type.hazard, api.zyl.checklist.hazardCheckTableSaveUpdate);
  return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: false }, ...data } });
}

// 新增编辑检查内容
export function getsaveOrUpdateApi(data: any) {
  const url = api.getUrl(api.type.hazard, api.zyl.checklist.saveOrUpdate);
  return $http.post(url, { data: { _cfg: { showTip: true }, ...data } });
}
// 检查表列表
export function getlistPage(data: any) {
  const url = api.getUrl(api.type.hazard, api.zyl.checklist.getChecklist);
  return $http.post(url, { data: { _cfg: { showTip: true }, ...data } });
}
// /hazardCheckTable/stopOrStartCheckTable  停用、启用
export function getstopOrStartCheckTableApi(data: any) {
  const url = api.getUrl(api.type.hazard, api.zyl.checklist.getstopOrStartCheckTable, { ...data });
  return $http.post(url, { data: { _cfg: { showTip: true }, ...data } });
}
// 删除
export function getdeleteApi(data: any) {
  const url = api.getUrl(api.type.hazard, api.zyl.checklist.getdelete, { ...data });
  return $http.post(url, { data: { _cfg: { showTip: true }, ...data } });
}

// 导入模板导出
export function checkAreaExportTempalte() {
  return api.getUrl(api.type.hazard, api.zyl.checklist.hazardCheckAreaExportTempalte);
}

// 风险库导入Api=====begin=======
export function queryDictDataList() {
  const url = api.getUrl(api.type.rmc, api.taskManagement.queryDictDataList);
  return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: false } } });
}
export function riskIdentPage(param: object) {
  const url = api.getUrl(api.type.rmc, api.taskManagement.riskIdentPage, param);
  return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: false }, ...param } });
}
export function importRisk(param: object) {
  const url = api.getUrl(api.type.hazard, api.taskManagement.importRisk, param);
  return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: false }, ...param } });
}
export function selectCheckItemList(param: object) {
  const url = api.getUrl(api.type.rmc, api.taskManagement.selectCheckItemList, param);
  return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: false }, ...param } });
}
export function selectHazardDetail(param: object) {
  const url = api.getUrl(api.type.rmc, api.taskManagement.selectHazardDetail, param);
  return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: false } } });
}
// 风险库导入Api=====end=======

// 隐患库列表
export function factorList(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.zyl.checklist.hazardEssentialFactorList);
  return $http.post(url, { data: { _cfg: { showTip: true }, ...data } });
}
// 从隐患库导入
export function checkAreaEssentImport(param: object) {
  const url = api.getUrl(api.type.hazard, api.zyl.checklist.hazardCheckAreaEssentImport, param);
  return $http.post(url, { data: { _cfg: { showTip: true }, ...param } });
}

// 隐患库列表
export function hazardEssentialFactorClassItemListPage(data: IObj<any>) {
  // const url = api.getUrl(api.type.hazard, api.ww.paramsConf.hazardEssentialFactorClassItemListPage);
  // return $http.post(url, {
  //   data: { _cfg: { showTip: true }, ...data },
  // });
  const url = api.getUrl(api.type.hazard, api.zyl.checklist.hazardEssentialFactorClassItemListPage);
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}
export function getTreeList(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.getTreeList);
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}
// // 计划列表
// export function getPageList(query: IObj<any>) {
//   const url = api.getUrl(api.type.hazard, api.zyl.checklist.getChecklist);
//   return $http.post<any>(url, {
//     data: { _cfg: { showTip: true }, ...query },
//   });
// }

export function checkAreaDelete(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.zyl.checklist.checkAreaDelete);
  return $http.post(url, { data: { _cfg: { showTip: true }, ...data } });
}

// 获取隐患等级
export function postGetHazardGradeListAPI(params: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.zyl.checklist.hazardGradeListNew);
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...params },
  });
}

export function exportCheckAreaByTableId() {
  const url = api.getUrl(api.type.hazard, '/hazardCheckTable/exportCheckAreaByTableId');
  return url;
}

// 同步至本单位
export function syncDataToCurrentUnit(params: IObj<any>) {
  const url = api.getUrl(api.type.hazard, '/hazardCheckTable/syncDataToCurrentUnit');
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...params },
  });
}
