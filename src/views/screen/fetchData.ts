import { ICheckTempPageRes, ICheckItem, ICheckCategoryTree, IFormData } from './type';
import { IObj } from '@/types';
import { PageList, statisticsData } from './type';
import { api } from '@/api';
import { $http } from '@tanzerfe/http';

// 随手拍列表页
export function getDataList(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.name.demoRequest.getDataList);
  return $http.post<PageList>(url, { data: { _cfg: { showTip: true }, ...data } });
}

// 随手拍统计
export function getStatisticsData(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.name.demoRequest.getStatisticsData);
  return $http.post<statisticsData>(url, { data: { _cfg: { showTip: true }, ...query } });
}

// 随手拍详情
export function get(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.name.demoRequest.getStatisticsData);
  return $http.post<statisticsData>(url, { data: { _cfg: { showTip: true }, ...query } });
}

// 删除模板
export function deleteTemp(query: IObj<any>) {
  const url = api.getUrl(api.type.demo, api.name.demo.tempDelete, query);
  return $http.get(url, { data: { _cfg: { showTip: true, showOkTip: true } } });
}

// 获取检查类别树
export function queryCategoryTree(query?: IObj<any>) {
  const url = api.getUrl(api.type.demo, api.name.demo.queryCategoryTree, query);
  return $http.get<ICheckCategoryTree[]>(url, { data: { _cfg: { showTip: true } } });
}

// 获取检查模板项
export function queryInspectTemplateItems(query: IObj<any>) {
  const url = api.getUrl(api.type.demo, api.name.demo.queryInspectItems, query);
  return $http.get<ICheckItem[]>(url, { data: { _cfg: { showTip: true } } });
}

// 创建检查模板
export function saveTemplate(data: IObj<any>) {
  const url = api.getUrl(api.type.demo, api.name.demo.saveTemplate, data);
  return $http.post<IFormData>(url, { data: { _cfg: { showTip: true, showOkTip: true }, ...data } });
}

// 模板详情
export function getTemplateDetail(query: IObj<any>) {
  const url = api.getUrl(api.type.demo, api.name.demo.queryDetailTemplate, query);
  return $http.get<IFormData>(url, { data: { _cfg: { showTip: true } } });
}

// 模板详情已选类别树
export function detailCategoryTree(query: IObj<any>) {
  const url = api.getUrl(api.type.demo, api.name.demo.detailCategoryTree, query);
  return $http.get<ICheckCategoryTree[]>(url, { data: { _cfg: { showTip: true } } });
}

// 模板详情——检查项
export function queryTemplateDetailItems(query: IObj<any>) {
  const url = api.getUrl(api.type.demo, api.name.demo.queryTemplateDetailItems, query);
  return $http.get<ICheckItem[]>(url, { data: { _cfg: { showTip: true } } });
}

// 编辑 模板详情
export function queryUpdateTemplateDetail(query: IObj<any>) {
  const url = api.getUrl(api.type.demo, api.name.demo.queryUpdateTemplateDetail, query);
  return $http.get<IFormData>(url, { data: { _cfg: { showTip: true } } });
}

// 更新检查模板
export function updateTemplate(data: IObj<any>) {
  const url = api.getUrl(api.type.demo, api.name.demo.updateTemplate, data);
  return $http.post<IFormData>(url, { data: { _cfg: { showTip: true, showOkTip: true }, ...data } });
}
