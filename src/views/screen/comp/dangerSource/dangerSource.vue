<template>
  <div class="chart-wrapper">
    <div ref="chartContainer" class="chart-container"></div>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, onUnmounted, ref } from 'vue';
import { PieData, SeriesItem, Series } from '../../type';
import * as echarts from 'echarts';
import 'echarts-gl'; // 确保引入了 echarts-gl

export default defineComponent({
  name: 'Pie3DChart',
  setup() {
    const chartContainer = ref<HTMLDivElement | null>(null);
    let myChart: echarts.ECharts | null = null;

    function getParametricEquation(startRatio: number, endRatio: any, isSelected: any, isHovered: any, k: any, h: any) {
      const midRatio = (startRatio + endRatio) / 2;
      const startRadian = startRatio * Math.PI * 2;
      const endRadian = endRatio * Math.PI * 2;
      const midRadian = midRatio * Math.PI * 2;

      if (startRatio === 0 && endRatio === 1) {
        isSelected = false;
      }

      k = typeof k !== 'undefined' ? k : 1 / 3;
      const offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0;
      const offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0;
      const hoverRate = isHovered ? 1.05 : 1;

      return {
        u: { min: -Math.PI, max: Math.PI * 3, step: Math.PI / 32 },
        v: { min: 0, max: Math.PI * 2, step: Math.PI / 20 },
        x: function (u: any, v: any) {
          if (u < startRadian) return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
          if (u > endRadian) return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
          return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate;
        },
        y: function (u: any, v: any) {
          if (u < startRadian) return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
          if (u > endRadian) return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
          return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate;
        },
        z: function (u: any, v: any) {
          return Math.sin(v) > 0 ? 1 * h * 0.1 : -1;
        },
      };
    }

    const optionData = [
      { name: '督查检查', value: 25, itemStyle: { color: '#ad5bc0' } },
      { name: '随机检查', value: 25, itemStyle: { color: '#dd537b' } },
      { name: '智能识别', value: 25, itemStyle: { color: '#3ededf' } },
      { name: '随手拍隐患', value: 25, itemStyle: { color: '#3f42ad' } },
    ];

    function getPie3D(pieData: any, internalDiameterRatio: any) {
      const series: any = [];
      let sumValue = 0;
      let startValue = 0;
      let endValue = 0;
      const legendData = [];
      const k =
        typeof internalDiameterRatio !== 'undefined'
          ? (1 - internalDiameterRatio) / (1 + internalDiameterRatio)
          : 1 / 3;

      for (let i = 0; i < pieData.length; i++) {
        sumValue += pieData[i].value;

        const seriesItem = {
          name: pieData[i].name || `series${i}`,
          type: 'surface',
          parametric: true,
          wireframe: { show: false },
          pieData: pieData[i],
          pieStatus: { selected: false, hovered: false, k },
          itemStyle: {},
        };

        if (pieData[i].itemStyle) {
          seriesItem.itemStyle = { ...pieData[i].itemStyle };
        }

        series.push(seriesItem);
      }
      for (let i = 0; i < series.length; i++) {
        endValue = startValue + series[i].pieData.value;
        series[i].pieData.startRatio = startValue / sumValue;
        series[i].pieData.endRatio = endValue / sumValue;
        series[i].parametricEquation = getParametricEquation(
          series[i].pieData.startRatio,
          series[i].pieData.endRatio,
          false,
          false,
          k,
          20 // 在此处传入饼图初始高度
        );
        startValue = endValue;
        legendData.push(series[i].name);
      }

      return {
        backgroundColor: '#123756',
        title: {
          // text: '立体饼图',
          top: '1%',
          textAlign: 'left',
          left: '1%',
          textStyle: { color: '#38adb9', fontSize: 32, fontWeight: '600' },
        },
        legend: {
          show: true,
          type: 'scroll',
          right: 0,
          bottom: 50,
          orient: 'vertical',
          icon: 'circle',
          itemHeight: 12,
          itemWidth: 12,
          itemGap: 5,
          textStyle: { color: '#709DD9', fontSize: 12, fontWeight: '400' },
          formatter: (name: any) => {
            const item = pieData.find((item: any) => item.name === name);
            return item ? `  ${name}` : '';
          },
        },
        color: ['#ad5bc0', '#dd537b', '#3ededf', '#3f42ad'],
        tooltip: {
          formatter: (params: any) => {
            if (params.seriesName !== 'mouseoutSeries') {
              return `${params.marker}${params.seriesName}：${pieData[params.seriesIndex].value}`;
            }
            return '';
          },
        },
        xAxis3D: { min: -1, max: 1 },
        yAxis3D: { min: -1, max: 1 },
        zAxis3D: { min: -1, max: 1 },
        grid3D: {
          show: false,
          boxHeight: 20,
          top: '5%',
          left: '-15%',
          viewControl: {
            alpha: 30,
            beta: 30,
            rotateSensitivity: 0, // 禁用旋转
            zoomSensitivity: 0,
            panSensitivity: 0,
            autoRotate: false, // 禁用自动旋转
            distance: 500,
          },
        },
        series,
      };
    }

    const option = getPie3D(optionData, 0);

    const initChart = () => {
      if (chartContainer.value) {
        myChart = echarts.init(chartContainer.value);
        myChart.setOption(option);
        window.addEventListener('resize', resizeChart);
      }
    };

    const resizeChart = () => {
      if (myChart) {
        myChart.resize();
      }
    };

    onMounted(() => {
      initChart();
    });

    onUnmounted(() => {
      if (myChart) {
        myChart.dispose();
        myChart = null;
        window.removeEventListener('resize', resizeChart);
      }
    });

    return {
      chartContainer,
    };
  },
});
</script>

<style scoped>
.chart-wrapper {
  width: 100%;
  height: 100%;
}

.chart-container {
  width: 100%;
  height: 100%;
}
</style>
