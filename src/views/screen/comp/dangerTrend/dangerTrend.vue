<template>
  <div class="chart-wrapper">
    <!-- 图表容器 -->
    <div ref="chartContainer" style="width: 100%; height: 100%"></div>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, onUnmounted, ref } from 'vue';
import * as echarts from 'echarts';

export default defineComponent({
  name: 'ElectricityChart',
  setup() {
    const chartContainer = ref<HTMLDivElement | null>(null);
    let myChart: echarts.ECharts | null = null;
    let resizeObserver: ResizeObserver | null = null;

    // 假设最近五个月的时间和数据
    const months = ['2024-05', '2024-06', '2024-07', '2024-08', '2024-09']; // 最近五个月
    const reportData = [10, 15, 12, 20, 25]; // 隐患上报数
    const rectifyData = [8, 14, 10, 18, 22]; // 隐患整改数

    const option = {
      backgroundColor: '#101736', // 背景色
      title: {
        // text: '隐患上报与整改统计',
        textStyle: {
          color: '#ffffff', // 标题文字颜色
        },
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          lineStyle: {
            color: '#57617B',
          },
        },
      },
      legend: {
        data: ['隐患上报数', '隐患整改数'],
        textStyle: {
          color: '#ffffff', // 图例文字颜色
          fontSize: 10,
        },
        right: '10%', // 图例靠右
        top: '5%', // 图例靠上
      },
      grid: {
        left: '10%',
        right: '10%',
        bottom: '10%',
        top: '20%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: months, // x轴展示最近五个月
        axisLabel: {
          textStyle: {
            color: '#ffffff', // 坐标轴刻度标签颜色
            fontSize: 10,
          },
        },
        axisLine: {
          lineStyle: {
            color: '#57617B',
          },
        },
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          textStyle: {
            color: '#ffffff', // Y轴刻度标签颜色
          },
        },
        axisLine: {
          lineStyle: {
            color: '#57617B',
          },
        },
        splitLine: {
          lineStyle: {
            color: '#57617B',
          },
        },
      },
      series: [
        {
          name: '隐患上报数',
          type: 'line',
          lineStyle: {
            color: '#ff6666', // 上报数线条颜色
            width: 3,
          },
          itemStyle: {
            color: '#ff6666', // 上报数节点颜色
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(255,102,102,0.3)',
              },
              {
                offset: 1,
                color: 'rgba(255,102,102,0)',
              },
            ]),
          },
          data: reportData, // 上报数数据
        },
        {
          name: '隐患整改数',
          type: 'line',
          lineStyle: {
            color: '#66cc66', // 整改数线条颜色
            width: 3,
          },
          itemStyle: {
            color: '#66cc66', // 整改数节点颜色
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(102,204,102,0.3)',
              },
              {
                offset: 1,
                color: 'rgba(102,204,102,0)',
              },
            ]),
          },
          data: rectifyData, // 整改数数据
        },
      ],
    };

    const initChart = () => {
      if (chartContainer.value) {
        myChart = echarts.init(chartContainer.value);
        myChart.setOption(option);

        // 监听容器大小的变化并调整图表大小
        resizeObserver = new ResizeObserver(() => {
          if (myChart) {
            myChart.resize();
          }
        });
        resizeObserver.observe(chartContainer.value);
      }
    };

    onMounted(() => {
      initChart();
    });

    onUnmounted(() => {
      if (resizeObserver && chartContainer.value) {
        resizeObserver.unobserve(chartContainer.value);
      }
      if (myChart) {
        myChart.dispose();
        myChart = null;
      }
    });

    return {
      chartContainer,
    };
  },
});
</script>

<style scoped>
.chart-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
}
</style>
