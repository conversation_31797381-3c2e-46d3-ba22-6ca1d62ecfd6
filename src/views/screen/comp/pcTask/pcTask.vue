<template>
  <!-- 确保外层容器有明确的大小 -->
  <div class="chart-wrapper">
    <!-- 图表容器 -->
    <div ref="chartContainer" style="width: 100%; height: 100%"></div>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, onUnmounted, ref } from 'vue';
import * as echarts from 'echarts';

export default defineComponent({
  name: 'EChartsComponent',
  setup() {
    const chartContainer = ref<HTMLDivElement | null>(null);
    let myChart: echarts.ECharts | null = null;
    let resizeObserver: ResizeObserver | null = null;

    const initChart = () => {
      if (chartContainer.value) {
        // 初始化图表
        myChart = echarts.init(chartContainer.value);

        // 图表配置选项
        const option = {
          backgroundColor: '#101736',
          color: ['#EAEA26', '#906BF9'], // 统计一和统计二的颜色
          title: {
            // text: '统计数据',
            textStyle: {
              color: '#fff',
              fontSize: 12,
            },
            padding: 0,
            top: 35,
            left: 25,
          },
          tooltip: {
            show: true,
            trigger: 'axis',
            axisPointer: {
              type: 'shadow',
            },
          },
          legend: {
            orient: 'horizontal',
            icon: 'circle',
            padding: 0,
            top: 35,
            right: 40,
            itemWidth: 14,
            itemHeight: 14,
            itemGap: 21,
            textStyle: {
              fontSize: 10,
              color: '#ffffff',
            },
          },
          grid: {
            top: '20%',
            left: '3%',
            right: '4%',
            bottom: '5%',
            containLabel: true,
          },
          xAxis: {
            show: true,
            type: 'category',
            boundaryGap: false,
            axisLine: {
              show: true,
              lineStyle: {
                color: 'rgba(255,255,255,.4)',
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              color: '#ffffff',
            },
            splitLine: {
              show: false,
            },
            // X 轴展示最近五个月的数据
            data: ['3月', '4月', '5月', '6月', '7月'],
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              color: '#ffffff',
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: 'rgba(255,255,255,.4)',
              },
            },
            splitLine: {
              show: false,
            },
          },
          series: [
            {
              name: '统计一',
              type: 'line',
              smooth: true,
              data: [120, 132, 101, 134, 90], // 最近五个月的数据
              areaStyle: {
                opacity: 0.2,
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: '#EAEA26',
                    },
                    {
                      offset: 1,
                      color: 'transparent',
                    },
                  ],
                },
              },
            },
            {
              name: '统计二',
              type: 'line',
              smooth: true,
              data: [220, 182, 191, 234, 290], // 最近五个月的另一组数据
              areaStyle: {
                opacity: 0.2,
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: '#906BF9',
                    },
                    {
                      offset: 1,
                      color: 'transparent',
                    },
                  ],
                },
              },
            },
          ],
        };

        // 设置图表选项
        myChart.setOption(option);

        // 使用 ResizeObserver 来监听容器的尺寸变化
        resizeObserver = new ResizeObserver(() => {
          if (myChart) {
            myChart.resize(); // 调整图表大小
          }
        });

        // 开始监听
        resizeObserver.observe(chartContainer.value);
      }
    };

    // 在组件挂载时初始化图表
    onMounted(() => {
      if (chartContainer.value) {
        initChart();
      }
    });

    // 在组件销毁时清理资源
    onUnmounted(() => {
      if (resizeObserver && chartContainer.value) {
        resizeObserver.unobserve(chartContainer.value);
      }
      if (myChart) {
        myChart.dispose();
        myChart = null;
      }
    });

    return {
      chartContainer,
    };
  },
});
</script>

<style scoped>
.chart-wrapper {
  width: 100%;
  height: 100%; /* 确保有一个明确的高度 */
  position: relative;
}
</style>
