<template>
  <div ref="chartContainer" class="chart-container"></div>
</template>

<script lang="ts">
import { defineComponent, onMounted, onUnmounted, ref } from 'vue';
import * as echarts from 'echarts';

export default defineComponent({
  name: 'PictorialBarChart',
  setup() {
    const chartContainer = ref<HTMLDivElement | null>(null);
    let myChart: echarts.ECharts | null = null;

    const chartData = [0, 0, 86, 825, 2672];
    const xAxisData = ['小进士', '小贡士', '小举人', '小秀才', '小童生'];

    // 使用随机图片URL替代Base64图像
    const base64Images = [
      'https://via.placeholder.com/40x28.png?text=小进士',
      'https://via.placeholder.com/89x39.png?text=小贡士',
      'https://via.placeholder.com/136x51.png?text=小举人',
      'https://via.placeholder.com/182x63.png?text=小秀才',
      'https://via.placeholder.com/240x83.png?text=小童生',
    ];

    const option = {
      tooltip: {
        show: true,
        formatter: function (params: any) {
          return `${params.name}: ${chartData[params.dataIndex]}`;
        },
      },
      grid: {
        left: '10%',
        top: '19%',
        bottom: '15%',
      },
      xAxis: {
        show: false,
        data: xAxisData,
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: '#5EA2ED',
          interval: 0,
        },
        axisLine: {
          lineStyle: {
            color: '#1B5BBA',
          },
        },
      },
      yAxis: {
        show: false,
        splitLine: { show: false },
        axisLine: {
          lineStyle: {
            color: '#1B5BBA',
          },
        },
        axisLabel: {
          color: '#5EA2ED',
          interval: 0,
        },
      },
      series: [
        {
          type: 'pictorialBar',
          data: [
            {
              name: '小进士',
              z: 100,
              value: 120,
              symbolSize: [40, 28],
              symbolPosition: 'center',
              symbolOffset: [84, 67],
              symbol: `image://${base64Images[0]}`,
            },
            {
              name: '小贡士',
              z: 90,
              value: 57,
              symbolSize: [89, 39],
              symbolPosition: 'center',
              symbolOffset: [-30, 0],
              symbol: `image://${base64Images[1]}`,
            },
            {
              name: '小举人',
              z: 80,
              value: 42,
              symbolSize: [136, 51],
              symbolPosition: 'center',
              symbolOffset: [-145, 0],
              symbol: `image://${base64Images[2]}`,
            },
            {
              name: '小秀才',
              z: 70,
              value: 27,
              symbolSize: [182, 63],
              symbolPosition: 'center',
              symbolOffset: [-257, 0],
              symbol: `image://${base64Images[3]}`,
            },
            {
              name: '小童生',
              z: 60,
              value: 10,
              symbolSize: [240, 83],
              symbolPosition: 'center',
              symbolOffset: [-370, 0],
              symbol: `image://${base64Images[4]}`,
            },
          ],
        },
      ],
    };

    const initChart = () => {
      if (chartContainer.value) {
        myChart = echarts.init(chartContainer.value);
        myChart.setOption(option);
        window.addEventListener('resize', resizeChart);
      }
    };

    const resizeChart = () => {
      if (myChart) {
        myChart.resize();
      }
    };

    onMounted(() => {
      initChart();
    });

    onUnmounted(() => {
      if (myChart) {
        myChart.dispose();
      }
      window.removeEventListener('resize', resizeChart);
    });

    return {
      chartContainer,
    };
  },
});
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 400px; /* 调整高度 */
}
</style>
