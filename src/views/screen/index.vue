<template>
  <div class="h-full w-full">
    <div>顶部标题</div>
    <div class="flex h-full w-full">
      <div class="flex flex-col left w-1/4">
        <div class="h-1/3"><pcTask></pcTask></div>
        <div class="h-1/3"><dangerTrend></dangerTrend></div>
        <div class="h-1/3"><dangerSource></dangerSource></div>
      </div>
      <div class="middle w-1/2 h-full" style="background-color: pink">中间</div>
      <div class="right w-1/4" style="background-color: red">
        <div class="h-1/3"><Level></Level></div>
        <div class="h-1/3"><dangerTrend></dangerTrend></div>
        <div class="h-1/3"><dangerSource></dangerSource></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import pcTask from './comp/pcTask/pcTask.vue';
import dangerTrend from './comp/dangerTrend/dangerTrend.vue';
import dangerSource from './comp/dangerSource/dangerSource.vue';
import Level from './comp/level/Level.vue';
defineOptions({ name: 'screenVisualization' });
</script>
<style scoped>
.n-layout-header,
.n-layout-footer {
  background: rgba(128, 128, 128, 0.2);
}

.n-layout-sider {
  background: rgba(128, 128, 128, 0.3);
}

.n-layout-content {
  background: rgba(128, 128, 128, 0.4);
}
</style>
