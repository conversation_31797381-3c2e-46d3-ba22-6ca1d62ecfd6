<template>
  <el-row :gutter="20" v-if="activeTab == 1 && source == 'detail'">
    <el-col :span="6">
      <div class="p-4 pl-0 text-lg font-bold">
        {{ treeUnitName }}
      </div>
    </el-col>
    <el-col :span="6" :offset="12" v-if="isBusinessFlag">
      <div class="flex justify-end">
        <n-button type="primary" @click="back()"> 返回 </n-button>
      </div>
    </el-col>
  </el-row>

  <el-tabs v-model="activeTab" class="demo-tabs" @tab-change="tabClick">
    <el-tab-pane
      style="font-size: 17px; font-weight: bold"
      :label="item.label"
      :name="item.name"
      :key="item.label"
      v-for="item in statistics"
    />
  </el-tabs>
  <el-row justify="space-between">
    <el-col :span="6" v-if="activeTab == 1">
      <n-button type="primary">
        {{ source == 'list' ? '单位任务检查完成情况' : '检查任务完成情况' }}
      </n-button>
    </el-col>
    <el-col :span="6" v-else-if="activeTab == 2">
      <n-button
        style="margin-right: 5px"
        :type="zindex === index ? 'primary' : ''"
        v-for="(item, index) in btnArr"
        :key="index"
        @click="btnClick(index)"
      >
        {{ item.name }}
      </n-button>
    </el-col>
    <el-col v-else :span="6"></el-col>
    <div class="flex">
      <n-radio-group
        @change="timeTypeChange"
        class="ml-auto"
        v-model:value="timeType"
        :theme-overrides="themeOverrides"
      >
        <n-radio-button
          v-for="song in songs"
          :key="song.value"
          :value="song.value"
          :label="song.label"
        />
      </n-radio-group>
      <n-date-picker
        v-model:value="timeRangeVal"
        type="daterange"
        size="small"
        class="ml-[20px] w-[238px]"
        format="yyyy-MM-dd"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        clearable
        @update:value="timeRangeValChange"
      />
    </div>
  </el-row>
  <!-- 统计指标数据展示 -->
  <el-row
    class="my-[15px]"
    :gutter="10"
    v-if="activeTab == 1"
    justify="space-between"
  >
    <el-col :span="17">
      <div class="flex justify-between items-center h-[80px]">
        <div
          class="flex justify-center items-center static-bg h-full w-1/5"
          :style="{ marginLeft: idx == 0 ? '0' : '' }"
          v-for="(item, idx) in staticNums"
          :key="idx"
        >
          <img :src="item.icon" alt="" class="w-[43px]" />
          <div>
            <div class="title">{{ item.title }}</div>
            <div class="num">
              <el-statistic :value="item.num" :value-style="item.style" />
            </div>
          </div>
        </div>
      </div>
    </el-col>

    <el-col :span="7">
      <el-row
        :gutter="10"
        class="h-[80px] px-[20px] py-[15px] static-bg relative"
      >
        <el-col :span="12">
          <div class="title">检查任务完成率</div>
          <el-statistic
            :value="formatNumber(taskEdAvgNum)"
            value-style="margin-top: 10px; color: #ff5000"
          />
          <img src="../img/warnIco.png" alt="" class="warnIco" />
        </el-col>
        <el-col :span="12">
          <div class="title">检查任务按期完成率</div>
          <el-statistic
            :value="formatNumber(taskTimeEdAvgNum)"
            value-style="margin-top: 10px; color: #ff5000"
          />
        </el-col>
      </el-row>
    </el-col>
  </el-row>
  <el-row
    class="my-[15px]"
    :gutter="10"
    v-else-if="activeTab == '2'"
    justify="space-between"
  >
    <el-col>
      <div class="flex justify-between items-center h-[80px]">
        <div
          class="flex justify-center items-center static-bg h-full w-1/5"
          :style="{ marginLeft: idx == 0 ? '0' : '' }"
          v-for="(item, idx) in staticUpdateNums"
          :key="idx"
        >
          <div>
            <div class="title">{{ item.name }}</div>
            <div class="num">
              <el-statistic
                :value="item.value"
                v-if="
                  item.name === '整改完成率' || item.name === '超期已整改率'
                "
                value-style="color: red"
              />
              <el-statistic :value="item.value" v-else />
            </div>
          </div>
        </div>
      </div>
    </el-col>
  </el-row>
  <el-row class="my-[15px]" :gutter="10" v-else justify="space-between">
    <el-col>
      <div class="flex justify-between items-center h-[80px]">
        <div
          class="flex justify-center items-center static-bg h-full w-1/5"
          :style="{ marginLeft: idx == 0 ? '0' : '' }"
          v-for="(item, idx) in staticCqNums"
          :key="idx"
        >
          <div>
            <div class="title">{{ item.title }}</div>
            <div class="num">
              <el-statistic :value="item.num" />
            </div>
          </div>
        </div>
      </div>
    </el-col>
  </el-row>
  <!-- filter 部分 -->
  <el-row :gutter="20" v-if="source == 'list'">
    <el-col :span="6">
      <n-form :show-feedback="false" label-placement="left">
        <n-form-item label="单位名称:" label-align="left" v-if="zindex === 0">
          <n-select
            v-model:value="selectLevelCode"
            placeholder="请选择"
            label-field="orgName"
            value-field="levelCode"
            :options="childUnitList"
            @update:value="selectLevelCodeChange"
            clearable
          />
        </n-form-item>
        <n-form-item label="隐患分类:" label-align="left" v-else>
          <n-select
            v-model:value="selectType"
            placeholder="请选择"
            label-field="className"
            value-field="id"
            :options="childTypeList"
            @update:value="selectTypeChange"
            clearable
          />
        </n-form-item>
      </n-form>
    </el-col>
    <el-col :span="6" :offset="12" v-if="activeTab == 1">
      <div class="flex justify-end">
        <n-button
          type="primary"
          @click="taskAllChildStatisticExportApi"
          :loading="exportLoading"
        >
          导出
        </n-button>
      </div>
    </el-col>
    <el-col :span="6" :offset="12" v-else>
      <div class="flex justify-end">
        <n-button
          style="margin-right: 10px"
          type="primary"
          @click="exportYhData"
          :loading="exportLoading"
        >
          导出隐患数据
        </n-button>
        <n-button
          type="primary"
          @click="taskExecuteDetailExportApi"
          :loading="exportLoading"
        >
          导出列表
        </n-button>
      </div>
    </el-col>
  </el-row>
  <el-row :gutter="20" v-if="source == 'detail'">
    <el-col :span="6">
      <n-form :show-feedback="false" label-placement="left">
        <n-form-item label="检查任务名称:" label-align="left">
          <n-input
            placeholder="请输入检查任务名称模糊搜索"
            maxlength="50"
            show-count
            v-model:value="taskName"
            clearable
            @update:value="doHandle()"
          />
        </n-form-item>
      </n-form>
    </el-col>
    <el-col :span="6" :offset="12" v-if="activeTab == 1">
      <div class="flex justify-end">
        <n-button
          type="primary"
          @click="taskExecuteDetailExportApi"
          :loading="exportLoading"
        >
          导出
        </n-button>
      </div>
    </el-col>
    <el-col v-else>
      <div :span="6" :offset="12" class="flex justify-end">
        <n-button
          style="margin-right: 10px"
          type="primary"
          @click="exportYhData"
          :loading="exportLoading"
        >
          导出隐患数据
        </n-button>
        <n-button
          type="primary"
          @click="taskExecuteDetailExportApi"
          :loading="exportLoading"
        >
          导出列表
        </n-button>
      </div>
    </el-col>
  </el-row>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { throttle } from 'lodash-es';
import icon1 from '@/views/data_statistics/img/ico1.png';
import icon2 from '@/views/data_statistics/img/ico2.png';
import icon3 from '@/views/data_statistics/img/ico3.png';
import icon4 from '@/views/data_statistics/img/ico4.png';
import icon5 from '@/views/data_statistics/img/ico5.png';
import {
  getTimeRange,
  ACTION,
  isRangeDateDisabled,
  formatNumber,
} from '@/views/data_statistics/constant';
import { useStore } from '@/store/index';
import { useRoute } from 'vue-router';
import dayjs from 'dayjs';
import { fileDownloader } from '@/utils/fileDownloader';
import {
  treeList,
  getOrgUnderling,
  getTaskAllStatistic,
  taskAllChildStatisticExport,
  taskExecuteDetailExport,
  yhzgcqyjtj,
  yhzgcqyjExport,
  yhzgcqyjyhExport,
  dwfxyhqktj,
  yhflqktj,
  dwfxyhqklbyhExport,
  yhflqklbyhExport,
  dwfxyhqklbExport,
  yhflqklbExport,
} from '@/views/data_statistics/fetchData';
const { userInfo } = useStore();
const route = useRoute();
const themeOverrides = {
  buttonHeightMedium: '28px',
  fontSizeMedium: '12px',
  buttonTextColorActive: '#ffffff',
  buttonColorActive: '#0080FF',
};
const props = defineProps({
  source: {
    type: String,
    default: 'list',
  },
  treeUnitId: {
    type: String,
    default: '',
  },
  treeLevelCode: {
    type: String,
    default: '',
  },
  treeUnitName: {
    type: String,
    default: '',
  },
  sorts: {
    type: Array,
    default: () => [],
  },
  parentId: {
    type: String,
    default: '',
  },
});
const zindex = ref(0);
const btnArr = ref([
  { id: 1, name: '单位发现隐患情况', flag: 1 },
  { id: 2, name: '隐患分类情况', flag: 2 },
]);
const btnClick = (i: number) => {
  chooseUnitId.value = ' ';
  chooseClassId.value = '';
  zindex.value = i;
  getTaskAllStatisticApi();
  doHandle();
};
const emits = defineEmits(['action', 'updateTab', 'exportTable']);
const taskName = ref<any>('');
const selectLevelCode = ref<any>(null);
const selectType = ref<any>(null);
const levelCodes = ref<any>([]);
const childUnitList = ref<any>([]);
const childTypeList = ref<any>([]);
const activeTab = ref<any>('1');
const timeType = ref<any>('0');
const startTime = ref<any>(null);
const endTime = ref<any>(null);
const taskEdAvgNum = ref<any>('');
const taskTimeEdAvgNum = ref<any>('');
const exportLoading = ref<any>(false);
const today = new Date(); // 获取当前时间
const start = new Date(today);
const end = new Date(today);

// 设置开始时间为00:00:00
start.setHours(0, 0, 0, 0);

// 设置结束时间为23:59:59
end.setHours(23, 59, 59, 999);
const timeRangeVal = ref([start, end]);
const isBusinessFlag = ref(false);
// 是否还存在下级业务单位
isBusinessFlag.value = localStorage.getItem('isBusinessFlag') == 'true';
const statistics = [
  {
    label: '检查任务完成情况',
    name: '1',
  },
  {
    label: '隐患整改情况',
    name: '2',
  },
  {
    label: '隐患整改超期预警',
    name: '3',
  },
];
const staticUpdateNums = ref<any>([
  // {
  //   title: '隐患总数',
  //   num: 0,
  //   icon: icon1,
  //   style: {
  //     color: '#165cdd',
  //   },
  // },
  // {
  //   title: '重大隐患数',
  //   num: 0,
  //   icon: icon2,
  //   style: {
  //     color: '#FF9500',
  //   },
  // },
  // {
  //   title: '较大隐患数',
  //   num: 0,
  //   icon: icon3,
  //   style: {
  //     color: '#165cdd',
  //   },
  // },
  // {
  //   title: '一般隐患数',
  //   num: 0,
  //   icon: icon4,
  //   style: {
  //     color: '#165cdd',
  //   },
  // },
  // {
  //   title: '低风险隐患数',
  //   num: 0,
  //   icon: icon5,
  //   style: {
  //     color: '#FF9500',
  //   },
  // },
  // {
  //   title: '未整改隐患数',
  //   num: 0,
  //   icon: icon5,
  //   style: {
  //     color: '#FF9500',
  //   },
  // },
  // {
  //   title: '已整改隐患数',
  //   num: 0,
  //   icon: icon5,
  //   style: {
  //     color: '#FF9500',
  //   },
  // },
  // {
  //   title: '整改完成率',
  //   num: 0,
  //   icon: icon5,
  //   style: {
  //     color: '#FF9500',
  //   },
  // },
  // {
  //   title: '超期未整改隐患数',
  //   num: 0,
  //   icon: icon5,
  //   style: {
  //     color: '#FF9500',
  //   },
  // },
  // {
  //   title: '超期已整改完成数',
  //   num: 0,
  //   icon: icon5,
  //   style: {
  //     color: '#FF9500',
  //   },
  // },
  // {
  //   title: '超期已整改率',
  //   num: 0,
  //   icon: icon5,
  //   style: {
  //     color: '#FF9500',
  //   },
  // },
]);
const staticCqNums = ref<any>([
  {
    title: '超期超1天(未整改)',
    num: 0,
    icon: icon1,
    style: {
      color: '#165cdd',
    },
  },
  {
    title: '超期超3天(未整改)',
    num: 0,
    icon: icon2,
    style: {
      color: '#FF9500',
    },
  },
  {
    title: '超期超7天(未整改)',
    num: 0,
    icon: icon3,
    style: {
      color: '#165cdd',
    },
  },
  {
    title: '超期超1天(已整改)',
    num: 0,
    icon: icon4,
    style: {
      color: '#165cdd',
    },
  },
  {
    title: '超期超3天(已整改)',
    num: 0,
    icon: icon5,
    style: {
      color: '#FF9500',
    },
  },
  {
    title: '超期超7天(已整改)',
    num: 0,
    icon: icon5,
    style: {
      color: '#FF9500',
    },
  },
]);
const staticNums = ref<any>([
  {
    title: '检查任务总数',
    num: 0,
    icon: icon1,
    style: {
      color: '#165cdd',
    },
  },
  {
    title: '未完成检查任务数',
    num: 0,
    icon: icon2,
    style: {
      color: '#FF9500',
    },
  },
  {
    title: '已完成检查任务数',
    num: 0,
    icon: icon3,
    style: {
      color: '#165cdd',
    },
  },
  {
    title: '按期已完成检查任务数',
    num: 0,
    icon: icon4,
    style: {
      color: '#165cdd',
    },
  },
  {
    title: '逾期已完成检查任务数',
    num: 0,
    icon: icon5,
    style: {
      color: '#FF9500',
    },
  },
]);
const songs = ref([
  {
    label: '今天',
    value: '0',
    getStart() {
      return new Date();
    },
  },
  {
    label: '昨天',
    value: '1',
    getStart() {
      return new Date().setTime(new Date().getTime() - 3600 * 1000 * 24);
    },
  },
  {
    label: '近一周',
    value: '2',
    getStart() {
      return new Date().setTime(new Date().getTime() - 3600 * 1000 * 24 * 6);
    },
  },
  {
    label: '近一个月',
    value: '3',
    getStart() {
      const pastDate = new Date();
      return pastDate.setMonth(pastDate.getMonth() - 1);
    },
  },
  {
    label: '近三个月',
    value: '4',
    getStart() {
      const pastDate = new Date();
      return pastDate.setMonth(pastDate.getMonth() - 3);
    },
  },
]);

// 批量导出
const taskAllChildStatisticExportApi = () => {
  let params = {
    startTime: startTime.value,
    endTime: endTime.value,
    levelCode: props.treeLevelCode,
    unitId: props.treeUnitId,
    levelCodes: props.source == 'list' ? levelCodes.value : null,
    sorts: props.sorts || null,
  };

  fileDownloader(taskAllChildStatisticExport(), {
    method: 'POST',
    contentType: 'application/json',
    body: JSON.stringify(params),
  })
    .then(() => {})
    .catch(() => {});
};
const exportYhData = () => {
  let params = {
    chooseUnitId: chooseUnitId.value,
    startTime: dayjs(timeRangeVal.value[0]).format('YYYY-MM-DD' + ' 00:00:00'),
    endTime: dayjs(timeRangeVal.value[1]).format('YYYY-MM-DD' + ' 23:59:59'),
    unitId: props.treeUnitId,
    orderType: 'desc',
  };
  if (activeTab.value == '2') {
    const api = zindex.value === 0 ? dwfxyhqklbyhExport : yhflqklbyhExport;
    fileDownloader(api(), {
      method: 'POST',
      contentType: 'application/json',
      body: JSON.stringify(params),
    })
      .then(() => {})
      .catch(() => {});
  } else {
    fileDownloader(yhzgcqyjyhExport(), {
      method: 'POST',
      contentType: 'application/json',
      body: JSON.stringify(params),
    })
      .then(() => {})
      .catch(() => {});
  }
};
const taskExecuteDetailExportApi = () => {
  let params =
    activeTab.value === '1'
      ? {
          startTime: dayjs(timeRangeVal.value[0]).format(
            'YYYY-MM-DD' + ' 00:00:00'
          ),
          endTime: dayjs(timeRangeVal.value[1]).format(
            'YYYY-MM-DD' + ' 23:59:59'
          ),
          levelCode: props.treeLevelCode,
          unitId: props.treeUnitId,
          levelCodes: props.source == 'list' ? levelCodes.value : null,
          taskName: taskName.value,
          sorts: props.sorts || null,
        }
      : {
          chooseUnitId: chooseUnitId.value,
          startTime: dayjs(timeRangeVal.value[0]).format(
            'YYYY-MM-DD' + ' 00:00:00'
          ),
          endTime: dayjs(timeRangeVal.value[1]).format(
            'YYYY-MM-DD' + ' 23:59:59'
          ),
          unitId: props.treeUnitId,
          orderType: 'desc',
        };

  const api: any =
    activeTab.value == '1'
      ? taskExecuteDetailExport()
      : activeTab.value == '3'
        ? yhzgcqyjExport()
        : activeTab.value == '2' && zindex.value == 0
          ? dwfxyhqklbExport()
          : yhflqklbExport();
  fileDownloader(api, {
    method: 'POST',
    contentType: 'application/json',
    body: JSON.stringify(params),
  })
    .then(() => {})
    .catch(() => {});
};

// 获取当前单位的所有直接直属下级单位
const getOrgUnderlingApi = async () => {
  let params = {
    unitId: props.treeUnitId,
  };
  selectLevelCode.value = null;
  try {
    let { data }: any = await getOrgUnderling(params);
    childUnitList.value = data;
  } catch (error) {
    childUnitList.value = [];
  }
};
const getTypelingApi = async () => {
  let params = {
    unitId: userInfo.topUnitId,
    parentId: '0',
  };
  selectType.value = null;
  try {
    let { data }: any = await treeList(params);
    childTypeList.value = data;
  } catch (error) {
    childTypeList.value = [];
  }
};
const chooseUnitId = ref('');
const chooseClassId = ref('');
const selectLevelCodeChange = () => {
  if (selectLevelCode.value) {
    let { levelCode, orgCode, orgName, orgType } = childUnitList.value.find(
      (item: any) => item.levelCode === selectLevelCode.value
    );
    levelCodes.value = [{ levelCode, orgCode, orgName, orgType }];
    chooseUnitId.value = orgCode;
    console.log(chooseUnitId.value, 'chooseUnitId.value');
  } else {
    levelCodes.value = null;
    chooseUnitId.value = '';
  }
  // 获取卡片数据
  getTaskAllStatisticApi();
  // 刷新列表
  doHandle();
};
const selectTypeChange = () => {
  if (selectType.value) {
    // let { id } = childTypeList.value.find(
    //   (item: any) => item.id === selectType.value
    // );
    console.log(selectType.value, '>>>>>');
    chooseClassId.value = selectType.value;
    // console.log(chooseClassId.value, 'chooseClassId.value');
  } else {
    chooseClassId.value = '';
  }
  // 获取卡片数据
  getTaskAllStatisticApi();
  // 刷新列表
  doHandle();
};

// 获取单位统计数据
const getTaskAllStatisticApi = async (levelCode?: any, unitId?: any) => {
  if (activeTab.value == '1') {
    let params = {
      unitId: props.treeUnitId,
      levelCode: props.treeLevelCode,
      levelCodes: props.source == 'list' ? levelCodes.value : null,
      startTime: dayjs(timeRangeVal.value[0]).format(
        'YYYY-MM-DD' + ' 00:00:00'
      ),
      endTime: dayjs(timeRangeVal.value[1]).format('YYYY-MM-DD' + ' 23:59:59'),
    };
    try {
      let {
        data: {
          taskAllNum,
          taskEdAvg,
          taskEdNum,
          taskTimeEdAvg,
          taskTimeEdNum,
          taskTimeOutNum,
          taskUnNum,
        },
      }: any = await getTaskAllStatistic(params);
      let arr = [
        taskAllNum,
        taskUnNum,
        taskEdNum,
        taskTimeEdNum,
        taskTimeOutNum,
      ];
      staticNums.value.forEach((item: any, idx: number) => {
        item.num = arr[idx] || 0;
      });
      taskEdAvgNum.value = taskEdAvg;
      taskTimeEdAvgNum.value = taskTimeEdAvg;
    } catch (error) {
      console.log('error>>>', error);
    }
  } else if (activeTab.value == '2') {
    let params = {
      chooseClassId: chooseClassId.value,
      chooseUnitId: chooseUnitId.value,
      startTime: dayjs(timeRangeVal.value[0]).format(
        'YYYY-MM-DD' + ' 00:00:00'
      ),
      endTime: dayjs(timeRangeVal.value[1]).format('YYYY-MM-DD' + ' 23:59:59'),
      unitId: props.treeUnitId,
      orderType: 'desc',
    };
    try {
      const api: any =
        zindex.value === 0 ? dwfxyhqktj(params) : yhflqktj(params);
      let res: any = await api;

      staticUpdateNums.value = res.data;
      // taskEdAvgNum.value = taskEdAvg;
      // taskTimeEdAvgNum.value = taskTimeEdAvg;
    } catch (error) {
      console.log('error>>>', error);
    }
  } else {
    // yhzgcqyjtj;
    let params = {
      chooseUnitId: chooseUnitId.value,
      startTime: dayjs(timeRangeVal.value[0]).format(
        'YYYY-MM-DD' + ' 00:00:00'
      ),
      endTime: dayjs(timeRangeVal.value[1]).format('YYYY-MM-DD' + ' 23:59:59'),
      unitId: props.treeUnitId,
      orderType: 'desc',
    };
    try {
      let {
        data: { wzgcq1t, wzgcq3t, wzgcq7t, ygcq1t, yzgcq3t, yzgcq7t },
      }: any = await yhzgcqyjtj(params);
      let arr = [wzgcq1t, wzgcq3t, wzgcq7t, ygcq1t, yzgcq3t, yzgcq7t];
      staticCqNums.value.forEach((item: any, idx: number) => {
        item.num = arr[idx] || 0;
      });
      // taskEdAvgNum.value = taskEdAvg;
      // taskTimeEdAvgNum.value = taskTimeEdAvg;
    } catch (error) {
      console.log('error>>>', error);
    }
  }
};

const timeTypeChange = () => {
  const obj: any = songs.value.find(
    (item: any) => item.value == timeType.value
  );
  let startTime = obj.getStart();

  timeRangeVal.value = [
    startTime,
    timeType.value === '1' ? startTime : new Date(),
  ];
  // timeType.value = tab;
  // timeRangeVal.value = null;
  // let times = getTimeRange(timeType.value);
  // startTime.value = times.startTime;
  // endTime.value = times.endTime;
  // // 获取卡片数据
  getTaskAllStatisticApi();
  // 刷新列表
  doHandle();
};
const timeRangeValChange = (value: any, formatvalue: any) => {
  timeType.value = null;
  if (formatvalue && formatvalue.length) {
    startTime.value = formatvalue[0];
    endTime.value = formatvalue[1];
    // 获取卡片数据
    getTaskAllStatisticApi();
    // 刷新列表
    doHandle();
  } else {
    timeTypeChange();
  }
};
const tabClick = (name: any) => {
  // 获取直属下级单位
  getOrgUnderlingApi();
  // 获取卡片数据
  getTaskAllStatisticApi();
  getTypelingApi();
  // 刷新列表
  doHandle();
  zindex.value = 0;
  emits('updateTab', activeTab.value);
};

const doHandle = throttle(() => {
  const params =
    activeTab.value == '1'
      ? {
          startTime: dayjs(timeRangeVal.value[0]).format(
            'YYYY-MM-DD' + ' 00:00:00'
          ),
          endTime: dayjs(timeRangeVal.value[1]).format(
            'YYYY-MM-DD' + ' 23:59:59'
          ),
          levelCode: props.treeLevelCode,
          unitId: props.treeUnitId,
          levelCodes: props.source == 'list' ? levelCodes.value : null,
          taskName: taskName.value,
        }
      : activeTab.value == '3'
        ? {
            chooseUnitId: chooseUnitId.value,
            startTime: dayjs(timeRangeVal.value[0]).format(
              'YYYY-MM-DD' + ' 00:00:00'
            ),
            endTime: dayjs(timeRangeVal.value[1]).format(
              'YYYY-MM-DD' + ' 23:59:59'
            ),
            unitId: props.treeUnitId,
            orderType: 'desc',
          }
        : activeTab.value === '2' && zindex.value === 0
          ? {
              chooseUnitId: chooseUnitId.value,
              startTime: dayjs(timeRangeVal.value[0]).format(
                'YYYY-MM-DD' + ' 00:00:00'
              ),
              endTime: dayjs(timeRangeVal.value[1]).format(
                'YYYY-MM-DD' + ' 23:59:59'
              ),
              unitId: props.treeUnitId,
              orderType: 'desc',
              zindex: zindex.value,
            }
          : {
              chooseClassId: chooseClassId.value,
              startTime: dayjs(timeRangeVal.value[0]).format(
                'YYYY-MM-DD' + ' 00:00:00'
              ),
              endTime: dayjs(timeRangeVal.value[1]).format(
                'YYYY-MM-DD' + ' 23:59:59'
              ),
              unitId: props.treeUnitId,
              orderType: 'desc',
              zindex: zindex.value,
            };

  emits('action', {
    action: ACTION.SEARCH,
    data: params,
  });
}, 1000);

// 树点击的数据优先于单位下拉框
watch(
  () => props.treeUnitId,
  () => {
    selectLevelCode.value = '';
    levelCodes.value = null;
    // 树节点改变初始化日期
    // let times = getTimeRange(timeType.value);
    // startTime.value = times.startTime;
    // endTime.value = times.endTime;
    // timeRangeVal.value = [null, null];
    // 获取直属下级单位
    getOrgUnderlingApi();
    // 获取卡片数据
    getTaskAllStatisticApi();
    getTypelingApi();
    // 刷新列表
    doHandle();
  }
);
function init() {
  if (Object.keys(route.query).length !== 0) {
    activeTab.value = route.query.tab1;
    zindex.value = route.query.tab2 === '2' ? 1 : 0;

    timeType.value = route.query.tab;
    const obj: any = songs.value.find(
      (item: any) => item.value == timeType.value
    );
    let start = obj.getStart();
    timeRangeVal.value = [start, new Date()];
    (startTime.value = dayjs(timeRangeVal.value[0]).format(
      'YYYY-MM-DD' + ' 00:00:00'
    )),
      (endTime.value = dayjs(timeRangeVal.value[1]).format(
        'YYYY-MM-DD' + ' 23:59:59'
      )),
      getTaskAllStatisticApi();
    doHandle();
    emits('updateTab', activeTab.value);
  }
  console.log(route.query, 'route');
}
init();
function back() {
  // 返回上一页
  window.history.back();
}
defineOptions({ name: 'checkTempFilterComp' });
</script>
<style lang="scss" scoped>
.active {
  background-color: #527cff !important;
  border-radius: 4px !important;
  color: white !important;
}

.static-bg {
  background: linear-gradient(180deg, #ffffff 0%, #e1e7fa 99%);
  box-shadow: 0px 2px 0px 0px rgba(0, 20, 82, 0.18);
  border-radius: 8px;
  margin: 0 10px;

  .title {
    font-weight: 500;
    font-size: 13px;
    color: #333333;
  }

  .num {
    font-size: 20px;
    color: #0256ff;
    line-height: 20px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin-top: 5px;
  }
}

.warnIco {
  position: absolute;
  width: 52px;
  right: 6px;
  bottom: 4px;
}
</style>
