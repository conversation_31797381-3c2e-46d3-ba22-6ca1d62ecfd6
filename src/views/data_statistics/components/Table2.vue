<template>
  <div class="flex flex-col justify-between" style="height: 100%">
    <n-data-table
      ref="tableRef"
      class="h-full com-table"
      :columns="columns"
      :data="tableData"
      :bordered="false"
      :flex-height="true"
      :scroll-x="2500"
      v-loading="loading"
      :get-csv-cell="getCsvCell"
      :get-csv-header="getCsvHeader"
      :on-update:sorter="sorterChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, h, onMounted } from 'vue';
import { NButton, NIcon } from 'naive-ui';
import dayjs from 'dayjs';
import {
  getTaskAllChildStatistic,
  checkUnitIdIsBusinessFlag,
  getOrgDetailInfo,
  yhzgcqyjlb,
  yhdjdtbt,
  dwfxyhqklb,
  dyhflqklb,
} from '@/views/data_statistics/fetchData';
import { useRouter } from 'vue-router';
import { CaretDown, CaretUp } from '@vicons/ionicons5';
const props = defineProps({
  treeUnitId: {
    type: String,
    default: '',
  },
});
const router = useRouter();
const emits = defineEmits(['CurrentNodeKeychange', 'getSorts']);
const tableData = ref();
const loading = ref(false);
const tableRef = ref();
const iconSize = 16; // 设置统一图标尺寸
let columns = ref([
  {
    title: '序号',
    key: 'index',
    width: 60,
    align: 'left',
    render(row: any, index: any) {
      return index + 1;
    },
  },
]);
const getColumnsData = async (params: any) => {
  let res: any = await yhdjdtbt({
    unitId: props.treeUnitId,
    type: params.zindex === 0 ? 1 : 2,
  });
  let arr = res.data.map((item: any) => {
    if (item.fieldName === '单位名称') {
      return {
        ellipsis: {
          tooltip: true,
        },
        title: item.fieldName,
        key: item.field,
        width: 200,
        align: 'left',
        resizable: true,
        render(row: any) {
          return h(
            NButton,
            {
              text: true,
              type: 'primary',
              onClick: () => {
                rowClick(row);
              },
            },
            row.unitName
          );
        },
      };
    } else if (item.fieldName === '隐患分类') {
      return {
        ellipsis: {
          tooltip: true,
        },
        title: item.fieldName,
        key: item.field,
        width: 200,
        align: 'left',
        resizable: true,
        render(row: any) {
          return h(
            NButton,
            {
              text: true,
              type: 'primary',
              onClick: () => {
                rowClick(row);
              },
            },
            row.className
          );
        },
      };
    } else {
      return {
        title: item.fieldName,
        key: item.field,
        defaultSortOrder: false,
        resizable: true,
        sorter: 'default',
        ellipsis: {
          tooltip: true,
        },
        width: 160,
        // CaretDown
        renderSorterIcon: ({ order }: any) => {
          if (order === false) {
            return [
              h(NIcon, { class: 'mb-[-4px]', size: iconSize }, () =>
                h(CaretUp)
              ),
              h(NIcon, { class: 'mt-[-4px]', size: iconSize }, () =>
                h(CaretDown)
              ),
            ];
          }
          if (order === 'ascend')
            return h(NIcon, { size: iconSize }, () => h(CaretUp));
          if (order === 'descend')
            return h(NIcon, { size: iconSize }, () => h(CaretDown));
        },
        render(row: any) {
          // 检查当前列是否是 yhwzg 或 yhzgl
          const isSpecialColumn = ['cqyzgl', 'yhzgl'].includes(item.field);

          return h(
            'span',
            {
              style: isSpecialColumn ? { color: '#ff0000' } : {}, // 只有特定列会变红
            },
            row[item.field]
          );
        },
      };
    }
  });
  // const uniqueKeys = new Set(columns.value.map((col) => col.key));
  // const filteredNewColumns = arr.filter((col) => !uniqueKeys.has(col.key));

  // 只追加不重复的列
  columns.value = [
    {
      title: '序号',
      key: 'index',
      width: 60,
      align: 'left',
      render(row: any, index: any) {
        return index + 1;
      },
    },
    ...arr,
  ];
};
const sorterChange = (options: any) => {
  let _filed = options['columnKey'];
  let _sortType = options['order'];
  let sorts = [
    {
      filed: _filed,
      sortType: _sortType == 'descend' ? 'DESC' : 'ASC',
    },
  ];
  emits('getSorts', sorts);
};

// 初始化
const paramsData = ref({});
emits('getSorts', [{ filed: 'taskAllNum', sortType: 'DESC' }]);
async function getTableData(params: any) {
  console.log(params, 'params');
  paramsData.value = params;
  const api = params.zindex === 0 ? dwfxyhqklb : dyhflqklb;
  loading.value = true;
  try {
    let { data }: any = await api(params);
    tableData.value = data || [];
    loading.value = false;
  } catch (error) {
    console.log(error);
    tableData.value = [];
    loading.value = false;
  }
  getColumnsData(params);
}

async function rowClick(row: any) {
  console.log(row, '>>>>');
  try {
    // let { data }: any = await checkUnitIdIsBusinessFlag({
    //   unitId: row.unitId,
    // });
    let { data }: any = await getOrgDetailInfo({
      orgCode: row.unitId,
    });
    if (paramsData.value === 0) {
      if (data.orgType != '1') {
        // 如果不存在下级业务单位，则切换机构数选中至当前单位节点
        emits('CurrentNodeKeychange', row.unitId);
      } else {
        // 跳转详情页
        router.push({
          path: 'hazard-management',
          query: {
            unitId: row.unitId,
            starTime: dayjs(paramsData.value.startTime).format(
              'YYYY-MM-DD' + ' 00:00:00'
            ),
            endTime: dayjs(paramsData.value.endTime).format(
              'YYYY-MM-DD' + ' 23:59:59'
            ),
          },
        });
      }
    } else {
      router.push({
        path: 'hazard-management',
        query: {
          unitId: row.unitId,
          starTime: dayjs(paramsData.value.startTime).format(
            'YYYY-MM-DD' + ' 00:00:00'
          ),
          endTime: dayjs(paramsData.value.endTime).format(
            'YYYY-MM-DD' + ' 23:59:59'
          ),
        },
      });
    }
    // console.log('data>>>', data);
  } catch (error) {
    console.log('error>>>', error);
  }
}
function getCurrentDateTime() {
  const now = new Date();

  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份从0开始，所以要加1
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');

  return `${year}${month}${day}${hours}${minutes}${seconds}`;
}

function exportData() {
  // const exportColumns = columns.filter((col) => col.key !== 'index');
  // console.log('exportColumns>>>', exportColumns);
  tableRef.value?.downloadCsv({
    fileName: '单位任务检查完成情况-' + getCurrentDateTime(),
    keepOriginalData: false,
    columns: columns.filter((col) => col.key !== 'index'),
  });
}
const getCsvCell = (value: any, _: any, column: any) => {
  if (column.key === 'index') return undefined; // 过滤序号列数据
  if (column.key === 'taskEdAvg' || column.key === 'taskTimeEdAvg') {
    return `${value}%`;
  }
  return value;
};
// 修改CSV表头处理
const getCsvHeader = (col: any) => {
  if (col.key === 'index') return undefined; // 过滤序号列表头
  return col.title;
};

defineExpose({
  getTableData,
  exportData,
});

defineOptions({ name: 'data_statistics_table' });
</script>
<style lang="scss" scoped>
::v-deep {
  .n-data-table .n-data-table-th .n-data-table-sorter {
    display: inline-flex !important;
    flex-direction: column !important;
  }
}
</style>
