<template>
  <div class="flex flex-col justify-between" style="height: 100%">
    <n-data-table
      ref="tableRef"
      class="h-full com-table"
      :columns="columns"
      :data="tableData"
      :bordered="false"
      :flex-height="true"
      v-loading="loading"
      :scroll-x="1900"
      :get-csv-cell="getCsvCell"
      :get-csv-header="getCsvHeader"
      :on-update:sorter="sorterChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, h } from 'vue';
import { NButton, NIcon } from 'naive-ui';
import {
  getTaskAllChildStatistic,
  checkUnitIdIsBusinessFlag,
  getOrgDetailInfo,
  yhzgcqyjlb,
} from '@/views/data_statistics/fetchData';
import { useRouter } from 'vue-router';
import { CaretDown, CaretUp } from '@vicons/ionicons5';

const router = useRouter();
const emits = defineEmits(['CurrentNodeKeychange', 'getSorts']);
const tableData = ref();
const loading = ref(false);
const tableRef = ref();
const iconSize = 16; // 设置统一图标尺寸
let columns = [
  {
    title: '序号',
    key: 'index',
    width: 60,
    align: 'left',
    render(row: any, index: any) {
      return index + 1;
    },
  },
  {
    title: '单位名称',
    key: 'unitName',
    width: 200,
    align: 'left',
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
    render(row: any) {
      return h(
        NButton,
        {
          text: true,
          type: 'primary',
          onClick: () => {
            rowClick(row);
          },
        },
        row.unitName
      );
    },
  },
  {
    title: '超期超1天(未整改)',
    key: 'wzgcq1t',
    width: 180,
    align: 'left',
    resizable: true,
    defaultSortOrder: false,
    ellipsis: {
      tooltip: true,
    },
    sorter: 'default',
    renderSorterIcon: ({ order }: any) => {
      if (order === false) {
        return [
          h(NIcon, { class: 'mb-[-4px]', size: iconSize }, () => h(CaretUp)),
          h(NIcon, { class: 'mt-[-4px]', size: iconSize }, () => h(CaretDown)),
        ];
      }
      if (order === 'ascend')
        return h(NIcon, { size: iconSize }, () => h(CaretUp));
      if (order === 'descend')
        return h(NIcon, { size: iconSize }, () => h(CaretDown));
    },
  },
  {
    title: '超期超3天(未整改)',
    key: 'wzgcq3t',
    width: 180,
    align: 'left',
    resizable: true,
    defaultSortOrder: false,
    ellipsis: {
      tooltip: true,
    },
    sorter: 'default',
    renderSorterIcon: ({ order }: any) => {
      if (order === false) {
        return [
          h(NIcon, { class: 'mb-[-4px]', size: iconSize }, () => h(CaretUp)),
          h(NIcon, { class: 'mt-[-4px]', size: iconSize }, () => h(CaretDown)),
        ];
      }
      if (order === 'ascend')
        return h(NIcon, { size: iconSize }, () => h(CaretUp));
      if (order === 'descend')
        return h(NIcon, { size: iconSize }, () => h(CaretDown));
    },
  },
  {
    title: '超期超7天(未整改)',
    key: 'wzgcq7t',
    width: 180,
    align: 'left',
    resizable: true,
    defaultSortOrder: false,
    ellipsis: {
      tooltip: true,
    },
    sorter: 'default',
    renderSorterIcon: ({ order }: any) => {
      if (order === false) {
        return [
          h(NIcon, { class: 'mb-[-4px]', size: iconSize }, () => h(CaretUp)),
          h(NIcon, { class: 'mt-[-4px]', size: iconSize }, () => h(CaretDown)),
        ];
      }
      if (order === 'ascend')
        return h(NIcon, { size: iconSize }, () => h(CaretUp));
      if (order === 'descend')
        return h(NIcon, { size: iconSize }, () => h(CaretDown));
    },
  },
  {
    title: '超期未整改隐患总数',
    key: 'cqwzgyhzs',
    width: 180,
    align: 'left',
    resizable: true,
    defaultSortOrder: false,
    ellipsis: {
      tooltip: true,
    },
    sorter: 'default',
    renderSorterIcon: ({ order }: any) => {
      if (order === false) {
        return [
          h(NIcon, { class: 'mb-[-4px]', size: iconSize }, () => h(CaretUp)),
          h(NIcon, { class: 'mt-[-4px]', size: iconSize }, () => h(CaretDown)),
        ];
      }
      if (order === 'ascend')
        return h(NIcon, { size: iconSize }, () => h(CaretUp));
      if (order === 'descend')
        return h(NIcon, { size: iconSize }, () => h(CaretDown));
    },
  },
  {
    title: '超期超1天(已整改)',
    key: 'ygcq1t',
    width: 180,
    align: 'left',
    resizable: true,
    defaultSortOrder: false,
    ellipsis: {
      tooltip: true,
    },
    sorter: 'default',
    renderSorterIcon: ({ order }: any) => {
      if (order === false) {
        return [
          h(NIcon, { class: 'mb-[-4px]', size: iconSize }, () => h(CaretUp)),
          h(NIcon, { class: 'mt-[-4px]', size: iconSize }, () => h(CaretDown)),
        ];
      }
      if (order === 'ascend')
        return h(NIcon, { size: iconSize }, () => h(CaretUp));
      if (order === 'descend')
        return h(NIcon, { size: iconSize }, () => h(CaretDown));
    },
  },
  {
    title: '超期超3天(已整改)',
    key: 'yzgcq3t',
    width: 180,
    align: 'left',
    resizable: true,
    defaultSortOrder: false,
    ellipsis: {
      tooltip: true,
    },
    sorter: 'default',
    renderSorterIcon: ({ order }: any) => {
      if (order === false) {
        return [
          h(NIcon, { class: 'mb-[-4px]', size: iconSize }, () => h(CaretUp)),
          h(NIcon, { class: 'mt-[-4px]', size: iconSize }, () => h(CaretDown)),
        ];
      }
      if (order === 'ascend')
        return h(NIcon, { size: iconSize }, () => h(CaretUp));
      if (order === 'descend')
        return h(NIcon, { size: iconSize }, () => h(CaretDown));
    },
  },
  {
    title: '超期超7天(已整改)',
    key: 'yzgcq7t',
    width: 180,
    align: 'left',
    resizable: true,
    defaultSortOrder: false,
    ellipsis: {
      tooltip: true,
    },
    sorter: 'default',
    renderSorterIcon: ({ order }: any) => {
      if (order === false) {
        return [
          h(NIcon, { class: 'mb-[-4px]', size: iconSize }, () => h(CaretUp)),
          h(NIcon, { class: 'mt-[-4px]', size: iconSize }, () => h(CaretDown)),
        ];
      }
      if (order === 'ascend')
        return h(NIcon, { size: iconSize }, () => h(CaretUp));
      if (order === 'descend')
        return h(NIcon, { size: iconSize }, () => h(CaretDown));
    },
  },
  {
    title: '超期已整改隐患总数',
    key: 'cqyzgyhzs',
    width: 180,
    align: 'left',
    resizable: true,
    defaultSortOrder: false,
    ellipsis: {
      tooltip: true,
    },
    sorter: 'default',
    renderSorterIcon: ({ order }: any) => {
      if (order === false) {
        return [
          h(NIcon, { class: 'mb-[-4px]', size: iconSize }, () => h(CaretUp)),
          h(NIcon, { class: 'mt-[-4px]', size: iconSize }, () => h(CaretDown)),
        ];
      }
      if (order === 'ascend')
        return h(NIcon, { size: iconSize }, () => h(CaretUp));
      if (order === 'descend')
        return h(NIcon, { size: iconSize }, () => h(CaretDown));
    },
  },
  {
    title: '超期隐患总数',
    key: 'cqyhzs',
    width: 180,
    align: 'left',
    resizable: true,
    defaultSortOrder: false,
    ellipsis: {
      tooltip: true,
    },
    sorter: 'default',
    renderSorterIcon: ({ order }: any) => {
      if (order === false) {
        return [
          h(NIcon, { class: 'mb-[-4px]', size: iconSize }, () => h(CaretUp)),
          h(NIcon, { class: 'mt-[-4px]', size: iconSize }, () => h(CaretDown)),
        ];
      }
      if (order === 'ascend')
        return h(NIcon, { size: iconSize }, () => h(CaretUp));
      if (order === 'descend')
        return h(NIcon, { size: iconSize }, () => h(CaretDown));
    },
  },
];

const sorterChange = (options: any) => {
  let _filed = options['columnKey'];
  let _sortType = options['order'];
  let sorts = [
    {
      filed: _filed,
      sortType: _sortType == 'descend' ? 'DESC' : 'ASC',
    },
  ];
  emits('getSorts', sorts);
};

// 初始化
const paramsData = ref({});
emits('getSorts', [{ filed: 'taskAllNum', sortType: 'DESC' }]);
async function getTableData(params: any) {
  loading.value = true;
  paramsData.value = params;
  try {
    let { data }: any = await yhzgcqyjlb(params);
    tableData.value = data || [];
    loading.value = false;
  } catch (error) {
    console.log(error);
    tableData.value = [];
    loading.value = false;
  }
}

async function rowClick(row: any) {
  try {
    // let { data }: any = await checkUnitIdIsBusinessFlag({
    //   unitId: row.unitId,
    // });
    let { data }: any = await getOrgDetailInfo({
      orgCode: row.unitId,
    });
    // console.log('data>>>', data);
    if (data.orgType != '1') {
      // 如果不存在下级业务单位，则切换机构数选中至当前单位节点
      emits('CurrentNodeKeychange', row.unitId);
    } else {
      // 跳转详情页
      router.push({
        path: 'hazard-management',
        query: {
          unitId: row.unitId,
          starTime: dayjs(paramsData.value.startTime).format(
            'YYYY-MM-DD' + '00:00:00'
          ),
          endTime: dayjs(paramsData.value.endTime).format(
            'YYYY-MM-DD' + '23:59:59'
          ),
        },
      });
    }
  } catch (error) {
    console.log('error>>>', error);
  }
}
function getCurrentDateTime() {
  const now = new Date();

  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份从0开始，所以要加1
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');

  return `${year}${month}${day}${hours}${minutes}${seconds}`;
}

function exportData() {
  // const exportColumns = columns.filter((col) => col.key !== 'index');
  // console.log('exportColumns>>>', exportColumns);
  tableRef.value?.downloadCsv({
    fileName: '单位任务检查完成情况-' + getCurrentDateTime(),
    keepOriginalData: false,
    columns: columns.filter((col) => col.key !== 'index'),
  });
}
const getCsvCell = (value: any, _: any, column: any) => {
  if (column.key === 'index') return undefined; // 过滤序号列数据
  if (column.key === 'taskEdAvg' || column.key === 'taskTimeEdAvg') {
    return `${value}%`;
  }
  return value;
};
// 修改CSV表头处理
const getCsvHeader = (col: any) => {
  if (col.key === 'index') return undefined; // 过滤序号列表头
  return col.title;
};

defineExpose({
  getTableData,
  exportData,
});
defineOptions({ name: 'data_statistics_table' });
</script>
<style lang="scss" scoped>
::v-deep {
  .n-data-table .n-data-table-th .n-data-table-sorter {
    display: inline-flex !important;
    flex-direction: column !important;
  }
}
</style>
