export const enum ACTION {
  SEARCH = 'SEARCH',
}

export const getTimeRange = (type: number) => {
  const now = new Date(); // 当前时间
  let startTime, endTime;

  switch (type) {
    case 0:
      startTime = new Date(now);
      startTime.setHours(0, 0, 0, 0); // 设置为当天的 00:00:00.000
      endTime = new Date(now);
      endTime.setHours(23, 59, 59, 999); // 设置为当天的 23:59:59.999
      break;
    case 1: // 昨日
      startTime = new Date(now);
      startTime.setDate(now.getDate() - 1); // 前一天
      startTime.setHours(0, 0, 0, 0); // 设置为当天的 00:00:00.000
      endTime = new Date(startTime);
      endTime.setHours(23, 59, 59, 999); // 设置为当天的 23:59:59.999
      break;

    case 2: // 本周
      startTime = new Date(now);
      startTime.setDate(
        now.getDate() - now.getDay() + (now.getDay() === 0 ? -6 : 1)
      ); // 本周一
      startTime.setHours(0, 0, 0, 0); // 设置为当天的 00:00:00.000
      endTime = new Date(now); //本周的时间截止到当前日期
      // endTime = new Date(startTime);
      // endTime.setDate(startTime.getDate() + 6); // 本周日
      endTime.setHours(23, 59, 59, 999); // 设置为当天的 23:59:59.999
      break;

    case 3: // 本月
      startTime = new Date(now);
      startTime.setDate(1); // 本月第一天
      startTime.setHours(0, 0, 0, 0); // 设置为当天的 00:00:00.000
      endTime = new Date(now); //本月的时间截止到当前日期
      // endTime.setMonth(now.getMonth() + 1, 0); // 本月最后一天
      endTime.setHours(23, 59, 59, 999); // 设置为当天的 23:59:59.999
      break;
    case 4:
      startTime = new Date(now);
      startTime.setMonth(now.getMonth() - 3); // 三个月前
      startTime.setHours(0, 0, 0, 0); // 设置为当天的 00:00:00.000
      endTime = new Date(now);
      endTime.setHours(23, 59, 59, 999); // 设置为当天的 23:59:59.999
      break;
    default:
      throw new Error('Invalid type. Type must be 1, 2, or 3.');
  }

  // 格式化日期为 YYYY-MM-DD
  const formatDate = (date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份补零
    const day = String(date.getDate()).padStart(2, '0'); // 日期补零
    return `${year}-${month}-${day}`;
  };

  return {
    startTime: formatDate(startTime),
    endTime: formatDate(endTime),
  };
};

// 禁用未来和一年前的数据不可选
export const isRangeDateDisabled = (
  ts: number,
  type: 'start' | 'end',
  range: [number, number] | null
) => {
  const now = Date.now();

  const currentDate = new Date();
  const pastDate = new Date(currentDate);
  pastDate.setFullYear(pastDate.getFullYear() - 3); // 3年前的日期
  const threeYearsAgo = pastDate.getTime();
  // 禁用未来的日期
  if (ts > now) {
    return true;
  }

  // 禁用3年前的日期
  if (ts < threeYearsAgo) {
    return true;
  }

  if (type === 'end' && range !== null) {
    const startDate = range[0];
    const oneYearAfterStart = startDate + 365 * 24 * 60 * 60 * 1000; // 开始时间后365天

    // 禁用超过开始时间365天后的日期
    if (ts > oneYearAfterStart) {
      return true;
    }
  }

  // // 禁用未来和一年前的日期
  // if (ts > now || ts < oneYearAgo) {
  //   return true;
  // }

  // 其他逻辑（例如间隔至少七天）
  // if (type === 'start' && range !== null) {
  //   return range[1] - ts < 7 * 86400000; // 间隔至少七天
  // }
  // if (type === 'end' && range !== null) {
  //   return ts - range[0] < 7 * 86400000; // 间隔至少七天
  // }
  return false;
};

export function formatNumber(num: any) {
  if (!num) {
    return 0 + '%';
  }
  const strNum = num.toString();
  const parts = strNum.split('.');
  if (parts[1] && parts[1].length > 0) {
    return num.toFixed(2) + '%'; // 展示两位小数
  } else {
    return num + '%'; // 展示传过来的整数
  }
}
