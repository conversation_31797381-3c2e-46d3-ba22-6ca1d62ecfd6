import { api } from '@/api';
import { $http } from '@tanzerfe/http';

//   获取直属下级机构列表
export function getOrgUnderling(data: any) {
  const url = api.getUrl(
    api.type.hazard,
    '/hazardPlanStatistic/unitSelectList'
  );
  return $http.post(url, {
    data: { _cfg: { showTip: false, showOkTip: false }, ...data },
  });
}
export function treeList(data: any) {
  const url = api.getUrl(
    'ehs-clnt-hazard-service',
    '/hazardEssentialFactorClass/treeList'
  );
  return $http.post(url, {
    data: { _cfg: { showTip: false, showOkTip: false }, ...data },
  });
}

//   任务总数统计
export function getTaskAllStatistic(data: any) {
  const url = api.getUrl(
    api.type.hazard,
    '/hazardPlanStatistic/taskAllStatistic'
  );
  return $http.post(url, {
    data: { _cfg: { showTip: false, showOkTip: false }, ...data },
  });
}
// 隐患整改超期预警列表
export function yhzgcqyjlb(data: any) {
  const url = api.getUrl('ehs-api-hazard-service', '/statisReport/yhzgcqyjlb');
  return $http.post(url, {
    data: { _cfg: { showTip: false, showOkTip: false }, ...data },
  });
}

// 隐患整改超期预警统计
export function yhzgcqyjtj(data: any) {
  const url = api.getUrl('ehs-api-hazard-service', '/statisReport/yhzgcqyjtj');
  return $http.post(url, {
    data: { _cfg: { showTip: false, showOkTip: false }, ...data },
  });
}
export function dwfxyhqktj(data: any) {
  const url = api.getUrl('ehs-api-hazard-service', '/statisReport/dwfxyhqktj');
  return $http.post(url, {
    data: { _cfg: { showTip: false, showOkTip: false }, ...data },
  });
}
export function yhflqktj(data: any) {
  const url = api.getUrl('ehs-api-hazard-service', '/statisReport/yhflqktj');
  return $http.post(url, {
    data: { _cfg: { showTip: false, showOkTip: false }, ...data },
  });
}
export function yhdjdtbt(data: any) {
  const url = api.getUrl(
    'ehs-api-hazard-service',
    '/statisReport/tjbbbtzd',
    data
  );
  return $http.post(url, {
    data: { _cfg: { showTip: false, showOkTip: false } },
  });
}
export function dyhflqklb(data: any) {
  const url = api.getUrl('ehs-api-hazard-service', '/statisReport/dyhflqklb');
  return $http.post(url, {
    data: { _cfg: { showTip: false, showOkTip: false }, ...data },
  });
}
export function dwfxyhqklb(data: any) {
  const url = api.getUrl('ehs-api-hazard-service', '/statisReport/dwfxyhqklb');
  return $http.post(url, {
    data: { _cfg: { showTip: false, showOkTip: false }, ...data },
  });
}
// 数据统计概况列表
export function getTaskAllChildStatistic(data: any) {
  const url = api.getUrl(
    api.type.hazard,
    '/hazardPlanStatistic/taskAllChildStatistic'
  );
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: false }, ...data },
  });
}

// 数据统计概况批量导出
export function taskAllChildStatisticExport() {
  const url = api.getUrl(
    api.type.hazard,
    '/hazardPlanStatistic/taskAllChildStatisticExport'
  );
  return url;
}

// 任务详情统计-任务执行情况列表
export function getTaskExecuteDetailPage(data: any) {
  const url = api.getUrl(
    api.type.hazard,
    '/hazardPlanStatistic/taskExecuteDetailPage'
  );
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: false }, ...data },
  });
}

// 数据统计概况批量导出
export function taskExecuteDetailExport() {
  const url = api.getUrl(
    api.type.hazard,
    '/hazardPlanStatistic/taskExecuteDetailExport'
  );
  return url;
}
export function yhzgcqyjExport() {
  const url = api.getUrl(
    'ehs-api-hazard-service',
    '/statisReport/yhzgcqyjExport'
  );
  return url;
}
export function yhzgcqyjyhExport() {
  const url = api.getUrl(
    'ehs-api-hazard-service',
    '/statisReport/yhzgcqyjyhExport'
  );
  return url;
}
export function dwfxyhqklbyhExport() {
  const url = api.getUrl(
    'ehs-api-hazard-service',
    '/statisReport/dwfxyhqklbyhExport'
  );
  return url;
}
export function yhflqklbyhExport() {
  const url = api.getUrl(
    'ehs-api-hazard-service',
    '/statisReport/yhflqklbyhExport'
  );
  return url;
}
export function dwfxyhqklbExport() {
  const url = api.getUrl(
    'ehs-api-hazard-service',
    '/statisReport/dwfxyhqklbExport'
  );
  return url;
}
export function yhflqklbExport() {
  const url = api.getUrl(
    'ehs-api-hazard-service',
    '/statisReport/yhflqklbExport'
  );
  return url;
}
// 校验当前单位是否是最底层的业务单位
export function checkUnitIdIsBusinessFlag(data: any) {
  const url = api.getUrl(
    api.type.hazard,
    '/hazardPlanStatistic/checkUnitIdIsBusinessFlag',
    data
  );
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: false } },
  });
}

// 根据机构id获取机构详情 /ehsUpms/getOrgDetailInfo
export function getOrgDetailInfo(data: any) {
  const url = api.getUrl(api.type.hazard, '/ehsUpms/getOrgDetailInfo', data);
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: false } },
  });
}
