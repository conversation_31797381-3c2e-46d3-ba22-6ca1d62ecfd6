<template>
  <div class="flex flex-col" v-if="isBusinessFlag">
    <com-bread :data="breadData" />
    <div class="item_content notVw flex-1">
      <UnitSelect
        ref="unitSelectRef"
        class="unit-tree"
        style="min-width: 323px"
        @updateVal="updateVal"
        @getTopUnitName="getTopUnitName"
        v-show="showTree"
      />
      <div class="right-table" :style="{ width: showTree ? '62vw' : '100%' }">
        <img
          src="@/assets/select_tree.png"
          class="select-tree"
          :style="{ width: toVw(30), left: `-${toVw(15)}` }"
          v-show="userInfo.unitOrgType == '2'"
          @click="showTree = !showTree"
          alt=""
        />
        <!-- 树面包屑 -->
        <com-bread :data="treeBreadData" />
        <Filter
          :treeUnitId="treeUnitId"
          :parentId="parentId"
          :treeLevelCode="treeLevelCode"
          @action="actionFn"
          :sorts="sorts"
          source="list"
          ref="filterRef"
          @updateTab="updateTab"
          @exportTable="exportTable"
        />
        <TableList
          v-if="currentTab == '1'"
          class="mt-4"
          ref="tableCompRef"
          @getSorts="getSorts"
          @CurrentNodeKeychange="CurrentNodeKeychange"
        />
        <TableList2
          :treeUnitId="treeUnitId"
          v-else-if="currentTab == '2'"
          class="mt-4"
          ref="tableCompRef"
          @getSorts="getSorts"
          @CurrentNodeKeychange="CurrentNodeKeychange"
        />
        <TableList3
          v-else
          class="mt-4"
          ref="tableCompRef"
          @getSorts="getSorts"
          @CurrentNodeKeychange="CurrentNodeKeychange"
        />

        <!-- <el-empty v-else description="快马加鞭开发中，敬请期待" /> -->
      </div>
    </div>
  </div>
  <!-- 如果当前的登录人单位是最底级的业务单位 则直接显示详情页面 -->
  <DataStatisticsDetail v-else :data="detailData" />
</template>

<script setup lang="ts">
import { ref, watchEffect, nextTick } from 'vue';
import UnitSelect from '@/components/unitSelect/index.vue';
import Filter from '@/views/data_statistics/components/Filter.vue';
import TableList from '@/views/data_statistics/components/Table.vue';
import TableList2 from '@/views/data_statistics/components/Table2.vue';
import TableList3 from '@/views/data_statistics/components/Table3.vue';
import DataStatisticsDetail from '@/views/data_statistics/detail/index.vue';
import ComBread from '@/components/breadcrumb/ComBread.vue';
import { ACTION } from '@/views/data_statistics/constant';
import { useStore } from '@/store';
import { toVw } from '@/utils/fit';

const { userInfo } = useStore();
const treeBreadData = ref([{ name: '' }]);
const filterRef = ref();
const tableCompRef = ref();

let filterVal = ref({ unitId: userInfo.unitId, levelCode: null });
const parentId = ref();
const showTree = ref(true);
const currentTab = ref('1');
const treeUnitId = ref();
const treeLevelCode = ref();
const unitSelectRef = ref();
const breadData = [{ name: '隐患治理系统' }, { name: '统计报表' }];
const isBusinessFlag = ref(false);
const sorts = ref([]);

// 是否还存在下级业务单位
isBusinessFlag.value = localStorage.getItem('isBusinessFlag') == 'true';
const detailData = ref({
  unitId: userInfo.unitId,
  unitName: userInfo.unitName,
  levelCode: localStorage.getItem('levelCode'),
});

function getSorts(_sorts: any) {
  sorts.value = _sorts;
  console.log('sorts.value>>>', sorts.value);
}

function updateTab(tab: string) {
  currentTab.value = tab;
}

function getTopUnitName(data: any) {
  treeBreadData.value[0].name = data;
}

function CurrentNodeKeychange(unitId: any) {
  unitId && unitSelectRef.value.changeCurrentNodeKey(unitId);
}

function exportTable() {
  tableCompRef.value?.exportData();
}

// 组织结构切换
function updateVal(unitId: any, _isBusinessUnit: boolean, data: any) {
  parentId.value = data.parentId;
  console.log('updateVal', unitId, _isBusinessUnit, data);
  if (treeBreadData.value[0].name != data.treeName) {
    treeBreadData.value = [
      { name: treeBreadData.value[0].name },
      { name: data.treeName },
    ];
  } else {
    treeBreadData.value = [{ name: treeBreadData.value[0].name }];
  }
  treeUnitId.value = data.id;
  treeLevelCode.value = data.levelCode;
}

watchEffect(() => {
  if (filterVal.value.unitId) {
    console.log('当前选择的单位id', filterVal.value.unitId);
  }
});

function actionFn({ action, data }: any) {
  console.log(data, '>>>>>');
  if (action === ACTION.SEARCH) {
    nextTick(() => {
      tableCompRef.value?.getTableData(data);
    });
  }
}
defineOptions({ name: 'data_statistics' });
</script>

<style module lang="scss"></style>
