<template>
  <div class="flex flex-col">
    <com-bread :data="breadData" />
    <div class="item_content notVw flex-1">
      <div class="right-table" :style="{ width: '100%' }">
        <Filter
          :treeUnitId="treeUnitId"
          :treeLevelCode="treeLevelCode"
          :treeUnitName="treeUnitName"
          @action="actionFn"
          :sorts="sorts"
          source="detail"
          @updateTab="updateTab"
        />
        <TableList @getSorts="getSorts" v-if="currentTab == '1'" class="mt-4" ref="tableCompRef" />
        <el-empty v-else description="快马加鞭开发中，敬请期待" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue';
import Filter from '@/views/data_statistics/components/Filter.vue';
import TableList from '@/views/data_statistics/detail/Table.vue';
import ComBread from '@/components/breadcrumb/ComBread.vue';
import { ACTION } from '@/views/data_statistics/constant';
import { useRoute } from 'vue-router';

const props = defineProps({
  data: {
    type: Object || null,
    default: () => {},
  },
});
const sorts = ref([]);
const route = useRoute();
const breadData = [{ name: '隐患治理系统' }, { name: '数据统计概况' }];
const tableCompRef = ref();
const currentTab = ref('1');
const treeUnitId = ref();
const treeLevelCode = ref();
const treeUnitName = ref();
function updateTab(tab: string) {
  currentTab.value = tab;
}
function getSorts(_sorts: any) {
  sorts.value = _sorts;
  console.log('sorts.value>>>', sorts.value);
}

function actionFn({ action, data }: any) {
  if (action === ACTION.SEARCH) {
    nextTick(() => {
      tableCompRef.value?.getTableData(data);
    });
  }
}

setTimeout(() => {
  if (props.data) {
    treeUnitId.value = props.data.unitId;
    treeLevelCode.value = props.data.levelCode;
    treeUnitName.value = props.data.unitName;
  } else {
    const { unitId, levelCode, unitName } = route.query;
    console.log(' unitId, levelCode ,unitName>>>', unitId, levelCode, unitName);
    treeUnitId.value = unitId;
    treeLevelCode.value = levelCode;
    treeUnitName.value = unitName;
  }
}, 0);

defineOptions({ name: 'data_statistics' });
</script>

<style module lang="scss"></style>
