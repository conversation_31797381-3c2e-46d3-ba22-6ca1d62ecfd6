<template>
  <div class="flex flex-col justify-between" style="height: 100%">
    <n-data-table
      class="h-full com-table"
      remote
      :columns="columns"
      :data="tableData"
      :bordered="false"
      :flex-height="true"
      v-loading="loading"
      :scroll-x="1500"
      :pagination="pagination"
      :render-cell="useEmptyCell"
      :row-key="(row: any) => row.id"
      @update:sorter="handleUpdateSorter"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, h, computed } from 'vue';
import { getTaskExecuteDetailPage } from '@/views/data_statistics/fetchData';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { taskState } from '@/components/table-col/taskStateTaskManage';
import { CaretDown, CaretUp } from '@vicons/ionicons5';
import { NIcon } from 'naive-ui';
const iconSize = 16; // 设置统一图标尺寸
const emits = defineEmits(['action', 'getSorts']);
const tableData = ref();
const loading = ref(false);
const { pagination, updateTotal } = useNaivePagination(getTableData);
let searchForm = ref({});
const sortStatesRef = ref([]);
const sortKeyMapOrderRef = computed(() =>
  sortStatesRef.value.reduce((result, { columnKey, order }) => {
    result[columnKey] = order;
    return result;
  }, {})
);
let columns = [
  {
    title: '序号',
    key: 'index',
    width: 80,
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
    render(row: any, index: number) {
      let { page, pageSize }: any = pagination;
      return (page - 1) * pageSize + index + 1;
    },
  },
  {
    title: '检查任务名称',
    key: 'taskName',
    align: 'left',
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '任务起止日期',
    align: 'left',
    defaultSortOrder: false,
    sorter: false,
    ellipsis: {
      tooltip: true,
    },
    render(row: any) {
      return row.planStartTime + ' - ' + row.planEndTime;
    },
  },
  {
    title: '检查类型',
    key: 'checkType',
    align: 'left',
    resizable: true,
    defaultSortOrder: false,
    sorter: 'default',
    renderSorterIcon: ({ order }: any) => {
      if (order === false) {
        return [
          h(NIcon, { class: 'mb-[-4px]', size: iconSize }, () => h(CaretUp)),
          h(NIcon, { class: 'mt-[-4px]', size: iconSize }, () => h(CaretDown)),
        ];
      }
      if (order === 'ascend') return h(NIcon, { size: iconSize }, () => h(CaretUp));
      if (order === 'descend') return h(NIcon, { size: iconSize }, () => h(CaretDown));
    },
    ellipsis: {
      tooltip: true,
    },
    // render(row: any) {
    //   return row.checkRange == 1 ? '综合检查计划' : '点位巡查计划';
    // },
  },
  {
    title: '任务实际开始时间',
    key: 'actualStartTime',
    align: 'left',
    resizable: true,
    // defaultSortOrder: false,
    sorter: 'default',
    renderSorterIcon: ({ order }: any) => {
      if (order === false) {
        return [
          h(NIcon, { class: 'mb-[-4px]', size: iconSize }, () => h(CaretUp)),
          h(NIcon, { class: 'mt-[-4px]', size: iconSize }, () => h(CaretDown)),
        ];
      }
      if (order === 'ascend') return h(NIcon, { size: iconSize }, () => h(CaretUp));
      if (order === 'descend') return h(NIcon, { size: iconSize }, () => h(CaretDown));
    },
    ellipsis: {
      tooltip: true,
    },
    render(row: any) {
      return row.actualStartTime || '--';
    },
  },
  {
    title: '任务实际结束时间',
    key: 'actualEndTime',
    align: 'left',
    resizable: true,
    defaultSortOrder: false,
    sorter: 'default',
    ellipsis: {
      tooltip: true,
    },
    renderSorterIcon: ({ order }: any) => {
      if (order === false) {
        return [
          h(NIcon, { class: 'mb-[-4px]', size: iconSize }, () => h(CaretUp)),
          h(NIcon, { class: 'mt-[-4px]', size: iconSize }, () => h(CaretDown)),
        ];
      }
      if (order === 'ascend') return h(NIcon, { size: iconSize }, () => h(CaretUp));
      if (order === 'descend') return h(NIcon, { size: iconSize }, () => h(CaretDown));
    },
    render(row: any) {
      return row.actualEndTime || '--';
    },
  },
  {
    title: '任务状态',
    key: 'taskState',
    align: 'left',
    defaultSortOrder: false,
    sorter: 'default',
    renderSorterIcon: ({ order }: any) => {
      if (order === false) {
        return [
          h(NIcon, { class: 'mb-[-4px]', size: iconSize }, () => h(CaretUp)),
          h(NIcon, { class: 'mt-[-4px]', size: iconSize }, () => h(CaretDown)),
        ];
      }
      if (order === 'ascend') return h(NIcon, { size: iconSize }, () => h(CaretUp));
      if (order === 'descend') return h(NIcon, { size: iconSize }, () => h(CaretDown));
    },
    ellipsis: {
      tooltip: true,
    },
    render(row: any) {
      return h(taskState, { row });
    },
  },
  {
    title: '任务时效',
    key: 'overdueDays',
    align: 'left',
    resizable: true,
    defaultSortOrder: false,
    sorter: 'default',
    renderSorterIcon: ({ order }: any) => {
      if (order === false) {
        return [
          h(NIcon, { class: 'mb-[-4px]', size: iconSize }, () => h(CaretUp)),
          h(NIcon, { class: 'mt-[-4px]', size: iconSize }, () => h(CaretDown)),
        ];
      }
      if (order === 'ascend') return h(NIcon, { size: iconSize }, () => h(CaretUp));
      if (order === 'descend') return h(NIcon, { size: iconSize }, () => h(CaretDown));
    },
    ellipsis: {
      tooltip: true,
    },
    render(row: any) {
      return row.overdueDays == '0' ? '正常' : '逾期' + row.overdueDays + '天';
    },
  },
  {
    title: '检查执行方式',
    key: 'checkExecuteMethod',
    defaultSortOrder: false,
    sorter: 'default',
    renderSorterIcon: ({ order }: any) => {
      if (order === false) {
        return [
          h(NIcon, { class: 'mb-[-4px]', size: iconSize }, () => h(CaretUp)),
          h(NIcon, { class: 'mt-[-4px]', size: iconSize }, () => h(CaretDown)),
        ];
      }
      if (order === 'ascend') return h(NIcon, { size: iconSize }, () => h(CaretUp));
      if (order === 'descend') return h(NIcon, { size: iconSize }, () => h(CaretDown));
    },
    ellipsis: {
      tooltip: true,
    },
    render(row: any) {
      return row.checkExecuteMethod == 1 ? '非逐一检查' : '逐一检查';
    },
  },
  {
    title: '检查内容数',
    key: 'checkContentCount',
    defaultSortOrder: false,
    sorter: 'default',
    renderSorterIcon: ({ order }: any) => {
      if (order === false) {
        return [
          h(NIcon, { class: 'mb-[-4px]', size: iconSize }, () => h(CaretUp)),
          h(NIcon, { class: 'mt-[-4px]', size: iconSize }, () => h(CaretDown)),
        ];
      }
      if (order === 'ascend') return h(NIcon, { size: iconSize }, () => h(CaretUp));
      if (order === 'descend') return h(NIcon, { size: iconSize }, () => h(CaretDown));
    },
    ellipsis: {
      tooltip: true,
    },
    render(row: any) {
      return row.checkContentCount;
    },
  },
  {
    title: '已检查内容数',
    key: 'taskTimeEdAvg',
    defaultSortOrder: false,
    sorter: 'default',
    renderSorterIcon: ({ order }: any) => {
      if (order === false) {
        return [
          h(NIcon, { class: 'mb-[-4px]', size: iconSize }, () => h(CaretUp)),
          h(NIcon, { class: 'mt-[-4px]', size: iconSize }, () => h(CaretDown)),
        ];
      }
      if (order === 'ascend') return h(NIcon, { size: iconSize }, () => h(CaretUp));
      if (order === 'descend') return h(NIcon, { size: iconSize }, () => h(CaretDown));
    },
    ellipsis: {
      tooltip: true,
    },
    render(row: any) {
      return row.checkExecuteMethod == 1 ? '--' : row.checkedContentCount;
    },
  },
  {
    title: '检查进度',
    key: 'checkProgress',
    defaultSortOrder: false,
    sorter: 'default',
    renderSorterIcon: ({ order }: any) => {
      if (order === false) {
        return [
          h(NIcon, { class: 'mb-[-4px]', size: iconSize }, () => h(CaretUp)),
          h(NIcon, { class: 'mt-[-4px]', size: iconSize }, () => h(CaretDown)),
        ];
      }
      if (order === 'ascend') return h(NIcon, { size: iconSize }, () => h(CaretUp));
      if (order === 'descend') return h(NIcon, { size: iconSize }, () => h(CaretDown));
    },
    ellipsis: {
      tooltip: true,
    },
    render(row: any) {
      return row.checkExecuteMethod == 1 ? '--' : (row.checkProgress || '0') + '%';
    },
  },
  {
    title: '发现隐患数',
    key: 'hazardCount',
    defaultSortOrder: false,
    sorter: 'default',
    renderSorterIcon: ({ order }: any) => {
      if (order === false) {
        return [
          h(NIcon, { class: 'mb-[-4px]', size: iconSize }, () => h(CaretUp)),
          h(NIcon, { class: 'mt-[-4px]', size: iconSize }, () => h(CaretDown)),
        ];
      }
      if (order === 'ascend') return h(NIcon, { size: iconSize }, () => h(CaretUp));
      if (order === 'descend') return h(NIcon, { size: iconSize }, () => h(CaretDown));
    },
    ellipsis: {
      tooltip: true,
    },
    render(row: any) {
      return row.taskState == '1' ? '--' : row.hazardCount || '0';
    },
  },
];
function handleUpdateSorter(sorters: any) {
  console.log(sorters);
  // let params = { sort: [] };
  // // { filed: '', sortType: '' }
  // params.sort.push({
  //   filed: sorters.columnKey,
  //   sortType: sorters.order == 'descend' ? 'desc' : 'asc',
  // });
  // console.log(params);
  // getTableData(params);
  // sortStatesRef.value = [].concat(sorters);
}
// 初始化
emits('getSorts', [{ filed: 'planStartTime', sortType: 'desc' }]);
async function getTableData(params: any) {
  loading.value = true;
  searchForm.value = { ...searchForm.value, ...params };
  try {
    let { data }: any = await getTaskExecuteDetailPage({
      ...searchForm.value,
      pageSize: pagination.pageSize,
      pageNo: pagination.page,
      sorts: [{ filed: 'planStartTime', sortType: 'desc' }],
    });
    tableData.value = data.rows || [];
    updateTotal(data.total ?? 0);
    loading.value = false;
  } catch (error) {
    console.log(error);
    tableData.value = [];
    loading.value = false;
  }
}
defineExpose({
  getTableData,
});
defineOptions({ name: 'data_statistics_table' });
</script>
<style lang="scss" scoped>
::v-deep {
  .n-data-table .n-data-table-th .n-data-table-sorter {
    display: inline-flex !important;
    flex-direction: column !important;
  }
}
</style>
