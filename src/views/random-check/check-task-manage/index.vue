<template>
  <div class="flex flex-col">
    <com-bread :data="breadData" />
    <div class="item_content notVw flex-1">
      <UnitSelect class="unit-tree" @updateVal="updateVal" v-if="showTree" />
      <div class="right-table" :style="{ width: showTree ? '62vw' : '100%' }">
        <img
          src="@/assets/select_tree.png"
          class="select-tree"
          :style="{ width: toVw(30), left: `-${toVw(15)}` }"
          v-if="userInfo.unitOrgType == '2'"
          @click="showTree = !showTree"
          alt=""
        />
        <Filter @action="actionFn" ref="filterRef" :objId="filterValUnitId" :pagination="tableCompRef?.pagination" />
        <TableList ref="tableCompRef" @action="actionFn" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Filter from './comp/Filter.vue';
import TableList from './comp/table/Table.vue';
import { $dialog } from '@/common/shareContext/useDialogCtx.ts';
import ComBread from '@/components/breadcrumb/ComBread.vue';
import { ACTION } from './constant';
import { deleteTemp } from './fetchData';
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { IBreadData } from '@/components/breadcrumb/type.ts';
import { useStore } from '@/store/index';
import { toVw } from '@/utils/fit';

const { userInfo } = useStore();
const currentAction = ref<any>({ action: ACTION.ADD, data: {} });
const breadData: IBreadData[] = [{ name: '隐患治理系统' }, { name: '随机检查' }];
const router = useRouter();
const filterRef = ref();
const tableCompRef = ref();
const showTree = ref(true);
const filterValUnitId = ref('');
let filterVal = { objId: userInfo.unitId };

// 组织结构切换
function updateVal(unitId: string) {
  filterRef.value?.clearFrom();
  filterValUnitId.value = unitId;
  filterVal = { objId: unitId, ...filterRef.value?.getVal() };
  tableCompRef.value.pagination.pageNo = 1;
  // tableCompRef.value.pagination.pageSize = 10;
  handleSearch(filterVal);
}

function actionFn(val: any) {
  currentAction.value = val;
  if (val.action === ACTION.ADD) {
    return router.push({ name: 'checklistConfModify' });
  }
  if (val.action === ACTION.SEARCH) {
    let data = filterRef.value?.getVal();
    filterVal = { objId: filterVal.objId, ...data };
    return handleSearch(filterVal);
  }
  if (val.action === ACTION.DELETE) {
    return handleDelete(val.data.id);
  }
}

function handleSearch(data: object) {
  tableCompRef.value?.getTableDataWrap(data);
}
// 删除
function handleDelete(templateId: string) {
  $dialog.error({
    title: '删除模板',
    content: `确定删除该模板？`,
    positiveText: '确定',
    transformOrigin: 'center',
    onPositiveClick: async () => {
      deleteTemp({ templateId }).then(() => {
        handleSearch(filterVal);
      });
    },
  });
}

defineOptions({ name: 'planned-management' });
</script>

<style module lang="scss"></style>
