import { taskState } from '@/components/table-col/taskState';
import { DataTableColumn } from 'naive-ui';
import { h } from 'vue';

export const cols: DataTableColumn[] = [
  {
    title: '随机检查名称',
    key: 'checkName',
    align: 'left',
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '检查对象',
    key: 'objName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '任务状态',
    key: 'checkStatusName',
    align: 'left',
    render: (row) => h(taskState, { row: { ...row, checkStatus: +row.checkStatus + 1 }, field: 'checkStatus' }),
  },
  {
    title: '创建时间',
    key: 'createTime',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
    render: (row: any) => {
      return row.createTime ? row.createTime : '--';
    },
  },
  {
    title: '创建人',
    key: 'createByName',
    align: 'left',
  },
];
