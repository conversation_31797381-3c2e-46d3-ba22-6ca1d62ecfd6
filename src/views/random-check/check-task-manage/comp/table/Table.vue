<!--
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-10-16 14:00:34
 * @LastEditors: 悦悦 <EMAIL>
 * @LastEditTime: 2024-10-16 15:20:21
 * @FilePath: /隐患一张图/ehs-hazard/src/views/random-check/check-task-manage/comp/table/Table.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <n-data-table
    class="com-table"
    style="height: 100%"
    remote
    striped
    :columns="columns"
    :data="tableData"
    :bordered="false"
    :flex-height="true"
    :loading="loading"
    :pagination="pagination"
    :render-cell="useEmptyCell"
    :scroll-x="1500"
  />
</template>

<script setup lang="ts">
import { h, ref, VNode, toRaw } from 'vue';
import { DataTableColumns, NButton } from 'naive-ui';
import { cols } from './columns';
import { useRoute, useRouter } from 'vue-router';
import { getRandomCheckList } from '@/views/random-check/check-task-manage/fetchData';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { IObj } from '@/types';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';

const router = useRouter();
const route = useRoute();
const emits = defineEmits(['action']);
const businessId = ref<string>(route.query?.id as string).value;

const [loading, search] = useAutoLoading(true);
const { pagination, updateTotal } = useNaivePagination(getTableData);

const columns = ref<DataTableColumns>([]);
const tableData = ref<any[]>([]);

function setColumns() {
  columns.value.push(...cols);
  // 添加操作栏 action
  columns.value.push({
    fixed: 'right',
    width: '100px',
    title: '操作',
    key: 'actions',
    align: 'center',
    render(row) {
      return getActionBtn(row);
    },
  });
}
setColumns();

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          size: 'small',
          type: 'primary',
          ghost: true,
          onClick: () => router.push({ name: 'checkTaskDetail', query: { id: row.id } }),
        },
        { default: () => '详情' }
      ),
    ],
  ];
  return useActionDivider(acList);
}

let filterData: IObj<any> = {}; // 搜索条件
function getTableData() {
  loading.value = true;
  const params = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    businessId,
    ...filterData,
  };

  search(getRandomCheckList(params)).then((res: any) => {
    tableData.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
  loading.value = false;
}

function getTableDataWrap(data: IObj<any>) {
  filterData = data;
  pagination.page = 1;
  getTableData();
}
defineOptions({ name: 'checkTampTableComp' });
defineExpose({ getTableDataWrap, getTableData, pagination });
</script>
<style module lang="scss"></style>
