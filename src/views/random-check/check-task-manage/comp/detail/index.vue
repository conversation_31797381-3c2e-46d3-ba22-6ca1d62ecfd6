<template>
  <div>
    <com-bread :data="breadData"></com-bread>
    <div class="com-table-filter">
      <Header :title="taskDetail.checkName" :blueIcon="true"></Header>
      <div class="grid grid-cols-4 gap-4 gap-8 py-5">
        <div class="flex">
          <div class="w-[105px]">检查类型：</div>
          <div class="truncate hover:text-clip">{{ taskDetail.hazardRandomCheck || '--' }}</div>
        </div>
        <div class="flex">
          <div class="w-[105px]">创建人：</div>
          <div class="truncate hover:text-clip">{{ taskDetail.createUserName || '--' }}</div>
        </div>
        <div class="flex">
          <div class="w-[105px]">创建时间：</div>
          <div class="truncate hover:text-clip">{{ taskDetail.createTime || '--' }}</div>
        </div>
        <div class="flex">
          <div class="w-[105px]">任务起止时间：</div>
          <div class="truncate hover:text-clip">{{ taskDetail.eventTime || '--' }}</div>
        </div>
        <div class="flex">
          <div class="w-[105px]">任务状态：</div>
          <div class="truncate hover:text-clip">{{ taskDetail.hazardRandomCheckStatus || '--' }}</div>
        </div>
        <div class="flex">
          <div class="w-[105px]">检查对象：</div>
          <div class="truncate hover:text-clip">{{ taskDetail.ObjName || '--' }}</div>
        </div>
        <div class="flex">
          <div class="w-[105px]">检查负责人：</div>
          <div class="truncate hover:text-clip">
            {{
              (taskDetail.commandUserList &&
                taskDetail.commandUserList.length &&
                taskDetail.commandUserList.map((item: any) => item.checkUserName).join(' / ')) ||
              '--'
            }}
          </div>
        </div>
        <div class="flex">
          <div class="w-[105px]">检查参与人：</div>
          <div class="truncate hover:text-clip">
            {{
              (taskDetail.checkUserList &&
                taskDetail.checkUserList.length &&
                taskDetail.checkUserList.map((item: any) => item.checkUserName).join(' / ')) ||
              '--'
            }}
          </div>
        </div>
      </div>
      <Header title="隐患检查情况" :blueIcon="true"></Header>
      <div class="flex justify-start items-center py-5">
        <div class="trouble-number flex justify-start items-center">
          <img src="./icon1.png" alt="" />
          <div class="ml-3">
            <div>隐患数量</div>
            <div style="color: #ff9500; font-size: 20px; margin-top: 10px">
              {{ recordStatistics.total || '0' }}
            </div>
          </div>
        </div>
        <div class="flex justify-start items-center other-card ml-5">
          <img src="./bg1.png" alt="" style="position: absolute; top: 0; left: 0" />
          <div style="z-index: 2">
            <div>已整改</div>
            <div style="color: #00b578; font-size: 20px; margin-top: 10px">
              {{ recordStatistics.disposedNum || '0' }}
            </div>
          </div>
        </div>
        <div class="flex justify-start items-center other-card ml-5">
          <img src="./bg1.png" alt="" style="position: absolute; top: 0; left: 0" />
          <div style="z-index: 2">
            <div>待整改</div>
            <div style="color: #fa5151; font-size: 20px; margin-top: 10px">
              {{ recordStatistics.unDisposedNum || '0' }}
            </div>
          </div>
        </div>
        <div class="flex justify-start items-center other-card ml-5">
          <img src="./bg2.png" alt="" style="position: absolute; top: 0; left: 0" />
          <div style="z-index: 2">
            <div>超期数量</div>
            <div style="color: #ff9500; font-size: 20px; margin-top: 10px">
              {{ recordStatistics.timeoutNum || '0' }}
            </div>
          </div>
        </div>
        <div
          class="ml-5 h-[80px] w-[494px] py-[15px] px-[32px] relative"
          v-if="levelStatistics && levelStatistics.length"
        >
          <img src="./bg3.png" alt="" style="position: absolute; top: 0; left: 0" />
          <div style="z-index: 2; position: relative; top: -6px" class="flex justify-start items-center">
            <div v-for="(item, idx) in levelStatistics" :key="idx" style="width: 25%">
              <div :title="item.hazardLevelName.length > 4 ? item.hazardLevelName : ''">
                {{ item.hazardLevelName.length > 4 ? `${item.hazardLevelName.slice(0, 4)}...` : item.hazardLevelName }}
              </div>
              <div style="color: #e23b50; font-size: 20px; margin-top: 10px" :style="{ color: colorList[idx] }">
                {{ item.total || '0' }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <Filter @action="actionFn" :userList="taskDetail.userList" />
      <TableList ref="tableCompRef" :hazardRandomCheckStatus="taskDetail.hazardRandomCheckStatus" action="actionFn" />
    </div>
  </div>
</template>

<script setup lang="ts">
import Filter from './Filter.vue';
import TableList from './Table.vue';
import { $dialog } from '@/common/shareContext/useDialogCtx.ts';
import ComBread from '@/components/breadcrumb/ComBread.vue';
import Header from '@/components/header/ComHeaderB.vue';

import { ACTION } from '../../constant';
import { deleteTemp, hazardRandomCheckDetail } from '../../fetchData';
import { IObj } from '@/types';
import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { IBreadData } from '@/components/breadcrumb/type.ts';

import { useStore } from '@/store/index';
import { levelStatisticsMeger, getStatisticsMeger } from '@/views/hazard-management/fetchData';

const { userInfo } = useStore();

const router = useRouter();
const route = useRoute();
const id = ref<string>(route.query?.id as string).value;
const taskDetail = ref<any>({});
const recordStatistics = ref<any>({});
const levelStatistics = ref<any>({});
const currentAction = ref<any>({ action: ACTION.ADD, data: {} });
const breadData: IBreadData[] = [
  { name: '隐患治理系统' },
  {
    name: '随机检查',
    clickable: true,
    routeRaw: {
      name: 'randomCheck',
    },
  },
  { name: '任务详情' },
];

const colorList = ['#e23b50', '#f59a23', '#bfbf00', '#76b90e'];
// 任务id
function actionFn(val: any) {
  currentAction.value = val;
  if (val.action === ACTION.ADD) {
    return router.push({ name: 'checklistConfModify' });
  }
  if (val.action === ACTION.SEARCH) {
    return handleSearch(val.data);
  }
  if (val.action === ACTION.DELETE) {
    return handleDelete(val.data as any);
  }
}

const tableCompRef = ref();
function handleSearch(data: IObj<any> = {}) {
  tableCompRef.value?.getTableDataWrap({ ...data, hazardRandomCheckStatus: taskDetail.value.hazardRandomCheckStatus });
}

// 获取任务详情
async function getTaskDetail() {
  const res: any = await hazardRandomCheckDetail({ id });
  let userList = [];
  if (res.data.commandUserList) {
    userList.push(...res.data.commandUserList);
  }
  if (res.data.checkUserList) {
    userList.push(...res.data.checkUserList);
  }
  // 去除重复 checkUserId
  let map = new Map();
  for (let item of userList) {
    map.set(item.checkUserId, item);
  }
  res.data.userList = [...map.values()];
  taskDetail.value = res.data;
}
let apiParams = {
  delFlag: 0,
  unitId: userInfo.unitId,
  flag: '01',
  businessId: id,
  includeLower: '1',
};

const unfinishedHazardStatisticsFn = () => {
  getStatisticsMeger(apiParams).then((res: any) => {
    recordStatistics.value = res.data;
  });
};
const unfinishedLevelStatisticsFn = () => {
  levelStatisticsMeger(apiParams).then((res: any) => {
    levelStatistics.value = res.data;
  });
};

// 删除
function handleDelete(data: any) {
  $dialog.error({
    title: '删除模板',
    content: `确定删除该模板？`,
    positiveText: '确定',
    transformOrigin: 'center',
    onPositiveClick: async () => {
      deleteTemp({ templateId: data.id }).then(() => {
        handleSearch();
      });
    },
  });
}

const getData = async () => {
  await getTaskDetail();
  unfinishedLevelStatisticsFn();
  unfinishedHazardStatisticsFn();
};
getData();

defineOptions({ name: 'randomCheckDetail' });
</script>

<style lang="scss" scoped>
.trouble-number {
  img {
    width: 66px;
    height: 66px;
  }

  width: 218px;
  height: 80px;
  padding: 15px 32px 17px 32px;
  background: linear-gradient(180deg, #ffffff 0%, #e1e7fa 99%);
  box-shadow: 0px 2px 0px 0px rgba(0, 20, 82, 0.18);
  border-radius: 8px;
}

.other-card {
  position: relative;
  width: 172px;
  height: 80px;
  padding: 15px 32px 17px 32px;
}
</style>
