<template>
  <div>
    <Header title="隐患清单" :blueIcon="true" class="mb-7"></Header>
    <n-form :model="filterForm" label-placement="left" inline :show-feedback="false" class="mb-[12px]">
      <n-grid :x-gap="12" :y-gap="8" :cols="3">
        <n-grid-item>
          <n-form-item label="检查人:">
            <n-select
              placeholder="请选择"
              label-field="checkUserName"
              class="flex items-center"
              value-field="checkUserId"
              v-model:value="filterForm.createBy"
              :options="props.userList"
              clearable
            />
          </n-form-item>
        </n-grid-item>
        <n-grid-item>
          <n-form-item label="">
            <n-input
              class="w-[370px]"
              placeholder="请输入检查人/隐患描述模糊搜索"
              v-model:value="filterForm.likeFieldValue"
              clearable
            />
          </n-form-item>
        </n-grid-item>
        <n-grid-item span="1">
          <div class="flex gap-4 justify-end items-center">
            <n-button type="primary" @click="exportEventListFn">
              {{ ACTION_LABEL.EXPORT }}
            </n-button>
          </div>
        </n-grid-item>
      </n-grid>
    </n-form>
  </div>
</template>

<script setup lang="ts">
import Header from '@/components/header/ComHeaderB.vue';
import { onMounted, ref, watch } from 'vue';
import { ACTION, ACTION_LABEL } from '../../constant';
import { exportEventList } from '../../fetchData';
import { trimObjNull } from '@/utils/obj.ts';
import { fileDownloader } from '@/utils/fileDownloader';
import OrgTree from '@/components/OrgTree/index.vue';
import { useRoute } from 'vue-router';
const emits = defineEmits(['action']);

const props = defineProps({
  userList: {
    type: Array,
    default: () => [],
  },
});
const route = useRoute();
const id = ref<string>(route.query?.id as string).value;

const filterForm = ref({
  likeFieldValue: '',
  likeFields: 'createByName,hazardDesc',
  businessId: id,
  createBy: null,
  flag: '01',
  objId: null,
});

const exportEventListFn = async () => {
  let data = {
    ...filterForm.value,
    fileName: '隐患清单',
    exportfields: [
      {
        key: 'hazardPosition',
        title: '隐患位置',
      },
      {
        key: 'hazardDesc',
        title: '隐患描述',
      },
      {
        key: 'hazardTypeName',
        title: '隐患分类',
      },
      {
        key: 'hazardLevelName',
        title: '隐患等级',
      },
      {
        key: 'eventTime',
        title: '上报时间',
      },
      {
        key: 'disposeStateName',
        title: '整改状态',
      },
      {
        key: 'timeoutDays',
        title: '是否超期',
      },
      { key: 'eventFileUrlStr1', title: '隐患图片1' },
      { key: 'eventFileUrlStr2', title: '隐患图片2' },
      { key: 'eventFileUrlStr3', title: '隐患图片3' },
      { key: 'dealFileUrlStr1', title: '验收图片1' },
      { key: 'dealFileUrlStr2', title: '验收图片2' },
      { key: 'dealFileUrlStr3', title: '验收图片3' },
    ],
  };
  fileDownloader(exportEventList(), {
    filename: '隐患清单.xlsx',
    method: 'POST',
    contentType: 'application/json',
    body: JSON.stringify(data),
  });
};

function doHandle(action: ACTION) {
  emits('action', {
    action: action,
    data: trimObjNull(filterForm.value),
  });
}
watch(filterForm.value, () => {
  doHandle(ACTION.SEARCH);
});

onMounted(() => {
  doHandle(ACTION.SEARCH);
});

defineOptions({ name: 'checkTempFilterComp' });
</script>
<style module lang="scss"></style>
