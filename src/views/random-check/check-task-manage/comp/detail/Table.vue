<template>
  <div>
    <n-data-table
      class="h-full com-table"
      remote
      striped
      style="height: 588px"
      :columns="columns"
      :data="tableData"
      :bordered="false"
      :flex-height="true"
      :loading="loading"
      :pagination="pagination"
      :render-cell="useEmptyCell"
    />
    <HazardDetails ref="HazardDetailsRef" />
  </div>
</template>

<script setup lang="ts">
import { h, ref, VNode } from 'vue';
import { DataTableColumns, NButton } from 'naive-ui';
import { cols } from './columns';
import { useRouter } from 'vue-router';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { IObj } from '@/types';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import HazardDetails from '@/views/hazard-management/hazard-details.vue';
import { pageEventMeger } from '@/views/hazard-management/fetchData';

const router = useRouter();
const emits = defineEmits(['action']);

const [loading, search] = useAutoLoading(true);
const { pagination, updateTotal } = useNaivePagination(getTableData);
const HazardDetailsRef = ref();
const columns = ref<DataTableColumns>([]);
const tableData = ref<any>([]);
function setColumns() {
  columns.value.push(...cols);
  // 添加操作栏 action
  columns.value.push({
    fixed: 'right',
    width: '100px',
    title: '操作',
    key: 'actions',
    align: 'center',
    render(row) {
      return getActionBtn(row);
    },
  });
}
setColumns();

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          size: 'small',
          type: 'primary',
          ghost: true,
          onClick: () => {
            HazardDetailsRef.value.open(row);
          },
        },
        { default: () => '详情' }
      ),
    ],
  ];
  return useActionDivider(acList);
}
let hazardRandomCheckStatus = '';
let filterData: IObj<any> = {}; // 搜索条件
async function getTableData() {
  const params = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    flag: '01',
    ...filterData,
  };
  tableData.value = [];
  const res = await search(pageEventMeger(params));
  tableData.value = res.data.rows || [];
  updateTotal(res.data.total || 0);
}

function getTableDataWrap(data: IObj<any>) {
  hazardRandomCheckStatus = data.hazardRandomCheckStatus;
  console.log('hazardRandomCheckStatus', data);
  filterData = Object.assign({}, data) || {};
  pagination.page = 1;
  getTableData();
}
defineOptions({ name: 'checkTampTableComp' });
defineExpose({ getTableDataWrap, getTableData });
</script>
<style module lang="scss"></style>
