import { disposeState } from '@/components/table-col/disposeState';
import { DataTableColumn } from 'naive-ui';
import { NButton } from 'naive-ui';
import { h, ref, VNode } from 'vue';

export const tagBgs = ['#F39600', '#00B578', '#527CFF'];

export const cols: DataTableColumn[] = [
  // {
  //   title: '检查对象',
  //   key: 'unitName',
  //   align: 'left',
  //   ellipsis: {
  //     tooltip: true,
  //   },
  //   render: (row: any) => {
  //     return row.unitName || '--';
  //   },
  // },
  {
    title: '隐患位置',
    key: 'hazardPosition',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '隐患描述',
    key: 'hazardDesc',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '隐患分类',
    key: 'hazardTypeName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '隐患等级',
    key: 'hazardLevelName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '上报时间',
    key: 'eventTime',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '整改状态',
    key: 'disposeStateName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
    render: (row) => h(disposeState, { row }),
  },
  {
    title: '是否超期',
    key: 'timeoutDays',
    align: 'left',
    render(row: any) {
      return row.timeoutDays > 0 ? `超期${row.timeoutDays}天` : '否';
    },
    ellipsis: {
      tooltip: true,
    },
  },
];
