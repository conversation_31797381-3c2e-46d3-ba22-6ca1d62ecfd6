<template>
  <el-row :gutter="20">
    <n-form label-placement="left" label-width="80px" inline>
      <el-col :span="7">
        <n-form-item label="创建时间:">
          <n-date-picker
            v-model:value="createTime"
            type="daterange"
            class="flex items-center"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="yyyy-MM-dd"
            clearable
            @update:value="timeChange"
          />
        </n-form-item>
      </el-col>
      <el-col :span="7">
        <n-form-item label="任务状态:">
          <n-select
            placeholder="请选择"
            class="flex items-center"
            v-model:value="filterForm.checkStatus"
            clearable
            :options="CHECK_STATUS_OPT"
            @update:value="doHandle(ACTION.SEARCH)"
          />
        </n-form-item>
      </el-col>
      <el-col :span="7">
        <n-form-item :label-width="0">
          <n-input
            maxlength="50"
            class="flex items-center"
            show-count
            placeholder="请输入随机检查名称/创建人模糊搜索"
            v-model:value="filterForm.search"
            clearable
            @update:value="doHandle(ACTION.SEARCH)"
          />
        </n-form-item>
      </el-col>
      <el-col :span="3">
        <div class="flex justify-end">
          <n-button type="primary" @click="exportEventListFn">
            {{ ACTION_LABEL.EXPORT }}
          </n-button>
        </div>
      </el-col>
    </n-form>
  </el-row>
</template>

<script setup lang="ts">
import { onMounted, ref, watch, nextTick } from 'vue';
import { ACTION, ACTION_LABEL, CHECK_STATUS_OPT } from '../constant';
import { trimObjNull } from '@/utils/obj.ts';
import { fileDownloader } from '@/utils/fileDownloader';
import OrgTree from '@/components/OrgTree/index.vue';
import { exportHazardRandomCheck } from '../fetchData';
import dayjs from 'dayjs';
import { useStore } from '@/store/index';
import { DeBabelOriginal } from '@kalimahapps/vue-icons';
import { pageEventMeger } from '@/views/hazard-management/fetchData';

const { userInfo } = useStore();
const emits = defineEmits(['action']);
const orgTreeRef = ref<any>(null);
const props = defineProps({
  objId: {
    type: String,
    default: '',
  },
  pagination: {
    type: Object,
    default: () => ({}),
  },
});
const filterForm = ref({
  checkStatus: null,
  search: null,
  startTime: '',
  endTime: '',
  // objId: userInfo.unitId,
  createBy: userInfo.id,
  checkUnitId: null,
  roleCodes: userInfo.roleCodes,
});
let createTime = ref(null);

const exportEventListFn = async () => {
  let data = {
    ...filterForm.value,
    objId: props.objId,
    pageSize: -1,
    pageNo: props.pagination.pageNo,
  };
  fileDownloader(exportHazardRandomCheck(), {
    filename: '随机检查任务.xlsx',
    method: 'POST',
    contentType: 'application/json',
    body: JSON.stringify(data),
  });
};

function timeChange(date: any) {
  if (date) {
    filterForm.value.startTime = dayjs(date[0]).format('YYYY-MM-DD HH:mm:ss');
    filterForm.value.endTime = dayjs(date[1]).format('YYYY-MM-DD') + ' 23:59:59';
  } else {
    filterForm.value.startTime = '';
    filterForm.value.endTime = '';
  }
  doHandle(ACTION.SEARCH);
}

function doHandle(action: ACTION) {
  emits('action', {
    action: action,
    data: trimObjNull(filterForm.value),
  });
}

function getVal() {
  return trimObjNull(filterForm.value);
}

function clearFrom() {
  filterForm.value = {
    checkStatus: null,
    search: null,
    startTime: '',
    endTime: '',
    // objId: userInfo.unitId,
    createBy: userInfo.id,
    checkUnitId: null,
    roleCodes: userInfo.roleCodes,
  };
  createTime.value = null;
}

defineOptions({ name: 'checkTempFilterComp' });
defineExpose({ getVal, clearFrom });
</script>
<style scoped lang="scss">
.n-date-picker {
  width: 100%;
}
</style>
