import { ICheckTempPageRes, ICheckItem, ICheckCategoryTree, IFormData } from './type';
import { IObj, IPageRes } from '@/types';
import { api } from '@/api';
import { $http } from '@tanzerfe/http';

export const jurisdictionPageList = (params: any) => {
  const url = api.getUrl(api.type.hazard, api.sh.hazardPlan.shjurisdictionPageList);
  return $http.post<ICheckTempPageRes>(url, { data: { _cfg: { showTip: true }, ...params } });
};

// 检查模板分页
export function getTempPageList(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.name.demo.getTempPageList, query);
  return $http.get<ICheckTempPageRes>(url, { data: { _cfg: { showTip: true } } });
}

// 删除模板
export function deleteTemp(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.name.demo.tempDelete, query);
  return $http.get(url, { data: { _cfg: { showTip: true, showOkTip: true } } });
}

// 获取检查类别树
export function queryCategoryTree(query?: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.name.demo.queryCategoryTree, query);
  return $http.get<ICheckCategoryTree[]>(url, { data: { _cfg: { showTip: true } } });
}

// 获取检查模板项
export function queryInspectTemplateItems(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.name.demo.queryInspectItems, query);
  return $http.get<ICheckItem[]>(url, { data: { _cfg: { showTip: true } } });
}

// 创建检查模板
export function saveTemplate(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.name.demo.saveTemplate, data);
  return $http.post<IFormData>(url, { data: { _cfg: { showTip: true, showOkTip: true }, ...data } });
}

// 模板详情
export function getTemplateDetail(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.name.demo.queryDetailTemplate, query);
  return $http.get<IFormData>(url, { data: { _cfg: { showTip: true } } });
}

// 模板详情已选类别树
export function detailCategoryTree(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.name.demo.detailCategoryTree, query);
  return $http.get<ICheckCategoryTree[]>(url, { data: { _cfg: { showTip: true } } });
}

// 模板详情——检查项
export function queryTemplateDetailItems(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.name.demo.queryTemplateDetailItems, query);
  return $http.get<ICheckItem[]>(url, { data: { _cfg: { showTip: true } } });
}

// 编辑 模板详情
export function queryUpdateTemplateDetail(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.name.demo.queryUpdateTemplateDetail, query);
  return $http.get<IFormData>(url, { data: { _cfg: { showTip: true } } });
}

// 更新检查模板
export function updateTemplate(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.name.demo.updateTemplate, data);
  return $http.post<IFormData>(url, { data: { _cfg: { showTip: true, showOkTip: true }, ...data } });
}

// ====================随机检查========================

// 随机检查
export function getRandomCheckList(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.getRandomCheckList);
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}

export function getRandomCheckDetail(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.getRandomCheckDetail, query);
  return $http.get<IFormData>(url, { data: { _cfg: { showTip: true } } });
}

export function exportHazardRandomCheck() {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.exportHazardRandomCheck);
  return url;
}

export function hazardRandomCheckDetail(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.hazardRandomCheckDetail, query);
  return $http.post(url, {
    data: { _cfg: { showTip: true } },
  });
}

export function getHazardInventory(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.getHazardInventory);
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}

export function exportEventList() {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.exportEventList);
  return url;
}
