<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <n-modal
    :show="show"
    :mask-closable="false"
    :show-icon="false"
    preset="dialog"
    title="选择检查表"
    style="width: 1100px"
    :on-close="cancel"
    :autoFocus="false"
  >
    <template #header>
      <div class="header">选择检查表</div>
    </template>
    <template #default>
      <div class="flex overflow-hidden">
        <div class="flex-1">
          <div class="flex flex-col ml-[20px]">
            <n-form
              class="my-[20px]"
              inline
              ref="formInstRef"
              :model="filterForm"
              :show-feedback="false"
              label-placement="left"
            >
              <n-grid :x-gap="12" :y-gap="12" :cols="2">
                <n-grid-item class="flex items-center">
                  <n-form-item label="编制单位:" class="flex-1">
                    <n-select
                      v-model:value="unitIdValue"
                      :options="unitOpts"
                      label-field="orgName"
                      value-field="orgCode"
                      placeholder="全部"
                      clearable
                      :disabled="standard"
                    />
                  </n-form-item>
                  <n-checkbox v-model:checked="standard" :on-update:checked="handleCheckbox">仅看本单位</n-checkbox>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item>
                    <n-input
                      placeholder="请输入检查表名称模糊搜索"
                      style="width: 100%"
                      maxlength="50"
                      :suffix-icon="Search"
                      v-model:value="filterForm.checkTableName"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
              </n-grid>
            </n-form>
            <n-data-table
              remote
              :columns="columns"
              :data="dataList"
              class="com-table"
              :min-height="450"
              :max-height="450"
              virtual-scroll
              :loading="loading"
              :row-key="(row: any) => row.id"
              :default-checked-row-keys="checkedRowKeys"
              @update:checked-row-keys="handleCheck"
              :pagination="pagination"
            />
          </div>
        </div>
      </div>
    </template>
    <template #action>
      <n-button type="primary" @click="submit">确认</n-button>
      <n-button @click="cancel">取消</n-button>
    </template>
  </n-modal>
  <!-- 检查表详情 -->
  <CheckTableDetail :show="showCheckTable" :row="checkDataDetail" @close="showCheckTable = false" />
</template>
<script setup lang="ts">
import { ref, defineEmits, h, watch, provide, computed } from 'vue';
import { useStore } from '@/store';
import { getlistPage as getCheckList } from '@/views/inspect-mgr/inspect/fetchData';
import { getUpAndDownUnitInfo, getCheckTableDetail } from '@/views/inspection_point/fetchData';
import { DataTableRowKey } from 'naive-ui';
import { Search } from '@element-plus/icons-vue';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import CheckTableDetail from '@/views/inspect-mgr/inspect/comp/CheckTableDetail.vue';

const [loading, search] = useAutoLoading(false);
const { pagination, updateTotal } = useNaivePagination(getCheckTableDataApi);

const { userInfo } = useStore();

const emits = defineEmits(['closeDialog', 'success']);
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  checkedRowKeys: {
    type: Array,
    default: () => [],
  },
});

const initFilterForm = () => {
  return {
    checkUnitId: null,
    checkAreaFormGrade: null,
    checkTableName: '',
    unitId: null,
  };
};
const standard = ref(false);

const unitIdValue = computed({
  get: () => (standard.value ? null : filterForm.value.unitId),
  set: (val) => {
    console.log('🚀 ~ val:', val);
    if (!standard.value) {
      filterForm.value.unitId = val;
    }
  },
});

const handleCheckbox = (val: any) => {
  standard.value = val;
  if (val) {
    filterForm.value.unitId = userInfo.unitId;
  } else {
    filterForm.value.unitId = unitIds.value;
  }
  getCheckTableDataApi();
};

const filterForm = ref<any>(initFilterForm());
const columns = [
  {
    type: 'selection',
    multiple: false,
  },
  {
    title: '序号',
    key: 'index',
    width: 60,
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '检查表名称',
    key: 'checkTableName',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '编制单位',
    key: 'unitName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
    render: (row: any) => {
      // unitId
      return row.unitId === userInfo.unitId ? row.unitName + '(本单位)' : row.unitName;
    },
  },
  // {
  //   title: '编制单位',
  //   key: 'unitName',
  //   render(row: any) {
  //     return row.unitName || '--';
  //   },
  //   ellipsis: {
  //     tooltip: true,
  //   },
  // },
  {
    title: '编制时间',
    key: 'createTime',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '操作',
    width: 120,
    render(row: any) {
      return [
        h(
          'div',
          {
            style: {
              color: '#1890ff',
              cursor: 'pointer',
            },
            onClick: () => {
              console.log(row, 'row');
              getCheckTableDetail(row.id).then((res: any) => {
                console.log(res, 'res');
                checkDataDetail.value = res.data;
                checkDataList.value = res.data.checkAreaList || [];
                showCheckTable.value = true;
              });
            },
          },
          '详情'
        ),
      ];
    },
  },
];
const dataList = ref<any[]>([]);
const unitOpts = ref<any[]>([]);
// 获取检查表列表
function getCheckTableDataApi() {
  let data = {
    checkTableType: 1, //检查表类型，1:综合检查，2:巡查点位检查
    checkStatus: 0, //状态为启用
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    topUnitId: userInfo.topUnitId,
    ...filterForm.value,
  };
  console.log(data);
  search(getCheckList(data)).then((res: any) => {
    dataList.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
}
const unitIds = ref('');
// 获取编制单位
const getOrgCodeUpListApi = async () => {
  const res: any = await getUpAndDownUnitInfo({
    unitId: userInfo.unitId,
  });
  unitOpts.value = res.data;
  unitIds.value = (res.data && res.data.map((i: any) => i.orgCode).join(',')) || '';
  unitOpts.value.unshift({ orgCode: unitIds.value, orgName: '全部' });
  console.log(unitIds.value, 'unitIds.value');
  filterForm.value.unitId = unitIds.value;
};

const checkoutData = ref([]);
const handleCheck = (rowKeys: DataTableRowKey[], data: any) => {
  console.log(rowKeys, data);
  checkoutData.value = data;
};

const cancel = () => {
  emits('closeDialog');
};

const submit = () => {
  emits('success', checkoutData.value);
};
const showCheckTable = ref<any>(false);
const checkDataDetail = ref<any>({});
const checkDataList = ref<any[]>([]);
const checkTableClose = () => {
  showCheckTable.value = false;
};

provide('closeDetail', checkTableClose);

watch(
  () => filterForm.value,
  (nv) => {
    pagination.page = 1;
    filterForm.value.unitId = filterForm.value.unitId ? filterForm.value.unitId : unitIds.value;
    getCheckTableDataApi();
  },
  { deep: true }
);

watch(
  () => props.show,
  (nv) => {
    if (nv) {
      pagination.page = 1;

      // 获取编制单位
      getOrgCodeUpListApi().then(() => {
        // 获取检查表表列表
        getCheckTableDataApi();
      });
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.header {
  position: relative;

  &::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 18px;
    background-color: #1890ff;
    position: absolute;
    top: 10%;
    left: -10%;
  }
}
</style>
