<template>
  <div class="p-4 w-[1000px]">
    <n-form
      ref="formRef"
      :label-width="80"
      :rules="rules"
      :model="item"
      size="small"
    >
      <div class="flex justify-between">
        <n-form-item
          label-placement="left"
          label="检查对象"
          path="unitId"
          ref="unitIdRef"
          v-if="userInfo.unitOrgType == '2'"
        >
          <el-tree-select
            class="notVw w-[20vw]"
            :disabled="formData.planState === '4'"
            v-model="groupData.unitId"
            check-strictly
            node-key="id"
            :props="treeProps"
            :render-after-expand="false"
            filterable
            clearable
            :data="treeData"
            placeholder="请选择检查对象"
            ref="treeSelectRef"
            @change="changeUnit"
            no-match-text="无匹配数据"
          >
            <template #default="{ data }">
              <span class="custom-tree-node">
                <img
                  :style="`width: 18px;`"
                  :src="getTreeItemIco(data)"
                  :alt="data.treeName"
                />
                <span
                  class="truncate-text"
                  style="margin-left: 10px; font-size: 14px"
                  :title="data.treeName"
                  :style="{
                    color: unitDis.includes(data.id)
                      ? '#527cff !important'
                      : '#333639',
                    maxWidth: 'calc(100% - 60px)',
                  }"
                  >{{ data.treeName }}</span
                >
                <span
                  v-if="
                    data.attributes.orgType == '1' &&
                    !data.attributes.erecordUnitId
                  "
                  style="margin-left: 5px"
                  >(暂未绑定)</span
                >
                <span
                  v-else
                  style="margin-left: 5px"
                  :style="{
                    color: unitDis.includes(data.id)
                      ? '#527cff !important'
                      : '',
                  }"
                  >{{ unitDis.includes(data.id) ? '(已选中)' : '' }}</span
                >
              </span>
            </template>
          </el-tree-select>
        </n-form-item>
        <!-- 占位符 -->
        <!-- <div v-else></div> -->
        <!-- v-if="userInfo.unitOrgType == '2'"  改为非必填 -->
        <n-form-item
          label-placement="left"
          :label-width="110"
          label="关联施工项目"
          name="projectIdref"
        >
          <el-select
            class="!w-[320px]"
            v-model="groupData.projectId"
            placeholder="关联施工项目"
            no-data-text="无匹配数据"
            no-match-text="无匹配数据"
            filterable
            clearable
            @change="changeobject"
          >
            <el-option
              v-for="(item, index) in ObjectList"
              :key="index"
              :label="item.projectName"
              :value="item.projectId"
            />
          </el-select>
        </n-form-item>

        <n-button
          v-if="formData.unitlist.length > 1"
          size="small"
          ghost
          type="error"
          @click="delUnit"
          >删除分组</n-button
        >
      </div>
      <!-- 综合检查不显示 -->
      <div v-if="checkRangeType != '1'">
        <n-form-item
          label-placement="left"
          label="巡查点位"
          path="checkPointlist"
        >
          <div class="flex justify-between w-full">
            <div class="depart"></div>
            <n-button
              type="primary"
              size="small"
              ghost
              @click="showPoint = true"
              >添加巡查点位</n-button
            >
          </div>
        </n-form-item>
        <n-data-table
          class="h-[300px] w-[500px] com-table my-2 mb-4"
          flex-height
          remote
          striped
          :columns="pointColumns"
          :data="item.checkPointlist"
          :row-key="(row: any) => row.id"
          :bordered="false"
        />
      </div>
      <!-- 新增点位巡检计划 -->
      <SelectPoint
        :unitId="groupData.unitId"
        :disabledUnitIds="unitDis"
        :show="showPoint"
        :pointList="item.checkPointlist"
        @closeDialog="showPoint = false"
        @success="successPoint"
      />

      <n-form-item
        label-placement="left"
        label="检查人员"
        path="userlist"
        ref="userlistRef"
      >
        <div class="flex justify-between w-full">
          <div class="depart"></div>
          <n-button
            v-if="userInfo.unitOrgType == '2'"
            :disabled="!item.unitId"
            type="primary"
            size="small"
            ghost
            @click="handlePersonnel()"
          >
            添加检查人员
          </n-button>
          <n-button
            v-else
            type="primary"
            size="small"
            ghost
            @click="handlePersonnel()"
          >
            添加检查人员
          </n-button>
        </div>
      </n-form-item>
      <n-data-table
        class="!h-[300px] w-[500px] com-table my-2"
        flex-height
        remote
        striped
        :columns="userColumns"
        :data="item.userlist"
        :row-key="(row: any) => row.id"
        :bordered="false"
      />
    </n-form>
    <!-- 选择检查人 -->
    <SelectUser
      title="选择检查人员"
      :unitId="groupData.unitId"
      :showSelectUserModal="showSelectUserModal"
      :userType="groupData.userType || 1"
      :userlist="groupData.userlist"
      @close="showSelectUserModal = false"
      @success="successFn"
    />
  </div>
</template>

<script lang="ts" setup>
import { DataTableColumns, NButton, NRadioGroup, NRadio } from 'naive-ui';
import { RowData } from 'naive-ui/es/data-table/src/interface';
import { IUnitItem, IHazardPlanForm } from './type';
import { computed, ref, h, watchEffect } from 'vue';
import SelectPoint from '@/views/inspection-planning/planned-management/Add/SelectPoint/index.vue';
import SelectUser from '@/views/inspection-planning/planned-management/Add/SelectUser.vue';
import {
  getOrgTree,
  getProjectByUnitIdAPI,
} from '@/views/inspection-planning/planned-management/fetchData.ts';
import { $dialog } from '@/common/shareContext/useDialogCtx.ts';
// import { getAllUnit } from '@/views/inspection_point/fetchData.ts';
import business from '@/components/unitSelect/business.png'; //业务单位
import section from '@/components/unitSelect/section.png'; //部门
import supervise from '@/components/unitSelect/supervise.png'; //监管单位
import { useStore } from '@/store';
import { useRoute } from 'vue-router';
import { watch } from 'fs';

const route = useRoute();
const checkRangeType = route.query.checkRange;
const emits = defineEmits(['updateUnitList']);
const userInfo = useStore()?.userInfo;
const showPoint = ref(false);
const showSelectUserModal = ref(false);
const formRef = ref();
const treeSelectRef = ref();
const props = defineProps({
  item: {
    type: Object as () => IUnitItem,
    default: () => ({}),
  },
  formData: {
    type: Object as () => IHazardPlanForm,
    default: () => ({}),
  },
  index: {
    type: Number,
    default: 0,
  },
  length: {
    type: Number,
    default: 0,
  },
});
const groupData = computed(() => props.item);
const data = computed(() => props.formData);
// console.log('data=========', data.value);
// console.log('groupData=========', groupData.value);
// 已选择的检查对象放到禁用列表
const unitDis: any = computed(() => {
  return props.formData.unitlist.map((item) => item.unitId) || [];
});
// 检查对象树参数配置
const treeProps = {
  label: 'treeName',
  children: 'children',
  disabled: (data: any, node: any) => {
    return (
      data.attributes.orgType != '1' ||
      !data.attributes.erecordUnitId ||
      unitDis.value.includes(node.key)
    );
  },
};
// const logo_url = `https://agjp.tanzervas.com/aqsc/v1/img1/systemLogo/${userInfo.zhLogo}.png`;
const logo_url = userInfo.logoPicUrl;

const getTreeItemIco = (row: any) => {
  if (row.parentId == -1) return logo_url;
  if (row.attributes.orgType == '0') {
    return section;
  } else if (row.attributes.orgType == '1') {
    return business;
  } else {
    return supervise;
  }
};
const treeData = ref([]);
// 查询检查对象树
const getTreeData = async () => {
  let res: any = await getOrgTree({ orgCode: userInfo.unitId });
  // 示例调用
  const filterTreeData =
    res.data && res.data.length ? filterDepartments(res.data) : [];
  treeData.value = filterTreeData;
  // 业务单位情况下直接调用
  selectObject(userInfo.serverUnitId);
};
// 过滤部门
function filterDepartments(data: any) {
  // 递归过滤函数
  function filterNodes(nodes: any) {
    return nodes
      .filter((node: any) => {
        // 如果当前节点是部门（orgType === '0'）且没有子节点，则过滤掉
        if (node.attributes.orgType === '0' && !node.children?.length) {
          return false;
        }
        return true;
      })
      .map((node: any) => {
        // 如果当前节点有子节点，递归处理子节点
        if (node.children && node.children.length) {
          node.children = filterNodes(node.children);
        }
        return node;
      });
  }

  // 对传入的数据进行过滤
  return filterNodes(data);
}
getTreeData();

// const unitList = ref();
// 获取业务单位
// const getAllUnitApi = async () => {
//   const res: any = await getAllUnit({ orgCode: userInfo.unitId, pageNo: 1, pageSize: -1, orgType: 1 });
//   unitList.value = res.data.rows ? res.data.rows.filter((item: any) => item.erecordUnitId) : [];
// };
// // 获取下级业务单位
// getAllUnitApi();

let pointColumns: DataTableColumns<RowData> = [
  {
    title: '序号',
    key: 'index',
    width: 60,
    render: (row: any, index: number) => index + 1,
  },
  {
    title: '巡查点位',
    key: 'pointFullName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
    width: 100,
  },
  {
    title: '检查表',
    key: 'checkTableName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
    width: 100,
    render(row: any) {
      return row.checkTableName || '-';
    },
  },
  {
    title: '操作',
    key: 'actions',
    align: 'left',
    width: 80,
    render(row: any) {
      return h(
        NButton,
        {
          size: 'small',
          type: 'error',
          ghost: true,
          onClick: () => {
            $dialog.warning({
              title: '提示',
              content: '确定删除吗？',
              positiveButtonProps: { type: 'primary', color: '#527cff' },
              positiveText: '确定',
              negativeText: '取消',
              transformOrigin: 'center',
              onPositiveClick: () => {
                groupData.value.checkPointlist =
                  groupData.value.checkPointlist.filter(
                    (item: any) => item.id !== row.id
                  );
              },
            });
          },
        },
        '删除'
      );
    },
  },
];

let userColumns: any = [];
let columns: DataTableColumns<RowData> = [
  {
    title: '序号',
    key: 'index',
    width: 60,

    render: (row: any, index: number) => index + 1,
  },
  {
    title: '检查人姓名',
    key: 'checkUserName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
    width: 100,
  },
  {
    title: '单位',
    key: 'userUnitName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
    width: 100,
  },
  {
    title: '检查人类型',
    key: 'length',
    width: 200,
    align: 'left',
    render(row: any) {
      // 默认检查类型 检查参与人
      if (!row.checkUserType) row.checkUserType = '2';
      return h(
        NRadioGroup,
        {
          value: row.checkUserType,
          onUpdateValue: (val) => {
            row.checkUserType = val;
          },
          // class: 'flex flex-wrap',
          style: {},
        },
        [
          { value: '1', label: '检查负责人' },
          { value: '2', label: '检查参与人' },
        ].map((i) => {
          return h(NRadio, i);
        })
      );
    },
  },
  {
    title: '操作',
    key: 'actions',
    align: 'left',
    width: 80,
    render(row: any) {
      return h(
        NButton,
        {
          size: 'small',
          type: 'error',
          ghost: true,
          onClick: () => {
            $dialog.warning({
              title: '提示',
              content: '确定删除吗？',
              positiveButtonProps: { type: 'primary', color: '#527cff' },
              positiveText: '确定',
              negativeText: '取消',
              transformOrigin: 'center',
              onPositiveClick: () => {
                delUser(row);
              },
            });
          },
        },
        '删除'
      );
    },
  },
];

watchEffect(() => {
  userColumns = columns;
});

/** 删除当前行用户 */
const delUser = (row: any) => {
  groupData.value.userlist = groupData.value.userlist.filter(
    (item: any) => item.id !== row.id
  );
};

/** 删除当前分组 */
const delUnit = () => {
  data.value.unitlist = props.formData.unitlist.filter(
    (item) => item !== props.item
  );
  emits('updateUnitList');
};

const handlePersonnel = () => {
  showSelectUserModal.value = true;
};
// 巡查点位
const successPoint = (val: any) => {
  if (val.length && !groupData.value.unitId) {
    groupData.value.unitId = val[0].unitId;
    groupData.value.unitName = groupData.value.unitId
      ? findNodeById(treeData.value, groupData.value.unitId)?.treeName
      : '';
  }
  groupData.value.checkPointlist = val?.map((item: any) => {
    item.checkPointId = item.id;
    return item;
  });
  showPoint.value = false;
};
// 人员
const successFn = (val: any, userType: any) => {
  groupData.value.userlist = [...val];
  groupData.value.userType = userType;
};

// 查找unitId对应节点
function findNodeById(tree: any, unitId: any) {
  // 遍历树的每一个节点
  for (let node of tree) {
    // 如果当前节点的id匹配，则返回当前节点
    if (node.id === unitId) {
      return node;
    }

    // 如果当前节点有子节点，则递归查找子节点
    if (node.children && node.children.length > 0) {
      let found: any = findNodeById(node.children, unitId);
      if (found) {
        return found;
      }
    }
  }
  // 如果没有找到匹配的节点，则返回null
  return null;
}
const ObjectList = ref([]);
const changeUnit = (unitId: string) => {
  // console.log('changeUnit>>>回显=====', unitId);
  treeSelectRef.value.visible = false;
  // 根据unitId查找对应的节点名称  用于详情回显
  groupData.value.unitName = unitId
    ? findNodeById(treeData.value, unitId)?.treeName
    : '';
  // 更改检查对象，清空巡查点位
  groupData.value.checkPointlist = [];
  // 更改检查对象，清空检查人
  groupData.value.userlist = [];
  // 更改检查对象，清空检查位置
  groupData.value.roomlist = [];
  // SelectUser 组件使用
  sessionStorage.setItem('checkUnitId', unitId as string);
  sessionStorage.setItem('deviceUnitId', unitId as string);
  // ObjectList.value = [];
  // groupData.value.projectId = '';
};

// 获取关联项目列表
async function selectObject(serverUnitId: string) {
  const res: any = await getProjectByUnitIdAPI({ unitIds: serverUnitId });
  console.log('selectObject>>>', res);
  if (res.code != 200) return;
  ObjectList.value = res.data;
}
const changeobject = (val: string) => {
  // console.log('changeobject>>>', val);
  groupData.value.projectId = val;
};

const rules = {
  unitId: {
    required: true,
    trigger: ['change', 'input'],
    message: '请选择检查对象',
  },
  // projectId: {
  //   required: true,
  //   trigger: ['change', 'input'],
  //   message: '请选择关联施工项目',
  // },
  userlist: {
    required: true,
    validator: (rule: any, value: any) => {
      if (!value.length) return Error('请选择检查人员');
      if (!value.some((item: any) => item.checkUserType === '1'))
        return Error('请选择检查负责人');
      return true;
    },
  },
  checkPointlist: {
    required: true,
    validator: (rule: any, value: string) => {
      return value.length > 0;
    },
    message: '请选择巡查点位',
  },
  devicelist: {
    required: true,
    validator: (rule: any, value: string) => {
      return value.length > 0;
    },
    message: '请选择设备',
  },
  roomlist: {
    required: true,
    validator: (rule: any, value: string) => {
      return value.length > 0;
    },
    message: '请选择位置',
  },
};
const validate = () => formRef.value.validate();

defineExpose({
  validate,
});
</script>
<style lang="scss" scoped>
.custom-tree-node {
  width: 100%;
  /* 改为百分比宽度 */
  max-width: 400px;
  /* 设置最大宽度 */
  display: flex;
  align-items: center;
  padding-right: 8px;
  /* 增加右侧留白 */
}

/* 新增响应式文本截断 */
.truncate-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-shrink: 1;
  /* 允许收缩 */

  @media (max-width: 1280px) {
    max-width: 120px;
    /* 小屏下更短 */
  }
}

/* 调整下拉框样式 */
:deep(.el-tree-select__popper) {
  max-width: 600px;
  /* 限制下拉最大宽度 */

  .el-tree-node__content {
    height: 34px;

    .custom-tree-node {
      width: 100% !important;
      max-width: none;
    }
  }

  @media (max-width: 768px) {
    max-width: 80vw;
    /* 移动端适配 */

    .el-select-dropdown__item {
      padding: 0 4px;
    }
  }
}
</style>
<style>
.el-tree {
  --el-tree-node-content-height: 34px;
}

.el-tree-select__popper .el-select-dropdown__item {
  padding-right: 0;
  height: auto;
}
</style>
