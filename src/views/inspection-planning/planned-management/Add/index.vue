<template>
  <div class="flex flex-col plan-wrap">
    <com-bread :data="breadData"></com-bread>
    <div
      class="p-[24px] flex-grow bg-[--com-container-bg] border-[--com-border] rounded shadow-[--com-container-shadow] relative"
    >
      <n-spin :show="showLoading">
        <n-scrollbar style="height: calc(100vh - 240px)">
          <n-form
            :key="formData"
            ref="formRef"
            :model="formData"
            :rules="rules"
            label-placement="left"
            label-width="200px"
            class="main-form"
            require-mark-placement="right-hanging"
            size="small"
          >
            <ComHeaderB title="计划基础信息" class="mb-[20px] cursor-pointer" />
            <n-form-item
              label="计划名称"
              path="planName"
              required
              class="w-[--form-item-width]"
            >
              <n-input
                v-model:value="formData.planName"
                placeholder="请输入计划名称"
                maxlength="50"
                show-count
                clearable
              />
            </n-form-item>
            <!-- 检查类型 -->
            <planType
              :stutas="status"
              :formData="formData"
              v-model:planTypeConfig="planTypeConfig"
              class="w-[--form-item-width]"
            />
            <!-- 计划起止时间 -->
            <times
              :stutas="status"
              :formData="formData"
              class="w-[--form-item-width]"
            />
            <!-- 检查频次 -->
            <frequencyTypeTmp :formData="formData" />

            <div class="flex gap-2" v-if="userInfo.unitOrgType == '2'">
              <n-form-item
                label="是否分组检查"
                path="planUnitGroupStatus"
                required
              >
                <n-radio-group
                  v-model:value="formData.planUnitGroupStatus"
                  @update:value="changePlanUnitGroupStatus"
                  :disabled="formData.planState === '4'"
                  name="radiogroup1"
                >
                  <n-radio :disabled="formData.planState === '4'" value="1"
                    >不分组</n-radio
                  >
                  <n-radio
                    :disabled="formData.planState === '4'"
                    value="2"
                    class="ml-5"
                    >分组</n-radio
                  >
                </n-radio-group>
              </n-form-item>
            </div>

            <!-- 检查要求 -->
            <checkDemand :formData="formData" class="w-[--form-item-width]" />
            <!-- 附件 -->
            <filelist :formData="formData" />
            <!-- 检查表 -->
            <n-form-item
              v-if="checkRangeType == '1'"
              label="检查表"
              path="checkTableId"
              required
              class="w-[--form-item-width]"
              @click="showInspect = true"
            >
              <n-select
                readonly
                v-model:value="formData.checkTableName"
                options="[]"
                placeholder="请选择检查表"
              />
            </n-form-item>
            <!-- 检查执行方式 -->
            <checkExecuteMethod
              :formData="formData"
              :planTypeConfig="planTypeConfig"
              :checkRange="checkRangeType"
            />
            <div style="position: relative">
              <!-- 检查对象和巡查点位 -->
              <div v-if="formData.planUnitGroupStatus == '1'">
                <ComHeaderB
                  :title="
                    checkRangeType == '1' ? '检查信息' : '检查对象和巡查点位'
                  "
                  class="cursor-pointer"
                />
                <GroupItem
                  style="transform: translateX(104px)"
                  ref="groupItemRef"
                  v-for="(item, index) of formData.unitlist"
                  :key="item.uid"
                  :index="index"
                  :item="item"
                  :formData="formData"
                  :length="formData.unitlist.length"
                />
              </div>
              <n-button
                style="position: absolute; top: 5px; right: 10px; z-index: 888"
                v-if="formData.planUnitGroupStatus === '2'"
                type="primary"
                @click="addGroup"
                :disabled="
                  selectOptionsStore.unitOptions.length <=
                    formData.unitlist.length || formData.unitlist.length >= 10
                "
              >
                添加分组
              </n-button>
              <div
                v-if="formData.planUnitGroupStatus != '1'"
                style="position: relative"
                :style="{ height: ` ${checkRangeType == '1' ? 560 : 980}px` }"
              >
                <ComHeaderB title="分组检查信息" class="cursor-pointer" />

                <div style="position: absolute; top: 2px; left: 214px">
                  <n-tabs v-model:value="currentTabs" type="line" animated>
                    <n-tab-pane
                      v-for="(item, index) of formData.unitlist"
                      :tab="`检查组${index + 1}`"
                      :name="index"
                      :key="index"
                    >
                      <GroupItem
                        style="transform: translateX(-110px)"
                        ref="groupItemRef"
                        :index="index"
                        :item="item"
                        :formData="formData"
                        :length="formData.unitlist.length"
                        @updateUnitList="updateUnitList(index)"
                      />
                    </n-tab-pane>
                  </n-tabs>
                </div>
              </div>
            </div>

            <!-- 检查结束操作 -->
            <checkEndOperate
              :formData="formData"
              :checkRange="checkRangeType"
              :planTypeConfig="planTypeConfig"
            />
            <!-- 是否需要检查人现场打卡 -->
            <isNeedClock
              :formData="formData"
              :checkRange="checkRangeType"
              :planTypeConfig="planTypeConfig"
            />
          </n-form>
        </n-scrollbar>
        <div
          class="absolute left-0 bottom-[-70px] p-[10px] flex justify-center w-full gap-[20px]"
        >
          <n-button type="primary" size="small" ghost @click="goBack"
            >取消</n-button
          >
          <n-button
            type="primary"
            size="small"
            v-if="formData.planState == '1'"
            @click="submitForm(false)"
          >
            存为草稿
          </n-button>
          <n-button type="primary" size="small" @click="submitForm(true)">
            发布
          </n-button>
        </div>
      </n-spin>
    </div>
    <!-- 检查表选择 -->
    <SelectCheckTable
      :show="showInspect"
      :checkedRowKeys="[formData.checkTableId]"
      @closeDialog="showInspect = false"
      @success="successInspect"
    />
  </div>
</template>

<script setup lang="ts">
import ComBread from '@/components/breadcrumb/ComBread.vue';
import ComHeaderB from '@/components/header/ComHeaderB.vue';
import { ref, onMounted } from 'vue';
import { defineComponent } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useStore } from '@/store';
import GroupItem from './groupItem.vue';
import SelectCheckTable from '@/views/inspection-planning/planned-management/Add/SelectCheckTable.vue';
import { IHazardPlanForm, initHazardPlanForm, addUnitItem } from './type';
import {
  checkEndOperate,
  isNeedClock,
  checkExecuteMethod,
  checkDemand,
  planType,
  filelist,
  times,
  frequencyTypeTmp,
} from './formItemComponents';
import { addPlan, startPlan, updatePlan } from './fetchData';
import { useMessage } from 'naive-ui';
import { useSelectOptionsStore } from '@/store/selectOptionsStore';
import {
  detailPlan,
  validateCheckTableVersionNum,
  validateCheckTableVersionNumByPlanId,
} from '@/views/inspection-planning/planned-management/fetchData.ts';
import { ElMessageBox } from 'element-plus';
const selectOptionsStore = useSelectOptionsStore();
const message = useMessage();
const { userInfo } = useStore();
const groupItemRef = ref();
const route = useRoute();
const router = useRouter();
const formRef = ref();

const checkRangeType = route.query.checkRange;
const status = route.query.status;

let breadName1 =
  checkRangeType == '1' ? '新增综合检查计划' : '新增点位巡查计划';
let breadName2 =
  checkRangeType == '1' ? '修改综合检查计划' : '修改点位巡查计划';

const breadData = [
  { name: '隐患治理系统' },
  {
    name: '计划管理',
    clickable: true,
    routeRaw: {
      name: 'plannedManagementIndex',
      query: {
        checkRange: checkRangeType,
      },
    },
  },
  {
    name: route.params.id ? breadName2 : breadName1,
  },
];

const showLoading = ref(false);
const currentTabs = ref(0);
const formData = ref<any>(initHazardPlanForm());
/** 检查类型对应的配置信息 */
const planTypeConfig = ref<any | null>(null);
// 检查表
const showInspect = ref(false);
const successInspect = (data: any) => {
  console.log(data[0], 'data');
  if (!data?.length) return (showInspect.value = false);
  formData.value.checkTableId = data[0].id;
  formData.value.checkTableName = data[0].checkTableName;
  showInspect.value = false;
};
const goBack = () =>
  router.push({
    name: 'plannedManagementIndex',
    query: { checkRange: checkRangeType },
    replace: true,
  });

const submitForm = async (isPublish = false) => {
  let params = route.params.id
    ? formData.value
    : {
        ...formData.value,
        checkRange: checkRangeType == '1' ? 1 : 3, // 设备/点位检查表设置（1 综合检查 3 点位巡查 ）
        createUnitName: userInfo.unitName,
      };
  console.log(params, '======params');
  try {
    const allValid = groupItemRef.value.map((item: any) => item.validate());
    allValid.push(formRef.value.validate());
    await Promise.all(allValid);
    // 分组无dom 校验
    if (formData.value.planUnitGroupStatus == '2') {
      formData.value.unitlist.forEach((item: any, idx: number) => {
        // 综合检查
        let flag = false;
        if (checkRangeType == '1') {
          if (!item.unitId || !item.userlist.length) flag = true;
        } else {
          //  点位检查
          if (
            !item.unitId ||
            !item.userlist.length ||
            !item.checkPointlist.length
          )
            flag = true;
        }
        if (flag) {
          let msg = `检查组${idx + 1}信息输入不完整，请完整输入后提交`;
          message.error(msg);
          throw new Error(msg);
        }
      });
    }

    let params = route.params.id
      ? formData.value
      : {
          ...formData.value,
          checkRange: checkRangeType == '1' ? 1 : 3, // 设备/点位检查表设置（1 综合检查 3 点位巡查 ）
          createUnitName: userInfo.unitName,
        };
    // console.log(params, "======params")
    // 加个参数 去除startPlan的调用， 2保存 1草稿
    isPublish ? (params.buttonType = 2) : (params.buttonType = 1);
    // 校验选择检查表是否被删除或者更新 ---点位的计划也需要加上检查表校验
    if (route.params.id) {
      let flag = await checkTableChangeValidate(route.params.id, isPublish);
      if (!flag) return;
    }

    // if (checkRangeType == '1' || checkRangeType == '3') {
    //   let flag = await checkTableChangeValidate(params.checkTableId, isPublish);
    //   if (!flag) return;
    // }
    showLoading.value = true;
    // 保存接口
    const res = await (route.params.id ? updatePlan : addPlan)(params);
    // 发布|启用接口
    // isPublish && (await startPlan({ planId: res.data.planId }));
    showLoading.value = false;
    message.success(
      isPublish ? '发布成功' : route.params.id ? '修改成功' : '新增成功'
    );
    goBack();
  } catch (error) {}
  showLoading.value = false;
};

// 校验选择检查表是否被删除或者更新
function checkTableChangeValidate(planId: any, isPublish = false) {
  return new Promise(async (resolve, reject) => {
    const typedata = checkRangeType == '3' ? { planType: 1 } : {};
    let {
      data: { versionDeletedFlag, versionNeedUpgradeFlag, checkTableStopFlag },
    }: any = await validateCheckTableVersionNumByPlanId({
      planId,
      ...typedata,
    });
    let message;
    // 优先提示删除, 其次是停用, 其次是编辑
    if (versionNeedUpgradeFlag === 1)
      message = '系统检测到当前计划关联的检查表已更新，是否使用更新后的检查表';
    if (checkTableStopFlag === 1)
      message = '系统检测到当前计划关联的检查表已被停用，当前计划不可被启用';
    if (versionDeletedFlag === 1)
      message = '系统检测到当前计划关联的检查表已被删除，当前计划不可被启用';
    versionDeletedFlag || versionNeedUpgradeFlag || checkTableStopFlag
      ? ElMessageBox.confirm(message, '系统提示', {
          confirmButtonText:
            versionDeletedFlag || checkTableStopFlag
              ? '我知道了'
              : isPublish
                ? '确定并发布'
                : '确定并保存',
          cancelButtonText: '取消',
          showCancelButton:
            versionDeletedFlag || checkTableStopFlag ? false : true,
          type: 'warning',
        }).then(async () => {
          resolve(versionDeletedFlag || checkTableStopFlag ? false : true);
        })
      : resolve(true);
  });
}

// function checkTableChangeValidate(checkTableId: any, isPublish = false) {
//   return new Promise(async (resolve, reject) => {
//     let {
//       data: { versionDeletedFlag, versionNeedUpgradeFlag },
//     }: any = await validateCheckTableVersionNum({ checkTableId });
//     let message = versionDeletedFlag
//       ? '系统检测到当前计划关联的检查表已被删除，当前计划不可被启用'
//       : '系统检测到当前计划关联的检查表已更新，是否使用更新后的检查表';
//     versionDeletedFlag || versionNeedUpgradeFlag
//       ? ElMessageBox.confirm(message, '系统提示', {
//         confirmButtonText: versionDeletedFlag ? '我知道了' : isPublish ? '确定并发布' : '确定并保存',
//         cancelButtonText: '取消',
//         showCancelButton: !versionDeletedFlag,
//         type: 'warning',
//       }).then(async () => {
//         resolve(versionNeedUpgradeFlag ? true : false);
//       })
//       : resolve(true);
//   });
// }

const changePlanUnitGroupStatus = () => {
  // 和产品确认，切换不分组时，删除后面多余的分组
  formData.value.unitlist.length = 1;
};

const addGroup = () => {
  formData.value.unitlist.push(addUnitItem());
};

const rules = {
  planName: {
    required: true,
    trigger: ['blur', 'input'],
    message: '请输入计划名称',
  },
  planTypeId: {
    required: true,
    trigger: ['change'],
    message: '请选择检查类型',
  },
  times: {
    required: true,
    trigger: ['change'],
    message: '请选择计划起止时间',
    validator: (rule: any, value: string) =>
      !!(formData.value.planStartDate && formData.value.planEndDate),
  },
  frequencyType: {
    required: true,
    trigger: ['blur', 'input'],
    message: '请输入检查频次',
  },
  startTime: {
    required: true,
    trigger: ['change'],
    message: '请选择检查开始时间',
  },
  endTime: {
    required: true,
    trigger: ['change'],
    message: '请选择检查开始时间',
  },
  checkTableId: {
    required: true,
    trigger: ['change'],
    message: '请选择检查表',
  },
};

// 分组检查信息删除某一项
const updateUnitList = (index: number) => {
  if (formData.value.unitlist.length === index) currentTabs.value = index - 1;
};

const getInfo = async (planId: string) => {
  try {
    const res = await detailPlan({ planId: planId });
    formData.value = res.data as unknown as IHazardPlanForm;
    // 检查频次字段回显处理
    formData.value.frequencyTypeTmp =
      +formData.value.frequencyType === 99 ? '2' : '1';
    formData.value.updateBy = userInfo.id;
    formData.value.updateByName = userInfo.userName;
  } catch (error) {}
};

onMounted(async () => {
  showLoading.value = true;
  try {
    if (route.params.id) await getInfo(route.params.id as string);
  } catch (error) {}
  showLoading.value = false;
});

defineComponent({ name: 'add-index' });
</script>

<style scoped>
::v-deep .n-tabs .n-tabs-pane-wrapper {
  overflow: visible !important;
}

.plan-wrap {
  --form-item-width: 1040px;
}
</style>
