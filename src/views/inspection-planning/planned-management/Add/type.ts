import { useStore } from '@/store';

const { userInfo } = useStore();
export interface IUserItem {
  checkUserId: string;
  checkUserName: string;
  checkUserType: '1' | '2'; //检查人类型(1:检查负责人;2:检查参与人)
  unitName: string;
}

/** 分组数据 */
export interface IUnitItem {
  groupNum: number;
  tenantryId: string; // 承租方id
  unitId: string | null; // 单元id
  unitName: string; // 单元名称
  userType: number; // 单元类型
  projectId: string; // 项目id
  userlist: IUserItem[];
  checkPointlist: any[]; // 巡查点位列表
  devicelist: any[]; // 设备列表
  roomlist: any[]; // 位置列表
  uid: string; // 唯一标识
  locationType: string; // 位置类型
}

export interface IHazardPlanForm {
  planUnitGroupStatus: '1' | '2'; // 分组状态(1:未分组;2:分组)
  unitlist: IUnitItem[]; // 分组信息
  checkDemand: string; // 检查要求
  checkEndOperate: '1' | '2'; // 检查结束操作(1:是;2:否)
  frequencyTypeTmp: '1' | '2'; // 检查频次(1: 重复； 2: 不重复)
  checkExecuteMethod: '1' | '2'; //检查执行方式(1:不需要逐一执行每个检查项;2:需要逐一执行每个检查项)
  checkRange: '1' | '2' | '3'; // 检查范围（1：区域；2：设备；3：点位）
  checkTableId: string | null; // 检查表id
  checkTableName: string; // 检查表名称
  checkTableSet: '1' | '2'; // 设备/点位检查表设置（1：使用设备/点位检查表；2：使用统一检查表）
  createBy: string; // 创建人
  createByName: string; // 创建人名称
  createTime: string; // 创建时间
  startTime: string | null; // 检查时段: 开始时间
  endTime: string | null; // 检查时段: 结束时间
  planStartDate: string; // 计划开始日期
  planEndDate: string; // 计划结束日期
  createUnitId: string; // 创建单位id
  delFlag: 0 | 1; // 删除标志，0:未删除，1:已删除
  filelist: [];
  frequency: number; // 频次
  frequencyType: '0' | '1' | '2' | '3' | '4' | '5' | '99'; // 计划频次类型：0:时;1:日;2:周;3:月;4:季度;5:年;99:不重复
  isNeedClock: '1' | '2'; // 是否需要打卡(1:是;2:否)
  planId?: string;
  planName: string; // 计划名称
  planState?: '1' | '2' | '3' | '4'; // 检查状态(1:草稿;2:进行中;3:已结束;4:已停用)
  planTypeConfig: string; // 计划类型权限配置
  planTypeId: string | null; // 计划类型id
  planTypeName: string; // 计划类型名称
  planTypeRemark: string; // 计划类型备注
  systemCode: string; // 计划来源系统编码
  updateBy?: string; // 更新人
  updateByName?: string; // 更新人名称
  updateTime?: string; // 更新时间
  zhId: string; // 租户id
}

const generateSimpleUID = () => Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
export const addUnitItem = () => {
  const result: IUnitItem = {
    groupNum: 0,
    tenantryId: '',
    unitId: userInfo.unitOrgType == '1' ? userInfo.unitId : '', //业务单位默认检查对象为自身
    unitName: userInfo.unitOrgType == '1' ? userInfo.unitName : '',
    userType: 1,
    userlist: [],
    checkPointlist: [], //巡查点位列表
    devicelist: [],
    roomlist: [],
    locationType: '',
    uid: generateSimpleUID(),
    projectId: '',
  };
  return result;
};

/** 初始化 */
export const initHazardPlanForm = () => {
  const result: IHazardPlanForm = {
    frequencyTypeTmp: '2',
    zhId: userInfo.zhId,
    planUnitGroupStatus: '1',
    unitlist: [addUnitItem()],
    checkDemand: '',
    checkEndOperate: '2',
    checkExecuteMethod: '1',
    checkRange: '1',
    checkTableId: null,
    checkTableName: '',
    checkTableSet: '1',
    createBy: userInfo.userId,
    createByName: userInfo.userName,
    createTime: '',
    startTime: null,
    endTime: null,
    planStartDate: '',
    planEndDate: '',
    createUnitId: userInfo.unitId,
    delFlag: 0,
    filelist: [],
    frequency: 1,
    frequencyType: '0',
    isNeedClock: '2',
    planId: '',
    planName: '',
    planState: '1',
    planTypeConfig: '',
    planTypeId: null,
    planTypeName: '',
    planTypeRemark: '',
    systemCode: '1',
    updateBy: '',
    updateByName: '',
    updateTime: '',
  };
  return result;
};
