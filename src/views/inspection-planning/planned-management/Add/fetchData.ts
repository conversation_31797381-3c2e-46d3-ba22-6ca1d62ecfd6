import { ICheckTempPageRes } from '../type';
import { IObj, IPageRes } from '@/types';
import { api } from '@/api';
import { $http } from '@tanzerfe/http';

// 计划新增
export function addPlan(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.sh.hazardPlan.addPlan);
  return $http.post<any>(url, { data: { _cfg: { showTip: true }, ...query } });
}
// 计划编辑
export function updatePlan(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.sh.hazardPlan.updatePlan);
  return $http.post<any>(url, { data: { _cfg: { showTip: true }, ...query } });
}
// /hazardPlan/startPlan

// 计划启用
export function startPlan(query: { planId: string }) {
  const url = api.getUrl(api.type.hazard, api.sh.hazardPlan.startPlan, query);
  return $http.post<ICheckTempPageRes>(url, {
    data: { _cfg: { showTip: true, showOkTip: false } },
  });
}
// 计划停用
export function stopPlan(query: { planId: string }) {
  const url = api.getUrl(api.type.hazard, api.sh.hazardPlan.stopPlan, query);
  return $http.post<ICheckTempPageRes>(url, { data: { _cfg: { showTip: true, showOkTip: true } } });
}

// 检查表列表
export function getCheckList(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.sh.checkList.listPage);
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}
// /hazardCheckTable/listPage
// 获取已开启的计划类型
export function getCheckTypeList(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.sh.hazardPlan.getCheckTypeData, data);
  return $http.post(url, {
    data: { _cfg: { showTip: true } },
  });
}
