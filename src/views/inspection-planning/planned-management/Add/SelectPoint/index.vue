<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <n-modal v-model:show="dialogVisible">
    <!-- 70vw -->
    <n-card
      style="background-color: #f8f8f8"
      class="dynamicswidth"
      :bordered="false"
      size="huge"
      preset="dialog"
      role="dialog"
      aria-modal="true"
    >
      <template #header>
        <div class="flex justify-between items-center">
          <div class="flex justify-start items-center">
            <img src="@/assets/title_ico.png" style="width: 17px" alt="" />
            <div style="font-size: 16px" class="ml-2 text-black">添加巡查点位</div>
          </div>
          <img src="@/assets/dialog_close.png" style="width: 42px" class="cursor-pointer" alt="" @click="closeDialog" />
        </div>
      </template>
      <!-- style="height: 65vh !important" -->
      <div class="bg-white notVw rounded p-4 h-full flex" style="height: 70vh !important">
        <UnitSelect
          class="unit-tree w-[320px] mx-[15px]"
          :unitId="unitId"
          :disabledUnitIds="disabledUnitIds"
          @updateVal="updateVal"
        />
        <div class="flex-1 ml-[20px] border border-1 border-gray-300 rounded p-[10px]">
          <div class="flex items-center mb-[10px]">
            <div class="w-[90px]">楼栋楼层：</div>
            <n-cascader
              class="mr-[20px] !w-[300px]"
              v-model:value="floorId"
              :options="optTreeData"
              expand-trigger="click"
              check-strategy="child"
              @change="changeSelect"
              clearable
            />
            <n-input
              class="!w-[380px]"
              v-model.trim="keyword"
              placeholder="请输入巡查点位名称"
              maxlength="50"
              show-word-limit
              clearable
              @input="getTabDataWarp"
            />
          </div>
          <div class="text-[#f56c6c] text-right my-[10px]">
            *巡查点位无检查表/楼栋楼层/位置/二维码信息暂不可选，请先补充点位数据
          </div>
          <n-data-table
            class="dynamicheight"
            flex-height
            remote
            :columns="columns"
            :data="dataList"
            v-model:checked-row-keys="checkedRowKeys"
            :pagination="pagination"
            :loading="loading"
            :row-key="(row: any) => row.id"
            @update:checked-row-keys="handleCheck"
            :row-class-name="rowClassName"
          />
        </div>
      </div>
      <template #footer>
        <div class="flex justify-end items-center">
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="submit">确认</el-button>
        </div>
      </template>
    </n-card>
  </n-modal>
  <el-dialog
    v-model="showCheckTable"
    :close-on-press-escape="false"
    width="65vw"
    top="5vw"
    :show-close="false"
    :close-on-click-modal="false"
    append-to-body
  >
    <template #header>
      <div class="flex justify-between items-center">
        <div class="flex justify-start items-center">
          <img src="@/assets/title_ico.png" style="width: 17px" alt="" />
          <div style="font-size: 16px" class="ml-2 text-black">检查表详情</div>
        </div>
        <img
          src="@/assets/dialog_close.png"
          style="width: 42px"
          class="cursor-pointer"
          alt=""
          @click="checkTableClose"
        />
      </div>
    </template>
    <div class="bg-white rounded p-4 h-full">
      <el-form inline>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="检查表:" class="text-[#1890ff]">
              {{ checkDataDetail.checkTableName }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="编制单位:">
              {{ checkDataDetail.unitName }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="编制时间:">
              {{ checkDataDetail.createTime }}
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-table empty-text="暂无数据" class="mt-4" :data="checkDataList" stripe height="62vh">
        <el-table-column type="index" label="序号" width="70" />
        <el-table-column prop="checkContent" align="left" label="检查内容" show-overflow-tooltip />
        <el-table-column align="left" label="常见隐患" show-overflow-tooltip>
          <template #default="{ row }">
            <FamiliarHazard
              v-if="row.areaEssentialFactorList"
              :yhlist="row.areaEssentialFactorList"
              :parentId="row.parentId"
              :id="row.id"
              :dataList="row.areaEssentialFactorList"
              :yhlistTotal="row.areaEssentialFactorList.length"
            />
            <span v-else>
              总计：{{
                row.childCheckAreaList && row.childCheckAreaList.length ? row.childCheckAreaList.length : 0
              }}条</span
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, defineEmits, h, watch, computed } from 'vue';
import UnitSelect from './unitSelect.vue';
import FamiliarHazard from '@/views/inspect-mgr/inspect/checkitems/table/GradeTag.vue';
import { flatData } from '@/views/inspect-mgr/inspect/comp/utils.ts';
import { getCheckPointList, getCheckTableDetail, getBuildingTreeByUnitId } from '@/views/inspection_point/fetchData';
import { getDefaultConfigList } from '@/views/paramsConf/comp/fetchData';
import { useStore } from '@/store';
import { DataTableRowKey, NImage } from 'naive-ui';
import { Search } from '@element-plus/icons-vue';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import getFileURL from '@/utils/getFileURL';

const [loading, search] = useAutoLoading(false);
const { pagination, updateTotal } = useNaivePagination(getTabData);

const floorId = ref<any>(null);
const optTreeData = ref([]);
const { userInfo } = useStore();

const emits = defineEmits(['closeDialog', 'success']);
const props = defineProps({
  unitId: {
    type: String,
    default: '',
  },
  //  禁用的组织id
  disabledUnitIds: {
    type: Array,
    default: () => [],
  },
  show: {
    type: Boolean,
    default: false,
  },
  pointList: {
    type: Array,
    default: () => [],
  },
});

const dialogVisible = computed(() => props.show);
const keyword = ref<string>('');
const checkedRowKeys = ref<number[]>([]);

const columns = [
  {
    type: 'selection',
    // 子单位新增点位巡查计划，添加巡查点位，未配置检查表的不能选择；未配置的也应该能选择，取参数配置点位默认检查项
    disabled(row: any) {
      // 检查表 参数配置 默认点位检查表
      return !row.checkTableId || !row.building || !row.floor || !row.location || !row.qrCodeUrl;
    },
  },
  {
    title: '序号',
    key: 'index',
    width: 70,
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '巡查点位名称',
    key: 'pointFullName',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '楼栋楼层',
    ellipsis: {
      tooltip: true,
    },
    render(row: any) {
      return row.building && row.floor ? row.building + row.floor : '--';
    },
  },
  {
    title: '位置',
    ellipsis: {
      tooltip: true,
    },
    render(row: any) {
      return row.location ? row.location : '--';
    },
  },
  {
    title: '检查表',
    key: 'checkTableName',
    ellipsis: {
      tooltip: true,
    },
    render(row: any) {
      return [
        h(
          'span',
          {
            style: {
              color: row.checkTableId ? '#1890ff' : '',
              cursor: row.checkTableId ? 'pointer' : '',
            },
            onClick: () => {
              if (row.checkTableId) {
                checkDataDetail.value = {};
                checkDataList.value = [];
                getCheckTableDetail(row.checkTableId).then((res: any) => {
                  checkDataDetail.value = res.data;
                  checkDataList.value = flatData(res.data.checkAreaList) || [];
                  showCheckTable.value = true;
                });
              }
            },
          },
          row.checkTableName || '--'
        ),
      ];
    },
  },
  {
    title: '点位二维码',
    ellipsis: {
      tooltip: true,
    },
    render(row: any) {
      let img = h(
        NImage,
        {
          style: 'width: 50px; height: 30px',
          src: getFileURL(row.qrCodeUrl),
        },
        () => row.hazardGradeName
      );
      return row.qrCodeUrl ? img : '--';
    },
  },
];

function rowClassName(row: any) {
  return !row.checkTableId || !row.building || !row.floor || !row.location || !row.qrCodeUrl ? 'too-old' : '';
}

// 默认检查点位
const defaultConfigTotal = ref([]);
const getDefaultConfigTotal = () => {
  let data = {
    checkTableType: 'point',
    pageSize: 9999,
    pageNo: 1,
    unitId: userInfo.topUnitId,
  };
  getDefaultConfigList(data).then((res: any) => {
    defaultConfigTotal.value = res.data.total;
  });
};

getDefaultConfigTotal();

const dataList = ref<any[]>([]);

function getTabDataWarp() {
  pagination.page = 1;
  getTabData();
}

const searchUnitId = ref<string>('');

function getTabData() {
  const params = {
    unitId: searchUnitId.value ? searchUnitId.value : userInfo.unitId,
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    keyword: keyword.value,
    floorId: floorId.value ? floorId.value : undefined,
  };

  search(getCheckPointList(params)).then((res: any) => {
    dataList.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
}

/** 获取楼栋楼层树数据 */
const getBuildingTreeByUnitIdApi = () => {
  let unitId = searchUnitId.value ? searchUnitId.value : userInfo.unitId;
  return new Promise(async (resolve, reject) => {
    const res: any = await getBuildingTreeByUnitId({ unitId });
    optTreeData.value = res.data.options;
    resolve(true);
  });
};
const changeSelect = (e: string) => {
  floorId.value = e;
  getTabDataWarp();
};

const checkPoint = ref([]);
const handleCheck = (rowKeys: DataTableRowKey[], data: any) => {
  checkPoint.value = data;
};

const closeDialog = () => {
  searchUnitId.value = '';
  checkPoint.value = [];
  emits('closeDialog');
};

const submit = () => {
  emits(
    'success',
    checkPoint.value.filter((item: any) => item)
  );
};
// 组织结构切换
function updateVal(unitId: string) {
  searchUnitId.value = unitId;
  floorId.value = null;
  keyword.value = '';
  pagination.page = 1;
  // pagination.pageSize = 10;
  getBuildingTreeByUnitIdApi();
  getTabDataWarp();
}

const showCheckTable = ref<any>(false);

const checkDataDetail = ref<any>({});
const checkDataList = ref<any[]>([]);

const checkTableClose = () => {
  showCheckTable.value = false;
};

watch(
  () => props.show,
  (nv) => {
    // 回显选中用户
    if (props.pointList.length) {
      checkedRowKeys.value = props.pointList.map((item: any) => item.checkPointId);
    } else {
      checkedRowKeys.value = [];
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.header {
  position: relative;

  &::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 18px;
    background-color: #1890ff;
    position: absolute;
    top: 10%;
    left: -10%;
  }
}

.dynamicswidth {
  width: 75vw;
}

.dynamicheight {
  min-height: 50vh;
  max-height: 50vh;
}

@media (max-width: 1480px) {
  .dynamicswidth {
    width: 90vw;
    min-width: 90vw;
  }
}

:deep(.too-old td) {
  background-color: rgb(250, 250, 252) !important;
  cursor: not-allowed;
  color: #ccc;
}
</style>
