<template>
  <!-- 只有监管单位显示树 -->
  <div
    class="flex flex-col justify-center justify-items-center border border-1 border-gray-300 rounded p-[10px]"
    v-if="userInfo.unitOrgType == '2'"
  >
    <div class="flex justify-start items-center mb-4">
      <img src="./icon.png" style="width: 18px" alt="" />
      <div style="font-size: 16px; font-weight: 600; margin-left: 10px">所属单位</div>
    </div>
    <el-input v-model="filterText" clearable placeholder="请输入组织名称" :suffix-icon="Search" />

    <div class="h-[calc(100%)] overflow-auto mt-2">
      <n-tree
        ref="treeRef"
        class="tree-cont"
        :show-irrelevant-nodes="false"
        :pattern="pattern"
        :data="filteredTreeDataList"
        selectable
        :cancelable="false"
        block-line
        :render-label="renderLabel"
        :theme-overrides="{ nodeHeight: '40px' }"
        default-expand-all
        :default-expanded-keys="defaultExpandedkeys"
        :default-selected-keys="defaultSelectedkeys"
        key-field="id"
        label-field="treeName"
        size="large"
        @update:selected-keys="handleNodeClick"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, h } from 'vue';
import { getOrgTree } from '@/views/mis/fetchData.ts';
import { Search } from '@element-plus/icons-vue';

import Dept from './assets/dept.png';
import YeWu from './assets/yewu.png';
import JianGuan from './assets/jianguan.png';

import { useStore } from '@/store';

const props = defineProps({
  unitId: {
    type: String,
    default: '',
  },
  //  禁用的组织id
  disabledUnitIds: {
    type: Array,
    default: () => [],
  },
});

const userInfo = useStore()?.userInfo;

const pattern = ref('');

const filterText = ref('');
const emits = defineEmits(['updateVal']);

interface Tree {
  [key: string]: any;
}

function renderLabel(e: any) {
  // 如果节点没有子节点，不显示展开图标
  if (e.option.children && e.option.children.length === 0) {
    e.option.isLeaf = true;
    return h(
      'div',
      {
        style: {
          display: 'flex',
          alignItems: 'center',
          width: '100%',
          padding: '10px 0 10px 0',
        },
      },
      [
        h('img', {
          style: {
            width: '16px',
            height: '16px',
            marginRight: '5px',
          },
          src: e.option.attributes?.orgType === '2' ? JianGuan : e.option.attributes?.orgType === '1' ? YeWu : Dept,
        }),
        h(
          'div',
          {
            style: {
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            },
            title: e.option.text,
          },
          e.option.text
        ),
      ]
    );
  } else {
    // 如果节点有子节点，正常显示
    if (e.option.root === 1) {
      // e.option.isLeaf = true;
      return h(
        'div',
        {
          style: {
            display: 'flex',
            alignItems: 'center',
            padding: '10px 0 10px 0',
          },
        },
        [
          h('img', {
            style: {
              width: '16px',
              height: '16px',
              marginRight: '5px',
              marginLeft: '24px',
            },
            // src: `https://agjp.tanzervas.com/aqsc/v1/img1/systemLogo/${userInfo.zhLogo}.png`,
            src: userInfo.logoPicUrl,
          }),
          h(
            'div',
            {
              style: {
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              },
              title: e.option.treeName,
            },
            e.option.treeName
          ),
        ]
      );
    } else {
      return h(
        'div',
        {
          style: {
            display: 'flex',
            alignItems: 'center',
            padding: '10px 0 10px 0',
          },
        },
        [
          h('img', {
            style: {
              width: '16px',
              height: '16px',
              marginRight: '5px',
            },
            src: e.option.attributes?.orgType === '2' ? JianGuan : e.option.attributes?.orgType === '1' ? YeWu : Dept,
          }),
          h(
            'div',
            {
              style: {
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              },
              title: e.option.treeName,
            },
            e.option.treeName
          ),
        ]
      );
    }
  }
}

const handleNodeClick = (rowKey: any[], rowData: any[]) => {
  // console.log('🚀 ~ handleNodeClick ~ val:', rowData);
  if (!rowData[0]) return;
  let isBusinessUnit = rowData[0].attributes.orgType == '1';
  emits('updateVal', rowData[0].id, isBusinessUnit);
};

const treeData = ref<any[]>([]);
const filteredTreeDataList = ref<any[]>([]);

watch(
  () => filterText.value,
  (val) => {
    if (val) {
      filteredTreeDataList.value = removeEmptyChildren(filterData(treeData.value, val));
    } else {
      filteredTreeDataList.value = treeData.value;
    }
  }
);

function filterData(nodes: any[], filterText: string) {
  return nodes.reduce((acc: any, node: any) => {
    const newNode = { ...node }; // 创建新节点，避免修改原数据
    const filteredChildren = filterData(node.children || [], filterText); // 递归筛选子节点

    // 如果当前节点的text包含filterText或有符合条件的子节点，则保留该节点
    if ((newNode.text && newNode.text.includes(filterText)) || filteredChildren.length > 0) {
      newNode.children = filteredChildren; // 仅保留符合条件的子节点
      acc.push(newNode);
    }

    return acc;
  }, []);
}

const defaultExpandedkeys = ref<string[]>([]);
const defaultSelectedkeys = ref<string[]>([]);
// 获取组织机构树
const getOrgUnitTree = async () => {
  const res = await getOrgTree({
    orgCode: props.unitId ? props.unitId : userInfo.unitId,
    needChildUnit: 1,
    unitStatus: 1,
  });

  if (res.data && res.data.length > 0) {
    filteredTreeDataList.value = treeData.value = removeEmptyChildren(addDisabledProperty(res.data));
  }
};

// 递归函数，用于遍历树数据并添加disabled属性
const addDisabledProperty = (data: any[]) => {
  return data.map((node: any) => {
    const newNode = { ...node, disabled: false };
    if (newNode.attributes.orgType != 1 || !newNode.attributes.erecordUnitId) {
      newNode.disabled = true;
      // 避免当前组节点被禁用
    } else if (!props.unitId && props.disabledUnitIds?.includes(newNode.id)) {
      newNode.disabled = true;
    } else {
      // 默认选择第一项
      if (!defaultSelectedkeys.value.length) {
        defaultSelectedkeys.value.push(newNode.id);
        handleNodeClick([newNode.id], [newNode]);
      }
    }
    if (newNode.children && newNode.children.length > 0) {
      newNode.children = addDisabledProperty(newNode.children);
    }
    return newNode;
  });
};

function removeEmptyChildren(array: any) {
  return array.map((item: any) => {
    // 创建一个新的对象，使用解构来复制原始属性
    const newItem = { ...item };
    // 检查是否存在children，并且是一个数组
    if (newItem.children && Array.isArray(newItem.children)) {
      newItem.children = removeEmptyChildren(newItem.children); // 递归调用
      // 如果children为空数组，则删除该属性
      if (newItem.children.length === 0) {
        delete newItem.children;
      }
    }

    return newItem; // 返回新对象
  });
}

getOrgUnitTree();
</script>
<style lang="scss" scoped>
.tree-cont {
  &.n-tree {
    .n-tree-node {
      padding-left: 16px !important;
    }
  }
}

.tree-scroll {
  margin-top: 20px;
  height: calc(100vh - 32px);
  overflow-y: scroll;
  background: transparent;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  font-size: 14px;
}

:deep(.el-tree-node__content) {
  padding: 20px 0;
}
</style>
