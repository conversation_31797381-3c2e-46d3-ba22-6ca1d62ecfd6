import { defineComponent, ref, watch, watchEffect } from 'vue';
import { IHazardPlanForm } from './type';
import {
  NFormItem,
  NSelect,
  NRadioGroup,
  NSpace,
  NRadio,
  SelectOption,
  NInput,
  NDatePicker,
  NInputGroup,
  NInputGroupLabel,
  NInputNumber,
  NTimePicker,
} from 'naive-ui';
import ComHeaderB from '@/components/header/ComHeaderB.vue';
import { computed } from 'vue';
import { useStore } from '@/store';
import { getCheckList, getCheckTypeList } from './fetchData';
import { FileUpload } from '@/components/upload';
import { debounce } from 'lodash-es';
import { IUploadRes } from '@/components/upload/type';

const checkTypeList: any = ref([]); // 检查类型列表

export const checkTable = defineComponent({
  props: {
    formData: {
      type: Object,
    },
  },

  setup(props) {
    const formData: any = props.formData;

    const { userInfo } = useStore();

    const generalOptions = ref<SelectOption[]>([]);
    const updateCheckTableName = (val: string, option: any) => {
      formData.checkTableId = val;
      formData.checkTableName = option.label;
    };

    /** 获取检查表 */
    const getCheckListName = async (checkTableType = '') => {
      const params = {
        checkTableType,
        unitId: `${userInfo.unitId},${userInfo.topUnitId}`,
        checkStatus: 0,
        filterEmptyContent: 1, // 1: 过滤空内容， 2: 不过滤空内容
        pageSize: -1,
        pageNo: 1,
      };
      const res: any = await getCheckList(params);
      generalOptions.value = res.data.rows.map((item: any) => ({
        label: item.checkTableName,
        value: item.id,
      }));
    };

    // watch(props.formData, () => {

    // })
    watchEffect(() => {
      getCheckListName(props.formData?.checkRange);
      // console.log("watchEffect", props.formData?.checkRange);
    });
    return () => (
      <NFormItem label="检查表" path="checkTableId">
        <NSelect
          value={formData.checkTableId}
          placeholder="请选择"
          on-update:value={updateCheckTableName}
          options={generalOptions.value}
        />
      </NFormItem>
    );
  },
});

export const checkEndOperate = defineComponent({
  props: {
    formData: {
      type: Object,
    },
    checkRange: {
      type: String,
    },
    planTypeConfig: {
      type: Object,
    },
    generalOptions: {
      type: Array,
    },
  },

  setup(props) {
    const formData: any = props.formData;
    const checkRange = props.checkRange;
    const showCheckEnd = ref(false);
    let planTypeConfig: any = {};
    watchEffect(() => {
      // 查找检查类型对应的配置项  （参数管理 / 检查类型设定配置）
      const planTypeId = formData.planTypeId;
      console.log('checkTypeList====', checkTypeList);
      console.log('planTypeId====', planTypeId);
      if (checkTypeList.value.length > 0 && planTypeId) {
        const planTypeObj = checkTypeList.value.filter((item: any) => item.planTypeId === planTypeId)[0];
        planTypeConfig = JSON.parse(planTypeObj.planTypeConfig) || {};
        console.log('planTypeConfig===', planTypeConfig);
        // 选中的检查项配置了检查结束操作 && 检查范围是位置
        // showCheckEnd.value = planTypeConfig?.checkEnd && formData.checkRange == '1';
        showCheckEnd.value = planTypeConfig?.checkEnd || formData.checkRange == '1';
        // 不展示的时候默认否
        if (!showCheckEnd.value) {
          formData.checkEndOperate = '2';
        }
      }
    });

    return () => (
      <div class="mt-[20px]">
        <ComHeaderB title="其他信息" class="mb-[20px] cursor-pointer" />

        <NFormItem label="检查结束操作" path="checkEndOperate" required>
          <div class="flex flex-col gap-4">
            <div>检查任务是否只允许检查负责人结束</div>
            {/* 点击新增点位巡查按钮 */}
            {/* 检查结束操作默认选择否，置灰不可选 */}
            <NRadioGroup
              disabled={checkRange === '3' ? true : false}
              value={formData.checkEndOperate}
              on-update:value={(val: string) => (formData.checkEndOperate = val)}
            >
              <NSpace>
                <NRadio value="1" > 是</NRadio>
                <NRadio value="2">否 </NRadio>
              </NSpace>
            </NRadioGroup>
            <div class=" text-[#999]">
              选择是，只有检查负责人可以结束检查任务。 <br /> 选择否，检查负责人和参与人都可以结束检查任务
            </div>
          </div>
        </NFormItem>
      </div>
    );
  },
});

export const isNeedClock = defineComponent({
  props: {
    formData: {
      type: Object,
    },
    checkRange: {
      type: String,
    },
    generalOptions: {
      type: Array,
    },
  },

  setup(props) {
    const formData: any = props.formData;
    const checkRange = props.checkRange;
    /** 判断是否展示是否需要检查人现场打卡 */
    const showClockIn = ref(false);
    let planTypeConfig: any = {};
    watchEffect(() => {
      // 查找检查类型对应的配置项  （参数管理 / 检查类型设定配置）
      const planTypeId = formData.planTypeId;
      if (checkTypeList.value.length > 0 && planTypeId) {
        const planTypeObj = checkTypeList.value.filter((item: any) => item.planTypeId === planTypeId)[0];
        planTypeConfig = JSON.parse(planTypeObj.planTypeConfig) || {};
        // 选中的检查项配置了是否需要检查人现场打卡 && 检查范围是位置
        // showClockIn.value = planTypeConfig.clockIn || formData.checkRange == '1';
        showClockIn.value = planTypeConfig.clockIn;
        // 不展示的时候默认否
        if (!showClockIn.value) {
          formData.isNeedClock = '2';
        }
      }
    });

    return () => (
      <NFormItem label="是否需要检查人现场打卡" path="isNeedClock" required>
        <div class="flex flex-col gap-4">
          <div>选择后需要检查人员现场拍照打卡才可以开始检查</div>
          <NRadioGroup value={formData.isNeedClock} on-update:value={(val: string) => (formData.isNeedClock = val)}>
            <NSpace>
              <NRadio   disabled={formData.planState === '4'} value="1">是 </NRadio>
              <NRadio  disabled={formData.planState === '4'}value="2">否 </NRadio>
            </NSpace>
          </NRadioGroup>
        </div>
      </NFormItem>
    );
  },
});

export const checkRange = defineComponent({
  props: {
    formData: {
      type: Object,
    },
  },

  setup(props) {
    const formData: any = props.formData;
    let planTypeConfig: any = {};
    watchEffect(() => {
      const planTypeId = formData.planTypeId;
      if (checkTypeList.value.length > 0 && planTypeId) {
        const planTypeObj = checkTypeList.value.filter((item: any) => item.planTypeId === planTypeId)[0];
        planTypeConfig = JSON.parse(planTypeObj.planTypeConfig) || {};
        // 检查范围不是位置的时候，清空位置信息 和位置类型
        if (!planTypeConfig.area) {
          formData.unitList.forEach((item: any) => {
            item.locationType = null;
            item.roomlist = [];
          });
        }
      }
    });

    const changeChecked = (value: string) => {
      formData.checkRange = value;
      // 检查范围不是位置的时候，清空位置信息 和位置类型
      if (value != '1') {
        // console.log(props.formData.unitlist);
        props.formData.unitlist.forEach((item: any) => {
          item.locationType = null;
          item.roomlist = [];
        });
      }
    };
    return () => (
      <NFormItem label="检查范围" path="checkRange" required>
        {formData.planTypeId && (
          <NRadioGroup value={formData.checkRange} on-update:value={changeChecked}>
            <NSpace>
              {planTypeConfig?.area && <NRadio value="1">位置</NRadio>}
              {planTypeConfig?.device && <NRadio value="2">设备</NRadio>}
              {planTypeConfig?.pointer && <NRadio value="3">巡查点位</NRadio>}
            </NSpace>
          </NRadioGroup>
        )}
      </NFormItem>
    );
  },
});

export const checkExecuteMethod = defineComponent({
  props: {
    formData: {
      type: Object,
    },
    planTypeConfig: {
      type: Object,
    },
    generalOptions: {
      type: Array,
    },
    checkRange: {
      type: String,
    },
  },

  setup(props) {
    const formData: any = props.formData;
    /** 判断是否展示是否需要检查人现场打卡 */
    const showClockIn = computed(() => {
      // 检查类型不展示
      if (!props.planTypeConfig?.clockIn) return false;
      return true;
    });

    const changeChecked = (value: string) => (formData.checkExecuteMethod = value);
    watchEffect(() => {
      // 区域的时候可以选择 检查执行方式，其他检查范围默认需要逐一检查
      if (+formData.checkRange !== 1) formData.checkExecuteMethod = '2';
    });
    if (Number(props.checkRange) === 3) {
      formData.checkExecuteMethod = '2';
    }
    // 区域的时候可以选择 检查执行方式，其他检查范围默认需要逐一检查
    return () => (
      <NFormItem label="检查执行方式" path="checkExecuteMethod" required>
        <div class="flex gap-4">
          <NRadioGroup
            value={formData.checkExecuteMethod}
            on-update:value={changeChecked}
            class="flex-shrink-0"
            disabled={Number(props.checkRange) === 3}
          >
            <NSpace>
              <NRadio  disabled={formData.planState === '4'} value="1">不需要逐一执行每个检查项 </NRadio>
              <NRadio  disabled={formData.planState === '4'} value="2">需要逐一执行每个检查项 </NRadio>
            </NSpace>
          </NRadioGroup>
          <span class="text-[#787b80]">选择需要逐一执行后，检查表中的每个检查项目都需要逐一填报是否存在隐患</span>
        </div>
      </NFormItem>
    );
  },
});
export const checkDemand = defineComponent({
  props: {
    formData: {
      type: Object,
    },
  },

  setup(props) {
    const formData: any = props.formData;

    const changeInput = (value: string) => (formData.checkDemand = value);
    return () => (
      <NFormItem label="检查要求">
        <NInput
          placeholder="请填写检查要求"
          type="textarea"
          show-count
          rows="2"
          maxlength="200"
   
          value={formData.checkDemand}
          on-update:value={changeInput}
          clearable
        />
      </NFormItem>
    );
  },
});

export const planType = defineComponent({
  props: {
    formData: {
      type: Object,
    },
  },
  emits: ['update:planTypeConfig'],

  setup(props, { emit }) {
    const formData: any = props.formData;
    console.log(props.formData,'formData>>>>>>')
    const userInfo = useStore().userInfo;

    /** 获取检查类型 */
    const getCheckTypeListFn = async () => {
      const res = await getCheckTypeList({
        planTypeStatus: '1',
        unitId: userInfo.topUnitId,
      });
      checkTypeList.value = res.data || [];
    };
    const changeSelect = (value: string, option: any) => {
      formData.planTypeId = value;
      // console.log(value, option);
      // formData.checkTableId = null;
      formData.isNeedClock = '2'; // 检查类型改变，打卡重置否
      formData.checkEndOperate = '2'; // 检查类型改变，检查结束操作重置否
      emit('update:planTypeConfig', JSON.parse(option.planTypeConfig));
      formData.planTypeName = option.planTypeName;
      formData.planTypeRemark = option.planTypeRemark;
    };
    getCheckTypeListFn();
    return () => (
      <NFormItem label="检查类型" path="planTypeId">
        <NSelect
          value={formData.planTypeId}

          placeholder="请选择检查类型"
          disabled={formData.planState === '4'}
          options={checkTypeList.value}
          label-field="planTypeName"
          value-field="planTypeId"
          ssss
          on-update:value={changeSelect}
        />
      </NFormItem>
    );
  },
});

export const filelist = defineComponent({
  props: {
    formData: {
      type: Object,
    },

  },

  setup(props) {
    const formData: any = props.formData;

    const handleUpdate = debounce((res: IUploadRes, data: IUploadRes[]) => {
      const _arr = data.map((i) => i.res || [i]);

      formData.filelist = _arr.flat().map((i: IUploadRes) => {
        return {
          fileName: i.fileName,
          fileType: i.fileType,
          fileUrl: i.fileUrl,
          saveName: i.saveName,
          saveType: i.saveType,
        };
      });
    }, 300);
    return () => (
      <NFormItem label="附件" path="filelist">
        <FileUpload
          size="10"
          data={formData.filelist}
          class="pt-0"
          tips="仅支持doc,pdf,docx文件类型"
          accept="doc,pdf,docx"
          onUpdate={handleUpdate}
        />
      </NFormItem>
    );
  },
});

export const times = defineComponent({
  props: {
    formData: {
      type: Object,
    },
    status: {
      type: String,
    },
  },

  setup(props) {
    const formData: any = props.formData;
    console.log(formData.value,'formData')
    const status: any = props.status;
    console.log(status, 'status');
    const planTimeRange = computed({
      get: () => {
        if (formData.planStartDate && formData.planEndDate) {
          return [formData.planStartDate.split(' ')[0], formData.planEndDate.split(' ')[0]];
        }
        return null;
      },
      set: (val: string[] | null) => {
        console.log('planTimeRange', val);
        if (val && val.length > 0) {
          formData.planStartDate = `${val[0]} 00:00:00`;
          formData.planEndDate = `${val[1]} 23:59:59`;
          console.log('planTimeRange', formData.planStartDate, formData.planEndDate);
        } else {
          formData.planStartDate = '';
          formData.planEndDate = '';
        }
      },
    });
    /** 小于今天的日期不能选 */
    const disablePreviousDate = (ts: number) => ts + 24 * 60 * 60 * 1000 < Date.now();
    return () => (
      <NFormItem label="计划起止时间" path="times">
        <NDatePicker
          class="w-full"
          disabled={formData.planState === '4'}
          formatted-value={planTimeRange.value}
          on-update:formatted-value={(val: any) => (planTimeRange.value = val)}
          type="daterange"
          clearable
          is-date-disabled={disablePreviousDate}
        />
      </NFormItem>
    );
  },
});

export const frequencyTypeTmp = defineComponent({
  props: {
    formData: {
      type: Object,
    },
  },

  setup(props) {
    const formData: any = props.formData;
    /** 检查频次类型切换 */
    const frequencyTypeTmpChange = (val: any) => {
      formData.frequencyTypeTmp = val;
      // 检查频次选择重复时，频次类型默认为天
      if (val === '1') formData.frequencyType = '1';
    };
    const showFrequencyType = computed(() => {
      console.log('frequencyTypeTmpChange', +formData.frequencyTypeTmp === 1);
      if (+formData.frequencyTypeTmp === 1) return true;
      return false;
    });
    const showFrequencyTime = computed(() => {
      if (formData.frequencyTypeTmp === '1' && `${formData.frequencyType}` === '0') return true;
      return false;
    });

    formData.frequencyType = Number(formData.frequencyType);
    const FrequencyTypeList = [
      { value: '0', label: '时' },
      { value: '1', label: '日' },
      { value: '2', label: '周' },
      { value: '3', label: '月' },
      { value: '4', label: '季度' },
      { value: '5', label: '年' },
    ];
    watchEffect(() => {
      if (+formData.frequencyTypeTmp === 2) formData.frequencyType = '99';
      else formData.frequencyType = String(formData.frequencyType);
    });
    console.log('formData.frequency', formData.frequency);
    formData.frequency = formData.frequency ? Number(formData.frequency) : null;
    return () => (
      <div class="flex gap-2">
        <NFormItem label="检查频次" path="frequencyTypeTmp" required>
          <NRadioGroup value={formData.frequencyTypeTmp} on-update:value={frequencyTypeTmpChange}>
            <NSpace >
              <NRadio  disabled={formData.planState === '4'} value="2">不重复 </NRadio>
              <NRadio  disabled={formData.planState === '4'} value="1" class="ml-5">
                重复
              </NRadio>
            </NSpace>
          </NRadioGroup>
        </NFormItem>

        {showFrequencyType.value && (
          <NFormItem label="" path="frequencyType" style={showFrequencyType.value}>
            <NInputGroup>
              <div class="flex jussify-center items-center">
                <NInputGroupLabel style="height: 28px">每</NInputGroupLabel>
                <NInputNumber
                 disabled={formData.planState === '4'}
                  value={formData.frequency}
                  on-update:value={(val: number) => (formData.frequency = val)}
                  clearable
                  min={1}
                  style="width: 150px"
                />
                <NSelect
                  style="width: 80px"
                  disabled={formData.planState === '4'}
                  value={formData.frequencyType}
                  placeholder="频次类型"
                  on-update:value={(val: string) => {
                    formData.frequencyType = val;
                    if (val == '0') {
                      formData.startTime = '00:00:00';
                      formData.endTime = '23:59:59';
                    }
                  }}
                  options={FrequencyTypeList}
                />
              </div>
            </NInputGroup>
          </NFormItem>
        )}
        {showFrequencyTime.value && (
          <NFormItem label="任务生成时段:" label-width="120px" path="startTime">
            <NTimePicker
               disabled={formData.planState === '4'}
              placeholder="开始时间"
              formatted-value={formData.startTime}
              onUpdate:formatted-value={(val: string) => (formData.startTime = val)}
            />
          </NFormItem>
        )}
        {showFrequencyTime.value && (
          <NFormItem label="" path="endTime" style={showFrequencyTime.value}>
            <NTimePicker
               disabled={formData.planState === '4'}
              placeholder="结束时间"
              formatted-value={formData.endTime}
              onUpdate:formatted-value={(val: string) => (formData.endTime = val)}
            />
          </NFormItem>
        )}
      </div>
    );
  },
});
