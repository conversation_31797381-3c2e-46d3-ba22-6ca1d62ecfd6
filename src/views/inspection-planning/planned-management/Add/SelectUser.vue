<template>
  <n-modal v-model:show="dialogVisible">
    <!-- width: 65vw; -->
    <n-card
      style="background-color: #f8f8f8"
      class="dynamicswidth"
      :bordered="false"
      size="huge"
      preset="dialog"
      role="dialog"
      aria-modal="true"
    >
      <template #header>
        <div class="flex justify-between items-center">
          <div class="flex justify-start items-center">
            <img src="@/assets/title_ico.png" style="width: 17px" alt="" />
            <div style="font-size: 16px" class="ml-2 text-black">
              {{ title }}
            </div>
          </div>
          <img src="@/assets/dialog_close.png" style="width: 42px" class="cursor-pointer" alt="" @click="cancel" />
        </div>
      </template>
      <div class="bg-white rounded p-4 h-full">
        <div class="flex justify-start items-start mb-4">
          <div class="text">已选人员:</div>
          <div class="user-box p-[3px]">
            <n-tag
              type="success"
              v-for="(item, index) in checkUsers"
              :key="index"
              closable
              size="small"
              class="m-[2px]"
              @close="handleClose(item.id)"
            >
              {{ item?.userName || item?.checkUserName }}
            </n-tag>
          </div>
        </div>
        <div class="main">
          <div class="left" v-loading="treeLoading">
            <ComTree
              ref="tabRef"
              :systemCodeList="systemCodeList"
              :userType="userTypeValue"
              :treeData="treeData"
              @tabChange="tabChange"
              :unitId="selunitId"
              @action="treeChange"
            />
          </div>
          <div class="right">
            <div class="flex justify-end items-center my-2">
              <n-input passively-activateds clearable :style="{ width: '40%' }" v-model:value="searchValue" />
              <n-button type="primary" @click="getTabData">
                <n-icon size="16">
                  <Search />
                </n-icon>
              </n-button>
            </div>
            <!-- min-height="40vh" max-height="40vh"  -->
            <n-data-table
              v-loading="loading"
              remote
              :columns="columns"
              :data="tableData"
              :pagination="pagination"
              class="dynamicheight"
              flex-height
              :checked-row-keys="checkIds"
              :row-key="(row: any) => row.id"
              @update:checked-row-keys="handleCheck"
            />
          </div>
        </div>
      </div>
      <template #footer>
        <div class="flex justify-end items-center">
          <el-button @click="cancel">关闭</el-button>
          <el-button type="primary" @click="onConfirm">确认</el-button>
        </div>
      </template>
    </n-card>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { Search } from '@vicons/ionicons5';
import { NTag, NIcon, TreeOption, DataTableRowKey } from 'naive-ui';
import ComTree from '@/views/inspection-planning/planned-management/comp/tree2/index.vue';
import {
  getOrgTree,
  getOrgUserList,
  getTenantTree,
  getTenantUserList,
  getAllUnit,
  getPerManagerList1,
  getSystemListApi,
  queryOrgTreeByTanzerApi,
} from '@/views/inspection-planning/planned-management/fetchData.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { useStore } from '@/store';
import { throttle } from 'lodash-es';
import { el } from 'element-plus/es/locale/index.mjs';
const userInfo = useStore()?.userInfo;
const { pagination, updateTotal } = useNaivePagination(getTabData);
const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  userType: {
    type: Number,
    default: 1,
  },
  unitId: {
    type: String,
    efault: '',
  },
  showSelectUserModal: {
    type: Boolean,
    default: false,
  },
  userlist: {
    type: Array,
    default: () => [],
  },
});

// 显示承租方（初始化调用是否有承租方数据 无数据则不展示承租方tab）
const dialogVisible = computed(() => props.showSelectUserModal);
const checkIds = computed(() => checkUsers.value.map((item) => item.checkUserId));
const tabRef = ref();
const systemCodeList = ref();
const userTypeValue = ref(1);
const emits = defineEmits(['close', 'treeChange', 'success']);
const treeId: any = ref(null);
const checkUsers = ref<Array<any>>([]);
const searchValue = ref<string>('');
const tabThreeName: any = userInfo.zhId == 'ycsyqt' ? '承包商' : '相关方';
let colList = [
  // 组织机构
  [
    {
      type: 'selection',
    },
    {
      title: '姓名',
      key: 'userName',
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '账号',
      key: 'loginName',
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '手机号',
      key: 'userTelphone',
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '岗位',
      key: 'postName',
      ellipsis: {
        tooltip: true,
      },
    },
  ],
  // 相关方/延长石油-承包商
  [
    {
      type: 'selection',
    },
    {
      title: '姓名',
      key: 'userName',
    },
    {
      title: tabThreeName + '企业',
      // key: 'unitName',
      //key: 'createOrgName',
      key: 'depart',
    },
    {
      title: '手机号',
      key: 'phone',
    },
  ],
  // 承租方
  [
    {
      type: 'selection',
    },
    {
      title: '姓名',
      key: 'userName',
    },
    {
      title: '账号',
      key: 'loginName',
    },
    {
      title: '手机号',
      key: 'phone',
    },
  ],
];
const columns = ref();
const tableData = ref([]);
const treeData: any = ref([]);
const loading = ref(false);
const treeLoading = ref(false);
const selunitId = ref<any>('');
//树结构点击
const treeChange = (v: TreeOption, isFirstLoad: boolean, val: any = {}) => {
  console.log('isFirstLoad', isFirstLoad);
  console.log('treeChange======', val);
  if (isFirstLoad) {
    if (userTypeValue.value == 1) {
      props.unitId && (selunitId.value = props.unitId);
      treeId.value = selunitId.value ? selunitId.value : v.id;
    } else {
      selunitId.value = v.id;
      treeId.value = v.id;
    }
    console.log('treeChange11111111', selunitId.value);
  } else {
    selunitId.value = val.unitId;
    console.log('treeChange', selunitId.value);
    treeId.value = v.id;
  }
  pagination.page = 1;
  // pagination.pageSize = 10;
  searchValue.value = '';
  !isFirstLoad && getTabData();
};
//获取树结构数据
async function tabChange(newTab: number, keyWord: any) {
  // checkUsers.value = [];
  userTypeValue.value = newTab;
  treeLoading.value = true;
  // 检查人员展示组织内人员是所有人员，相关人和承租方人员是检查对象内的
  if (newTab == 1) {
    let { data } = await getOrgTree({
      orgCode: userInfo.topUnitId,
      orgName: keyWord,
    });
    if (!data || data.length == 0) {
      tableData.value = [];
      updateTotal(0);
    }
    treeLoading.value = false;
    treeData.value = (data && childrenFn(data)) || [];
    treeChange(treeData.value[0], true);
  } else if (newTab == 2) {
    // 相关方
    let { data } = await queryOrgTreeByTanzerApi({
      needChildUnit: 1,
      needself: 1,
      type: 2,
      keyWord,
    });
    if (!data || data.length == 0) {
      tableData.value = [];
      updateTotal(0);
    }
    treeLoading.value = false;
    // 去掉箭头
    treeData.value = (data && childrenFn(data)) || [];
    treeChange(treeData.value[0], true);
  } else if (newTab == 3) {
    // zyl----1.0.6版本修改相关方数据
    let { data } = await getTenantTree({
      unitId: props.unitId,
      likeParam: keyWord,
    });
    if (!data || data.length == 0) {
      tableData.value = [];
      updateTotal(0);
    }
    treeLoading.value = false;
    treeData.value =
      (data &&
        data.map((item: any) => ({
          ...item,
          id: item.tenantryId,
          text: item.tenantryName,
        }))) ||
      [];
    treeChange(treeData.value[0], true);
  }
  pagination.page = 1;
  getTabData();
}

// 尝试获取中台系统列表
async function getSystemList(): Promise<boolean> {
  try {
    const { data }: any = await getSystemListApi();
    systemCodeList.value = data ? data.map((item: any) => item.sysCode) : [];
    return true; // 操作成功
  } catch (error) {
    console.warn(error);
    return false; // 操作失败
  }
}
function childrenFn(_arr: any) {
  if (!_arr.length) return _arr;
  for (let index = 0; index < _arr.length; index++) {
    const element = _arr[index];
    if (element.children?.length) {
      childrenFn(element.children);
    } else {
      delete element.children;
    }
  }
  return _arr;
}

watch(
  () => searchValue.value,
  () => {
    doHandle();
  }
);
const doHandle = throttle(() => {
  getTabData();
}, 1000);

//获取组织机构人员
const getOrgUserListApi = async (orgCode: any, pageData: any, userName: any) => {
  let {
    data: { rows, total },
  }: any = await getOrgUserList({
    orgCode,
    userName,
    ...pageData,
  });
  tableData.value = rows
    ? rows.map((item: any) => {
        return {
          ...item,
          checkUserId: item.id,
        };
      })
    : [];
  updateTotal(total || 0);
  loading.value = false;
};

//获取承租方人员
const getTenantUserListApi = async (tenantryId: any, pageData: any, likeParam: any) => {
  let {
    data: { rows, total },
  }: any = await getTenantUserList({
    tenantryId,
    likeParam,
    ...pageData,
  });
  console.log(rows);
  tableData.value = rows
    ? rows.map((item: any) => {
        return {
          ...item,
          unitId: props.unitId,
          id: item.loginUserId,
        };
      })
    : [];
  updateTotal(total ?? 0);
  loading.value = false;
};

//获取相关方人员
const getPerManagerListApi = async (unitId: any, pageData: any, keyWord: any) => {
  let {
    data: { rows, total },
  }: any = await getPerManagerList1({
    sysCode: 'web',
    isBlack: 0,
    unitId,
    type: '0',
    name: keyWord,
    ...pageData,
  });
  tableData.value = rows
    ? rows?.map((item: any) => {
        return {
          ...item,
          checkUserId: item.id,
          checkUserName: item.name,
          userName: item.name,
          unitId: item.createOrgCode,
        };
      })
    : [];
  updateTotal(total ?? 0);
  loading.value = false;
};

async function getTabData() {
  // 动态获取列
  columns.value = colList[userTypeValue.value - 1];
  let orgCode = treeId.value && (treeId.value || treeData.value[0].id || null);
  let pageData = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
  };
  loading.value = true;
  try {
    userTypeValue.value == 1 && (await getOrgUserListApi(orgCode, pageData, searchValue.value));
    userTypeValue.value == 3 && (await getTenantUserListApi(orgCode, pageData, searchValue.value));
    userTypeValue.value == 2 && (await getPerManagerListApi(orgCode, pageData, searchValue.value));
  } catch (error) {
    tableData.value = [];
    loading.value = false;
  }
}
//勾选事件
function handleCheck(rowKeys: DataTableRowKey[], data: any, meta: any) {
  let userType: number;
  // 因为接口 入参type的数据结构不一样，所以需要做一下处理  之前type2是承租方  现在是3
  if (userTypeValue.value == 3) {
    userType = 2;
  } else if (userTypeValue.value == 2) {
    userType = 3;
  } else {
    userType = 1;
  }
  // 承租方 userTypeValue 2 用户id 传 loginUserId
  if (meta.action === 'check') {
    checkUsers.value.push({
      ...meta.row,
      checkUserName: meta.row.userName,
      checkUserId: userTypeValue.value == 3 ? meta.row.loginUserId : meta.row.id,
      userType,
    });
  } else if (meta.action === 'uncheck') {
    checkUsers.value = checkUsers.value.filter((item) => item.id !== meta.row.id);
  } else if (meta.action === 'checkAll') {
    const map = new Map();
    const result: any[] = [];
    [...data, ...checkUsers.value].forEach((item) => {
      if (item && !map.has(item.id)) {
        map.set(item.id, true);
        result.push({
          ...item,
          checkUserName: item.userName,
          checkUserId: userTypeValue.value == 3 ? item.loginUserId : item.id,
          userType,
        });
      }
    });
    checkUsers.value = result;
  } else if (meta.action === 'uncheckAll') {
    let arr = tableData.value;
    checkUsers.value = checkUsers.value.filter((item) => {
      return !arr.find((item2: any) => item2.id === item.id);
    });
  }
}
// 取消选择事件
const handleClose = (value: any) => {
  checkUsers.value = checkUsers.value.filter((item) => {
    return item.id !== value;
  });
};

const cancel = () => {
  emits('close');
  // 置空id合集
  userTypeValue.value = 1;
  treeData.value = [];
  treeId.value = null;
  checkUsers.value = [];
  tableData.value = [];
};
//输出id合集
const onConfirm = () => {
  checkUsers.value = checkUsers.value.map((itme) => {
    return {
      ...itme,
      userUnitId: itme.userType === 3 ? itme.orgCode : itme.unitId,
      userUnitName: itme.userType === 3 ? itme.depart : itme.unitName,
      //  组织机构传unitId,unitName
      userMasterUnitId:
        itme.userType === 1 ? itme.unitId : itme.userType === 3 ? itme.createOrgCode : itme.userMasterUnitId,
      userMasterUnitName:
        itme.userType === 1 ? itme.unitName : itme.userType === 3 ? itme.createOrgName : itme.userMasterUnitName,
    };
  });
  emits('success', checkUsers.value, userTypeValue.value);
  cancel();
};

watch(
  () => props.showSelectUserModal,
  async (value) => {
    if (value) {
      // 用于判断是否显示承租方tab
      await getSystemList();
      await tabChange(props.userType || userTypeValue.value || 1);
      // 回显选中用户
      if (props.userlist.length) {
        checkUsers.value = props.userlist;
      }
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.user-box {
  flex: 1;
  min-height: 34px;
  max-height: 59px;
  margin-left: 10px;
  border-radius: 3px;
  border: 1px solid #dcdfe6;
  background-color: #fff;
  overflow-y: auto;
}

.main {
  height: 65vh;
  display: flex;
  justify-content: space-between;

  .left {
    width: 350px;
    margin-right: 10px;
    background-color: #fff;
    border-radius: 3px;
    border: 1px solid #e1e1e1;

    .list {
      height: 100%;
      overflow-y: auto;
    }

    .org {
      height: 50px;
      font-size: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f5f7fa;
    }
  }

  .right {
    flex: 1;
    padding: 10px 10px;
    background-color: #fff;
    border-radius: 3px;
    border: 1px solid #e1e1e1;
  }

  .n-data-table .n-data-table__pagination {
    min-width: 600px;
  }
}

.dynamicswidth {
  width: 75vw;
}

.dynamicheight {
  min-height: 50vh;
  max-height: 50vh;
}

@media (max-width: 1480px) {
  .dynamicswidth {
    width: 90vw;
    min-width: 90vw;
  }
}
</style>
