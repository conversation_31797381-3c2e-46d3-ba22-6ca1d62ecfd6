// import { NButton, NRadioGroup, NRadio } from 'naive-ui';

// 0:时;1:日;2:周;3:月;4:季度;5:年
export const FrequencyTypeList = [
  { value: '0', label: '时' },
  { value: '1', label: '日' },
  { value: '2', label: '周' },
  { value: '3', label: '月' },
  { value: '4', label: '季度' },
  { value: '5', label: '年' },
];

export const UnitColumns = [
  {
    title: '序号',
    key: 'index',
    align: 'left',
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '检查对象',
    key: 'unitName',
  },
];
