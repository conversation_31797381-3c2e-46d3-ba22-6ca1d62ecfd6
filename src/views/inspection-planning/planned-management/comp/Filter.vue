<template>
  <div style="padding-left: 20px">
    <el-tabs v-model="activeTab" class="demo-tabs" @tab-change="tabClick">
      <el-tab-pane
        style="font-size: 17px; font-weight: bold"
        :label="`${item.planTypeName}(${item.countNum})`"
        :name="item.planTypeId"
        :key="item.planTypeId"
        v-for="item in contList"
      />
    </el-tabs>
  </div>
  <div style="padding-left: 20px; padding-right: 20px; padding-bottom: 10px">
    <n-form
      :model="filterForm"
      label-placement="left"
      inline
      :show-feedback="false"
    >
      <n-grid :x-gap="12" :y-gap="8" :cols="3">
        <n-grid-item>
          <n-form-item label="计划状态:">
            <n-select
              v-model:value="filterForm.planState"
              class="w-full flex items-center"
              :options="planStateOptions"
              placeholder="全部"
              clearable
              @update:value="doHandle(ACTION.SEARCH)"
            />
          </n-form-item>
        </n-grid-item>
        <n-grid-item>
          <n-form-item label="">
            <n-input
              placeholder="请输入计划名称"
              class="w-full flex items-center"
              maxlength="50"
              show-count
              v-model:value="filterForm.planName"
              clearable
              @update:value="doHandle(ACTION.SEARCH)"
            />
          </n-form-item>
        </n-grid-item>
        <n-grid-item span="1">
          <div class="flex gap-4 justify-end items-center">
            <n-button type="primary" @click="add()">
              <IconAdd />
              {{
                checkRange === '1'
                  ? ACTION_LABEL.ADD_INSPECTION
                  : ACTION_LABEL.ADD_POINT
              }}
            </n-button>
            <!-- <n-button type="primary" @click="add()" v-if="btnAuths.includes('新增计划')">
            <IconAdd />
            {{ ACTION_LABEL.ADD }}
          </n-button> -->
            <!-- <n-button type="primary" @click="doHandle(ACTION.EXPORT)" v-if="btnAuths.includes('导出')">
            {{ ACTION_LABEL.EXPORT }}
          </n-button> -->
          </div>
        </n-grid-item>
      </n-grid>
    </n-form>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch, defineProps } from 'vue';
import { Search } from '@element-plus/icons-vue';
import { FlFilledAdd as IconAdd } from '@kalimahapps/vue-icons';
import { ACTION, ACTION_LABEL } from '../constant';
import { trimObjNull } from '@/utils/obj.ts';
import { useRouter } from 'vue-router';
import { useStore } from '@/store';
import { IPlanType } from '@/views/task-management/type';
import { getCheckTypeList } from '../Add/fetchData';
import { getTypeCount } from '../fetchData';
// import { planStateOptions } from '@/components/table-col/planState';
import { ElMessage } from 'element-plus';
import dayjs from 'dayjs';
const activeTab = ref<any>('');
const { userInfo } = useStore();
const router = useRouter();
const planStateOptions = [
  { label: '草稿', value: 1 },
  { label: '待开始', value: 5 },
  { label: '进行中', value: 2 },
  { label: '已结束', value: 3 },
  { label: '已停用', value: 4 },
];
const emits = defineEmits(['action']);

const statistics = [
  {
    label: '检查任务完成情况',
    name: '1',
  },
  {
    label: '隐患整改情况',
    name: '2',
  },
  {
    label: '隐患整改超期预警',
    name: '3',
  },
];
const type = ref('');
const tabClick = (name: any) => {
  console.log(name, '>>>>>>name');

  // if (activeTab.value == 1) {
  //   // 获取直属下级单位
  //   getOrgUnderlingApi();
  //   // 获取卡片数据
  //   getTaskAllStatisticApi();
  //   // 刷新列表
  filterForm.value.planTypeId = name;
  doHandle(ACTION.SEARCH);
  // }
  // emits('updateTab', activeTab.value);
};

// const createTime = ref<any>();
const filterForm: any = ref(initForm());
let btnAuths = userInfo.resourceVoList
  .find((item) => item.resUrl === 'plannedManagementIndex')
  ?.childrens.filter((item) => item.resType == '2')
  .map((item) => item.resName);

const props = defineProps({
  checkRange: {
    type: String,
    default: '',
  },
});
const contList: any = ref([]);
const getType = async (type, id) => {
  const params = {
    topUnitId: userInfo.topUnitId,
    unitId: id || '',
    checkRange: type,
  };
  let res = await getTypeCount(params);

  contList.value = res.data;
  activeTab.value = res.data[0].planTypeId;
};
function initForm() {
  return {
    unitId: userInfo.unitId,
    userRoleCodes: userInfo.roleIds,
    planTypeId: '',

    // planStartDate: '',
    // planEndDate: '',
    // planTypeId: null,
    // planTypeName: null,
    planState: null,
    planName: null,
  };
}

const planTypeOptions = ref([]);

function getVal() {
  return trimObjNull(filterForm.value);
}

function clearFrom() {
  filterForm.value = {
    userRoleCodes: userInfo.roleIds,
    planTypeId: '',
    // planStartDate: '',
    // planEndDate: '',
    // planTypeId: null,
    // planTypeName: null,
    planState: null,
    planName: null,
  };
  // createTime.value = null;
}

// watch(createTime, (date) => {
//   timeChange(date);
// });

// function timeChange(date: any) {
//   if (date) {
//     filterForm.value.planStartDate = dayjs(date[0]).format('YYYY-MM-DD HH:mm:ss');
//     filterForm.value.planEndDate = dayjs(date[1]).format('YYYY-MM-DD HH:mm:ss');
//   } else {
//     filterForm.value.planStartDate = '';
//     filterForm.value.planEndDate = '';
//   }
// }
const add = () => {
  if (userInfo.unitOrgType == 1 && !userInfo.erecordUnitId)
    return ElMessage.warning('该单位尚未开通');
  router.push({
    name: 'plannedManagementAdd',
    query: { checkRange: props.checkRange },
  });
};

function doHandle(action: ACTION) {
  emits('action', {
    action: action,
    data: filterForm.value,
  });
}
onMounted(() => {
  getType(props.checkRange);
});

defineOptions({ name: 'checkTempFilterComp' });
defineExpose({ getVal, clearFrom, getType });
</script>
<style module lang="scss">
.form-content {
  // @apply w-full ;
  display: grid;
  grid-template-columns: repeat(auto-fill, 300px);
  grid-row-gap: 10px;
  grid-column-gap: 10px;
}
</style>
