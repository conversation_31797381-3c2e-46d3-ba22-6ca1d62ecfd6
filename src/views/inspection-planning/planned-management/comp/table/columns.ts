import { DataTableColumn } from 'naive-ui';
import { inspectType, planStatusList } from '../../constant';
import { h } from 'vue';
import { planState } from '@/components/table-col/planState';
import { formatDateRange } from '../../Details/utils';

export const tagBgs = ['#F39600', '#89f12b', '#00B578', '#527CFF', '#f00'];

export const cols: DataTableColumn[] = [
  // {
  //   title: '序号',
  //   key: 'center',
  //   width: 52,
  //   align: 'left',
  //   ellipsis: {
  //     tooltip: true,
  //   },
  //   render: (_: any, index: number) => {
  //     return index + 1;
  //   },
  // },
  {
    title: '计划名称',
    key: 'planName',
    align: 'left',
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '检查类型',
    key: 'planTypeName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '计划起止时间',
    key: 'startTime',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
    render(row) {
      return formatDateRange(row.planStartDate, row.planEndDate);
    },
  },
  {
    title: '计划状态',
    key: 'planState',
    align: 'left',
    render: (row) => h(planState, { row }),
  },
  {
    title: '计划频次',
    key: 'frequency',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
    render(row: any) {
      const typeObj: any = {
        '0': '时',
        '1': '日',
        '2': '周',
        '3': '月',
        '4': '季度',
        '5': '年',
        '99': '不重复',
      };
      if (row.frequencyType === '99') return '不重复';
      return `每${row.frequency}${typeObj[row.frequencyType]}`;
    },
  },
  {
    title: '创建单位',
    key: 'createUnitName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '创建人',
    key: 'createByName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '创建时间',
    key: 'createTime',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
];
