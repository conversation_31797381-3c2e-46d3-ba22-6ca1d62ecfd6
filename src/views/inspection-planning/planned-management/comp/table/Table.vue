<template>
  <n-data-table
    class="h-full com-table"
    style="height: 100%"
    remote
    striped
    :columns="columns"
    :data="tableData"
    :bordered="false"
    :flex-height="true"
    :row-key="(row: any) => row.planId"
    :loading="loading"
    :pagination="pagination"
    :render-cell="useEmptyCell"
    :scroll-x="1500"
    :row-props="rowProps"
  />
</template>

<script setup lang="ts">
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { useStore } from '@/store';
import { IObj } from '@/types';
import { ACTION, ACTION_LABEL } from '@/views/inspection-planning/planned-management/constant.ts';
import {
  getPageList,
  validateCheckTableVersionNumByPlanId,
} from '@/views/inspection-planning/planned-management/fetchData.ts';
import { ElMessageBox } from 'element-plus';
import { DataTableColumns, NButton } from 'naive-ui';
import { h, nextTick, ref, toRaw, VNode } from 'vue';
import { useRouter } from 'vue-router';
import { cols } from './columns';
const { userInfo } = useStore();

const router = useRouter();
const emits = defineEmits(['action']);

const [loading, search] = useAutoLoading(true);
const { pagination, updateTotal } = useNaivePagination(getTableData);

const props = defineProps({
  checkRange: {
    type: String,
    default: '1',
  },
});

let filterData: IObj<any> = {}; // 搜索条件

const columns = ref<DataTableColumns>([]);
const tableData = ref<any[]>([]);

const rowProps = (row: any) => {
  return {
    onContextmenu: (e: MouseEvent) => {
      // e.preventDefault();
      nextTick().then(async () => {
        try {
          await navigator.clipboard.writeText(row.planId);
          // message.success('ID已复制到剪贴板');
        } catch (err) {
          // message.error('复制失败');
        }
      });
    },
  };
};

let btnAuths = userInfo.resourceVoList
  .find((item: any) => item.resUrl === 'plannedManagementIndex')
  ?.childrens.filter((item: any) => item.resType == '2')
  .map((item: any) => item.resName);

function setColumns() {
  columns.value.push(...cols);
  // 添加操作栏 action
  columns.value.push({
    title: '操作',
    key: 'actions',
    align: 'center',
    fixed: 'right',
    width: 240,
    render(row: any) {
      return getActionBtn(row);
    },
  });
}
setColumns();
const currentTime = Date.now();
function checkTableChangeValidate(row: any) {
  return new Promise(async (resolve, reject) => {
    const typedata = row.checkRange == '3' ? { planType: 1 } : {};
    let {
      data: { versionDeletedFlag, versionNeedUpgradeFlag, checkTableStopFlag },
    }: any = await validateCheckTableVersionNumByPlanId({
      planId: row.planId,
      ...typedata,
    });
    let message;
    // 优先提示删除, 其次是停用, 其次是编辑
    if (versionNeedUpgradeFlag === 1) message = '系统检测到当前计划关联的检查表已更新，是否使用更新后的检查表';
    if (checkTableStopFlag === 1) message = '系统检测到当前计划关联的检查表已被停用，当前计划不可被启用';
    if (versionDeletedFlag === 1) message = '系统检测到当前计划关联的检查表已被删除，当前计划不可被启用';
    versionDeletedFlag || versionNeedUpgradeFlag || checkTableStopFlag
      ? ElMessageBox.confirm(message, '系统提示', {
          confirmButtonText: versionDeletedFlag || checkTableStopFlag ? '我知道了' : '确定并启用',
          cancelButtonText: '取消',
          showCancelButton: versionDeletedFlag || checkTableStopFlag ? false : true,
          type: 'warning',
        }).then(async () => {
          resolve(versionDeletedFlag || checkTableStopFlag ? false : true);
        })
      : resolve(true);
  });
}

// function checkTableChangeValidate(row: any) {
//   return new Promise(async (resolve, reject) => {
//     let {
//       data: { versionDeletedFlag, versionNeedUpgradeFlag, checkTableStopFlag },
//     }: any = await validateCheckTableVersionNumByPlanId({ planId: row.planId });

//     let message = versionDeletedFlag
//       ? '系统检测到当前计划关联的检查表已被删除，当前计划不可被启用'
//       : '系统检测到当前计划关联的检查表已更新，是否使用更新后的检查表';
//     versionDeletedFlag || versionNeedUpgradeFlag
//       ? ElMessageBox.confirm(message, '系统提示', {
//           confirmButtonText: versionDeletedFlag ? '我知道了' : '确定并启用',
//           cancelButtonText: '取消',
//           showCancelButton: !versionDeletedFlag,
//           type: 'warning',
//         }).then(async () => {
//           resolve(versionNeedUpgradeFlag ? true : false);
//         })
//       : resolve(true);
//   });
// }

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          style: {
            display: btnAuths.includes('详情') ? 'inline-block' : 'none',
          },
          size: 'small',
          type: 'primary',
          ghost: true,
          onClick: () =>
            router.push({
              name: 'plannedManagementDetails',
              params: { id: row.planId },
              query: {
                checkRange: props.checkRange,
              },
            }),
        },
        { default: () => '详情' }
      ),
    ],
    [
      h(
        NButton,
        {
          style: {
            display: btnAuths.includes('编辑') ? 'inline-block' : 'none',
          },
          size: 'small',
          type: 'primary',
          ghost: true,
          disabled: row.allowUpdate != 1 || ['2', '3'].includes(row.planState),
          onClick: () =>
            router.push({
              name: 'plannedManagementEdit',
              query: { checkRange: props.checkRange, status: row.planState },
              params: { id: row.planId },
            }),
        },
        { default: () => '编辑' }
      ),
      true, // 需求变更，去掉编辑功能
    ],
    [
      h(
        NButton,
        {
          style: {
            display: btnAuths.includes('启用') ? 'inline-block' : 'none',
          },
          size: 'small',
          type: 'warning',
          ghost: true,
          disabled: row.allowUpdate != 1 || ['3'].includes(row.planState),
          onClick: async () => {
            let flag: any = true;
            if (
              (row.planState === '1' || row.planState === '4') &&
              row.checkRange &&
              (row.checkRange == '1' || row.checkRange == '3')
            ) {
              if (row.planState === '4' && new Date(row.planEndDate).getTime() <= currentTime) {
                // ElMessageBox.confirm('当前计划已结束，不可启用');
                ElMessageBox.confirm('开始时间已过，请重新设置计划开始时间', '系统提示', {
                  confirmButtonText: '我知道了',
                  cancelButtonText: '取消',
                  showCancelButton: false,
                  type: 'warning',
                });
                return;
              } else {
                // flag = await checkTableChangeValidate(row);
              }
            }
            // flag &&
            emits('action', {
              action: row.planState === '1' || row.planState === '4' ? ACTION.START : ACTION.STOP,
              data: toRaw(row),
            });
          },
        },
        {
          default: () =>
            row.planState === '1' || row.planState === '4' ? ACTION_LABEL[ACTION.START] : ACTION_LABEL[ACTION.STOP],
        }
      ),
    ],
    [
      h(
        NButton,
        {
          style: {
            display: btnAuths.includes('删除') ? 'inline-block' : 'none',
          },
          size: 'small',
          type: 'error',
          ghost: true,
          // disabled: row.allowUpdate != 1 || ['2', '3'].includes(row.planState),
          disabled: row.allowUpdate != 1 || ['2', '3', '4'].includes(row.planState),
          onClick: () => {
            emits('action', { action: ACTION.DELETE, data: toRaw(row) });
          },
        },
        { default: () => '删除' }
      ),
    ],
  ];
  return useActionDivider(acList);
}
function getTableData() {
  const params = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    // unitId: userInfo.unitId,
    ...filterData,
  };
  search(getPageList(params)).then((res) => {
    tableData.value = res.data.rows || [];
    updateTotal(res.data.total ?? 0);
  });
}

function getTableDataWrap(data: IObj<any>) {
  filterData = data;
  pagination.page = 1;
  getTableData();
}
defineExpose({ getTableDataWrap, getTableData, pagination });
defineOptions({ name: 'myTable' });
</script>
<style lang="scss" scoped>
.com-table {
  height: calc(100% - 70px) !important;
  // border: 1px solid red;
}

.csBtn {
  position: absolute;
  left: 0;
  top: 0px;
  background-color: red;
  transform: translateX(-50%);
}
</style>
