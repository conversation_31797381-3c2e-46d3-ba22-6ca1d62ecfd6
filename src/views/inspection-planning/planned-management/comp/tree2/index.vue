<template>
  <div :class="[$style.treeInput]">
    <n-input v-model:value="pattern" placeholder="搜索" @input="patternChange">
      <template #suffix>
        <BySearch :class="$style['icon']" />
      </template>
    </n-input>
  </div>
  <div class="org">
    <span
      v-for="item in tabs"
      :key="item.value"
      :class="props.userType == item.value ? 'active_span' : ''"
      @click="changeTab(item.value)"
      >{{ item.label }}</span
    >
  </div>
  <div :class="[$style.comTree]" class="h-[calc(100%-150px)] overflow-auto">
    <n-tree
      class="tree-cont"
      :show-irrelevant-nodes="false"
      :data="treeData"
      block-line
      :override-default-node-click-behavior="override"
      :render-prefix="renderPrefix"
      :default-expanded-keys="defaultExpandedkeys"
      :default-selected-keys="defaultSelectedkeys"
      :key-field="['id', 'id', 'id'][props.userType - 1]"
      :label-field="['text', 'text', 'text'][props.userType - 1]"
      size="large"
      style="
        --n-node-content-height: 44px;
        --n-line-height: 44px;
        --n-node-border-radius: 4px;
        --n-node-color-hover: rgba(82, 124, 255, 0.1);
      "
    />
  </div>
</template>

<script lang="ts" setup>
import { BySearch } from '@kalimahapps/vue-icons';
import type { TreeOption } from 'naive-ui';
import { h, ref, watch, watchEffect } from 'vue';
import TreeParentIcon from './comp/TreeParentIcon.vue';
import TreeChildIcon from './comp/TreeChildIcon.vue';
import { useStore } from '@/store';
import { debounce } from 'lodash-es'; // 直接导入 debounce

const emits = defineEmits(['action', 'tabChange']);
const pattern = ref('');
const defaultExpandedkeys = ref<string[]>([]);
const defaultSelectedkeys = ref<string[]>([]);
const userInfo = useStore()?.userInfo;
const tabThreeName: any = userInfo.zhId == 'ycsyqt' ? '承包商' : '相关方';
const props = defineProps({
  treeData: {
    type: Array,
    required: true,
  },
  userType: {
    type: Number,
    default: 1, // 设置默认值为组织架构
  },
  // 中台子系统模块列表
  systemCodeList: {
    type: Array,
    default: () => [],
  },
  unitId: {
    type: String,
    default: '',
  },
});

const tabs = ref<any>([]);
watch(
  () => props.treeData,
  (nv) => {
    if (nv) {
      if (!props.treeData[0]) return;
      // let curKey = props.treeData[0].id;
      let curKey = props.unitId;
      console.log('props.treeData', curKey);
      let pkey = findAllParentIds(props.treeData, curKey) || '';
      defaultExpandedkeys.value = pkey.slice(1);
      defaultSelectedkeys.value.push(curKey);
    }
  },
  { immediate: true, deep: true }
);
const patternChange = debounce(() => {
  emits('tabChange', props.userType, pattern.value);
}, 500);
// 递归查找所有 parentId (包含自己)
function findAllParentIds(treeData: any[], curKey: string): string[] {
  const parentIds: string[] = [];
  function findNode(node: any, path: string[]): boolean {
    if (node.id === curKey) {
      parentIds.push(...path);
      if (node.parentId) {
        parentIds.push(node.parentId);
      }
      return true;
    }
    if (node.children && node.children.length > 0) {
      for (const child of node.children) {
        if (findNode(child, [...path, node.parentId || ''])) {
          return true;
        }
      }
    }
    return false;
  }
  for (const node of treeData) {
    if (findNode(node, [])) {
      break;
    }
  }
  return parentIds;
}

watchEffect(() => {
  let tabs1: any = [];
  const tenantCode = 'tenant_web';
  const interCode = 'inter_web';
  // 延长没有承租方
  if (userInfo.zhId != 'ycsyqt') {
    tabs1 = [
      { label: '组织架构', value: 1, unitId: '' },
      { label: '承租方', value: 3, unitId: '' },
      props.systemCodeList.includes(interCode) ? { label: tabThreeName, value: 2, unitId: '' } : null,
    ];
  } else {
    tabs1 = [
      { label: '组织架构', value: 1 },
      props.systemCodeList.includes(tenantCode) ? { label: '承租方', value: 3 } : null,
      props.systemCodeList.includes(interCode) ? { label: tabThreeName, value: 2 } : null,
    ];
  }
  tabs.value = tabs1.filter((item: any) => item);
});

function changeTab(val: any) {
  pattern.value = '';
  emits('tabChange', val, pattern.value);
}

function override({ option }: { option: TreeOption }) {
  const op: any = option;
  if (props.userType == 1) {
    localStorage.setItem('_riskUnitID', op.id);
    localStorage.setItem('_riskUnitName', op.text);
  } else {
    localStorage.setItem('_riskUnitIDTenant', op.id);
    localStorage.setItem('_riskUnitNameTenant', op.text);
  }
  emits('action', option);
}

function renderPrefix(info: { option: TreeOption; checked: boolean; selected: boolean }) {
  if (info.option.key === '0') {
    return h(TreeParentIcon);
  }
  return h(TreeChildIcon);
}

defineOptions({ name: 'ComRadioTabE' });
</script>

<style module lang="scss">
.treeInput {
  margin: 16px 12px 12px;

  .icon {
    color: #c0c4cc;
  }
}

.comTree {
  .n-tree .n-tree-node {
    padding-left: 10px !important;
  }
}
</style>
<style scoped lang="scss">
.org {
  height: 50px;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;

  span {
    height: 100%;
    flex: 1;
    line-height: 50px;
    text-align: center;
    cursor: pointer;
  }

  .active_span {
    font-size: 18px;
    font-weight: 700;
    border-bottom: 2px solid #000;
  }
}

.tree-cont {
  &.n-tree {
    .n-tree-node {
      padding-left: 20px !important;
    }
  }
}

:deep(.n-tree-node-content) {
  .n-tree-node-content__text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
</style>
