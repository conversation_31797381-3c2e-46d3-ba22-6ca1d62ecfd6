/*
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-10-12 09:24:03
 * @LastEditors: xginger <EMAIL>
 * @LastEditTime: 2025-06-02 14:15:55
 * @FilePath: /ehs-hazard/src/views/inspection-planning/planned-management/constant.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
export const enum PROVIDE_KEY {
  currentAction = 'currentAction',
}

export const enum ACTION {
  SEARCH = 'SEARCH',
  ADD = 'ADD',
  DETAIL = 'DETAIL',
  EDIT = 'EDIT',
  DELETE = 'DELETE',
  CHECK = 'CHECK',
  UN_CHECK = 'UN_CHECK',
  EXPORT = 'EXPORT',
  // STARTORSTOP = 'STARTORSTOP',
  START = 'START',
  STOP = 'STOP',
  ADD_INSPECTION = 'ADD_INSPECTION',
  ADD_POINT = 'ADD_POINT',
}

export const ACTION_LABEL: { [key in ACTION]: string } = {
  [ACTION.SEARCH]: '搜索',
  [ACTION.ADD]: '新增计划',
  [ACTION.ADD_INSPECTION]: '新增综合检查',
  [ACTION.ADD_POINT]: '新增点位巡查',
  [ACTION.DETAIL]: '详情',
  [ACTION.EDIT]: '编辑',
  [ACTION.DELETE]: '删除',
  [ACTION.CHECK]: '选择检查项',
  [ACTION.UN_CHECK]: '取消检查项',
  [ACTION.EXPORT]: '导出',
  [ACTION.START]: '启用',
  [ACTION.STOP]: '停用',
};

// 计划类型
export const tabList: { name: string; label: string }[] = [
  { name: '1', label: '综合检查计划' },
  { name: '3', label: '点位巡查计划' },
];

// 检查类型
export const inspectType: { label: string; value: string }[] = [
  // 1:督察检查;2:专项检查;3:自查检查
  {
    label: '督察检查',
    value: '1',
  },
  {
    label: '专项检查',
    value: '2',
  },
  {
    label: '自查检查',
    value: '3',
  },
];

// 	string
// 检查状态(1:草稿;2:进行中;3:已结束;4:已停用)
export const planStatusList: { label: string; value: string }[] = [
  {
    label: '草稿',
    value: '1',
  },
  {
    label: '进行中',
    value: '2',
  },
  {
    label: '已结束',
    value: '3',
  },
  {
    label: '已停用',
    value: '4',
  },
];
