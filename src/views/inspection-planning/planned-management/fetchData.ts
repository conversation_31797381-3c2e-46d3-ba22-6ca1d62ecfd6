import { ICheckTempPageRes, FromData } from './type';
import { IObj } from '@/types';
import { api } from '@/api';
import { $http } from '@tanzerfe/http';
import cookie from 'js-cookie';

// 计划列表
export function getPageList(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.sh.hazardPlan.getList);
  return $http.post<ICheckTempPageRes>(url, {
    data: { _cfg: { showTip: true }, ...query },
  });
}

// 计划启用
export function startPlan(query: { planId: string }) {
  const url = api.getUrl(api.type.hazard, api.sh.hazardPlan.startPlan, query);
  return $http.post<ICheckTempPageRes>(url, {
    data: { _cfg: { showTip: true } },
  });
}
// 计划停用
export function stopPlan(query: { planId: string }) {
  const url = api.getUrl(api.type.hazard, api.sh.hazardPlan.stopPlan, query);
  return $http.post<ICheckTempPageRes>(url, {
    data: { _cfg: { showTip: true } },
  });
}
// 计划删除
export function deletePlan(query: { planId: string }) {
  const url = api.getUrl(api.type.hazard, api.sh.hazardPlan.deletePlan, query);
  return $http.post<ICheckTempPageRes>(url, {
    data: { _cfg: { showTip: true } },
  });
}

// ​/hazardPlan​/updatePlan
// 计划详情
export function detailPlan(query: { planId: string }) {
  const url = api.getUrl(api.type.hazard, api.sh.hazardPlan.detailPlan, query);
  return $http.post<FromData>(url, { data: { _cfg: { showTip: true } } });
}

// 获取单位111
export function getAllUnit(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.public.getAllUnit);
  return $http.post<FromData>(url, {
    data: { _cfg: { showTip: true }, ...query },
  });
}
// 获取组织机构树
export function getOrgTree(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.public.getOrgTree, { ...query });
  return $http.post<any[]>(url, { data: { _cfg: { showTip: true } } });
}

export function getOrgUserList(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.public.getOrgUserPage);
  return $http.post(url, { data: { _cfg: { showTip: true }, ...query } });
}
export function getTypeCount(query: any) {
  const url = api.getUrl(api.type.hazard, api.public.getTypeCount, {
    ...query,
  });
  return $http.get<any[]>(url, {
    data: { _cfg: { showTip: true } },
  });
}
// 获取承租方树
export function getTenantTree(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.public.getTenantTree, {
    ...query,
  });
  return $http.get<any[]>(url, {
    data: { _cfg: { showTip: true } },
  });
}

// 获取承租方人员信息
export function getTenantUserList(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.public.getTenantUserPage, {
    ...query,
  });
  return $http.get<any[]>(url, { data: { _cfg: { showTip: true } } });
}
// 获取承租方人员信息
export function getTenantBuildList(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.public.getTenantTreeBuild);
  return $http.post<any[]>(url, {
    data: { _cfg: { showTip: true }, ...query },
  });
}

// 获取承租方人员信息是否展示承租方字段
export function getTenantInfoData(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.public.getTenantInfo);
  return $http.post<any[]>(url, {
    data: { _cfg: { showTip: true }, ...query },
  });
}

// 根据楼栋楼层查询区域
export function getAreaData(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.public.getAreaDataByFid);
  return $http.post<any[]>(url, {
    data: { _cfg: { showTip: true }, ...query },
  });
}

// 获取设备列表
export function getUnitBuildingInfoList(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.getUnitBuildingInfoList);
  return $http.post(url, {
    data: { _cfg: { showTip: false }, ...data },
  });
}

// 获取楼栋楼层树
export function getBuildingTreeByUnitId(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.getBuildingTreeByUnitId, data);
  return $http.post(url, {
    data: { _cfg: { showTip: false } },
  });
}

export function deviceTypeList(data: any) {
  const url = api.getUrl(api.type.hazard, api.cj.inspectMgr.hazardCheckDeviceTypeList);
  return $http.post(url, { data: { _cfg: { showTip: false }, ...data } });
}

// 获取相关方人员信息
export function getPerManagerList(query: IObj<any>) {
  const url = api.getUrl('edu-inter-server', '/perManager/pageList');
  return $http.post<any[]>(url, {
    data: { _cfg: { showTip: true }, ...query },
  });
}
// 获取相关方人员信息1
export function getPerManagerList1(query: IObj<any>) {
  // const url = api.getUrl('edu-inter-server', '/perManager/pageList');
  // return $http.post<any[]>(url, {
  //   data: { _cfg: { showTip: true }, ...query },
  // });
  const url = api.getUrl('edu-app-server', 'api/workbench/staff/queryList', {
    ...query,
  });
  return $http.get<any[]>(url, {
    data: { _cfg: { showTip: true } },
  });
}

// 检查【检查表】版本
export function validateCheckTableVersionNum(data: any) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.validateCheckTableVersionNum, data);
  return $http.post(url, { data: { _cfg: { showTip: false } } });
}

// 检查计划-校验所关联的【检查表】版本
export function validateCheckTableVersionNumByPlanId(data: any) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.validateCheckTableVersionNumByPlanId, data);
  return $http.post(url, { data: { _cfg: { showTip: false } } });
}

// 获取中台子模块列表
export function getSystemListApi() {
  // querySystemListByZhid
  const url = api.getUrl(api.type.platform, 'workbench/msg/querySystemListByZhid');
  return $http.get(url, {
    data: { _cfg: { showTip: true } },
  });
}

//agjp.tanzervas.com/aqsc/v1/api/v1/train-server/org/queryOrgTreeByTanzer?needChildUnit=1&needself=1&type=1  企业
//agjp.tanzervas.com/aqsc/v1/api/v1/train-server/org/queryOrgTreeByTanzer?needChildUnit=1&needself=1&type=2  承包商
//agjp.tanzervas.com/aqsc/v1/api/v1/train-server/org/queryOrgTreeByTanzer?needChildUnit=1&needself=1&type=3  承租方
// 选人 左侧树结构
export function queryOrgTreeByTanzerApi(query: IObj<any>) {
  const url = api.getUrl('train-server', '/org/queryOrgTreeByTanzer', {
    ...query,
  });
  return $http.post<any[]>(url, {
    data: { _cfg: { showTip: true } },
  });
}

// /hazardPlan/getProjectByUnitId
export function getProjectByUnitIdAPI(data: any) {
  const url = api.getUrl(api.type.hazard, '/hazardPlan/getProjectByUnitId', data);
  return $http.post(url, { data: { _cfg: { showTip: false } } });
}
