<template>
  <div class="flex flex-col">
    <com-bread :data="breadData"></com-bread>
    <div class="item_content notVw flex-1">
      <transition name="slide-fade">
        <UnitSelect
          v-show="formVisible"
          class="unit-tree"
          @updateVal="updateVal"
        />
      </transition>
      <div
        style="flex: 5; position: relative"
        :style="{ width: formVisible ? '62vw' : '100%' }"
      >
        <img
          @click="formVisible = !formVisible"
          v-if="userInfo.unitOrgType == '2'"
          src="@/assets/open.png"
          style="
            cursor: pointer;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            z-index: 10;
          "
          :style="{ width: toVw(30), left: `-${toVw(15)}` }"
        />
        <radio-tab
          class="!pt-[0]"
          :tab-list="tabList"
          :tab="tabType"
          @change="handleChange"
          v-if="btnAuths.includes('addPointPlan')"
        />
        <div
          class="right-table !p-[0]"
          :style="{
            height: btnAuths.includes('addPointPlan')
              ? `calc(100% - ${toVw(34)})`
              : '100%',
          }"
        >
          <Filter
            class="com-table-filter"
            style="border-radius: 5px 5px 0 0"
            @action="actionFn"
            ref="filterRef"
            :checkRange="tabType"
          />
          <TableList
            class="com-table-container !pt-[0]"
            style="border-radius: 0 0 5px 5px"
            ref="tableCompRef"
            @action="actionFn"
            :checkRange="tabType"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { api } from '@/api';
import { $dialog } from '@/common/shareContext/useDialogCtx.ts';
import ComBread from '@/components/breadcrumb/ComBread.vue';
import { IBreadData } from '@/components/breadcrumb/type.ts';
import RadioTab from '@/components/tab/ComRadioTabE.vue';
import { useStore } from '@/store';
import { IObj } from '@/types';
import { fileDownloader } from '@/utils/fileDownloader';
import { toVw } from '@/utils/fit';
import { useMessage } from 'naive-ui';
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import Filter from './comp/Filter.vue';
import TableList from './comp/table/Table.vue';
import { ACTION, tabList } from './constant';
import { deletePlan, startPlan, stopPlan } from './fetchData';
import { IActionData, ICheckTempRow } from './type';

const { userInfo } = useStore();
const currentAction = ref<IActionData>({ action: ACTION.ADD, data: {} });
const breadData: IBreadData[] = [
  { name: '隐患治理系统' },
  { name: '计划管理' },
];
const message = useMessage();
const route = useRoute();
const router = useRouter();

let btnAuths = userInfo.resourceVoList
  .find((item: any) => item.resUrl === 'plannedManagementIndex')
  ?.childrens.filter((item: any) => item.resType == '2')
  .map((item: any) => item.resUrl);

const filterRef = ref();
const tabType = ref<string>((route.query?.checkRange as string) || '1');

let filterVal: any = { unitId: userInfo.unitId, checkRange: tabType.value };

const formVisible = ref(true);
const uid = ref('');
const handleChange = (val: string) => {
  router.push({ query: { checkRange: val } });
  tabType.value = val;
  tableCompRef.value.pagination.page = 1;
  // tableCompRef.value.pagination.pageSize = 10;
  filterRef.value.getType(val, uid.value);
  handleSearch(filterVal);
};

// 组织结构切换
function updateVal(unitId: string) {
  filterRef.value?.clearFrom();
  uid.value = unitId;
  filterRef.value.getType(tabType.value, unitId);
  filterVal = { unitId, ...filterRef.value?.getVal() };
  tableCompRef.value.pagination.page = 1;
  // tableCompRef.value.pagination.pageSize = 10;
  handleSearch(filterVal);
}

function actionFn(val: IActionData) {
  currentAction.value = val;
  if (val.action === ACTION.ADD) {
    return router.push({ name: 'checklistConfModify' });
  }
  if (val.action === ACTION.SEARCH) {
    filterVal = { unitId: filterVal.unitId, ...val.data };
    return handleSearch(filterVal);
  }
  if (val.action === ACTION.DELETE) {
    return handleDelete(val.data as ICheckTempRow);
  }
  if (val.action === ACTION.START) {
    return handlStartPlan(val.data.planId);
  }
  if (val.action === ACTION.STOP) {
    return handlStopPlan(val.data.planId);
  }
  if (val.action === ACTION.EXPORT) {
    const downUrl = api.getUrl(api.type.hazard, api.sh.file.exportPlanList);
    fileDownloader(downUrl, {
      filename: '检查计划',
      method: 'POST',
      contentType: 'application/json',
      body: JSON.stringify(val.data),
    })
      .then(() => {})
      .catch(() => {});
  }
}

const tableCompRef = ref();
function handleSearch(data?: IObj<any>) {
  data = { ...data, checkRange: tabType.value };
  tableCompRef.value?.getTableDataWrap(data);
}
const handlStopPlan = async (planId: string) => {
  $dialog.warning({
    title: '提示',
    positiveButtonProps: { type: 'primary', color: '#527cff' },
    content:
      '确定要停用计划吗？停用后，将会回收已下发的任务（包括正在进行中的任务），已完成检查的任务则不会被回收。',
    positiveText: '确定',
    negativeText: '取消',
    transformOrigin: 'center',
    onPositiveClick: async () => {
      await stopPlan({ planId });
      handleSearch(filterVal);
      message.success('停用成功');
    },
  });
};
const handlStartPlan = async (planId: string) => {
  $dialog.warning({
    title: '提示',
    content: '确定启用计划，启用后相关人员将接收到任务！',
    positiveButtonProps: { type: 'primary', color: '#527cff' },
    positiveText: '确定',
    negativeText: '取消',
    transformOrigin: 'center',
    onPositiveClick: async () => {
      await startPlan({ planId });
      handleSearch(filterVal);
      message.success('启用成功');
    },
  });
};
// 删除
function handleDelete(data: ICheckTempRow) {
  $dialog.warning({
    title: '提示',
    content: '确定删除计划，删除后相关任务将被删除！',
    positiveButtonProps: { type: 'primary', color: '#527cff' },
    positiveText: '确定',
    negativeText: '取消',
    transformOrigin: 'center',
    onPositiveClick: async () => {
      await deletePlan({ planId: data.planId });
      tableCompRef.value.pagination.page = 1;
      // tableCompRef.value.pagination.pageSize = 10;
      handleSearch(filterVal);
      message.success('删除成功');
    },
  });
}
// 新增
const handelAdd = () => {
  router.push({
    name: 'plannedManagementAdd',
    query: { checkRange: tabType.value },
  });
};

defineOptions({ name: 'planned-management' });
</script>

<style lang="scss" scoped>
.right-table {
  border-top-left-radius: 0;
}
</style>
