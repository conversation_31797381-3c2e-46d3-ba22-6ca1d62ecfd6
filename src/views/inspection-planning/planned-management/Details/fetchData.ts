import { ICheckTempPageRes, ICheckItem, ICheckCategoryTree, IFormData } from '../type';
import { IObj, IPageRes } from '@/types';
import { api } from '@/api';
import { $http } from '@tanzerfe/http';

// 更新检查模板
export function updateTemplate(data: IObj<any>) {
  const url = api.getUrl(api.type.demo, api.sh.demo.updateTemplate, data);
  return $http.post<IFormData>(url, { data: { _cfg: { showTip: true, showOkTip: false }, ...data } });
}

export function getPalnTaskPageList(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.sh.planTask.getPalnTaskPageList, data);
  return $http.post<IPageRes<any>>(url, { data: { _cfg: { showTip: true, showOkTip: false }, ...data } });
}

export function exportPlanTaskList() {
  const url = api.getUrl(api.type.hazard, api.sh.planTask.exportPlanTaskList);
  return url;
}
