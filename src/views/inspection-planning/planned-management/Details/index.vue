<template>
  <div v-if="planInfo">
    <com-bread :data="breadData" />
    <RadioTab :tabList="tabOpts" :tab="curTab" @change="tabChange" />
    <component
      class="h-full"
      :class="{ 'com-table-container': curTab != 1 }"
      :is="currentComp"
      :data-list="detailList"
      :device-list="deviceList"
      :planInfo="planInfo"
    />
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, computed } from 'vue';
import ComBread from '@/components/breadcrumb/ComBread.vue';
import { IBreadData } from '@/components/breadcrumb/type.ts';
import { useRoute, useRouter } from 'vue-router';
import RadioTab from '@/components/tab/ComRadioTabE.vue';
import { planInfoLable } from './constant';
import PlanDesc from './comp/PlanDesc.vue';
import TaskExecutionStatus from './comp/TaskExecutionStatus.vue';
import EndHazardList from './comp/EndHazardList.vue';
import { pointerList } from '@/components/indoorMap/data';
import { detailPlan } from '../fetchData';

const deviceList = ref(pointerList.data);
const route = useRoute();
const router = useRouter();
const curTab = ref<number | string>(0);
const planInfo: any = ref();
const tabOpts = [
  { name: 0, label: '计划信息', comp: PlanDesc },
  { name: 1, label: '任务执行情况', comp: TaskExecutionStatus },
  { name: 2, label: '隐患清单', comp: EndHazardList },
];
const currentComp = computed(() => {
  return tabOpts.find((item) => item.name == curTab.value)?.comp;
});
const tabChange = (tab: number | string) => {
  curTab.value = tab;
  router.push({
    query: { tab },
  });
};
const breadData: IBreadData[] = [
  { name: '隐患治理系统' },
  {
    name: '计划管理',
    clickable: true,
    routeRaw: {
      name: 'plannedManagementIndex',
      query: { checkRange: route.query?.checkRange },
    },
  },
  { name: '计划详情' },
];
const emits = defineEmits(['action']);
const detailList = planInfoLable;
onMounted(async () => {
  const res = await detailPlan({ planId: route.params?.id.toString() });
  planInfo.value = res.data;
});

defineOptions({ name: 'plannedManagementDetails' });
</script>
