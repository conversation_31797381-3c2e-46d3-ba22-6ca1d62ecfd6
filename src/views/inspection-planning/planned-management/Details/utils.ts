import { useStore } from '@/store';

export const formatDateRange = (start, end) => {
  if (!start) return '';
  start = start.split(' ')[0];
  if (!end) return start;
  return `${start} ~ ${end.split(' ')[0]}`;
};

/** 判断当前用户是否是集团单位 */
export const isTopUnit = () => {
  const store = useStore();
  return store.userInfo.unitId == store.userInfo.topUnitId;
};

/** 判断当前用户是否是集团单位 */
export const getOrgType = () => {
  const store = useStore();
  return store.userInfo.unitOrgType;
};
