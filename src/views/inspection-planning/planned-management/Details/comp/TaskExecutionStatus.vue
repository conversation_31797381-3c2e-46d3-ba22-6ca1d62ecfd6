<template>
  <div class="detail-info rounded">
    <!-- 分组检查-->
    <div class="bg-[#DCE4F4] mx-[-24px] px-[24px] mt-[-24px]" v-if="tabs.length > 1">
      <n-tabs type="line" animated tab-style="height: 51px" v-model:value="filterForm.planUnitId">
        <n-tab :name="tab.name" :tab="tab.label" v-for="tab of tabs" :key="tab.name"> </n-tab>
      </n-tabs>
    </div>
    <!-- 计划频次 不重复 -->
    <TaskDetails
      isComponents
      :taskId="tableData[0]?.taskId"
      v-if="planInfo.frequencyType == '99'"
      :class="{ 'mt-[20px]': tabs.length > 1 }"
    />
    <!-- 计划频次 重复 -->
    <div v-if="planInfo.frequencyType != '99'" :class="{ 'mt-[20px]': tabs.length > 1 }">
      <div class="flex gap-[15px]">
        <div
          style="box-shadow: 0px 2px 0px 0px rgba(0, 20, 82, 0.2)"
          class="relative w-[292px] h-[98px] rounded border border-[#CED8E7]"
        >
          <img width="53" class="left-[21px] top-[22px] absolute" :src="icon" alt="" />
          <div class="absolute left-[86px] flex flex-col justify-center h-full">
            <div class="text-[#222222] text-[22px]">{{ statisticsData.allTaskNum }}</div>
            <div class="">计划任务数</div>
          </div>
        </div>
        <div
          style="box-shadow: 0px 2px 0px 0px rgba(0, 20, 82, 0.2)"
          class="relative w-[292px] h-[98px] rounded border border-[#CED8E7]"
        >
          <img width="53" class="left-[21px] top-[22px] absolute" :src="icon" alt="" />
          <div class="absolute left-[86px] flex flex-col justify-center h-full">
            <div class="text-[#222222] text-[22px]">{{ statisticsData.completeTaskNum }}</div>
            <div class="">已完成任务数</div>
          </div>
        </div>
        <div
          style="box-shadow: 0px 2px 0px 0px rgba(0, 20, 82, 0.2)"
          class="relative w-[292px] h-[98px] rounded border border-[#CED8E7]"
        >
          <img width="53" class="left-[21px] top-[22px] absolute" :src="icon" alt="" />
          <div class="absolute left-[86px] flex flex-col justify-center h-full">
            <div class="text-[#222222] text-[22px]">{{ statisticsData.noStartTaskNum }}</div>
            <div class="">未开始任务</div>
          </div>
        </div>
        <div
          style="box-shadow: 0px 2px 0px 0px rgba(0, 20, 82, 0.2)"
          class="relative w-[292px] h-[98px] rounded border border-[#CED8E7]"
        >
          <img width="53" class="left-[21px] top-[22px] absolute" :src="icon" alt="" />
          <div class="absolute left-[86px] flex flex-col justify-center h-full">
            <div class="text-[#222222] text-[22px]">{{ statisticsData.timeLimitOutNum }}</div>
            <div class="">已逾期任务</div>
          </div>
        </div>
      </div>
      <el-row :gutter="15" class="mt-[25px]">
        <el-col :span="6">
          <el-form-item label="检查表名称:">
            {{ planInfo?.checkTableName }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="检查频次:">
            {{ frequencyText(planInfo) }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="初始排查日期:">
            {{ planInfo.planStartDate || '--' }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="计划起止时间:">
            {{ formatDateRange(planInfo?.planStartDate, planInfo?.planEndDate) }}
          </el-form-item>
        </el-col>
        <el-col :span="6" v-if="planInfo.checkRange != '2' && isTopUnit() && filterForm.planUnitId">
          <el-form-item label="检查对象:">
            {{ !filterForm.planUnitId ? getAllCheckUserName() : getCheckUserName() }}
          </el-form-item>
        </el-col>
      </el-row>
      <n-form :show-feedback="false" label-placement="left">
        <n-grid :x-gap="12" :y-gap="8" :cols="4">
          <n-grid-item v-if="planInfo.checkRange != '2' && isTopUnit()">
            <n-form-item label="检查对象:">
              <n-select
                v-model:value="filterForm.planUnitId"
                placeholder="请选择"
                :options="unitOptions"
                clearable
                class="flex items-center"
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="任务状态:">
              <n-select
                v-model:value="filterForm.taskState"
                :options="taskStateOptionslist"
                clearable
                class="flex items-center"
              />
            </n-form-item>
          </n-grid-item>

          <n-grid-item>
            <n-form-item label="任务时效:">
              <n-select
                v-model:value="filterForm.timeState"
                placeholder="请选择"
                :options="options"
                clearable
                class="flex items-center"
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item v-if="!(tabs.length && filterForm.planUnitId) && planInfo.checkRange != '2' && isTopUnit()">
            <n-form-item>
              <n-input
                placeholder="请输入检查对象模糊搜索"
                v-model:value="filterForm.keyWords"
                clearable
                class="flex items-center"
              >
                <template #suffix>
                  <BsSearch />
                </template>
              </n-input>
            </n-form-item>
          </n-grid-item>
          <n-grid-item
            :span="
              tabs.length < 2 || (!(tabs.length && filterForm.planUnitId) && planInfo.checkRange != '2' && isTopUnit())
                ? 4
                : 1
            "
          >
            <n-form-item>
              <div class="flex justify-end w-full">
                <n-button type="primary" @click="exportEventListFn"> 导出 </n-button>
              </div>
            </n-form-item>
          </n-grid-item>
        </n-grid>
      </n-form>
      <n-data-table
        class="com-table mt-[20px]"
        remote
        striped
        :columns="columns"
        :data="tableData"
        :bordered="false"
        :loading="loading"
        :pagination="pagination"
        :render-cell="useEmptyCell"
        :row-props="rowProps"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { getPlanTaskCount } from '@/views/paramsConf/comp/fetchData';
import TaskDetails from '@/views/task-management/TaskDetails.vue';
import { NButton, SelectOption } from 'naive-ui';
import { computed, h, nextTick, onMounted, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import { ITaskExecutionStatusStatisticsData } from '../../type';
import icon from './icon.png';
// import { taskStateOptions } from '@/views/task-management/type';
import { useAutoLoading } from '@/common/hooks/useAutoLoading';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination';
import { taskState } from '@/components/table-col/taskState';
import { fileDownloader } from '@/utils/fileDownloader';
import { ITaskState } from '@/views/task-management/type';
import { useRoute } from 'vue-router';
import { detailPlan } from '../../fetchData';
import { exportPlanTaskList, getPalnTaskPageList } from '../fetchData';
import { formatDateRange, isTopUnit } from '../utils';
import { frequencyText } from './hazard-list-table/columns';
const router = useRouter();
const [loading, search] = useAutoLoading(true);
const route = useRoute();
const taskStateOptions = [
  { label: ITaskState[1], value: 1 },
  { label: ITaskState[2], value: 2 },
  { label: ITaskState[3], value: 3 },
  { label: ITaskState[4], value: 3 },
];
interface Props {
  planInfo: any;
}
interface IFilterData {
  planUnitId: string | null;
  taskState: string | null;
  keyWords: string | null;
  timeState: string | null;
}

const rowProps = (row: any) => {
  return {
    onContextmenu: (e: MouseEvent) => {
      // e.preventDefault();
      nextTick().then(async () => {
        try {
          await navigator.clipboard.writeText(row.taskId);
          // message.success('ID已复制到剪贴板');
        } catch (err) {
          // message.error('复制失败');
        }
      });
    },
  };
};

const props = withDefaults(defineProps<Props>(), {});
const columns = computed(() => {
  const result = [];
  if (isTopUnit())
    result.push({
      title: '检查对象',
      key: 'unitlist',
      align: 'left',
      ellipsis: {
        tooltip: true,
      },
      render: (row: any) => row.unitlist.map((item: any) => item.unitName).join(','),
    });
  result.push(
    {
      title: '任务周期',
      align: 'left',
      key: '',
      ellipsis: {
        tooltip: true,
      },
      render(row: any) {
        // 时 展示时分秒
        if (row.frequencyType == '0') {
          return `${row.planStartTime} ~ ${row.planEndTime}`;
        } else {
          return formatDateRange(row.planStartTime, row.planEndTime);
        }
      },
    },

    {
      title: '任务开始时间',
      key: 'beginTime',
      align: 'left',
      ellipsis: {
        tooltip: true,
      },
      render: (row: any) => row.beginTime || '--',
    },
    {
      title: '任务完成时间',
      key: 'finishTime',
      align: 'left',
      ellipsis: {
        tooltip: true,
      },
      render: (row: any) => row.finishTime || '--',
    },
    {
      title: '任务状态',
      key: 'taskState',
      align: 'left',
      ellipsis: {
        tooltip: true,
      },
      render: (row: any) => h(taskState, { row }),
    },
    {
      title: '任务时效',
      key: 'timeState',
      align: 'left',
      ellipsis: {
        tooltip: true,
      },
      render: (row: any) => row.timeState && (row.timeState == '1' ? '正常' : '逾期'),
    },
    {
      title: '操作',
      key: 'actions',
      align: 'left',
      fixed: 'right',
      ellipsis: {
        tooltip: true,
      },
      render(row: any) {
        return h(
          NButton,
          {
            size: 'small',
            type: 'primary',
            ghost: true,
            class: 'com-action-button',
            onClick: () => router.push({ name: 'hazardTaskDetail', params: { id: row.taskId as string } }),
          },
          { default: () => '详情' }
        );
      },
    }
  );
  return result;
});

const taskStateOptionslist = [
  { label: ITaskState[1], value: 1 },
  { label: ITaskState[2], value: 2 },
  { label: ITaskState[3], value: 3 },
  // { label: ITaskState[4], value: 4 },
];

const options = [
  { label: '正常', value: 1 },
  { label: '逾期', value: 2 },
];

const statisticsData = ref<ITaskExecutionStatusStatisticsData>({
  allTaskNum: 0,
  completeTaskNum: 0,
  inProgressTaskNum: 0,
  noStartTaskNum: 0,
  timeLimitOutNum: 0,
});
const tableData = ref<any[]>([]);

const filterForm = ref<IFilterData>({
  planUnitId: null,
  taskState: null,
  keyWords: null,
  timeState: null,
});
const unitOptions = ref<SelectOption[]>([]);

defineOptions({ name: 'planned-management' });

const getTableData = () => {
  const params = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    planId: route.params.id,
    ...filterForm.value,
  };

  search(getPalnTaskPageList(params)).then((res) => {
    tableData.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
};
const { pagination, updateTotal } = useNaivePagination(getTableData);

const getData = () => {
  // 计划频次不重复不执行查询
  getPlanTaskCount({ planId: route.params.id, planUnitId: filterForm.value.planUnitId }).then((res) => {
    statisticsData.value = res.data;
  });
};

const getTableDataWrap = () => {
  pagination.page = 1;
  getTableData();
};

const getFilterData = async () => {
  detailPlan({ planId: route.params?.id.toString() }).then((res: any) => {
    unitOptions.value = res.data.unitlist.map((item: any) => ({ label: item.unitName, value: item.unitId }));
  });
};

const exportEventListFn = async () => {
  // let body = {
  //   ...filterForm.value,
  //   planId: route.params.id,
  //   // exportfields: [
  //   //   { key: 'eventFileUrlStr1', title: '隐患图片1' },
  //   //   { key: 'eventFileUrlStr2', title: '隐患图片2' },
  //   //   { key: 'eventFileUrlStr3', title: '隐患图片3' },
  //   //   { key: 'dealFileUrlStr1', title: '验收图片1' },
  //   //   { key: 'dealFileUrlStr2', title: '验收图片2' },
  //   //   { key: 'dealFileUrlStr3', title: '验收图片3' },
  //   // ],
  //   fileName: '隐患清单',
  // };
  // body = JSON.stringify(body);
  // console.log(body);
  // fileDownloader(exportPlanTaskList(), {
  //   filename: '隐患清单-' + new Date().getTime(),
  //   method: 'POST',
  //   contentType: 'application/json',
  //   body,
  // });
  fileDownloader(exportPlanTaskList(), {
    filename: '任务执行情况',
    method: 'POST',
    contentType: 'application/json',
    body: JSON.stringify({ ...filterForm.value, planId: route.params.id }),
  });
};
const getAllCheckUserName = () => {
  let result = [];
  for (let i = 0; i < props?.planInfo?.unitlist.length; i++) {
    const item = props.planInfo.unitlist[i];
    result.push(item.userlist.map((itm: any) => itm.checkUserName).join('、'));
  }
  return result.join('、');
};

const getCheckUserName = () => {
  let unitlist = props.planInfo.unitlist;
  let unitId = filterForm.value.planUnitId;
  let idx = unitlist.findIndex((item: any) => item.unitId == unitId);
  return idx > -1 ? unitlist[idx].userlist.map((item: any) => item.checkUserName).join('、') : '';
};

// 分组的情况会有tab展示
const tabs = computed(() => {
  return props.planInfo.planUnitGroupStatus == '2'
    ? props.planInfo.unitlist.map((item: any) => ({ name: item.unitId, label: item.unitName }))
    : [];
});

watch(filterForm.value, () => {
  getTableDataWrap();
  getData();
});

onMounted(() => {
  getData();
  getFilterData();
  getTableData();
  if (props.planInfo.planUnitGroupStatus == '2') filterForm.value.planUnitId = tabs.value[0].name;
});
</script>

<style lang="scss">
.tab {
  height: 50px;
}

.detail-info {
  height: 83vh;
  overflow-y: scroll;
  padding: 20px;
  border-top-left-radius: 0;
  background-color: rgb(238 247 255 / var(--tw-bg-opacity, 1));
}
</style>
