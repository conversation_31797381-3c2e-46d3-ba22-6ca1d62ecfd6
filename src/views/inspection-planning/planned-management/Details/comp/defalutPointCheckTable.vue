<template>
  <n-modal
    :show="show"
    :show-icon="false"
    preset="card"
    class="models"
    :mask-closable="false"
    :on-close="closeDialog"
    style="width: 1200px"
    :autoFocus="false"
  >
    <template #header>
      <div class="flex flex-row items-center">
        <img class="w-[17px] h-[12px] mr-[20px]" src="@/components/header/assets/icon-title-arrow3.png" />
        <div class="text-[16px] text-[#222222] font-bold">默认巡查点位详情</div>
      </div>
    </template>
    <template #default>
      <div class="p-[24px]">
        <n-data-table
          class="h-[500px]"
          flex-height
          :columns="columns"
          :data="tableData"
          :loading="loading"
          :bordered="true"
          :single-line="false"
        ></n-data-table>
      </div>
    </template>
  </n-modal>
</template>
<script setup lang="ts">
import { ref, reactive, onBeforeMount, onMounted, watch, inject, h } from 'vue';
import { getDefaultConfigList } from '@/views/paramsConf/comp/fetchData';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
const [loading, search] = useAutoLoading(true);

import { useStore } from '@/store';
const { userInfo } = useStore();

const cancel = inject('closeDetail');
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  row: {
    type: Object,
    default: () => {},
  },
});

let columns: any = [
  {
    title: '序号',
    key: 'index',
    width: 80,
    align: 'center',
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '检查内容',
    key: 'checkContent',
  },
];

const closeDialog = () => {
  tableData.value = [];
  cancel();
};

const tableData: any = ref([]);

const getTableData = async () => {
  let data = {
    pageNo: 1,
    pageSize: 9999,
    checkTableType: 'point',
    unitId: userInfo.topUnitId,
  };
  const res: any = await search(getDefaultConfigList(data));
  if (res.code === 'success') {
    tableData.value = res.data.rows;
  }
};

watch(
  () => props.show,
  (newVal) => {
    if (newVal) {
      getTableData();
    }
  }
);
</script>

<style lang="scss" scoped>
.header {
  position: relative;

  &::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 18px;
    background-color: #1890ff;
    position: absolute;
    top: 5px;
    left: -10%;
  }
}

:deep(.parent td) {
  background-color: rgba(215, 241, 253, 1) !important;
}
</style>
