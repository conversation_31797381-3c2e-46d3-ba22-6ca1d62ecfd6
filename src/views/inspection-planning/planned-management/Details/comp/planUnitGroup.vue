<template>
  <div class="relative mt-[12px]">
    <ComHeaderB title="分组检查信息" :activeIcon="true"></ComHeaderB>
    <!-- 2025/01/06 更换tab样式 -->
    <div class="pb-[12px] sysTabs">
      <n-tabs type="line" animated>
        <n-tab-pane
          v-for="(item, index) of unitlist"
          :key="index"
          :name="`检查组${index + 1}`"
          :tab="`检查组${index + 1}`"
        >
          <div class="unit w-full rounded px-4">
            <div class="flex flex-row py-[12px]">
              <div class="mr-[12px] w-[80px]">检查对象:</div>
              <div>{{ item.unitName }}</div>
              <div class="ml-[12px] mr-[12px] w-[100px]">关联施工项目:</div>
              <div>{{ item.projectName }}</div>
            </div>
            <div class="flex flex-row py-[12px]">
              <div class="mr-[12px] w-[80px]">检查人员:</div>
              <n-data-table
                class="h-[300px] w-[500px] com-table mt-2"
                flex-height
                remote
                stripeds
                :columns="columns"
                :data="item.userlist"
                :bordered="false"
                :style="{ height: `250px` }"
              />
            </div>
            <div class="flex flex-row py-[12px]" v-if="item.checkPointlist?.length">
              <div class="mr-[12px] w-[80px]">巡查点位:</div>
              <n-data-table
                class="h-[300px] w-[500px] com-table mt-2"
                flex-height
                remote
                striped
                :columns="checkPointColumns"
                :data="item.checkPointlist"
                :bordered="false"
              />
            </div>
          </div>
        </n-tab-pane>
      </n-tabs>
    </div>
    <!-- <n-scrollbar x-scrollable class="!w-[1580px]" style="height: calc(100% - 36px)">
      <div class="flex gap-4 mt-[20px]">
        <div
          class="unit w-[450px] border border-white bg-white rounded p-4"
          v-for="(item, index) of unitlist"
          :key="index"
        >
          <div class="flex items-center">
            <div class="text-[16px] font-semibold">检查组{{ index + 1 }}</div>
          </div>
          <div class="mt-2">检查对象: {{ item.unitName }}</div>
          <div class="mt-2">检查人员</div>
          <n-data-table
            class="h-[300px] w-[500px] com-table mt-2"
            flex-height
            remote
            stripeds
            :columns="columns"
            :data="item.userlist"
            :bordered="false"
            :style="{ height: `250px` }"
          />
          <div v-if="item.checkPointlist?.length">
            <div class="mt-2">巡查点位</div>
            <n-data-table
              class="h-[300px] w-[500px] com-table mt-2"
              flex-height
              remote
              striped
              :columns="checkPointColumns"
              :data="item.checkPointlist"
              :bordered="false"
            />
          </div>
        </div>
      </div>
    </n-scrollbar> -->
    <!-- 检查表详情 -->
    <CheckTableDetail :show="showCheckTable" :row="checkDataDetail" @close="showCheckTable = false" />
    <!-- 默认点位检查项 -->
    <defalutPointCheckTable v-model:show="showDefaultPointCheckTable" />
  </div>
</template>

<script setup lang="ts">
import { ref, h, provide, watch } from 'vue';
import ComHeaderB from '@/components/header/ComHeaderB.vue';
import CheckTableDetail from '@/views/inspect-mgr/inspect/comp/CheckTableDetail.vue';
import defalutPointCheckTable from '@/views/inspection-planning/planned-management/Details/comp/defalutPointCheckTable.vue';
import { getCheckTableDetail } from '@/views/inspection_point/fetchData';
import { NTooltip } from 'naive-ui';

interface Props {
  unitlist: any[];
}
const props = withDefaults(defineProps<Props>(), {});

const tabsCurrent = ref(0);

watch(
  () => props.unitlist,
  (newValue, oldValue) => {
    console.log(props.unitlist, '======props.unitlist');
    if (newValue) {
      tabsCurrent.value = newValue[0].unitId;
    }
  },
  {
    deep: true,
  }
);

const columns = [
  {
    title: '序号',
    key: 'index',
    render: (row: any, index: number) => index + 1,
  },
  {
    title: '检查人姓名',
    key: 'checkUserName',
  },
  {
    title: '单位',
    key: 'userUnitName',
  },
  {
    title: '检查人类型',
    key: 'checkUserType',
    render: (row: any) => ['', '检查负责人', '检查参与人'][row.checkUserType],
  },
];

const checkPointColumns = [
  {
    title: '序号',
    key: 'index',
    render: (row: any, index: number) => index + 1,
  },
  {
    title: '巡查点位',
    key: 'pointFullName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '检查表',
    key: 'checkTableName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
    render(row: any) {
      return [
        h(
          NTooltip,
          {},
          {
            trigger: () =>
              h(
                'span',
                {
                  style: {
                    color: '#1890ff',
                    cursor: 'pointer',
                  },
                  onClick: () => {
                    // 默认巡查点位检查项
                    if (!row.checkTableId) showDefaultPointCheckTable.value = true;
                    if (row.checkTableId) checkTableDetail(row.checkTableId);
                  },
                },
                row.checkTableName
                  ? row.checkTableName.length > 10
                    ? row.checkTableName.slice(0, 10) + '...'
                    : row.checkTableName
                  : '--'
              ),
            default: () => row.checkTableName || '-',
          }
        ),
      ];
    },
  },
];
const showDefaultPointCheckTable = ref(false);
const showCheckTable = ref(false);
const checkDataDetail = ref<any>({});

const checkTableClose = () => {
  showCheckTable.value = false;
  showDefaultPointCheckTable.value = false;
  checkDataDetail.value = {};
};

provide('closeDetail', checkTableClose);

const checkTableDetail = (id: string) => {
  getCheckTableDetail(id).then((res) => {
    checkDataDetail.value = res.data;
    showCheckTable.value = true;
  });
};
</script>
<style lang="scss" scoped>
.sysTabs ::v-deep .n-tabs .n-tabs-nav {
  line-height: 1.3;
  display: flex;
  position: absolute;
  top: 0px;
  left: 200px;
}
</style>
