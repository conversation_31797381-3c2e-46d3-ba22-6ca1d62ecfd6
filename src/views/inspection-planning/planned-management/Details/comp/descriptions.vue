<template>
  <Header :title="title" :activeIcon="true"></Header>
  <el-row class="py-[.625rem]">
    <el-col class="mr-[24px]" :span="item.span || 8" v-for="item in dataList" :key="item.label">
      <el-form-item :label="item.label + ':'" label-position="left">
        <div v-if="item.render">{{ item.render(planInfo) }}</div>
        <div class="w-full h-300px relative" v-else-if="item.type === 'checktype'">
          <span v-if="planInfo?.checkExecuteMethod">
            {{ planInfo?.checkExecuteMethod == '1' ? '不需要逐一执行每个检查项' : '需要逐一执行每个检查项' }}
          </span>
          <span v-else>--</span>
        </div>
        <div class="w-full h-300px relative" v-else-if="item.type === 'checkPointTable'">
          <n-data-table
            v-if="planInfo?.unitlist[0]?.checkPointlist"
            class="w-[500px] com-table"
            :min-height="300"
            :max-height="300"
            flex-height
            remote
            striped
            :columns="checkPointTableColumns"
            :data="planInfo?.unitlist[0]?.checkPointlist"
            :bordered="false"
          />
          <span v-else>--</span>
        </div>
        <div class="w-full h-300px relative" v-else-if="item.type === 'checkTable'">
          <n-data-table
            v-if="planInfo?.unitlist[0]?.userlist"
            class="w-[500px] com-table"
            :min-height="300"
            :max-height="300"
            flex-height
            remote
            striped
            :columns="item.columns"
            :data="planInfo?.unitlist[0]?.userlist"
            :bordered="false"
          />
          <span v-else>--</span>
        </div>
        <div class="w-full h-300px relative" v-else-if="item.type === 'table'">
          <n-data-table
            v-if="planInfo && planInfo[item.key]"
            class="w-[500px] com-table"
            remote
            striped
            :columns="item.columns"
            :data="planInfo ? planInfo[item.key] : []"
            :bordered="false"
            :style="{ height: `mt-[16px]` }"
            :min-height="200"
            :max-height="200"
            virtual-scroll
          />
          <span v-else>--</span>
        </div>
        <div v-else-if="item.type === 'file'">
          <div
            class="cursor-pointer text-[#5278EF] mb-[10px] flex items-center gap-4"
            v-for="n in planInfo ? planInfo[item.key] : []"
            :key="n.id"
          >
            <span>{{ n.fileName }}</span>
            <img
              :src="download"
              alt=""
              class="flex-shrink-0 w-[14px] h-[14px] cursor-pointer"
              @click="downloadFile(n)"
            />
          </div>
        </div>
        <div v-else-if="item.key == 'checkTableName'" class="flex gap-[8px] w-full">
          <span class="look-detail" @click="checkTableDetail(planInfo?.checkTableId)">
            {{ planInfo?.checkTableName }}
          </span>
          <el-button
            v-if="planInfo?.checkTableName"
            type="primary"
            plain
            size="small"
            @click="checkTableDetail(planInfo?.checkTableId)"
          >
            查看
          </el-button>
        </div>
        <!-- 1:草稿;2:进行中;3:已结束;4:已停用 -->
        <div v-else-if="item.key == 'planState'" class="flex justify-between w-full">
          <span>
            {{
              planInfo?.planState === '1'
                ? '草稿'
                : planInfo?.planState === '2'
                  ? '进行中'
                  : planInfo?.planState === '3'
                    ? '已结束'
                    : planInfo?.planState === '4'
                      ? '已停用'
                      : planInfo?.planState === '5'
                        ? '待开始'
                        : '--'
            }}
          </span>
        </div>
        <div class="" v-else>
          {{
            planInfo
              ? item.key2
                ? formatDateRange(planInfo[item.key], planInfo[item.key2])
                : planInfo[item.key] || '--'
              : '--'
          }}
        </div>
      </el-form-item>
    </el-col>
  </el-row>
  <!-- 检查表详情 -->
  <CheckTableDetail :show="showCheckTable" :row="checkDataDetail" @close="showCheckTable = false" />
  <!-- 默认点位检查项 -->
  <defalutPointCheckTable v-model:show="showDefaultPointCheckTable" />
</template>
<script lang="ts" setup>
import { ref, provide, h } from 'vue';
import { NTooltip } from 'naive-ui';
import { FromData } from '../../type';
import CheckTableDetail from '@/views/inspect-mgr/inspect/comp/CheckTableDetail.vue';
import defalutPointCheckTable from '@/views/inspection-planning/planned-management/Details/comp/defalutPointCheckTable.vue';
import { getCheckTableDetail } from '@/views/inspection_point/fetchData';
import { formatDateRange } from '../utils';
import getFileURL from '@/utils/getFileURL';
import download from '@/assets/download.png';
import Header from '@/components/header/ComHeaderB.vue';
interface ListItem {
  label: string;
  key: string;
  type?: string;
  span?: number | string;
  key2?: string;
  columns?: any[];
  [prop: string]: any;
}

interface Props {
  title: string;
  planInfo: FromData;
  dataList: ListItem[];
}

const props = defineProps<Props>();
console.log('props.dataList========', props.dataList);
const downloadFile = async (item: { fileUrl: string; fileName: string; fileType: string }) => {
  let downUrl = getFileURL(item.fileUrl);
  try {
    const response = await fetch(downUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/octet-stream',
      },
    });

    if (!response.ok) {
      throw new Error('Network response was not ok');
    }

    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${item.fileName}.${item.fileType}`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('There has been a problem with your fetch operation:', error);
  }
};

// 检查表
const showCheckTable = ref(false);
const checkDataDetail = ref<any>({});
// 默认点位
const showDefaultPointCheckTable = ref(false);
const checkTableClose = () => {
  showCheckTable.value = false;
  showDefaultPointCheckTable.value = false;
  checkDataDetail.value = {};
};

provide('closeDetail', checkTableClose);

const checkTableDetail = (id: string) => {
  getCheckTableDetail(id).then((res) => {
    checkDataDetail.value = res.data;
    showCheckTable.value = true;
  });
};

const checkPointTableColumns = [
  {
    title: '序号',
    key: 'index',
    width: 65,
    align: 'center',
    render: (row: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '巡查点位',
    key: 'pointFullName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '检查表',
    key: 'checkTableName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
    render(row: any) {
      return [
        h(
          NTooltip,
          {},
          {
            trigger: () =>
              h(
                'span',
                {
                  style: {
                    color: '#1890ff',
                    cursor: 'pointer',
                  },
                  onClick: () => {
                    // 默认巡查点位检查项
                    if (!row.checkTableId) showDefaultPointCheckTable.value = true;
                    if (row.checkTableId) checkTableDetail(row.checkTableId);
                  },
                },
                row.checkTableName
                  ? row.checkTableName.length > 20
                    ? row.checkTableName.slice(0, 20) + '...'
                    : row.checkTableName
                  : '--'
              ),
            default: () => row.checkTableName || '-',
          }
        ),
      ];
    },
  },
];

defineOptions({ name: 'DescriptionsM' });
</script>
<style lang="scss" scoped>
.look-detail {
  cursor: pointer;
  color: #527cff;
}
</style>
