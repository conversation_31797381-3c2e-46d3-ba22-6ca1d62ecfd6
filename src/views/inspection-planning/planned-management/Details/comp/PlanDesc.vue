<template>
  <div class="detail-info rounded">
    <descriptions title="计划基本信息" :dataList="planInfoLableData" :planInfo="planInfo" />
    <descriptions
      :title="planInfo?.checkRange == '1' ? '检查信息' : '检查对象和巡查点位'"
      v-if="planInfo?.planUnitGroupStatus != 2"
      :dataList="ChecklistLableData"
      :planInfo="planInfo"
    />
    <planUnitGroup v-if="planInfo?.planUnitGroupStatus == 2" :unitlist="planInfo.unitlist" />
    <descriptions title="其他信息" :dataList="CheckOtherLableData" :planInfo="planInfo" />
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue';
import { detailPlan } from '../../fetchData';
import { useRoute } from 'vue-router';
import descriptions from './descriptions.vue';
import { planInfoLable, ChecklistLable, ontherInfoTableLable } from '../constant';
import { useStore } from '@/store';
import planUnitGroup from './planUnitGroup.vue';
import { getCheckSettingList } from '@/views/paramsConf/comp/fetchData';

const { unitId } = useStore()?.userInfo;
const route = useRoute();
const planInfo = ref();
const typeObj: any = {
  '0': '时',
  '1': '日',
  '2': '周',
  '3': '月',
  '4': '季度',
  '5': '年',
  '99': '不重复',
};
const CheckOtherLableData = computed(() => {
  return ontherInfoTableLable(planInfo.value || {}, checkSettingData.value);
});
const ChecklistLableData = computed(() => {
  return ChecklistLable(planInfo.value || {}, checkSettingData.value);
});
const planInfoLableData = computed(() => planInfoLable(planInfo.value || {}));
const checkSettingData = ref<any>([]);

onMounted(async () => {
  const { data } = await detailPlan({ planId: route.params?.id.toString() });
  let { checkEndOperate, frequencyType, frequency, isNeedClock } = data;
  data.checkEndOperateStr =
    checkEndOperate === '1' ? '只有检查负责人可以结束检查任务' : '检查负责人和参与人都可以结束检查任务';
  data.frequencyStr = frequencyType === '99' ? '不重复' : `每${frequency}${typeObj[frequencyType]}`;
  data.isNeedClock = isNeedClock ? (isNeedClock == 1 ? '是' : '否') : '--';
  planInfo.value = data;
  const response: any = await getCheckSettingList({
    planTypeName: planInfo.value.planTypeName,
    unitId: unitId,
    planTypeStatus: '',
  });
  checkSettingData.value = response.data[0] || [];
});
</script>

<style scoped>
.detail-info {
  height: 83vh;
  overflow-y: scroll;
  padding: 1.25rem;
  background-color: rgb(238 247 255 / var(--tw-bg-opacity, 1));
}
</style>
