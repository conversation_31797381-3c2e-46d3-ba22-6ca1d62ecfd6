<template>
  <div class="detail-info rounded">
    <div class="bg-[#DCE4F4] mx-[-24px] px-[24px] mt-[-24px] relative" v-if="tabList.length > 1">
      <n-tabs type="line" animated tab-style="height: 51px" v-model:value="filterForm.unitId" @update:value="changeTab">
        <n-tab :name="tab.name" :tab="tab.label" v-for="tab of tabList" :key="tab.name"> </n-tab>
      </n-tabs>
    </div>
    <span style="color: #f3a33c">*隐患清单中，为检查任务结束后生成的正式隐患数据</span>
    <div class="flex justify-start items-center" :class="{ 'mt-[20px]': tabList.length > 1 }">
      <div class="trouble-number flex justify-start items-center">
        <img src="src/views/hazard-management/comp/icon1.png" alt="" />
        <div class="ml-3">
          <div>隐患数量</div>
          <n-statistic tabular-nums style="--n-value-text-color: #ff9500">
            <n-number-animation :from="0" :to="statisticsData?.total" />
          </n-statistic>
        </div>
      </div>
      <div class="flex justify-start items-center other-card ml-5 bg1">
        <div style="z-index: 2">
          <div>已整改</div>
          <n-statistic tabular-nums style="--n-value-text-color: #00b578">
            <n-number-animation :from="0" :to="statisticsData?.disposedNum" />
          </n-statistic>
        </div>
      </div>
      <div class="flex justify-start items-center other-card ml-5 bg1">
        <div style="z-index: 2">
          <div>整改中</div>
          <n-statistic tabular-nums style="--n-value-text-color: #00b578">
            <n-number-animation :from="0" :to="statisticsData?.disposingNum" />
          </n-statistic>
        </div>
      </div>
      <div class="flex justify-start items-center other-card ml-5 bg1">
        <div style="z-index: 2">
          <div>待整改</div>
          <n-statistic tabular-nums style="--n-value-text-color: #fa5151">
            <n-number-animation :from="0" :to="statisticsData._num" />
          </n-statistic>
        </div>
      </div>
      <div class="flex justify-start items-center other-card ml-5 bg1">
        <div style="z-index: 2">
          <div>超期待整改数</div>
          <n-statistic tabular-nums style="--n-value-text-color: #ff9500">
            <n-number-animation :from="0" :to="statisticsData.timeoutUnDisposeNum" />
          </n-statistic>
        </div>
      </div>
      <div class="flex justify-start items-center other-card ml-5 bg1">
        <div style="z-index: 2">
          <div>超期整改中数</div>
          <n-statistic tabular-nums style="--n-value-text-color: #ff9500">
            <n-number-animation :from="0" :to="statisticsData.timeoutDisposingNum" />
          </n-statistic>
        </div>
      </div>
      <!-- <div class="other-card ml-5 bg3 flex-1" v-if="levelStatisticsData && levelStatisticsData.length">
        <div style="z-index: 2; position: relative; top: -6px" class="flex justify-start items-center">
          <div v-for="(item, idx) in levelStatisticsData" :key="idx" style="width: 25%">
            <div :title="item.hazardLevelName.length > 4 ? item.hazardLevelName : ''">
              {{ item.hazardLevelName.length > 4 ? `${item.hazardLevelName.slice(0, 4)}...` : item.hazardLevelName }}
            </div>
            <n-statistic tabular-nums :style="{ '--n-value-text-color': colorList[idx] }">
              <n-number-animation :from="0" :to="item.total" />
            </n-statistic>
          </div>
        </div>
      </div> -->
    </div>
    <el-row :gutter="15" class="mt-[25px]">
      <el-col :span="8" v-if="planInfo?.checkTableName">
        <el-form-item label="检查表名称:">
          {{ planInfo?.checkTableName }}
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="检查频次:">
          <frequencyType :row="planInfo" />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="初始排查日期:">{{ planInfo?.planStartDate }}</el-form-item>
      </el-col>
      <!-- <el-col :span="8">
        <el-form-item label="计划起止时间:">{{ formatDateRange(planInfo?.planStartDate, planInfo?.planEndDate)
          }}</el-form-item>
      </el-col> -->
      <el-col
        :span="8"
        v-if="userInfo.unitId == userInfo.topUnitId && planInfo.planUnitGroupStatus == '2' && filterForm.unitId"
      >
        <el-form-item label="检查对象:">{{
          tabList.find((item) => item.name == filterForm.unitId)?.label
        }}</el-form-item>
      </el-col>
      <el-col
        :span="8"
        v-if="userInfo.unitId == userInfo.topUnitId && planInfo.planUnitGroupStatus == '2' && filterForm.unitId"
      >
        <el-form-item label="检查人:">{{ getCheckUserName() }}</el-form-item>
      </el-col>
    </el-row>
    <Filter
      :value="filterForm"
      :planInfo="planInfo"
      ref="filterRef"
      @action="actionFn"
      :disabled="tabList.length > 1"
    />
    <TableList class="mt-[25px]" ref="tableListRef" @action="actionFn" />
    <HazardDetails ref="hazardDetailsRef" :showMap="false" />
  </div>
</template>

<script setup lang="ts">
import { IActionType, IHazardStatistic } from '@/views/hazard-management/type';
import Filter from './hazard-list-table/Filter.vue';
import TableList from './hazard-list-table/Table.vue';
import { computed, onMounted, ref } from 'vue';
import { getStatisticsMeger, hazardUrge } from '@/views/hazard-management/fetchData';
import { useRoute } from 'vue-router';
import { useMessage } from 'naive-ui';
import HazardDetails from '@/views/hazard-management/hazard-details.vue';
import { frequencyType } from '@/components/table-col/frequencyType';
import { formatDateRange, isTopUnit } from '@/views/inspection-planning/planned-management/Details/utils';
import { useStore } from '@/store';

const { userInfo } = useStore();
const message = useMessage();
const route = useRoute();
interface Props {
  planInfo: any;
}

const tabList = computed(() => {
  const result = [{ name: null, label: '全部' }];
  if (props.planInfo.planUnitGroupStatus == '2')
    result.push(
      ...props.planInfo.unitlist.map((item: any) => ({
        name: item.unitId,
        label: item.unitName,
      }))
    );
  return result;
});

const changeTab = () => {
  actionFn({ action: IActionType.search });
};

const getAllCheckUserName = () => {
  let result = [];
  for (let i = 0; i < props?.planInfo?.unitlist.length; i++) {
    const item = props.planInfo.unitlist[i];
    result.push(item.userlist.map((itm: any) => itm.checkUserName).join('、'));
  }
  return result.join('、');
};

const getCheckUserName = () => {
  let unitlist = props.planInfo.unitlist;
  let unitId = filterForm.value.unitId;
  let idx = unitlist.findIndex((item: any) => item.unitId == unitId);
  return idx > -1 ? unitlist[idx].userlist.map((item: any) => item.checkUserName).join('、') : '';
};

/** 过滤组件数据 */
const filterForm = ref({
  unitId: null,
  keyWords: null,
  likeFields: '',
  likeFieldValue: null,
  createBy: null,
  planId: route.params?.id,
  placeholder: '',
});
if (isTopUnit()) {
  filterForm.value.likeFields = 'unitName,createByName,hazardDesc';
  filterForm.value.placeholder = '请输入检查对象/检查人/隐患描述模糊搜索';
} else {
  filterForm.value.likeFields = 'createByName,hazardDesc';
  filterForm.value.placeholder = '请输入检查人/隐患描述模糊搜索';
}

const statisticsData = ref<IHazardStatistic>({});

const props = withDefaults(defineProps<Props>(), {});

const filterRef = ref();
const tableListRef = ref();
const hazardDetailsRef = ref();

const actionFn = (data: any) => {
  if (data.action === IActionType.search) {
    getHazardStatisticsFn();
    tableListRef.value.getTableDataWrap(filterForm.value);
  } else if (data.action === IActionType.hazardUrge) {
    hazardUrgeFn(data.data);
  } else if (data.action === IActionType.showDetails) {
    hazardDetailsRef.value.open(data.data);
  }
};
const hazardUrgeFn = async ({ disposeId, subCenterCode }: any) => {
  const params = {
    disposeId,
    eventType: 4,
    subCenterCode,
    operatorId: userInfo.id,
    operatorTel: userInfo.userTelphone,
    operatorName: userInfo.userName,
  };
  await hazardUrge(params);
  message.success('催促成功');
};
const getHazardStatisticsFn = async () => {
  const res = await getStatisticsMeger({
    planId: route.params.id,
    unitId: filterForm.value.unitId,
  });
  statisticsData.value = res.data;
  statisticsData.value._num = Number(statisticsData.value.unDisposedNum) + Number(statisticsData.value.disposingNum);
};

onMounted(() => {
  getHazardStatisticsFn();
});
</script>

<style scoped>
.trouble-number {
  img {
    width: 66px;
    height: 66px;
  }

  width: 218px;
  height: 80px;
  padding: 15px 32px 17px 32px;
  background: linear-gradient(180deg, #ffffff 0%, #e1e7fa 99%);
  box-shadow: 0px 2px 0px 0px rgba(0, 20, 82, 0.18);
  border-radius: 8px;
}

.bg1 {
  background-size: 100% 100%;
  background-image: url('@/views/hazard-management/comp/bg1.png');
}

.other-card {
  position: relative;
  width: 172px;
  height: 80px;
  padding: 15px 32px 17px 32px;
}
</style>
