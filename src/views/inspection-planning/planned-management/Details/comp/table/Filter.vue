<template>
  <n-form :show-feedback="false" label-placement="left">
    <div class="flex justify-between">
      <div :class="$style['form-content']">
        <n-form-item label="任务起止时间:">
          <n-input placeholder="请输入模板名称关键词" v-model:value="filterForm.templateName" clearable />
        </n-form-item>
        <n-form-item label="检查类型:">
          <n-input placeholder="请输入模板名称关键词" v-model:value="filterForm.templateName" clearable />
        </n-form-item>
        <n-form-item label="检查状态:">
          <n-input placeholder="请输入模板名称关键词" v-model:value="filterForm.templateName" clearable />
        </n-form-item>
        <n-form-item label="分管单位:">
          <n-input placeholder="请输入模板名称关键词" v-model:value="filterForm.templateName" clearable />
        </n-form-item>
        <n-form-item label="检查对象:">
          <n-input placeholder="请输入模板名称关键词" v-model:value="filterForm.templateName" clearable />
        </n-form-item>
      </div>
    </div>
  </n-form>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';
import { FlFilledAdd as IconAdd } from '@kalimahapps/vue-icons';
// import { ACTION, ACTION_LABEL } from '../constant';
import { trimObjNull } from '@/utils/obj.ts';
import { useRouter } from 'vue-router';
const router = useRouter();
const emits = defineEmits(['action']);

const filterForm = ref(initForm());
function initForm() {
  return { templateName: '' };
}
const add = () => {
  router.push({ name: 'plannedManagementAdd' });
};

function doHandle(action: any) {
  emits('action', {
    action: action,
    data: trimObjNull(filterForm.value),
  });
}

watch(filterForm.value, () => {
  doHandle('666');
});

onMounted(() => {
  doHandle('777');
});

defineOptions({ name: 'checkTempFilterComp' });
</script>
<style module lang="scss">
.form-content {
  // @apply w-full;
  width: 100%;
  display: grid;
  grid-template-columns: repeat(auto-fill, 280px);
  grid-row-gap: 10px;
  grid-column-gap: 10px;
}
</style>
