/*
 * @Author: fan<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-11-14 10:22:44
 * @LastEditors: fangweiwei <EMAIL>
 * @LastEditTime: 2025-01-09 10:22:45
 * @FilePath: \ehs-hazard-mgr\src\views\inspection-planning\planned-management\Details\comp\table\columns.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { DataTableColumn } from 'naive-ui';
import { h } from 'vue';

export const cols: DataTableColumn[] = [
  // {
  //   title: '序号',
  //   key: 'index',
  //   width: 65,
  //   align: 'center',
  //   render: (row: any, index: number) => {
  //     return index + 1;
  //   },
  // },
  {
    title: '计划名称',
    key: 'templateName',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '检查类型',
    key: 'templateScene',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
    // render: (row: any, index: number) => {
    //   return inspectType.find((i) => i.value === row.templateScene);
    // },
  },
  {
    title: '计划状态',
    key: 'createUser',
    align: 'center',
    width: 80,
  },
  {
    title: '计划起止时间',
    key: 'orgName',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '计划频次',
    key: 'templateScene',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '创建时间',
    key: 'templateScene',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '计划创建人',
    key: 'createTime',
    align: 'center',
    width: 170,
    ellipsis: {
      tooltip: true,
    },
  },
];
