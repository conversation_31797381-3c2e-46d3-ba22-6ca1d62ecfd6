<template>
  <n-data-table
    class="h-full com-table"
    remote
    striped
    :columns="columns"
    :data="tableData"
    :bordered="false"
    :flex-height="true"
    :loading="loading"
    :pagination="pagination"
    :render-cell="useEmptyCell"
  />
</template>

<script setup lang="ts">
import { h, ref, VNode, toRaw } from 'vue';
import { DataTableColumns, NButton } from 'naive-ui';
import { cols } from './columns';
import { useRouter } from 'vue-router';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { IObj } from '@/types';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';

const enum ACTION {
  SEARCH = 'SEARCH',
  ADD = 'ADD',
  DETAIL = 'DETAIL',
  EDIT = 'EDIT',
  DELETE = 'DELETE',
  CHECK = 'CHECK',
  UN_CHECK = 'UN_CHECK',
}

const router = useRouter();
const emits = defineEmits(['action']);

const [loading] = useAutoLoading(true);
const { pagination } = useNaivePagination(getTableData);

const columns = ref<DataTableColumns>([]);
const tableData = ref<any[]>([]);
function setColumns() {
  columns.value.push(...cols);
  // 添加操作栏 action
  columns.value.push({
    title: '操作',
    key: 'actions',
    align: 'center',
    width: 240,
    render(row) {
      return getActionBtn(row);
    },
  });
}
setColumns();

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-action-button',
          onClick: () =>
            router.push({ name: 'plannedManagementDetails', params: { id: row.createTime ?? '999', num: '456789' } }),
        },
        { default: () => '详情' }
      ),
    ],
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-action-button',
          onClick: () => router.push({ name: 'plannedManagementDetails', params: { id: row.id } }),
        },
        { default: () => '编辑' }
      ),
      true, // 需求变更，去掉编辑功能
    ],
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-action-button',
          onClick: () => {
            emits('action', { action: ACTION.DELETE, data: toRaw(row) });
          },
        },
        { default: () => '停用' }
      ),
    ],
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-action-button',
          onClick: () => emits('action', { action: ACTION.DELETE, data: toRaw(row) }),
        },
        { default: () => '删除' }
      ),
    ],
  ];
  return useActionDivider(acList);
}

let filterData: IObj<any> = {}; // 搜索条件
function getTableData() {
  loading.value = false;
  tableData.value = [
    {
      createTime: '1',
    },
    {
      createTime: '2',
    },
  ];
}

function getTableDataWrap(data: IObj<any>) {
  filterData = Object.assign({}, data) || {};
  pagination.page = 1;
  getTableData();
}
defineOptions({ name: 'checkTampTableComp' });
defineExpose({ getTableDataWrap, getTableData });
</script>
<style module lang="scss"></style>
