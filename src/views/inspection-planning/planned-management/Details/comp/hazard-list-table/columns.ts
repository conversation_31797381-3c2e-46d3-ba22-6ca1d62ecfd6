import { disposeState } from '@/components/table-col/disposeState';
import { tagBgs } from '@/views/random-check/check-task-manage/comp/detail/columns';
import { DataTableColumn } from 'naive-ui';
import { h } from 'vue';

export const frequencyText = (row: any) => {
  if (!row?.frequencyType) return;
  const typeObj: any = {
    '0': '时',
    '1': '日',
    '2': '周',
    '3': '月',
    '4': '季度',
    '5': '年',
    '99': '不重复',
  };
  if (row.frequencyType === '99') return '不重复';
  return `每${row.frequency}${typeObj[row.frequencyType]}`;
};
export const cols: DataTableColumn[] = [
  {
    title: '检查对象',
    key: 'unitName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '隐患位置',
    key: 'hazardPosition',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '隐患描述',
    key: 'hazardDesc',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '隐患分类',
    key: 'hazardTypeName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '隐患等级',
    key: 'hazardLevelName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '上报时间',
    key: 'eventTime',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '整改状态',
    key: 'disposeStateName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
    render: (row) => h(disposeState, { row }),
  },
  {
    title: '检查人',
    key: 'createByName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },

  {
    title: '是否超期',
    ellipsis: {
      tooltip: true,
    },
    key: 'timeoutDays',
    align: 'left',
    render: (row: any) => {
      if (row.timeoutDays == 0 || !row.timeoutDays) {
        return '否';
      } else {
        return `超期${row.timeoutDays}天`;
      }
    },
  },
];
