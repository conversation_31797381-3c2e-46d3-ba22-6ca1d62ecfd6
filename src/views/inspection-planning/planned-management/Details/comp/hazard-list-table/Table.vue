<template>
  <n-data-table
    class="h-full com-table"
    remote
    striped
    :columns="columns"
    :data="tableData"
    :bordered="false"
    :flex-height="true"
    :loading="loading"
    :pagination="pagination"
    :render-cell="useEmptyCell"
  />
</template>

<script setup lang="ts">
import { h, ref, VNode, toRaw, computed } from 'vue';
import { DataTableColumns, NButton } from 'naive-ui';
import { cols } from './columns';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { IObj } from '@/types';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { pageEventMeger } from '@/views/hazard-management/fetchData';
import { IActionType, IDisposeState } from '@/views/hazard-management/type';
import { isTopUnit } from '../../utils';

const emits = defineEmits(['action']);

const [loading, search] = useAutoLoading(true);
const { pagination, updateTotal } = useNaivePagination(getTableData);

const tableData = ref<any[]>([]);

const columns = computed(() => {
  let result = [
    ...cols,
    {
      title: '操作',
      key: 'actions',
      align: 'left',
      fixed: 'right',
      width: 180,
      render(row: any) {
        return getActionBtn(row);
      },
    },
  ];
  if (!isTopUnit()) result = result.filter((item: any) => item.key !== 'unitName');
  return result;
});

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          size: 'small',
          type: 'primary',
          ghost: true,
          class: 'com-action-button',
          onClick: () =>
            emits('action', {
              action: IActionType.showDetails,
              data: row,
            }),
        },
        { default: () => '详情' }
      ),
    ],
    [
      h(
        NButton,
        {
          size: 'small',
          type: 'primary',
          ghost: true,
          class: 'com-action-button',
          disabled: row.disposeState !== IDisposeState.待整改 || row.flag == 1,
          onClick: () =>
            emits('action', {
              action: IActionType.hazardUrge,
              data: row,
            }),
        },
        { default: () => '催促' }
      ),
    ],
  ];
  return useActionDivider(acList);
}

let filterData: IObj<any> = {}; // 搜索条件
function getTableData() {
  const params = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    ...filterData,
  };

  search(pageEventMeger(params)).then((res) => {
    tableData.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
}

function getTableDataWrap(data: IObj<any>) {
  filterData = Object.assign({}, data) || {};
  pagination.page = 1;
  getTableData();
}
defineOptions({ name: 'checkTampTableComp' });
defineExpose({ getTableDataWrap, getTableData });
</script>
<style module lang="scss"></style>
