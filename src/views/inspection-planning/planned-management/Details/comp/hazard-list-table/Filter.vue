<template>
  <n-form :show-feedback="false" label-placement="left" ref="formRef">
    <n-grid :x-gap="12" :y-gap="8" :cols="4">
      <n-grid-item v-if="isTopUnit()">
        <n-form-item label="检查对象:">
          <n-select
            v-model:value="filterForm.unitId"
            filterable
            :options="unitOptions"
            clearable
            placeholder="请选择"
            class="flex items-center"
          />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="检查人:">
          <n-select
            v-model:value="filterForm.createBy"
            filterable
            :options="hazardReporters"
            clearable
            class="flex items-center"
          />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item>
          <n-input
            :placeholder="filterForm.placeholder"
            v-model:value="filterForm.likeFieldValue"
            clearable
            class="flex items-center"
          >
            <template #suffix>
              <BsSearch />
            </template>
          </n-input>
        </n-form-item>
      </n-grid-item>

      <n-grid-item :span="1" :offset="isTopUnit() ? 0 : 1">
        <n-form-item>
          <div class="flex justify-end w-full">
            <n-button type="primary" @click="exportEventListFn"> 导出 </n-button>
          </div>
        </n-form-item>
      </n-grid-item>
    </n-grid>
  </n-form>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import { BsSearch } from '@kalimahapps/vue-icons';
import { SelectOption } from 'naive-ui';
import { exportEventList, getPlanUnitByPland } from '@/views/hazard-management/fetchData';
import { useStore } from '@/store/index.ts';
import { IActionType } from '@/views/hazard-management/type';
import { useRoute } from 'vue-router';
import { fileDownloader } from '@/utils/fileDownloader';
import { getUsersList } from '@/views/hidden-dangers-photo/fetchData';
import { isTopUnit } from '../../utils';
import { queryEventNumGroupByUser } from '@/views/task-management/fetchData';
import { cols } from './columns';

interface Props {
  value: any;
  planInfo: any;
  disabled: boolean;
}

const props = withDefaults(defineProps<Props>(), {});

const router = useRoute();
const { userInfo } = useStore();
const emits = defineEmits(['action']);
const formRef = ref();
const filterForm = computed(() => props.value);
const disabled = computed(() => props.disabled);
const unitOptions = ref<SelectOption[]>([]);
const usersList = ref<SelectOption[]>([]);

function doHandle(action: IActionType) {
  emits('action', {
    action: action,
  });
}

const exportEventListFn = async () => {
  // 非监管单位导出去除检查对象
  let _cols = userInfo.unitOrgType == '2' ? cols : cols.slice(1);
  let body = {
    ...filterForm.value,
    exportfields: [
      ..._cols.map(({ key, title }: any) => {
        return { key, title };
      }),
      { key: 'eventFileUrlStr1', title: '隐患图片1' },
      { key: 'eventFileUrlStr2', title: '隐患图片2' },
      { key: 'eventFileUrlStr3', title: '隐患图片3' },
      { key: 'dealFileUrlStr1', title: '验收图片1' },
      { key: 'dealFileUrlStr2', title: '验收图片2' },
      { key: 'dealFileUrlStr3', title: '验收图片3' },
    ],
    fileName: '隐患清单',
  };
  body = JSON.stringify(body);
  console.log(body);
  fileDownloader(exportEventList(), {
    filename: '隐患清单-' + new Date().getTime(),
    method: 'POST',
    contentType: 'application/json',
    body,
  });
};

const hazardReporters = ref<any[]>([]);
const queryEventNumGroupByUserFn = async () => {
  // const res: any = await queryEventNumGroupByUser({ planId: props.value.planId, taskId: props.value.taskId });
  // hazardReporters.value = (res.data || []).map((item: any) => ({ label: item.checkUserName, value: item.checkUserId }));
  let arr: any[] = [];
  if (props.value.unitId) {
    // arr = planInfo.ss
    arr = props.planInfo.unitlist.filter((item: any) => item.unitId == props.value.unitId)[0]['userlist'];
  } else {
    props.planInfo?.unitlist.map((item: any) => {
      arr = arr.concat(item.userlist);
    });
  }
  hazardReporters.value = (arr || []).map((item: any) => ({ label: item.checkUserName, value: item.checkUserId }));
};
watch(
  () => filterForm.value.unitId,
  () => {
    filterForm.value.createBy = null;
    queryEventNumGroupByUserFn();
  },
  {
    immediate: true,
  }
);
watch(filterForm.value, () => {
  console.log(filterForm.value);
  doHandle(IActionType.search);
});

onMounted(() => {
  doHandle(IActionType.search);
  queryEventNumGroupByUserFn();
  getPlanUnitByPland({ planId: router.params.id }).then(({ data }: any) => {
    unitOptions.value = data.map((item: any) => ({ label: item.unitName, value: item.unitId }));
  });
  getUsersList({ pageSize: -1, orgCode: userInfo.unitId }).then((res) => {
    usersList.value = res.data.rows.map((item) => ({ label: item.userName, value: item.id }));
  });
});

const resetField = () => {};

defineExpose({ resetField });
defineOptions({ name: 'checkTempFilterComp' });
</script>
<style module lang="scss"></style>
