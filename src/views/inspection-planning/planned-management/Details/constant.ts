import { checkUserType } from '@/components/table-col/checkUserType';
import { h } from 'vue';
import { getOrgType, isTopUnit } from './utils';
import { getProjectByUnitIdAPI } from '../fetchData';

export const enum PROVIDE_KEY {
  currentAction = 'currentAction',
}
export const TableList = [
  { name: '1', label: '计划信息' },
  { name: '2', label: '任务执行情况' },
  // { name: '3', label: '检查报告' },
  { name: '4', label: '隐患清单' },
];
export const BreadData = [
  {
    name: '计划管理',
    clickable: true,
    routeRaw: {
      name: 'plannedManagementIndex',
    },
  },
  { name: '计划详情' },
];

export const ontherInfoTableLable = ({ checkRange = '1' }, data: any) => {
  const result: any[] = [];

  // if (checkRange === '1') {
  //   result.push({
  //     label: '是否需要检查人现场打卡',
  //     key: 'isNeedClock',
  //     span: 24,
  //   });
  // }
  result.push(
    {
      label: '是否需要检查人现场打卡',
      key: 'isNeedClock',
      span: 24,
    },
    {
      label: '检查结束操作',
      key: 'checkEndOperateStr',
      span: 24,
    }
  );
  return result;
};

export const planInfoLable = ({ checkTableName = '' }) => {
  const result: any[] = [
    {
      label: '计划名称',
      key: 'planName',
    },
    {
      label: '检查类型',
      key: 'planTypeName',
    },
    {
      label: '计划起止时间',
      key: 'planStartDate',
      key2: 'planEndDate',
    },
    {
      label: '计划频次',
      key: 'frequencyStr',
    },
    {
      label: '创建人',
      key: 'createByName',
    },
    {
      label: '创建时间',
      key: 'createTime',
    },
    {
      label: '检查执行方式',
      key: 'checkExecuteMethod',
      type: 'checktype',
    },
    {
      label: '创建单位',
      key: 'createUnitName',
    },
    {
      label: '计划状态',
      key: 'planState',
    },
    {
      label: '检查要求',
      key: 'checkDemand',
      span: 24,
    },

    {
      label: '附件',
      key: 'filelist',
      span: 24,
      type: 'file',
    },
  ];

  // 检查表
  if (checkTableName) {
    result.push({
      label: '检查表',
      key: 'checkTableName',
      span: 24,
    });
  }
  return result;
};
export const ChecklistLable = ({ planUnitGroupStatus = '', checkRange = '1' }, data: any) => {
  const result: any = [];

  if (planUnitGroupStatus === '1') {
    // 二级单位也需要展示检查对象
    (isTopUnit() || getOrgType() == '2') &&
      result.push({
        label: '检查对象',
        key: 'unitlist',
        render: (row: any) => {
          return row?.unitlist?.map((item: any) => item.unitName).join(',');
        },
        span: '24',
      });
    result.push({
      label: '关联施工项目',
      key: 'projectName',
      render: (row: any) => {
        return row?.unitlist?.map((item: any) => item.projectName).join(',');
      },
      span: '24',
    });
    result.push({
      label: '检查人信息',
      key: 'userlist',
      span: 24,
      type: 'checkTable',
      columns: [
        {
          title: '序号',
          key: 'index',
          width: 65,
          align: 'center',
          render: (row: any, index: number) => {
            return index + 1;
          },
        },
        {
          title: '检查人姓名',
          key: 'checkUserName',
        },
        {
          title: '检查人员组织机构',
          key: 'userUnitName',
        },
        {
          title: '检查人类型',
          key: 'checkUserType',
          render: (row: any) => h(checkUserType, { row }),
        },
      ],
    });
  }

  // 点位巡查列表
  if (planUnitGroupStatus === '1' && checkRange == '3') {
    result.push({
      label: '巡查点位信息',
      key: 'checkPointlist',
      span: 24,
      type: 'checkPointTable',
      columns: [
        {
          title: '序号',
          key: 'index',
          width: 65,
          align: 'center',
          render: (row: any, index: number) => {
            return index + 1;
          },
        },
        {
          title: '巡查点位',
          key: 'pointFullName',
          align: 'left',
          ellipsis: {
            tooltip: true,
          },
        },
        {
          title: '检查表',
          key: 'checkTableName',
          align: 'left',
          ellipsis: {
            tooltip: true,
          },
          render(row: any) {
            return row.checkTableName || '-';
          },
        },
      ],
    });
  }
  return result;
};
