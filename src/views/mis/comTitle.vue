<template>
  <div class="header w-full" :style="`height: ${toVh(45)}`">
    <div class="com-title" :style="`font-size: ${toVh(24)}`">{{ title }}</div>
  </div>
</template>

<script setup lang="ts">
import { toVw, toVh } from '@/utils/fit';
interface Props {
  title: string;
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
});

defineOptions({ name: 'ComHeaderB' });
</script>

<style lang="scss" scoped>
.header {
  width: 100%;
  height: 45px;
  background: url('./assets/title-bj.png') bottom left no-repeat;
  background-size: 100% auto;
}

.com-title {
  font-family: Alibaba PuHuiTi;
  font-weight: 400;
  font-size: 24px;
  color: #ffffff;
  text-shadow: 1px 3px 2px rgba(0, 0, 0, 0.4);
  font-style: italic;
  margin-left: 48px;
}
</style>
