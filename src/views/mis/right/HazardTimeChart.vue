<template>
  <div class="h-full overflow-hidden">
    <ComTitle title="隐患整改时长" />
    <div class="h-[calc(100%-38px)] w-full" ref="chartRef"></div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, nextTick } from 'vue';
import ComTitle from '../comTitle.vue';
import * as echarts from 'echarts';
import { getEssentialFactorDisposeTimeGroup } from '../fetchData';
import useResize from '../useResize';
import { useStore, useCurrentUnit } from '@/store';
const currentUnit = useCurrentUnit();
const chartRef = ref();
let chart: any = null;
const serieData = ref([]);

const getData = async ({ date }: { date: any }) => {
  serieData.value = [];
  const { data } = await getEssentialFactorDisposeTimeGroup({
    unitId: currentUnit.currentUnitId,
    startTime: date ? `${date[0]} 00:00:00` : undefined,
    endTime: date ? `${date[1]} 23:59:59` : undefined,
  });
  const result: any = Array.from({ length: 21 }, () => 0);

  data.forEach((item: any) => (result[item.timeDifferenceGroup - 1] = item.recordCount));
  serieData.value = result;
};

const xAxisData = [...Array.from({ length: 20 }, (_, index) => index + 1), '>20'];
const chartOption = computed(() => {
  return {
    grid: {
      top: '20%', // 上边距
      right: '3%', // 右边距
      bottom: '12%', // 下边距（为X轴标签留空间）
      left: '8%', // 左边距
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 0, 0, 0.6)',
      borderWidth: 0,
      textStyle: {
        color: '#fff',
      },
      formatter: ([param]: any) => {
        return `${param.name}天<br/> ${param.marker} ${param.value}次`;
      },
    },
    xAxis: [
      {
        type: 'category',
        axisTick: {
          alignWithLabel: true,
        },
        data: xAxisData,
        name: '天',
        nameTextStyle: {
          color: '#fff',
        },
        axisLabel: {
          show: true,
          textStyle: {
            color: '#EBF3FF', //X轴文字颜色
          },
        },
        axisLine: {
          show: true, //隐藏X轴轴线
          lineStyle: {
            color: '#5D7AA6',
          },
        },
      },
    ],
    yAxis: {
      type: 'value',
      name: '次',
      minInterval: 1,
      nameTextStyle: {
        color: '#fff',
      },
      splitLine: {
        lineStyle: {
          color: '#4f5867',
        },
      },
      axisLabel: {
        fontSize: 12,
        margin: 8, // 标签与轴线间距
      },
      axisLine: {
        show: false,
        lineStyle: {
          color: '#fff',
        },
      },
    },
    series: [
      {
        type: 'bar',
        barWidth: 'auto', // 自动调整柱宽
        barMaxWidth: 60, // 最大柱宽限制
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#007FEF' },
            { offset: 1, color: 'rgba(0,41,98,0.7)' },
          ]),
        },
        data: serieData.value,
      },
    ],
  };
});

const updateChart = () => {
  if (!chart) chart = echarts.init(chartRef.value);
  chart.setOption(chartOption.value);
};

const update = async (data: any) => {
  if (chart) chart.showLoading();
  try {
    await getData(data);
    updateChart();
  } catch (error) {}
  if (chart) chart.hideLoading();
};

useResize(chartRef, () => {
  if (chart)
    nextTick(() => {
      chart.resize();
    });
});
defineExpose({ update });
</script>
