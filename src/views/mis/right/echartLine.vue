<template>
  <div class="line-part w-full h-full" ref="line1Chart"></div>
</template>

<script lang="ts" setup>
import { defineComponent, onMounted, ref, watch } from 'vue';
import * as echarts from 'echarts';

const props = defineProps({
  dataList: {
    type: Object,
    default: () => ({}),
  },
});

watch(
  () => props.dataList,
  (val) => {
    initEcharts();
  }
);
defineComponent({ name: 'publicPuline1Chart' });
const line1Chart = ref();
let arrName = [];
let arrValue = [];
let sum = 0;
let pieSeries: any[] = [],
  lineYAxis: any[] = [];
const colorObj = ['#FF3E6C', '#CCCC00', '#00CC03', '#0091FF', '#009494', '#e23f1b'];

let myChart: any;
let option = {};
function initEcharts() {
  sum = 0;
  pieSeries = [];
  if (myChart) myChart.dispose();
  // 数据处理
  arrName.length = 0;
  arrValue.length = 0;
  props.dataList.forEach((v: any, i: any) => {
    arrName.push(v.name);
    arrValue.push(v.value);
    sum = sum + v.value;
  });
  pieSeries.length = 0;
  lineYAxis.length = 0;
  props.dataList.forEach((v: any, i: any) => {
    props.dataList.forEach((i: any) => {
      i.reat = ((i.value || 0) / (sum || 1)).toFixed(2);
    });
    pieSeries.push({
      name: '隐患等级',
      type: 'pie',
      clockWise: false,
      hoverAnimation: false,
      radius: [65 - i * 15 + '%', 57 - i * 15 + '%'],
      center: ['35%', '50%'],
      label: {
        show: false,
      },
      data: [
        {
          value: v.reat * 0.75,
          // value: 50,

          name: v.hazardLevelName,
        },
        {
          // value: 200 - 50,
          value: 1 - v.reat * 0.75,
          name: '',
          itemStyle: {
            color: 'rgba(0,0,0,0)',
          },
        },
      ],
    });
    pieSeries.push({
      name: '',
      type: 'pie',
      silent: true,
      z: 1,
      clockWise: false, //顺时加载
      hoverAnimation: false, //鼠标移入变大
      radius: [65 - i * 15 + '%', 57 - i * 15 + '%'],
      center: ['35%', '50%'],
      label: {
        show: false,
      },
      data: [
        {
          value: 7.5,
          itemStyle: {
            color: '#E3F0FF',
          },
        },
        {
          value: 2.5,
          name: '',
          itemStyle: {
            color: 'rgba(0,0,0,0)',
          },
        },
      ],
    });
    // v.percent = ((v.value / sum) * 100).toFixed(1) + '%';
    v.percent = (((v.value || 0) / (sum || 1)) * 100).toFixed(1) + '%';
    lineYAxis.push({
      value: i,
      textStyle: {
        rich: {
          circle: {
            color: colorObj[i],
            padding: [0, 2],
          },
        },
      },
    });
  });
  // 标记一个对象，使其永远不会再成为响应式对象
  myChart = echarts.init(line1Chart.value);
  option = {
    color: colorObj,
    grid: {
      top: '15%',
      bottom: '54%',
      left: '31%',
      z: 100,
      zlevel: 100,
      containLabel: false,
    },
    yAxis: [
      {
        type: 'category',
        inverse: true,
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          formatter: function (params: any) {
            let item = props.dataList[params];
            let _num = (((item.value || 0) / (sum || 1)) * 100).toFixed(2);
            let _m = _num.split('.')[1] == '00' ? _num.split('.')[0] : _num;
            return (
              '{line|}{circle|●}' + '{rate|' + `${item.value} ${_m}%}` + '{name|' + item.hazardLevelName + '}'
              // +'{bd||}{percent|'+
              // item.percent +
              // '}{value|' +
              // item.value +
              // '}{unit|元}'
            );
          },
          interval: 0,
          inside: true,
          textStyle: {
            color: '#eee',
            fontSize: 10,
            rich: {
              name: {
                color: '#eee',
                fontSize: 10,
              },
              rate: {
                fontSize: 10,
                padding: [0, 2, 0, 0],
              },
              bd: {
                color: '#ccc',
                padding: [0, 0],
                fontSize: 10,
              },
              percent: {
                color: '#333',
                fontSize: 10,
              },
              value: {
                color: '#333',
                fontSize: 10,
                fontWeight: 500,
                padding: [0, 0, 0, 10],
              },
              unit: {
                fontSize: 10,
              },
            },
          },
          show: true,
        },
        data: lineYAxis,
      },
    ],
    xAxis: [
      {
        show: false,
      },
    ],
    series: pieSeries,
  };
  myChart.setOption(option);
  window.addEventListener('resize', () => {
    if (myChart) myChart.resize();
  });
}

onMounted(() => {
  initEcharts();
});

// 把initEcharts暴露出去
defineExpose({
  initEcharts,
});
</script>

<style scoped></style>
