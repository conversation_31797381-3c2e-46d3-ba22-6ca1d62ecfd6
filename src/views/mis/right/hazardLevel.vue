<template>
  <div ref="chartContainer" class="h-full">
    <ComTitle title="隐患分级分类" />
    <div class="h-[calc(100%-38px)] flex justify-betweens flex-shrink-0">
      <div class="chart-1 w-[50%] h-full flex-shrink-0">
        <EchartsLine v-if="leftChartData.length" :dataList="leftChartData" ref="lineChartRef" />
        <div v-else class="w-[100%] h-full flex flex-1 justify-center items-center flex-shrink-0">
          <n-empty description="暂无数据" />
        </div>
      </div>
      <!-- <div class="chart-1 w-[50%] h-full" ref="chart1Ref"></div> -->
      <div class="chart-2 h-full w-[50%]" v-if="isHaschart2Daata" ref="chart2Ref"></div>
      <div class="w-[100%] h-full flex flex-1 justify-center items-center flex-shrink-0" v-else>
        <n-empty description="暂无数据" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, nextTick, onMounted, onBeforeUnmount } from 'vue';
import EchartsLine from './echartLine.vue';
import ComTitle from '../comTitle.vue';
import * as echarts from 'echarts';
import useResize from '../useResize';
import { getHazrdTypeStastics } from '../fetchData';
import { debounce } from 'lodash-es';
import { useStore, useCurrentUnit } from '@/store';
import { levelStatisticsMeger } from '@/views/hazard-management/fetchData';
const currentUnit = useCurrentUnit();
const store = useStore();

const chartContainer = ref();
const chart1Ref = ref();
let chart1: any = null;
const chart2Ref = ref();
let chart2: any = null;
const leftChartData = ref([]);
const colorObj = ['#FF3E6C', '#CCCC00', '#00CC03', '#0091FF', '#009494', '#e23f1b'];
const isHaschart2Daata = ref(false);

const total = ref(0);

const widths = () => {
  const _w = window.screen.width;
  if (_w <= 1366) {
    return [25, 32];
  } else if (_w <= 1920) {
    return [35, 52];
  } else if (1920 < _w && _w <= 2440) {
    return [50, 90];
  } else {
    return [60, 100];
  }
};

const createPieSeries = (data: any) => {
  const serieData = data.map((item: any) => ({ total: item.total, value: item.proportion, name: item.hazardTypeName }));
  total.value = serieData.reduce((pre: number, next: any) => pre + next.value, 0);

  return [
    {
      type: 'pie',
      radius: widths(),
      center: ['23%', '50%'],
      data: serieData,
      label: {
        show: false,
        position: 'center',
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)',
        },
      },
    },
  ];
};

const getTopStasticsFn = ({ date }: { date: any }) => {
  const params = {
    unitId: currentUnit.currentUnitId,
    startTime: date ? `${date[0]} 00:00:00` : undefined,
    endTime: date ? `${date[1]} 23:59:59` : undefined,
  };
  if (chart1) chart1.showLoading();
  levelStatisticsMeger(params)
    .then(({ data }) => {
      data.forEach((item: any, idx: number) => {
        item.value = item.total;
        item.itemStyle = { color: colorObj[idx] };
      });
      // 取前5项的，产品需求
      nextTick(() => {
        leftChartData.value = data.slice(0, 5);
      });
    })
    .catch(() => {})
    .finally(() => {});
  if (chart2) chart2.showLoading();
  delete params.flag;
  getHazrdTypeStastics(params)
    .then(({ data }) => {
      if (data.length > 0) {
        isHaschart2Daata.value = true;
      } else {
        isHaschart2Daata.value = false;
        return;
      }
      nextTick(() => {
        const option = createChart2Option(createPieSeries(data));
        updateChart2(option);
      });
    })
    .catch(() => {
      updateChart2(createChart2Option([]));
    })
    .finally(() => {
      if (chart2) chart2.hideLoading();
    });
};
const getLegendFontSize = () => {
  const chartWidth = chart2Ref.value?.offsetWidth || 800; // 获取饼图容器实际宽度
  return Math.max(8, Math.min(16, chartWidth / 50)); // 限制在8-16px之间
};

const createChart2Option = (series: any = []) => {
  const fontSize = getLegendFontSize();
  const isSmallScreen = window.innerWidth < 1600;

  return {
    grid: {
      top: 40, // 上边距
      right: 20, // 右边距（给图例留空间）
      bottom: 40, // 下边距
      left: 20, // 左边距
      containLabel: true, // 确保标签在 grid 区域内
    },
    tooltip: {
      trigger: 'item',
      formatter: (prams: any) => {
        const str = `<div> ${prams.marker}&nbsp ${prams.name} &nbsp ${prams.data.total}  &nbsp ${prams.value}%</div>`;
        return str;
      },
      backgroundColor: 'rgba(0, 0, 0, 0.6)',
      borderWidth: 0,
      textStyle: {
        color: '#fff',
        fontSize: 12,
      },
    },
    legend: {
      orient: 'vertical',
      right: '10%',
      bottom: isSmallScreen ? 0 : undefined,
      top: 'middle',
      itemGap: fontSize * 1.5, // 动态间距
      itemWidth: fontSize * 1.2,
      itemHeight: fontSize,
      textStyle: {
        color: '#fff',
        fontSize: fontSize, // 动态字号
        rich: {
          percent: {
            fontSize: fontSize * 0.6, // 百分比小字号
            verticalAlign: 'bottom',
          },
        },
      },
      formatter: (name: string) => {
        if (series.length === 0) return name;
        const currentData = series[0].data.find((item: any) => item.name === name);
        const currentNum = currentData?.value || 0;
        const maxNameLength = isSmallScreen ? 3 : 4; // 动态名称长度
        const _t = ((currentNum / total.value) * 100).toFixed(1) + '%';
        const _name = name.length > maxNameLength ? name.substring(0, maxNameLength) + '...' : name;

        return `{name|${_name}} {percent|${_t}}`;
      },
    },
    series: series,
  };
};

// const updateChart2 = (option: any) => {
//   if (!chart2) chart2 = echarts.init(chart2Ref.value);
//   chart2.setOption(option);
// };

const updateChartSize = () => {
  if (chart1) chart1.resize();
  if (chart2) chart2.resize();
};

// 修改handleResize函数
const handleResize = debounce(() => {
  nextTick(() => {
    if (chart2) {
      const newOption = createChart2Option(chart2.getOption().series);
      newOption.series[0].radius = widths();
      chart2.setOption(newOption, {
        replaceMerge: ['legend'], // 智能合并配置
      });
      chart2.resize();
    }
  });
}, 300);

// 修改updateChart2方法
const updateChart2 = (option: any) => {
  if (!chart2) {
    chart2 = echarts.init(chart2Ref.value);
    // 添加容器尺寸监听
    new ResizeObserver(handleResize).observe(chart2Ref.value);
  }
  chart2.setOption(option);
};

onMounted(() => {
  window.addEventListener('resize', handleResize);
});

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize);
  if (chart1) chart1.dispose();
  if (chart2) chart2.dispose();
});

useResize(chartContainer, updateChartSize);

defineExpose({ update: getTopStasticsFn });
</script>
<style scoped></style>
