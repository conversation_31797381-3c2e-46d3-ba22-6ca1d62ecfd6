<template>
  <!-- <div class="flex flex-col"> -->
  <ComTitle title="隐患点TOP" class="mb-2" />
  <div class="h-[calc(100%-50px)] overflow-auto">
    <div class="flex flex-col h-full" v-if="list.length">
      <div
        class="item h-[42px] relative pl-[11px] flex gap-2 items-center justify-between flex-shrink-0"
        v-for="(item, index) of list"
        :key="index"
      >
        <div class="flex relative">
          <img :style="{ width: toVw(20) }" :src="iconList[index] || iconList[3]" alt="" />
          <span class="text-white absolute top-[1px] left-[6px]">{{ index + 1 }}</span>
          <span class="text-white ml-4 text-[16px]">{{ item.hazardTypeName }}</span>
        </div>
        <div class="text-white ml-4 text-[16px]">{{ item.recordCount }}次</div>
      </div>
    </div>
    <!-- <Empty v-else /> -->
    <n-empty v-else class="mt-[10%]"></n-empty>
  </div>
  <!-- </div> -->
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import ComTitle from '../comTitle.vue';
import one from '../assets/one.png';
import two from '../assets/two.png';
import three from '../assets/three.png';
import other from '../assets/other.png';
import { getEssentialFactorTypeGroup } from '../fetchData';
import Empty from '@/components/empty/index.vue';
import { useStore, useCurrentUnit } from '@/store';
import { toVw, toVh } from '@/utils/fit';
const currentUnit = useCurrentUnit();
const iconList = [one, two, three, other];

const list = ref<any[]>([]);

const getData = async ({ date }: { date: any }) => {
  const { data } = await getEssentialFactorTypeGroup({
    unitId: currentUnit.currentUnitId,
    startTime: date ? `${date[0]} 00:00:00` : undefined,
    endTime: date ? `${date[1]} 23:59:59` : undefined,
  });
  list.value = data;
};

defineExpose({ update: getData });
</script>

<style scoped>
.item {
  background: url('../assets/topBj.png') center center no-repeat;
  background-size: 100% 100%;
  /* position: absolute; */
  /* color: white */
}

.item + .item {
  margin-top: 0.5rem;
}

.scrollbar {
  -ms-overflow-style: none;
  /* IE和Edge */
  scrollbar-width: none;
}

::-webkit-scrollbar {
  display: none;
}
</style>
