import { debounce } from 'lodash-es';
import { unref } from 'vue';
import { onBeforeUnmount, onMounted } from 'vue';

/**
 * 当前元素大小变化时，更新函数
 * @param el 需要监听对象的元素，可以是ref或者dom元素
 * @param fn 更新函数
 */
const useResize = (el: any, fn: any) => {
  if (!el) throw new Error('el is required');
  const updateFn = debounce(fn, 1000 / 60);
  let resizeObserver: any;
  // 监听元素大小变化
  onMounted(() => {
    resizeObserver = new ResizeObserver(updateFn);
    resizeObserver.observe(unref(el));
  });
  onBeforeUnmount(() => {
    if (!resizeObserver) return;
    resizeObserver.disconnect();
    resizeObserver = null;
  });
};

export default useResize;
