<template>
  <div class="bg w-full flex flex-col" style="padding-bottom: 0; height: 100vh">
    <div class="header h-[80px]">
      <!-- 隐患排查治理系统改成隐患治理系统  -->
      <div class="head">
        <span class="head-title"> 隐患治理系统 </span>
      </div>

      <div class="h-[51px] mt-[19px] relative flex-shrink-0">
        <div class="left-[10px] absolute flex gap-2 pt-[8px]">
          <headerWeather />
        </div>

        <div class="right-0 absolute flex gap-2 pt-[16px]">
          <div class="w-[180px] relative z-[99]">
            <n-config-provider :theme-overrides="cascaderthem">
              <n-cascader
                class="unitSelect"
                placeholder="请选择单位"
                v-model:value="currentUnitId"
                :show-path="false"
                :filterable="true"
                :options="unitTreeList"
                check-strategy="child"
                expand-trigger="click"
                children-field="children"
                key-field="id"
                size="small"
                @update:value="handleUpdateValue"
              />
            </n-config-provider>
          </div>
          <n-date-picker
            size="small"
            class="w-[240px] date-picker flex-shrink-0 flex-grow-0"
            v-model:formatted-value="timeRange"
            value-format="yyyy-MM-dd"
            type="daterange"
            @updateValue="updateFn(IACTION.update)"
            clearable
          />
          <div @click="openGis">
            <img src="@/assets/gis.png" width="30" alt="" />
          </div>
        </div>
      </div>
    </div>

    <div class="flex-1 overflow-hidden flex-shrink-0 flex">
      <div class="w-[30%] h-full flex flex-col justify-around">
        <div class="w-full flex-1" style="min-height: 33.3%" v-if="misGisSetting.includes('301')">
          <troublesTask ref="troublesTaskRef" />
        </div>
        <div class="w-full flex-1" v-if="misGisSetting.includes('401')">
          <executeTask ref="executeTaskRef" />
        </div>
        <div class="w-full flex-1" style="height: 33.3%" v-if="misGisSetting.includes('501')">
          <hiddenDanger ref="hiddenDangerRef" />
        </div>
      </div>
      <div class="w-[40%] h-full flex flex-col">
        <div class="w-full h-full">
          <PanelCenter :rangeDate="timeRange" :showPanel="misGisSetting.includes('601')" />
        </div>
      </div>
      <div class="w-[30%] h-full flex flex-col">
        <div class="w-full flex-1" style="height: 33.3%" v-if="misGisSetting.includes('701')">
          <HazardLevel ref="hazardLevelRef" />
        </div>
        <div class="w-full flex-1 overflow-hidden" v-if="misGisSetting.includes('801')">
          <HazardTimeChart ref="hazardTimeChartRef" />
        </div>
        <div class="w-full flex-1" style="height: 33.3%" v-if="misGisSetting.includes('901')">
          <HazardTop ref="hazardTopRef" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import headerWeather from './header/comp/Weather/index.vue';

import { misToGisBridgeService } from '@/service/bridge/misToGisBridge';
import { misToGisTypes } from '@/service/bridge/types';
import { useCurrentUnit, useStore } from '@/store';
import { getHazardMisSetting } from '@/views/paramsConf/comp/fetchData';
import dayjs from 'dayjs';
import { NCascader, NConfigProvider } from 'naive-ui';
import { onMounted, ref, watch } from 'vue';
import { getOrgTree } from './fetchData';
import executeTask from './left/execute_task/index.vue';
import hiddenDanger from './left/hidden_danger/index.vue';
import troublesTask from './left/troubles_task/index.vue';
import PanelCenter from './middle/PanelCenter.vue';
import HazardLevel from './right/hazardLevel.vue';
import HazardTimeChart from './right/HazardTimeChart.vue';
import HazardTop from './right/HazardTop.vue';
import { IACTION } from './type';

misToGisBridgeService.init();

const cascaderthem = {
  Cascader: {
    menuDividerColor: 'rgba(255,255,255,.09)',
    menuColor: 'rgb(19,40,66)',
    optionTextColorDisabled: '#eee',
    optionTextColor: '#98B9D9',
    optionCheckMarkColor: '#00f',
    loadingColor: '#0E0E0EFF',
    optionColorHover: 'rgb(39,63,86)',
  },
};

const userInfo = useStore().userInfo;
const currentUnitInfo = useCurrentUnit();
// 写死筛选项单位id
// currentUnitInfo.currentUnitId = '10ef1565d0f74a0184dffc532c6dd82b';
const currentUnitId = ref();
const troublesTaskRef = ref();
const executeTaskRef = ref();
const hiddenDangerRef = ref();

const hazardLevelRef = ref();
const hazardTimeChartRef = ref();
const hazardTopRef = ref();
const today = dayjs().format('YYYY-MM-DD');
// 使用dayjs计算今天往前一个月的日期
const beforeMonth = dayjs().add(-1, 'month').format('YYYY-MM-DD');
const timeRange = ref([beforeMonth, today]);
const position = ref('');
const options: any = ref([]);
defineOptions({ name: 'mis-com' });

watch(
  () => currentUnitInfo.currentUnitId,
  (val) => {
    console.log('🚀 ~ watch ~ val:', val);
    currentUnitId.value = currentUnitInfo.currentUnitId;
    updateFn(IACTION.update);
  }
);

function getOptions(depth = 3, iterator = 1, prefix = '') {
  const length = 12;
  const options: any[] = [];
  for (let i = 1; i <= length; ++i) {
    if (iterator === 1) {
      options.push({
        id: `v-${i}`,
        label: `l-${i}`,
        disabled: i % 5 === 0,
        children: getOptions(depth, iterator + 1, `${String(i)}`),
      });
    } else if (iterator === depth) {
      options.push({
        id: `v-${prefix}-${i}`,
        label: `l-${prefix}-${i}`,
        disabled: i % 5 === 0,
      });
    } else {
      options.push({
        id: `v-${prefix}-${i}`,
        label: `l-${prefix}-${i}`,
        disabled: i % 5 === 0,
        children: getOptions(depth, iterator + 1, `${prefix}-${i}`),
      });
    }
  }
  return options;
}

const unitTreeList = ref([]);
const handleUpdateValue = (val: any) => {
  // console.log('🚀 ~ handleUpdateValue ~ val:', val);
  currentUnitInfo.currentUnitId = val;
  misToGisBridgeService.sendEvents(misToGisTypes.screenSetUnitId, {
    unitId: val,
  });
  // updateFn(IACTION.update);
};

const updateFn = (type: IACTION, data?: any) => {
  // 解决事件触发时，数据未更新问题
  setTimeout(() => {
    if (IACTION.update === type) {
      const params = {
        date: timeRange.value,
        position: position.value,
      };
      troublesTaskRef.value && troublesTaskRef.value.update(params);
      executeTaskRef.value && executeTaskRef.value.update(params);
      hiddenDangerRef.value && hiddenDangerRef.value.update(params);
      hazardLevelRef.value && hazardLevelRef.value.update(params);
      hazardTimeChartRef.value && hazardTimeChartRef.value.update(params);
      hazardTopRef.value && hazardTopRef.value.update(params);
    }
  }, 500);
};

const openGis = () => {
  const base_url = window.$SYS_CFG.base_url;
  const gisWindow = window.open(
    // `${_host}/aqsc/v1/ehs-gis/index.html#/?project=hazardMgr&orgCode=${currentUnitId.value}`,
    `${base_url}/ehs-gis/index.html#/?project=hazardMgr&orgCode=${currentUnitId.value}`,
    'hazardMgr-gisWindow'
  );
  gisWindow?.blur();
  sessionStorage.setItem('isOpenGis', 'true');
};

const fomartData = (list = []) => {
  list.forEach((i: any) => {
    i.value = i.id;
    i.label = i.text;
    if (i.children && i.children.length > 0) {
      return fomartData(i.children);
    } else {
      delete i.children;
    }
  });
};

const getFirstUnit = (unitList = []) => {
  if (unitList && unitList.length > 0) {
    let _data: any = unitList[0];
    if (_data.children && _data.children.length > 0) {
      return getFirstUnit(_data.children);
    } else {
      return _data;
    }
  }
  // if (unitList && unitList.length > 0) return getUnitid(unitList)
};

onMounted(async () => {
  const params = {
    orgCode: userInfo.unitId,
    unitStatus: 1,
  };
  options.value = getOptions();
  // console.log(getOptions());
  const res: any = await getOrgTree(params);

  fomartData(res.data || []);
  const _unit = getFirstUnit(res.data);
  currentUnitId.value = _unit.id;
  // currentUnitId.value = '10ef1565d0f74a0184dffc532c6dd82b';
  // console.log('🚀 ~ onMounted ~ _unit.id:', _unit.id);
  // currentUnitInfo.currentUnitId = '10ef1565d0f74a0184dffc532c6dd82b';
  currentUnitInfo.currentUnitId = _unit.id;

  unitTreeList.value = res.data;

  // getAllUnit({ orgCode: userInfo.unitId, pageNo: 1, pageSize: -1 }).then((res: any) => {});
  updateFn(IACTION.update);
  getMisGisSetting();
  // 第一次进入时打开gis
  if (sessionStorage.getItem('isOpenGis')) return;
  // openGis();
});

//获取参数配置项
const misGisSetting = ref([]);
const getMisGisSetting = async () => {
  const res: any = await getHazardMisSetting({
    createBy: userInfo.id,
    unitId: userInfo.topUnitId,
  });
  misGisSetting.value = res.data;
};
</script>

<style scoped lang="scss">
:deep(.n-input .n-input-wrapper) {
  padding-left: 6px;
  padding-right: 6px;
}

.unitSelect {
  :deep(.n-base-selection__border) {
    border-color: #086cc4 !important;
  }

  :deep(.n-base-selection .n-base-selection-label) {
    // background-color: red !important;
    background-color: rgba(0, 0, 0, 0);

    .n-base-selection-overlay__wrapper {
      color: #fff !important;
    }
  }
}

:deep(.n-base-selection-label) {
  background-color: #03152a !important;
}

:deep(.n-base-selection__border) {
  border: 1px solid #086cc4 !important;
}

:deep(.n-base-selection-input__content) {
  color: #8aa2c5 !important;
}

.com-header {
  z-index: 59;
}

.bg {
  background-image: url('./assets/bj.png');
  background-size: 100% 100%;
  position: relative;
}

.bottom {
  background-image: url('./assets/bottom.png');
  background-size: 100% 100%;
}

.head {
  position: absolute;
  overflow: hidden;
  background: url('./assets/title.png') center center no-repeat;
  background-size: 80% 80%;
  width: 100%;
  height: 5.46875vw;
  left: 0;

  .head-title {
    color: #fff;
    position: absolute;
    left: 50%;
    top: 35%;
    transform: translate(-50%, -50%);
    font-size: 40px;
    letter-spacing: 6px;
    font-family: 'youshe', sans-serif;
  }
}

.rightSide {
  background: url('./assets/right-side.png') center center no-repeat;
  background-size: 100% 100%;
  background-position: 6px 0px;
}

.leftSide {
  background: url('./assets/right-side.png') center center no-repeat;
  background-size: 100% 100%;
  transform: scaleX(-1);
  background-position: 6px 0px;
}

.left-arrow {
  position: absolute;
  left: 54%;
  top: 49.5%;
  transform: translate(50%, -50%);
  transform: scaleX(-1);
}

.right-arrow {
  position: absolute;
  left: 54%;
  top: 49.5%;
  transform: translate(50%, -50%);
  transform: scaleX(-1);
}

.date-picker :deep(.n-input) {
  background-color: rgba(0, 0, 0, 0);
}

.date-picker :deep(.n-input) {
  background-color: rgba(0, 0, 0, 0);
  --n-text-color: #fff !important;
  --n-icon-color: #fff !important;
}

.date-picker :deep(.n-input .n-input__border) {
  border-color: #086cc4;
}

.select :deep(.n-base-selection .n-base-selection-label) {
  background-color: rgba(0, 0, 0, 0);
  --n-text-color: #fff;
}
</style>
