<template>
  <div class="h-full content">
    <comTitle title="待处置隐患" />
    <div class="bottom-part" id="parent" @mouseenter="handleMouseenter" @mouseleave="handleMouseleave">
      <n-empty v-if="!hazardList.length" class="mt-[12%]"></n-empty>

      <div class="scroll-parent" v-if="hazardList.length">
        <n-scrollbar class="n-scrollbar-view" style="max-height: 100%" :on-scroll="onScrollFn">
          <div class="scroll" id="child">
            <div
              v-for="item in hazardList"
              :key="item.id"
              class="bottom-item"
              :style="{ padding: `${toVh(10)} ${toVh(12)}`, fontSize: toVh(14) }"
              @click="handleClick(item)"
            >
              <div class="">
                <div class="flex">
                  <div class="title" :style="{ fontSize: toVh(16) }">
                    {{ item.hazardDesc }}
                  </div>
                  <div class="level" :class="'level-' + item.gradeSort" :style="{ height: toVh(22) }">
                    {{ item.hazardLevelName }}
                  </div>
                </div>
                <div class="title2">
                  {{ item.hazardPosition }}
                </div>
                <div class="date">检查时间：{{ item.eventTime }}</div>
              </div>
              <div>
                <img :style="{ height: toVh(74) }" v-if="item.files" :src="getFileURL(item.files)" alt="" />
                <n-empty :style="{ height: toVh(74) }" v-else class="no-image" description="暂无图片">
                  <template #icon>
                    <n-icon>
                      <BsCardImage />
                    </n-icon>
                  </template>
                </n-empty>
              </div>
            </div>
          </div>
        </n-scrollbar>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import comTitle from '../../comTitle.vue';
import { onUnmounted, ref, nextTick } from 'vue';
import { getUnCheckHazard } from '../../fetchData';
import { toVw, toVh } from '@/utils/fit';
import { misToGisBridgeService } from '@/service/bridge/misToGisBridge';
import { misToGisTypes } from '@/service/bridge/types';
import { BsCardImage } from '@kalimahapps/vue-icons';
import { useCurrentUnit } from '@/store';
import getFileURL from '@/utils/getFileURL';

const currentUnit = useCurrentUnit();
const scrollHeight = ref(0);
misToGisBridgeService.init();

interface hazardList {
  id?: string;
  hazardDesc?: string;
  hazardLevelName?: string;
  gradeSort?: string;
  hazardTypeName?: string;
  eventTime?: string;
  hazardPosition?: string;
  files?: string;
}
const hazardList = ref<hazardList[]>([]);

let timer: any = null;
// 定时滚动的方法
function scrollFn() {
  // 计算出父盒子和子盒子的高度
  const child = document.querySelector('#child') as HTMLElement;
  const parent = document.querySelector('#parent') as HTMLElement;
  const parentHeight = parent.offsetHeight;
  const childHeight = child?.offsetHeight || 0;
  if (childHeight < parentHeight) return;

  // 检查之前是否有定时器，如果有则清除
  if (timer) clearInterval(timer);
  // 当有数据的时候才进行滚动
  if (hazardList.value.length <= 2) return;
  timer = setInterval(() => {
    scrollHeight.value += 1;

    //如果滚动的高度大于等于子盒子的高度，则重新设置为0
    if (scrollHeight.value >= childHeight - parentHeight) {
      scrollHeight.value = 0;
    }
    if (!document.querySelector('.n-scrollbar-view')?.querySelector('.n-scrollbar-container')) return;
    (document.querySelector('.n-scrollbar-view')?.querySelector('.n-scrollbar-container') as HTMLElement).scrollTop =
      scrollHeight.value;
    // (
    //   document.querySelector('.n-scrollbar-container') as HTMLElement
    // ).scrollTop = scrollHeight.value;
  }, 20);
}
// 鼠标移入父盒子停止滚动
const handleMouseenter = () => {
  clearInterval(timer);
};

// 鼠标移出父盒子开始滚动
const handleMouseleave = () => {
  // clearInterval(timer);
  scrollFn();
};

// 鼠标滚动触发的事件
const onScrollFn = (e: Event) => {
  scrollHeight.value = (e as any).target.scrollTop;
};
const openGis = () => {
  const base_url = window.$SYS_CFG.base_url;
  const gisWindow = window.open(`${base_url}/ehs-gis/index.html#/?project=hazardMgr`, 'hazardMgr-gisWindow');
  gisWindow?.blur();
  sessionStorage.setItem('isOpenGis', 'true');
};
const handleClick = (info: any) => {
  let data = {
    unitId: info.unitId,
    eventTime: info.eventTime,
    hazardLevel: info.hazardLevel,
  };
  openGis();
  misToGisBridgeService.sendEvents(misToGisTypes.SHOW_GIS_BUILD_DEVICE, { hazardInfo: JSON.stringify(info) });
};

const getList = ({ date }: { date: any }) => {
  hazardList.value = [];
  let data = {
    unitId: currentUnit.currentUnitId,
    // startTime: date ? `${date[0]} 00:00:00` : undefined,
    // endTime: date ? `${date[1]} 23:59:59` : undefined,
    startTime: date ? date[0] : undefined,
    endTime: date ? date[1] : undefined,
    likeFields: 'unitName,hazardTypeName,hazardLevelName',
    pageSize: 10,
    pageNo: 1,
  };
  getUnCheckHazard(data).then((res) => {
    hazardList.value = res.data.rows;
    nextTick(() => {
      scrollFn();
    });
  });
};

onUnmounted(() => {
  clearInterval(timer);
  const parent = document.querySelector('#parent') as HTMLElement;
  if (!parent) return;
  parent.onmouseenter = null;
  parent.onmouseleave = null;
});

const update = (data: any) => {
  getList(data);
};

defineExpose({ update });

defineOptions({ name: 'hidden_danger' });
</script>

<style lang="scss" scoped>
.content {
  // height: 200px;
}

.bottom-part {
  width: 100%;
  height: calc(100% - 36px);

  .scroll-parent {
    padding: 20px 0;
    width: 100%;
    height: 100%;
    overflow: hidden;

    .scroll {
      // overflow-y: auto;
      display: grid;
      grid-template-columns: 1;
      row-gap: 10px;
      width: 100%;
      // height: 100%;

      .bottom-item {
        width: 100%;
        // height: 94px;
        background: linear-gradient(0deg, #002e63 50%, #015194 100%);
        border: 1px solid #0f5ea3;
        padding: 10px 12px;
        display: flex;
        justify-content: space-between;
        font-family: Alibaba PuHuiTi;
        font-size: 14px;
        cursor: pointer;

        .title {
          font-weight: 500;
          font-size: 16px;
          color: #ffffff;
          //超出一行省略号
          max-width: 200px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        .level {
          font-weight: 400;
          color: #ffffff;
          width: 80px;
          height: 22px;
          border-radius: 3px;
          padding-left: 10px;
          clip-path: polygon(0% 100%, 8px 0%, 100% 0%, 100% 0, calc(100% - 8px) 100%, 8px 100%);
        }

        .level-0 {
          background: linear-gradient(0deg, #9f0025 0%, #ff3e6c 100%);
          border: 1px solid #fa3d6a;
        }

        .level-1 {
          background: linear-gradient(0deg, #808005 0%, #d5d50b 100%);
          border: 1px solid #cccc00;
        }

        .level-2 {
          background: linear-gradient(0deg, #026409 0%, #2fe048 100%);
          border: 1px solid #00de3b;
        }

        .level-3 {
          background: linear-gradient(0deg, #a64600 0%, #ff7800 100%);
          border: 1px solid #ff9600;
        }

        .level-4 {
          background: linear-gradient(0deg, #023a64 0%, #2f96e0 100%);
          border: 1px solid #549fe5;
        }

        .title2 {
          margin: 8px 0;
          font-weight: 400;
          color: #dee6f0;
        }

        .date {
          font-family: Alibaba;
          font-weight: normal;
          color: #dee6f0;
        }

        img,
        .no-image {
          // width: 98px;
          height: 74px;
          border-radius: 2px;
        }
      }
    }
  }
}
</style>
