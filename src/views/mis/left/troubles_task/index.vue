<template>
  <div class="content mt-[8px]">
    <comTitle title="近期排查任务" class="mb-[16px]" />
    <div class="flex justify-between">
      <div style="flex: 1">
        <!-- <n-scrollbar :style="{'height': toVh(140)}"> -->
        <n-empty class="mw-[200px] pt-[10%]" v-if="!list.length" description="暂无数据" />
        <div class="detial-list" :style="{ height: toVh(180) }">
          <div v-for="item of list" :key="item.taskId" class="list-item" @click="itemClick(item)">
            {{ item.planStartTime }} {{ item.planName }}
          </div>
        </div>
        <!-- </n-scrollbar> -->
        <div class="flex justify-center" v-if="list.length" style="cursor: pointer" @click="clickMore">
          <img src="./更多.png" alt="更多" title="更多" />
        </div>
      </div>
      <div class="chart-wrapper" style="flex: 2">
        <div ref="chartContainer" style="width: 100%; height: 100%"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onBeforeMount, onUnmounted, watch } from 'vue';
import * as echarts from 'echarts';
import { toVw, toVh } from '@/utils/fit';
import comTitle from '../../comTitle.vue';
import { getTaskCount, getTaskRecentList } from '../../fetchData';
import useResize from '../../useResize';
import { useRouter } from 'vue-router';
import { useStore, useCurrentUnit } from '@/store';
const { userInfo } = useStore();
const currentUnit = useCurrentUnit();
// watch(
//   () => currentUnit.currentUnitId,
//   (val) => {
//     // alert(val);
//     getList();
//   }
// );

const router = useRouter();
interface List {
  taskId?: string;
  planName?: string;
  planStartTime?: string;
}

interface dataList {
  completeTotalList: string[];
  dateList: string[];
  totalList: string[];
}
const list = ref<List[]>([]);

const dataObj = ref<dataList>({
  completeTotalList: [],
  dateList: [],
  totalList: [],
});

const dateTime = ref<any>();

const getList = ({ date }: { date: any }) => {
  if (!date) {
    dataObj.value = {
      completeTotalList: [],
      dateList: [],
      totalList: [],
    };
    months.value = [];
    return updateChart();
  }

  dateTime.value = date;
  let data = {
    unitIds: currentUnit.currentUnitId,
    startTime: date ? `${date[0]} 00:00:00` : undefined,
    endTime: date ? `${date[1]} 23:59:59` : undefined,
  };
  getTaskRecentList(data).then((res) => {
    list.value = res.data;
  });
  getTaskCount(data).then((res) => {
    dataObj.value = res.data;
    months.value = dataObj.value.dateList;
    updateChart();
  });
};

const itemClick = (row: any) => {
  router.push({
    path: 'task-management/hazard-task-detail/' + row.taskId,
  });
};
const clickMore = () => {
  console.log(dateTime.value);
  router.push({
    path: 'task-management',
    query: {
      dateTime: JSON.stringify(dateTime.value),
    },
  });
};

const chartContainer = ref<HTMLDivElement | null>(null);
let myChart: echarts.ECharts | null = null;
let resizeObserver: ResizeObserver | null = null;

const updateChart = () => {
  if (!myChart) myChart = echarts.init(chartContainer.value);
  myChart.setOption(chartOption.value);
};

// 假设最近五个月的时间和数据
// const months = ['2024-05', '2024-06', '2024-07', '2024-08', '2024-09']; // 最近五个月
const months = ref<any[]>([]); // 最近五个月
const reportData = [10, 15, 12, 20, 25]; // 隐患上报数
const rectifyData = [8, 14, 10, 18, 22]; // 隐患整改数

const chartOption = computed(() => {
  return {
    //   backgroundColor: '#101736', // 背景色
    title: {
      // text: '隐患上报与整改统计',
      textStyle: {
        color: '#ffffff', // 标题文字颜色
      },
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        lineStyle: {
          color: '#57617B',
        },
      },
    },
    legend: {
      data: ['排查任务数', '任务执行数'],
      textStyle: {
        color: '#ffffff', // 图例文字颜色
        fontSize: 10,
      },
      itemWidth: 20,
      itemHeight: 3,
      height: '3px',
      right: '10%', // 图例靠右
      top: '5%', // 图例靠上
      icon: 'rect',
    },
    grid: {
      left: '10%',
      right: '10%',
      bottom: '10%',
      top: '20%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: months.value, // x轴展示最近五个月
      axisLabel: {
        textStyle: {
          color: '#ffffff', // 坐标轴刻度标签颜色
          fontSize: 10,
        },
      },
      axisLine: {
        lineStyle: {
          color: '#57617B',
        },
      },
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        textStyle: {
          color: '#ffffff', // Y轴刻度标签颜色
        },
      },
      axisLine: {
        lineStyle: {
          color: '#57617B',
        },
      },
      splitLine: {
        lineStyle: {
          color: '#57617B',
        },
      },
    },
    series: [
      {
        name: '排查任务数',
        type: 'line',
        smooth: true,
        lineStyle: {
          color: '#00BBDD', // 上报数线条颜色
          width: 2,
        },
        itemStyle: {
          color: '#00BBDD', // 上报数节点颜色
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(12,192,0 ,0.3)',
            },
            {
              offset: 1,
              color: 'rgba(12,192,0 ,0)',
            },
          ]),
        },
        // data: reportData, // 上报数数据
        data: dataObj.value.totalList, // 上报数数据
      },
      {
        name: '任务执行数',
        type: 'line',
        smooth: true,
        lineStyle: {
          color: '#1A85FF', // 整改数线条颜色
          width: 2,
        },
        itemStyle: {
          color: '#1A85FF', // 整改数节点颜色
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(26,133,255,0.3)',
            },
            {
              offset: 1,
              color: 'rgba(26,133,255,0)',
            },
          ]),
        },
        // data: rectifyData, // 整改数数据
        data: dataObj.value.completeTotalList, // 上报数数据
      },
    ],
  };
});

onUnmounted(() => {
  // if (resizeObserver && chartContainer.value) {
  //   resizeObserver.unobserve(chartContainer.value);
  // }
  if (myChart) {
    myChart.dispose();
    myChart = null;
  }
});

const update = (data: any) => {
  getList(data);
};

useResize(chartContainer, () => {
  if (myChart) myChart.resize();
});
defineExpose({ update });

defineOptions({ name: 'troubles_task' });
</script>

<style lang="scss" scoped>
.content {
  color: #fff;
  .detial-list {
    height: 123px;
    overflow: auto;
    &::-webkit-scrollbar {
      display: none;
    }
  }
  .list-item {
    margin-bottom: 6px;
    padding-left: 6px;
    max-width: 260px;
    height: 25px;
    line-height: 25px;
    background: linear-gradient(0deg, #002e63 0%, #015194 100%);
    border-radius: 2px;
    border: 1px solid #055499;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;
  }
}
.chart-wrapper {
  width: 100%;
  height: 180px;
  position: relative;
}
</style>
