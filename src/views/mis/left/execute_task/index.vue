<template>
  <div class="w-[100%] py-[0] h-full">
    <comTitle :title="taskType == 1 ? '正在执行任务' : '历史任务'" />

    <div class="flex flex-col h-full">
      <div class="title" :style="{ marginTop: toVh(6), fontSize: toVh(16) }">
        <div>{{ dataObj.planName || '-' }}</div>
        <div class="changebtn">
          <button @click="dwon">
            <div class="btnicon"></div>
            切换
          </button>
        </div>
      </div>
      <div class="flex justify-between" :style="{ margin: `${toVh(10)} 0` }">
        <div class="header-item" :style="{ height: toVh(32), lineHeight: toVh(32), fontSize: toVh(14) }">
          <div class="san_jiao"></div>
          检查类型：{{ dataObj.planTypeName || '-' }}
        </div>
        <div class="header-item" :style="{ height: toVh(32), lineHeight: toVh(32), fontSize: toVh(14) }">
          <div class="san_jiao"></div>
          检查人员：{{ dataObj.fzrs || '-' }} {{ dataObj.cyrs ? ',' + dataObj.cyrs : '' }}
        </div>
      </div>
      <div>
        <div class="step grid grid-cols-3">
          <div v-for="(item, index) of stepsList" :key="index" class="flex justify-center">
            <div class="xh" :class="item.date ? 'active_xh' : ''">{{ index + 1 }}</div>
            <div :class="item.date ? 'active_text' : ''">{{ item.title }}</div>
          </div>
        </div>

        <div class="step_img" :style="{ margin: `${toVh(5)} 0` }">
          <img src="./assets/step_bg.png" alt="line" class="w-[100%] h-[18px]" :style="{ height: toVh(18) }" />
          <div class="icon_view left" @click="upper">
            <img class="icon mt-[5px]" src="./assets/active_icon.png" alt="line" title="上一个" />
          </div>
          <div class="icon_view right" @click="dwon">
            <img class="icon mt-[9px]" src="./assets/active_icon.png" alt="line" title="下一个" />
          </div>
          <!-- 12.5 37.5  -->
          <div class="step_bg" :style="{ width: stepWidth() }"></div>
        </div>

        <div class="grid grid-cols-3 mt-[-16px]">
          <div v-for="(item, index) of stepsList" :key="index" class="flex justify-center">
            <img v-if="item.date" src="./assets/step_icon2.png" alt="" />
            <img v-else src="./assets/step_icon1.png" alt="" />
          </div>
        </div>

        <div class="grid grid-cols-3 text-[14px] text-[#C5D3E8]">
          <div v-for="(item, index) of stepsList" :key="index" class="flex justify-center">
            {{ item.date ? item.date : '-' }}
          </div>
        </div>
      </div>
      <div class="flex justify-between" :style="{ marginTop: toVh(6) }">
        <div class="btn-1 flex items-center flex-col justify-center" :style="{ height: toVh(70) }">
          <div class="">已排查隐患数量</div>
          <div class="text-[22px]" :style="{ marginTop: toVh(0) }">
            {{ (list.length && list.map((item) => item.total).reduce((a: any, b: any) => a + b, 0)) || 0 }}
          </div>
        </div>
        <div class="btn-2 flex items-center justify-center" :style="{ height: toVh(70) }">
          <!-- class="grid grid-cols-5" -->
          <div class="grid" :class="`grid-cols-${list.length}`">
            <div v-for="(item, index) of list" :key="index" style="text-align: center">
              <div class="hazardLevelName" :title="item.hazardLevelName">{{ item.hazardLevelName }}</div>
              <div
                class="text-[22px]"
                :style="{ color: ['#FA3D6A', '#CCCC00', '#00DE3B', '#FF9600', '#549FE5'][index], marginTop: toVh(0) }"
              >
                {{ item.total }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import comTitle from '../../comTitle.vue';
import { hazardTaskPage, getHazardPlanDetai } from '../../fetchData';
import { getGradeList } from '@/views/paramsConf/comp/fetchData';
import { toVw, toVh } from '@/utils/fit';
import { listProps } from 'naive-ui';
import { useStore, useCurrentUnit } from '@/store';
import { levelStatisticsMeger } from '@/views/hazard-management/fetchData';
const { userInfo } = useStore();
const currentUnit = useCurrentUnit();

const stepsList = ref([
  {
    title: '计划下发',
    date: '',
  },
  {
    title: '任务执行',
    date: '',
  },
  {
    title: '任务完成',
    date: '',
  },
]);
const resetFrom = () => {
  stepsList.value = [
    {
      title: '计划下发',
      date: '',
    },
    {
      title: '任务执行',
      date: '',
    },
    {
      title: '任务完成',
      date: '',
    },
  ];
  taskList.value = [];
  dataObj.value = {};
  list.value = [];
  taskId.value = '';
};
const stepWidth = () => {
  const length = stepsList.value.filter((item) => item.date).length;
  if (length >= 3) {
    return '100%';
  } else {
    return `${-16.65 + length * 33.3}%`;
  }
};
interface taskIdObj {
  taskId?: string;
}
// 计划列表
const taskList = ref<taskIdObj[]>([]);
// 详情
interface obj {
  planName?: string; //检查计划名称
  planTypeName?: string; //检查类型
  fzrs?: string; //检查负责人
  cyrs?: string; //检查参与人
  disposedNum?: string; // 已排查隐患数量
}
const dataObj = ref<obj>({});

interface listProps {
  hazardLevelName?: string;
  total?: number;
}
// 隐患列表
const list = ref<listProps[]>([]);
// 任务类型
const taskType = ref(1);
const getList = ({ date }: { date: any }) => {
  resetFrom();
  taskType.value = 1;
  let data = {
    unitId: currentUnit.currentUnitId,
    planStartDate: date ? `${date[0]} 00:00:00` : undefined,
    planEndDate: date ? `${date[1]} 23:59:59` : undefined,
    taskState: 2,
    pageNo: 1,
    pageSize: 3,
  };
  hazardTaskPage(data).then((res: any) => {
    taskList.value = res.data.rows;
    // 没有当前任务查询历史任务
    if (!res.data.rows.length) getHistory(data);
    if (res.data.rows.length) getDetail(res.data.rows[0].taskId);
  });
};
// 查看历史任务
const getHistory = (params: any) => {
  taskType.value = 2;
  let data = {
    ...params,
    taskState: 3,
  };
  hazardTaskPage(data).then((res: any) => {
    taskList.value = res.data.rows;
    if (res.data.rows.length) return getDetail(res.data.rows[0].taskId);
    // 历史任务无数据时，需默认展示隐患等级，数据为0
    getGradeList({
      pageNo: 1,
      pageSize: 9999,
      unitId: userInfo.topUnitId,
    }).then((res: any) => {
      list.value = res.data.rows?.map((item: any) => {
        return {
          hazardLevelName: item.gradeName,
          total: 0,
        };
      });
    });
  });
};
//任务id
const taskId = ref<string>('');
const getDetail = (id: string) => {
  taskId.value = id;
  getHazardPlanDetai({ taskId: id }).then((res) => {
    dataObj.value = res.data;
    stepsList.value[0].date = res.data.createTime;
    stepsList.value[1].date = res.data.beginTime;
    stepsList.value[2].date = res.data.finishTime;
    levelStatisticsMeger({ businessId: id, unitId: res.data.unitIds }).then(({ data }) => {
      list.value = data.slice(0, 5);
    });
  });
};

// 上一个
const upper = () => {
  if (!taskList.value.length) return;
  const currentIndex = taskList.value.findIndex((item) => item.taskId === taskId.value);
  if (currentIndex === 0) {
    // 如果当前是第一个，那么上一个就是最后一个
    getDetail(taskList.value[taskList.value.length - 1].taskId);
  } else {
    // 否则，获取前一个的taskId
    getDetail(taskList.value[currentIndex - 1].taskId);
  }
};

// 下一个
const dwon = () => {
  if (!taskList.value.length) return;
  const currentIndex = taskList.value.findIndex((item) => item.taskId === taskId.value);
  if (currentIndex === taskList.value.length - 1) {
    // 如果当前是最后一个，那么下一个就是第一个
    getDetail(taskList.value[0].taskId);
  } else {
    // 否则，获取下一个的taskId
    getDetail(taskList.value[currentIndex + 1].taskId);
  }
};

const update = (data: any) => {
  getList(data);
};

defineExpose({ update });

defineOptions({ name: 'excute_task' });
</script>

<style lang="scss" scoped>
.title {
  // border: 1px solid red;
  background: url(./assets/title.png) no-repeat;
  // background-size: 100% 100%;
  background-size: 70% 95%;
  height: 32px;
  width: 345px;
  // margin-top: 6PX;
  font-family: Alibaba PuHuiTi;
  font-weight: 500;
  font-size: 16px;
  color: #ffffff;
  line-height: 34px;
  padding-left: 25px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;

  display: flex;
  justify-content: space-between;

  // align-items: center;
  .changebtn {
    color: white;

    button {
      height: 26px;
      width: 60px;
      border: 1px solid #0e5390;
      line-height: 25px;
      display: flex;
      align-items: center;
      justify-content: space-evenly;
      font-size: 12px;
      background-color: #032240;
      color: #329bed;
      margin-top: 5px;
    }

    .btnicon {
      width: 16px;
      height: 13px;
      background: url('./assets/change.png');
      background-size: 100% 100%;
    }
  }
}

.hazardLevelName {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.header-item {
  width: calc(50% - 20px);
  height: 32px;
  background: rgba(19, 50, 85, 0.5);
  border: 1px solid #0e5390;
  position: relative;
  overflow: hidden;

  font-family: Alibaba;
  font-weight: normal;
  font-size: 14px;
  color: #ffffff;
  line-height: 32px;
  padding-left: 10px;

  .san_jiao {
    position: absolute;
    right: -3px;
    top: -3px;
    width: 6px;
    height: 6px;
    background: #0f69b9;
    transform: rotate(45deg);
  }
}

.step_img {
  // margin: 5px 0;
  position: relative;
  // background: url('./step_bg.png');
  // width: 100%;
  clip-path: polygon(0% 50%, 10px 0%, calc(100% - 10px) 0%, 100% 50%, calc(100% - 10px) 100%, 10px 100%);
  overflow: hidden;

  .icon_view {
    width: 20px;
    height: 20px;
    cursor: pointer;
  }

  .left {
    position: absolute;
    z-index: 999999;
    top: -1px;
    left: 10px;
  }

  .right {
    position: absolute;
    top: 0;
    right: 10px;
    transform: rotate(180deg);
  }

  .step_bg {
    margin-top: -24px;
    height: 20px;
    background: linear-gradient(0deg, #0e2c56 0%, #0693e6 100%) !important;
  }
}

.step {
  color: #bad9ff;

  img {
    background: linear-gradient(180deg, #0c94f6 0%);
    border: 1px solid;
    border-image: linear-gradient(180deg, #0c94f6) 10 10;
  }

  .active_text {
    color: #ffffff !important;
  }

  .active_xh {
    background: linear-gradient(0deg, #0e2c56 0%, #0693e6 100%) !important;
  }

  .xh {
    width: 18px;
    height: 18px;
    line-height: 18px;
    text-align: center;
    background: linear-gradient(0deg, #2c394a 0%, #6c83a0 100%);
    border-radius: 50%;
    border: 1px solid #839bb9;
    margin-right: 6px;
  }
}

.btn-1 {
  background: url(./assets/box_bg1.png) no-repeat;
  background-size: 100% 100%;
  // width: calc(100% - 10px);
  width: 140px;
  height: 88px;
  // padding: 14px;
  color: #fff;
  text-align: center;
}

.btn-2 {
  background: url(./assets/box_bg2.png) no-repeat;
  background-size: 100% 100%;
  width: calc(100% - 160px);
  // padding: 14px;
  color: #fff;
}
</style>
