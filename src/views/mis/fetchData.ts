// import { ICheckTempPageRes, ICheckItem, ICheckCategoryTree, IFormData } from './type';
import { IObj, IPageRes } from '@/types';
// import { PageList, statisticsData } from './type';
import { api } from '@/api';
import { $http } from '@tanzerfe/http';

// 近期任务排查
export function getTaskRecentList(params: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.mis.getTaskRecentList, params);
  return $http.post<any>(url, { data: { _cfg: { showTip: true }, ...params } });
}
// 近期任务排查 任务排查数、任务执行数
export function getTaskCount(params: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.mis.getTaskCount, params);
  return $http.post<any>(url, { data: { _cfg: { showTip: true }, ...params } });
}

// 近期排查任务 列表
export function hazardTaskPage(params: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.mis.hazardTaskPage, params);
  return $http.post<any>(url, { data: { _cfg: { showTip: true }, ...params } });
}
// 近期排查任务-任务详情
export function getHazardPlanDetai(params: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.mis.getHazardPlanDetai, params);
  return $http.post<any>(url, { data: { _cfg: { showTip: true } } });
}
// 近期排查任务-隐患统计

// 待处置隐患
export function getUnCheckHazard(params: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.mis.getUnCheckHazard, params);
  return $http.post<any>(url, { data: { _cfg: { showTip: true }, ...params } });
}
// 隐患整改时长
export function getEssentialFactorDisposeTimeGroup(params: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.mis.getEssentialFactorDisposeTimeGroup, params);
  return $http.post<any>(url, { data: { _cfg: { showTip: true }, ...params } });
}

// 隐患点TOP
export function getEssentialFactorTypeGroup(params: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.mis.getEssentialFactorTypeGroup, params);
  return $http.post<any>(url, { data: { _cfg: { showTip: true }, ...params } });
}
// 位置分布
export function getTopPosition(params: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.name.hazard.getTopPosition, params);
  return $http.post<any>(url, { data: { _cfg: { showTip: true }, ...params } });
}
// 隐患类型
export function getDangerUnitCount(params: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.name.hazard.getHazrdSourceStastics, params);
  return $http.post<any>(url, { data: { _cfg: { showTip: true }, ...params } });
}
// 隐患统计
export function getTopStastics(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.name.hazard.getTopStastics, query);
  return $http.post(url, { data: { _cfg: { showTip: true } } });
}

export function getHazrdTypeStastics(params: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.mis.getHazrdTypeStastics, params);
  return $http.post<any>(url, { data: { _cfg: { showTip: true }, ...params } });
}

export function getAllUnit(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.getAllUnit);
  return $http.post<any>(url, { data: { _cfg: {}, ...query } });
}

// 获取组织机构树
export function getOrgTree(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.public.getOrgTree, { ...query });
  return $http.post<any[]>(url, { data: { _cfg: { showTip: true } } });
}

//获取天气
export function getRealWeatherInfoByDistinctCode(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.public.getRealWeatherInfoByDistinctCode, { ...query });
  return $http.post<any[]>(url, { data: { _cfg: { showTip: true } } });
}
export function getUnitInfobyOrgCode(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.public.changeOrgCode, { ...query });
  return $http.get<any[]>(url, { data: { _cfg: { showTip: true } } });
}

export function getNewWeather(query: IObj<any>) {
  const url = api.getUrl(api.type.weather, api.name.weather.nowWeather, { ...query });
  return $http.get<any[]>(url, { data: { _cfg: { showTip: true } } });
}
