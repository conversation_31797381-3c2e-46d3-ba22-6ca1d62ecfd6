<template>
  <div class="flex">
    <div class="w-[155px] mr-[24px]">
      <div :class="$style['sfm']">{{ currentTime }}</div>
      <div :class="$style['day']">
        {{ currentDay }}<span class="ml-[12px]">{{ currentWeek }}</span>
      </div>
    </div>
    <div class="flex-1">
      <div class="w-[26px]">
        <!-- <img :class="$style['icon-weather']" :src="getWeatherIcon(info.weatherImg)" alt="weather" /> -->
      </div>
      <!-- <div class="ml-[28px]">
        <div class="flex mt-[3px] text-[#fff]">
          <div :class="$style['temperature']" class="min-w-[61px]">{{ info.weatherTemperature }}℃</div>
          <div class="mx-[6px]">|</div>
          <div class="min-w-[88px] text-center">{{ info.windDirect + '/' + info.windPower }}</div>
          <div class="mx-[6px]">|</div> -->
      <!-- <div class="min-w-[96px] text-center">空气质量 {{ info.weatherFeelst }}</div> -->
      <!-- </div>
        <div :class="$style['weather-bottom']" class="flex">
          <div class="min-w-[61px] text-center">降水 {{ info.weatherRain }} mm</div>
          <div class="mx-[6px]">|</div>
          <div class="min-w-[96px] text-center">风速 {{ info.windSpeed }} m/s</div> -->
      <!-- <div class="mx-[6px]">|</div>
          <div class="min-w-[88px] text-center">紫外线 较弱</div> -->

      <!-- <div class="mx-[6px]">|</div>
          <div class="min-w-[96px] text-center">紫外线 较弱</div> -->
      <!-- </div> -->

      <!-- </div> -->
      <div class="ml-[28px] text-[#fff]">
        <div class="flex mt-[3px]">
          <div :class="$style['temperature']" class="min-w-[61px]">{{ info.temp }}℃</div>
          <div class="mx-[6px]">|</div>
          <div class="min-w-[88px] text-center">{{ info.windDir + '/' + getWindLevel(info.windScale) }}</div>
          <!-- <div class="mx-[6px]">|</div>
          <div class="min-w-[96px] text-center">空气质量 {{ info.weatherFeelst }}</div> -->
        </div>
        <div :class="$style['weather-bottom']" class="flex">
          <div class="min-w-[61px] text-center">降水 {{ info.precip }}MM</div>
          <div class="mx-[6px]">|</div>
          <div class="min-w-[88px] text-center">风速 {{ info.windSpeed }} KM/H</div>
          <!-- <div class="mx-[6px]">|</div>
          <div class="min-w-[96px] text-center">紫外线 较弱</div> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onBeforeUnmount, ref, watch } from 'vue';
import dayjs from 'dayjs';
import icon from './icon.ts';
import { getRealWeatherInfoByDistinctCode, getUnitInfobyOrgCode, getNewWeather } from '../../../fetchData.ts';
import { useStore, useCurrentUnit } from '@/store';

const userinfo = useStore().userInfo;
const currentUnit = useCurrentUnit();

const currentTime = ref();
const currentDay = ref();
const currentWeek = ref();
const interval = ref();
// const info = ref({
//   stationCode: 'xqcji',
//   stationCity: '合肥',
//   staticonProvince: '安徽省',
//   publishTime: '2024-09-29 16:45:00',
//   weatherTemperature: '29.7',
//   weatherTemperatureDiff: '9999.0',
//   weatherAirpressure: '9999.0',
//   weatherHumidity: '56.0',
//   weatherRain: '0.0',
//   weatherRcomfort: '72',
//   weatherIcomfort: '1',
//   weatherInfo: '晴',
//   weatherImg: '0',
//   weatherFeelst: '31.4',
//   windDirect: '东南风',
//   windPower: '3级',
//   windSpeed: '3.6',
//   createTime: '2024-09-29 16:59:29',
//   lastUpdateTime: '2024-09-29 16:59:29',
// });
const defaultInfo = {
  vis: '--',
  temp: '--',
  obsTime: '--',
  icon: '--',
  wind360: '--',
  windDir: '--',
  pressure: '--',
  feelsLike: '--',
  cloud: '--',
  precip: '--',
  dew: '--',
  humidity: '--',
  text: '--',
  windSpeed: '--',
  windScale: '--',
  province: '--',
  city: '--',
  county: '--',
  warnText: '',
};
const info = ref({ ...defaultInfo });
function updateTime() {
  const now = new Date();
  currentTime.value = now.toLocaleTimeString();
  currentDay.value = dayjs(now).format('YYYY-MM-DD');
  currentWeek.value = toFormat(dayjs(now).day());
}

function toFormat(str: number) {
  switch (str) {
    case 1:
      return '星期一';
    case 2:
      return '星期二';
    case 3:
      return '星期三';
    case 4:
      return '星期四';
    case 5:
      return '星期五';
    case 6:
      return '星期六';
    default:
      return '星期日';
  }
}

const getWindLevel = (val: string) => {
  switch (val) {
    case '0':
      return '无风';
    case '1':
      return '软风';
    case '2':
      return '轻风';
    case '3':
      return '微风';
    case '4':
      return '和风';
    case '5':
      return '清风';
    case '6':
      return '强风';
    case '7':
      return '疾风';
    case '8':
      return '大风';
    case '9':
      return '烈风';
    case '10':
      return '狂风';
    case '11':
      return '暴风';
    case '12':
      return '飓风';
    default:
      return '--';
  }
};

const getUnitInfo = async () => {
  // 优先获取切换单位的 currentOrgcode，如果没有的话，使用 mis 项目传递过来的 orgCode
  // let res: any;
  // let distinctCode = '';
  // if (!currentUnit.currentUnitId) return;
  // try {
  //   res = await getUnitInfobyOrgCode({
  //     orgCode: currentUnit.currentUnitId,
  //   });
  //   if (res.code === 'success') {
  //     distinctCode = res.data.city ?? res.data.province;
  //   }
  // } catch (error) { }

  // // console.log('🚀 ~ getUnitInfo ~ res:', res);
  // // province 省  city 市  county 区
  // if (!distinctCode) return;
  // getRealWeatherInfoByDistinctCode({ distinctCode: distinctCode }).then((res: any) => {
  //   info.value = res.data;
  // });
  // getUnitDetail(parms).then((res: any) => {
  //   if (res.code == 'success') {
  //     let distinctCode = res.data?.city;
  //     if (distinctCode) {

  //     }
  //   }
  // });
  const res = await getNewWeather({
    orgCode: currentUnit.currentUnitId,
  });

  info.value = res.data ? res.data : { ...defaultInfo };
};

watch(
  () => useCurrentUnit().currentUnitId,
  (nv) => {
    if (nv) {
      getUnitInfo();
    }
  },
  { immediate: true }
);

function init() {
  interval.value = setInterval(updateTime, 1000);
  getUnitInfo();
}

// 天气图标
function getWeatherIcon(val: string) {
  if (val) {
    return icon[`weather_${Number(val) + 1}` as keyof typeof icon];
  }
}

init();

onBeforeUnmount(() => clearInterval(interval.value));

defineOptions({ name: 'WeatherComp' }); // 天气组件
</script>

<style module>
.sfm {
  font-weight: 500;
  font-size: 20px;
  color: #ffffff;
}

.day {
  margin-top: -6px;
  font-weight: 400;
  font-size: 16px;
  color: #ffffff;
}

.icon-weather {
  width: 25px;
  height: 26px;
  position: absolute;
  top: 40%;
  transform: translateY(-50%);
  /* 向左向上移动自身宽高的一半 */
}

.weather-bottom {
  margin-top: 1px;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
}

.temperature {
  text-align: center;
  font-weight: 500;
  font-size: 20px;
  line-height: 20px;
}
</style>
