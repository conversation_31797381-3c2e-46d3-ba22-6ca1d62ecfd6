<template>
  <div class="panel-center" ref="viewportHeight">
    <div class="card-list pt-[20px]" ref="cardListBox">
      <p @click="clickCard(0)">
        <span>{{ cardList[0].num }}</span>
        <span>{{ cardList[0].text }}</span>
      </p>
      <p @click="clickCard(1)">
        <span>{{ cardList[1].num }}</span>
        <span>{{ cardList[1].text }}</span>
      </p>
      <p @click="clickCard(2)">
        <span>{{ cardList[2].num }}</span>
        <span>{{ cardList[2].text }}</span>
      </p>
      <p>
        <span>
          <span>{{ cardList[3].num }}</span>
          <span style="font-size: 16px">%</span>
        </span>
        <span>{{ cardList[3].text }}</span>
      </p>
    </div>
    <div class="bubble-box" ref="bubbleBox" :class="[!show ? 'max-h-[66%]' : '']">
      <div class="bubble-left">
        <p
          :style="{ top: info.top, left: info.left }"
          class="left-bg1"
          v-for="(info, i) in bubbleInfo.infoLeft"
          :key="i"
        >
          <span class="info-content">
            <!--<span class="mr-[10px]">{{ info.num }}</span>-->
            <span>{{ info.proportion + '%' }}</span>
          </span>
          <span class="text-nowrap">{{ info.hazardSourceName && info.hazardSourceName.split('-')[0] }}</span>
        </p>
        <div class="total-info left-big1" style="top: 27%; left: 33%">
          <span class="info-content">
            <!--<span class="mr-[10px]">{{ bubbleInfo.leftTotal.num }}</span>-->
            <span>{{ bubbleInfo.leftTotal.proportion + '%' }}</span>
          </span>
          <span class="text-nowrap">{{ bubbleInfo.leftTotal.hazardSourceName }}</span>
        </div>
        <div class="hidden-list"></div>
      </div>
      <div class="bubble-right">
        <p
          :style="{ top: info.top, right: info.right }"
          class="right-bg1"
          v-for="(info, i) in bubbleInfo.infoRight"
          :key="i"
        >
          <span class="info-content">
            <!-- <span class="mr-[10px]">{{ info.num }}</span>-->
            <span>{{ info.proportion + '%' }}</span>
          </span>
          <span class="text-nowrap">{{ info.hazardSourceName }}</span>
        </p>
        <div class="total-info right-big1" style="top: 29%; right: 33%">
          <span class="info-content">
            <!--<span class="mr-[10px]">{{ bubbleInfo.rightTotal.num }}</span>-->
            <span>{{ bubbleInfo.rightTotal.proportion + '%' }}</span>
          </span>
          <span class="text-nowrap">{{ bubbleInfo.rightTotal.hazardSourceName }}</span>
        </div>
      </div>
    </div>
    <div class="hidden-list-box" v-if="show">
      <comTitle title="隐患位置分布" class="mb-[16px]" />
      <div class="hidden-list">
        <ul v-if="hiddenList.infoList?.length > 0">
          <li></li>
          <!-- <li></li> -->
          <li>{{ hiddenList.titleList.text0 }}</li>
          <li>{{ hiddenList.titleList.text1 }}</li>
          <li>{{ hiddenList.titleList.text2 }}</li>
          <li>{{ hiddenList.titleList.text3 }}</li>
          <li>{{ hiddenList.titleList.text4 }}</li>
        </ul>
        <div class="unit-list overflow-auto" ref="unitList" style="overflow-y: auto">
          <template v-if="hiddenList.infoList?.length > 0">
            <ul v-for="(info, index) in [...hiddenList.infoList]" :key="index">
              <li class="finger"></li>
              <li class="unitName" :title="info.hazardPosition?.length > 13 ? info.hazardPosition : ''">
                {{ info.hazardPosition || '（空）' }}
              </li>
              <li>
                <PercentageDisplayVue type="one" :percentage="info.totalNum" :tip="info.totalNum" color="#4099FF" />
              </li>
              <li>
                <PercentageDisplayVue
                  type="one"
                  :percentage="info.unDisposeNum"
                  :tip="info.unDisposeNum"
                  color="#DE9513"
                />
              </li>
              <li>
                <PercentageDisplayVue type="one" :percentage="info.overdueNum" :tip="info.overdueNum" color="#CE4A4C" />
              </li>
              <li>
                <p>
                  <PercentageDisplayVue
                    type="one"
                    :percentage="Math.round(info.disposedRate * 100)"
                    :tip="Math.round(info.disposedRate * 100)"
                    color="#07BFB8"
                    unit="%"
                  />
                </p>
                <span>{{ Math.round(info.disposedRate * 100) + '%' }}</span>
              </li>
            </ul>
          </template>
          <n-empty v-else class="mt-[10%]"></n-empty>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, onMounted, onBeforeUnmount, computed } from 'vue';
import LabelStack from './LabelStack.vue';
import comTitle from '../comTitle.vue';
import PercentageDisplayVue from './PercentageDisplay.vue';
import { useStore, useCurrentUnit } from '@/store';
import { getTopPosition, getDangerUnitCount, getTopStastics } from '../fetchData';
// import router from '~/router';
const { userInfo: ui } = useStore();
const currentUnit = useCurrentUnit();
const props = defineProps({
  rangeDate: {
    type: Array<string>,
    default: () => [],
  },
  activeAll: {
    type: Number,
    default: 0,
  },
  showPanel: {
    type: Boolean,
    default: false,
  },
});
const show = computed(() => {
  console.log('🚀 ~ show ~ props.showPanel:', props.showPanel);
  return props.showPanel;
});
const emits = defineEmits(['changeActive']);
const active = ref(0);
/****顶部card列表*****/
let cardList = reactive([
  { num: 0, text: '上报数', code: 'totalNum' },
  { num: 0, text: '未整改完成数', code: 'undisposeNum' },
  { num: 0, text: '超期未整改完成数', code: 'overdueNum' },
  { num: '0%', text: '整改完成率', code: 'disposedRate' },
]);

function clickCard(n: number) {
  emits('changeActive', n);
  active.value = n;
}

const bubbleInfo: any = ref({
  infoLeft: [],
  leftTotal: {
    hazardSource: '',
    hazardSourceName: '隐患排查',
    num: '0',
    proportion: '0',
  },

  infoRight: [],
  rightTotal: {
    hazardSource: '',
    hazardSourceName: '智能识别',
    num: '0',
    proportion: '0',
  },
});

const leftPositionList = [
  {
    top: '1%',
    left: '24%',
  },
  {
    top: '25%',
    left: '-9%',
  },
  {
    top: '59%',
    left: '-9%',
  },
  {
    top: '75%',
    left: '34%',
  },
  {
    top: '3%',
    left: '78%',
  },
  {
    top: '71%',
    left: '81%',
  },
];
const rightPositionList = [
  {
    top: '1%',
    right: '24%',
  },
  {
    top: '25%',
    right: '-9%',
  },
  {
    top: '59%',
    right: '-9%',
  },
  {
    top: '75%',
    right: '34%',
  },
  {
    top: '3%',
    right: '78%',
  },
  {
    top: '71%',
    right: '81%',
  },
];

/****隐患分布*****/
const hiddenList: any = reactive({
  titleList: {
    text0: '位置',
    text1: '上报数',
    text2: '待整改数',
    text3: '超期隐患数',
    text4: '整改率',
  },
  infoList: [],
});

// 模仿数据变化
watch(
  () => props.rangeDate,
  (newVal, oldVal) => {
    if (oldVal?.toString() !== newVal?.toString()) {
      // 隐患分布
      init();
    }
  }
);
watch(
  () => currentUnit.currentUnitId,
  (val) => {
    if (!val) return;
    init();
  }
);

// 获取隐患统计
const getTopStasticsFn = async () => {
  const params = {
    startTime: (props.rangeDate && props.rangeDate[0]) || '',
    endTime: (props.rangeDate && props.rangeDate[1]) || '',
    unitId: currentUnit.currentUnitId,
    buildId: '',
  };
  // console.log('🚀 ~ getTopStasticsFn ~ params:', currentUnit.currentUnitId);
  if (currentUnit.currentUnitId === '') return;
  const recode: any = await getTopStastics(params);
  Object.keys(recode.data).forEach((item: any) => {
    cardList.forEach((child) => {
      if (item === child.code) {
        child.num = recode.data[item];
      } else if (child.code === 'undisposeNum') {
        child.num = recode.data['undisposeNum'] + recode.data['disnosingNum'];
        console.log(child.num);
      }
    });
  });
};

// 初始化
const init = () => {
  getDangerTrend('01'); // 两次接口请求，分别请求两个类型的数据
  getDangerTrend('02'); // 两次接口请求，分别请求两个类型的数据
  getTopStasticsFn();
  getTopPositionFn();
};

// 隐患分布情况
const getDangerTrend = async (type: string) => {
  const params = {
    startTime: (props.rangeDate && props.rangeDate[0]) || '',
    endTime: (props.rangeDate && props.rangeDate[1]) || '',
    unitId: currentUnit.currentUnitId,
    hazardSource: type,
  };
  const recode: any = await getDangerUnitCount(params);
  if (recode.code === 'success' && recode.data) {
    if (type === '01') {
      recode.data.list.forEach((item: any, idx: number) => {
        item.top = leftPositionList[idx].top;
        item.left = leftPositionList[idx].left;
      });
      bubbleInfo.value.infoLeft = recode.data.list;
      bubbleInfo.value.leftTotal.num = recode.data.num;
      bubbleInfo.value.leftTotal.proportion = recode.data.proportion;
      bubbleInfo.value.leftTotal.hazardSourceName = recode.data.hazardSourceName;
    } else if (type === '02') {
      recode.data.list.forEach((item: any, idx: number) => {
        item.top = rightPositionList[idx].top;
        item.right = rightPositionList[idx].right;
      });
      bubbleInfo.value.infoRight = recode.data.list;
      bubbleInfo.value.rightTotal.num = recode.data.num;
      bubbleInfo.value.rightTotal.proportion = recode.data.proportion;
      bubbleInfo.value.rightTotal.hazardSourceName = recode.data.hazardSourceName;
    }
  }
};

const getTopPositionFn = async () => {
  const params = {
    startTime: (props.rangeDate && props.rangeDate[0] + ' 00:00:00') || '',
    endTime: (props.rangeDate && props.rangeDate[1] + ' 23:59:59') || '',
    unitId: currentUnit.currentUnitId,
  };
  if (!params.unitId) return;
  const recode = await getTopPosition(params);
  if (recode.data) {
    hiddenList.infoList = recode.data || [];
  }
};
// 监听视口
const cardListBox = ref<HTMLElement | null>(null);
const bubbleBox = ref<HTMLElement | null>(null);
const unitList = ref<HTMLElement | null>(null);

const updateUnitListHeight = () => {
  if (cardListBox.value && bubbleBox.value && unitList.value) {
    const cardListHeight = cardListBox.value.clientHeight;
    const bubbleBoxHeight = bubbleBox.value.clientHeight;
    const viewportHeight = window.innerHeight;
    const newHeight = viewportHeight - cardListHeight - bubbleBoxHeight - 220;
    unitList.value.style.height = `${newHeight}px`;
  }
};
onMounted(() => {
  init();
  updateUnitListHeight();
  window.addEventListener('resize', updateUnitListHeight);
});
onBeforeUnmount(() => {
  window.removeEventListener('resize', updateUnitListHeight);
});
</script>

<style lang="scss" scoped>
.panel-center {
  // margin-top: 1.04vw;
  width: 100%;
  height: 100%;
  padding: 0 20px;
  // position: absolute;
  // top: 131px;
  // left: 28%;
  display: flex;
  flex-direction: column;

  // 上——card列表
  .card-list {
    width: 100%;
    // height: 13%;
    height: 5.21vw;
    display: flex;
    justify-content: space-between;
    transition: all 1s;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-column-gap: 0.78vw;
    flex-shrink: 0;

    p {
      width: 100%;
      height: calc(100% - 0.83vw);
      padding: 0.68vw 0.68vw 0.68vw 0.88vw;
      display: flex;
      flex-direction: column;
      justify-content: center;
      border-radius: 0.1vw;

      span:nth-child(1) {
        font-size: 1.56vw;
        font-family: D-DIN;
        font-family: D-DIN-PRO;
        font-weight: bold;
        color: #ffffff;
        line-height: 1.56vw;
        margin-bottom: 0.26vw;
      }

      span:nth-child(2) {
        font-size: 14px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #f8feff;
        line-height: 0.94vw;
      }
    }

    p:nth-child(1) {
      // background: rgba(38, 131, 236, 0.5);
      // border: 1px solid #6eb6ff;
      // box-shadow: 0px 0px 16px 0px #56aaff inset;
      background: url('../assets/card-pc1.png') no-repeat center center;
      background-size: 100% 100%;
      // cursor: pointer;

      // &.card0,
      // &:hover {
      //   background: linear-gradient(268deg, #006ce6 0%, #3b9cff 100%);
      //   box-shadow: 0px 0px 26px 5px #56aaff;
      // }
    }

    p:nth-child(2) {
      // background: rgba(197, 125, 0, 0.5);
      // border: 1px solid #ffcb6f;
      // box-shadow: 0px 0px 16px 0px #ffc35a inset;
      background: url('../assets/card-pc3.png') no-repeat center center;
      background-size: 100% 100%;
      // cursor: pointer;

      // &.card1,
      // &:hover {
      //   background: linear-gradient(-90deg, #c57d00 0%, #ffb423 100%);
      //   box-shadow: 0px 0px 26px 5px #ffc35a;
      // }
    }

    p:nth-child(3) {
      // background: rgba(171, 23, 23, 0.5);
      // border: 1px solid #ff6069;
      // box-shadow: 0px 0px 16px 0px #ff5656 inset;
      background: url('../assets/card-pc2.png') no-repeat center center;
      background-size: 100% 100%;
      // cursor: pointer;

      // &.card2,
      // &:hover {
      //   background: linear-gradient(-90deg, #d82c2c 0%, #f64949 100%);
      //   box-shadow: 0px 0px 26px 5px #ff5656;
      // }
    }

    p:nth-child(4) {
      // background: rgba(0, 146, 120, 0.5);
      // border: 1px solid #83ffef;
      // box-shadow: 0px 0px 16px 0px #4bffe8 inset;
      background: url('../assets/card-pc4.png') no-repeat center center;
      background-size: 100% 100%;
    }
  }

  // 中——气泡图
  .bubble-box {
    margin: 1.04vw 0;
    width: 100%;
    // height: 49%;
    // height: 405px;
    flex: 1;
    display: flex;
    justify-content: space-evenly;
    flex-shrink: 0;

    .bubble-left {
      width: 38%;
      height: calc(100% - 1.04vw);
      margin-right: 13%;
      position: relative;

      .total-info {
        width: 7.76vw;
        height: 7.76vw;
        position: absolute;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        transition: all 1s;

        &.left-big1 {
          background: url('../assets/bubble-left-big1.png') no-repeat 0 0;
          background-size: 100% 100%;
        }

        span:nth-child(1) {
          font-size: 2.19vw;
          font-family: D-DIN;
          font-weight: bold;
          color: #ffffff;
          line-height: 2.4vw;
          margin-bottom: 1.04vw;
        }

        span:nth-child(2) {
          font-size: 1.25vw;
          font-family: Alibaba PuHuiTi;
          font-weight: 500;
          color: #ffffff;
          line-height: 1.25vw;
        }
      }

      p {
        position: absolute;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        transition: all 1s;

        &.left-bg1 {
          background: url('../assets/bubble-left-bg1.png') no-repeat 0 0;
          background-size: 109% 100%;
        }

        &.left-bg2 {
          background: url('../assets/bubble-left-bg2.png') no-repeat 0 0;
          background-size: 109% 100%;
        }

        &.left-bg3 {
          background: url('../assets/bubble-left-bg3.png') no-repeat 0 0;
          background-size: 109% 100%;
        }
      }

      p:nth-child(1),
      p:nth-child(2),
      p:nth-child(3),
      p:nth-child(4),
      p:nth-child(5),
      p:nth-child(6) {
        width: 4.6875vw;
        height: 4.6875vw;

        span:nth-child(1) {
          // font-size: 0.94vw;
          // font-family: D-DIN;
          // font-weight: 400;
          // color: #ffffff;
          // line-height: 0.73vw;
          // margin-bottom: 11px;
          font-family: D-DIN-PRO;
          font-weight: 500;
          font-size: 1.15vw;
          color: #ffffff;
        }

        span:nth-child(2) {
          font-size: 0.73vw;
          font-family: Alibaba PuHuiTi;
          font-weight: 400;
          color: #ffffff;
          line-height: 0.73vw;
        }
      }
    }

    .bubble-right {
      width: 34%;
      height: calc(100% - 1.04vw);
      position: relative;

      .total-info {
        width: 7.76vw;
        height: 7.76vw;
        position: absolute;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        transition: all 1s;

        &.right-big1 {
          background: url('../assets/bubble-right-big1.png') no-repeat 0 0;
          background-size: 100% 100%;
        }

        span:nth-child(1) {
          font-size: 2.19vw;
          font-family: D-DIN;
          font-weight: bold;
          color: #ffffff;
          line-height: 2.4vw;
          margin-bottom: 1.04vw;
        }

        span:nth-child(2) {
          font-size: 1.25vw;
          font-family: Alibaba PuHuiTi;
          font-weight: 500;
          color: #ffffff;
          line-height: 1.25vw;
        }
      }

      p {
        position: absolute;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        transition: all 1s;

        &.right-bg1 {
          background: url('../assets/bubble-right-bg1.png') no-repeat 0 0;
          background-size: 109% 100%;
        }

        &.right-bg2 {
          background: url('../assets/bubble-right-bg2.png') no-repeat 0 0;
          background-size: 109% 100%;
        }

        &.right-bg3 {
          background: url('../assets/bubble-right-bg3.png') no-repeat 0 0;
          background-size: 109% 100%;
        }
      }

      p:nth-child(1),
      p:nth-child(2),
      p:nth-child(3),
      p:nth-child(4),
      p:nth-child(5),
      p:nth-child(6) {
        width: 4.6875vw;
        height: 4.6875vw;

        span:nth-child(1) {
          font-size: 1.15vw;
          font-family: D-DIN;
          font-weight: 400;
          color: #ffffff;
          line-height: 1.15vw;
          margin-bottom: 0.57vw;
        }

        span:nth-child(2) {
          font-size: 0.73vw;
          font-family: Alibaba PuHuiTi;
          font-weight: 400;
          color: #ffffff;
          line-height: 0.73vw /* 14px -> 0.73vw */;
        }
      }
    }
  }

  // 下——隐患分布
  .hidden-list-box {
    // flex: 1;
    width: 100%;
    height: 100%;
    height: 33%;

    .hidden-list {
      width: 100%;
      height: 100%;
      // height: calc(100% - 2.08vw);

      .unit-list {
        height: 200px;

        .finger {
          width: 1.56vw;
          height: 20px;
        }

        .unitName {
          cursor: pointer;
        }
      }

      ul {
        width: 100%;
        display: flex;

        li {
          height: 1.04vw;
          margin: 8px 0;
          line-height: 1.04vw;
          font-size: 0.63vw;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: #ffffff;
        }

        li:nth-child(1) {
          width: 1.88vw;
          line-height: 1.04vw;
          font-size: 0.83vw;
          font-family: YouSheBiaoTiHei;
          font-weight: 400;
          font-style: italic;
          color: #ffffff;
          font-style: italic;
          letter-spacing: 0.1vw;
          padding-left: 0.1vw;
          margin-top: 0;

          span {
            opacity: 0.7;
          }
        }

        li:nth-child(2) {
          width: 8.02vw;
          padding: 0 0.73vw 0 0;
          text-align: left;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        li:nth-child(3),
        li:nth-child(4),
        li:nth-child(5) {
          width: calc((100% - 12.5vw) / 4);
          padding-right: 10px;
        }

        li:nth-child(6) {
          width: calc((100% - 12.5vw) / 4 + 1.56vw);
          display: flex;

          p {
            width: calc(100% - 2.08vw);
          }

          span {
            width: 2.08vw;
            text-align: right;
          }
        }
      }

      > div {
        height: calc(100% - 1.82vw);
        overflow-y: auto;

        &::-webkit-scrollbar {
          width: 0.42vw;
          border-radius: 0.05208rem;
        }

        &::-webkit-scrollbar-thumb {
          background-color: #5b6f8f;
          background-color: rgba(64, 153, 255, 0.5);
          border-radius: 5px;
        }

        &::-webkit-scrollbar-track {
          background-color: #5b6f8f;
          border-radius: 5px;
        }
      }
    }
  }
}

::-webkit-scrollbar {
  display: none;
}
</style>
