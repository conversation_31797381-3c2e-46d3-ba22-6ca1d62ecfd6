<template>
  <div class="lable-stack" :class="long && 'long'">
    <p>{{ labelText }}</p>
    <el-select v-if="tag" v-model="selectValue" @change="change" size="small">
      <el-option v-for="item in options" :label="item.label" :value="item.value" :key="item.value" />
    </el-select>
  </div>
</template>
<script lang="ts" setup>
import { ref } from 'vue';
defineProps({
  labelText: {
    type: String,
    default: '',
  },
  tag: {
    type: Boolean,
    default: false,
  },
  options: {
    type: Array<any>,
    default: () => [],
  },
  long: {
    type: Boolean,
    default: false,
  },
});
let selectValue = ref<string>('');

const emits = defineEmits(['changeSelect']);
function change() {
  emits('changeSelect', selectValue.value);
}
</script>
<style lang="scss" scoped>
.lable-stack {
  width: 100%;
  height: 2.08vw;
  background: url('../assets/label-bg.png') no-repeat 0 0;
  background-size: 100% 100%;
  position: relative;
  &.long {
    background: url('../assets/label-long-bg.png') no-repeat 0 0;
    background-size: 100% 100%;
  }
  p:nth-child(1) {
    position: absolute;
    left: 2.24vw;
    top: -0.42vw;
    // font-family: Alibaba PuHuiTi;
    font-weight: 400;
    font-size: 1.35vw;
    color: #ffffff;
    text-shadow: 1px 3px 2px rgba(0, 0, 0, 0.4);
    font-style: italic;
  }
  .el-select--small {
    position: absolute;
    top: 0.42vw;
    right: 0.31vw;
    width: 9.64vw;
    height: 1.41vw;

    :deep(.el-input__inner) {
      height: 1.41vw !important;
      line-height: 1.41vw !important;
      background: rgba(0, 186, 255, 0.4) !important;
      box-shadow: none;
      border: 1px solid #00baff;
      color: #cee3fc;
      text-align: center;
    }
  }
}
</style>
