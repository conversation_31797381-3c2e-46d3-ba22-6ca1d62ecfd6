<template>
  <div class="percentage-display" v-if="type == ''">
    <div>
      <p :title="name.length > 5 ? name : ''">{{ name }}</p>
      <p>
        <span ref="spanRef"></span>
      </p>
      <p>{{ num }}</p>
    </div>
  </div>
  <n-tooltip trigger="hover">
    <template #trigger>
      <div class="percentage-one" v-if="type == 'one'">
        <p ref="pRef">
          <span :style="{ background: color }"></span>
        </p>
      </div>
    </template>
    {{ tip + unit }}
  </n-tooltip>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, onUpdated } from 'vue';
const props = defineProps({
  type: {
    type: String,
    default: '',
  },
  name: {
    type: String,
    default: '',
  },
  num: {
    type: Number,
    default: 0,
  },
  percentage: {
    type: Number,
    default: 0,
  },
  color: {
    type: String,
    default: '',
  },
  tip: {
    type: Number,
    default: 0,
  },
  unit: {
    type: String,
    default: '',
  },
});
let spanRef = ref(),
  timer = ref<any>(),
  pRef = ref();

onMounted(() => {
  timer.value = setTimeout(() => {
    if (props.type == '') {
      spanRef.value.style.width = props.percentage * 0.94 + '%';
    } else {
      pRef.value.style.width = props.percentage * 0.94 + '%';
    }
  }, 200);
});
onUpdated(() => {
  if (props.type == '') {
    spanRef.value.style.width = props.percentage * 0.94 + '%';
  } else {
    pRef.value.style.width = props.percentage * 0.94 + '%';
  }
});
onUnmounted(() => {
  clearTimeout(timer.value);
});
</script>

<style lang="scss" scoped>
.percentage-display {
  display: inline-block;
  width: 100%;
  margin-bottom: 15px;
  & > div {
    width: 100%;
    display: flex;
    align-items: center;
    p:nth-child(1) {
      width: 80px;
      margin-right: 12px;
      line-height: 14px;
      font-size: 14px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #ffffff;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    p:nth-child(2) {
      box-sizing: border-box;
      width: calc(100% - 147px);
      height: 20px;
      position: relative;
      &::before {
        content: '';
        display: block;
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 20px;
        border: 1px solid #18253a;
        background: linear-gradient(90deg, rgba(255, 255, 255, 0.03) 0%, rgba(255, 255, 255, 0.07) 100%);
        opacity: 0.75;
      }
      span {
        position: absolute;
        left: 3%;
        top: 6px;
        width: 0;
        transition: all 0.6s;
        height: 8px;
        background: linear-gradient(90deg, rgba(0, 214, 239, 0.4) 0%, #00d6ef 100%);
        border-radius: 0px 4px 4px 0px;
        &::before {
          content: '';
          display: block;
          position: absolute;
          right: -9px;
          top: -4px;
          width: 18px;
          height: 17px;
          background: url('../assets/percentage-bg.png') no-repeat 0 0;
          background-size: 100% 100%;
        }
      }
    }
    p:nth-child(3) {
      width: 40px;
      margin-left: 15px;
      line-height: 14px;
      font-size: 14px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #ffffff;
    }
  }
}
.percentage-one {
  display: inline-block;
  box-sizing: border-box;
  width: 100%;
  height: 20px;
  position: relative;
  &::before {
    content: '';
    display: block;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 20px;
    border: 1px solid #18253a;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.03) 0%, rgba(255, 255, 255, 0.07) 100%);
    opacity: 0.75;
  }
  p {
    position: absolute;
    left: 3%;
    top: 6px;
    height: 8px;
    width: 0;
    overflow: hidden;
    transition: all 0.6s;
    span {
      display: inline-block;
      position: absolute;
      // left: -4px;
      width: 100%;
      height: 8px;
      // transform: skewX(-45deg);
    }
  }
}
</style>
