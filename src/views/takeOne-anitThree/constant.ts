/*
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-10-12 09:24:03
 * @LastEditors: 悦悦 <EMAIL>
 * @LastEditTime: 2024-11-22 20:27:44
 * @FilePath: /ehs-hazard/src/views/inspection-planning/planned-management/constant.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
export const enum PROVIDE_KEY {
  currentAction = 'currentAction',
}

export const enum ACTION {
  ADD = 'ADD',
  EXPORT = 'EXPORT',
}

export const ACTION_LABEL: { [key in ACTION]: string } = {
  [ACTION.ADD]: '新增类比排查',
  [ACTION.EXPORT]: '导出',
};

// 计划类型
export const tabList: { name: string; label: string }[] = [
  { name: '1', label: '类比排查任务' },
  { name: '2', label: '频发典型问题' },
];

// 检查类型
export const sourceOptions: { label: string; value: string }[] = [
  {
    label: '省级督导检查',
    value: '0',
  },
  {
    label: '市级督导检查',
    value: '1',
  },
  {
    label: '行业专家指导检查',
    value: '2',
  },
  {
    label: '集团督导检查',
    value: '3',
  },
  {
    label: '公司督导检查',
    value: '4',
  },
  {
    label: '频发典型问题',
    value: '5',
  },
];

// 	string
// 检查状态(1:草稿;2:进行中;3:已结束;4:已停用)
export const taskStateOptions: { label: string; value: string }[] = [
  {
    label: '进行中',
    value: '1',
  },
  {
    label: '已结束',
    value: '2',
  },
];
