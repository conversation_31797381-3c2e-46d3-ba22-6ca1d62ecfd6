<!--
 * @Author: fangweiwei <EMAIL>
 * @Date: 2024-12-05 19:47:39
 * @LastEditors: fangweiwei <EMAIL>
 * @LastEditTime: 2025-01-09 09:49:08
 * @FilePath: \ehs-hazard-mgr\src\views\takeOne-anitThree\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="flex flex-col">
    <com-bread :data="breadData"></com-bread>
    <div class="item_content notVw flex-1">
      <transition name="slide-fade">
        <UnitSelect v-show="formVisible" show class="unit-tree" @updateVal="updateVal" />
      </transition>
      <div style="flex: 6; position: relative" :style="{ width: formVisible ? '62vw' : '100%' }">
        <img
          @click="formVisible = !formVisible"
          v-if="userInfo.unitOrgType == '2'"
          src="@/assets/open.png"
          style="cursor: pointer; position: absolute; top: 50%; transform: translateY(-50%); z-index: 10"
          :style="{ width: toVw(30), left: `-${toVw(15)}` }"
        />
        <radio-tab class="!pt-[0]" :tab-list="tabList" :tab="tabType" @change="handleChange" />
        <TableList1 v-show="tabType == '1'" ref="tableCompRef1" :btnAuths="btnAuths" />
        <TableList2 v-show="tabType == '2'" ref="tableCompRef2" :btnAuths="btnAuths" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import TableList1 from './comp/tab1/Table.vue';
import TableList2 from './comp/tab2/Table.vue';
import RadioTab from '@/components/tab/ComRadioTabE.vue';
import ComBread from '@/components/breadcrumb/ComBread.vue';
import { tabList } from './constant';
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { IBreadData } from '@/components/breadcrumb/type.ts';
import { useStore } from '@/store';
import { toVw } from '@/utils/fit';

const { userInfo } = useStore();
const breadData: IBreadData[] = [{ name: '隐患治理系统' }, { name: '举一反三  ' }];
const route = useRoute();
const router = useRouter();
const tabType = ref<string>((route.query?.tabs as string) || '1');
let btnAuths = userInfo.resourceVoList
  .find((item: any) => item.resUrl === 'takeOneAnitThree')
  ?.childrens.filter((item: any) => item.resType == '2')
  .map((item: any) => item.resUrl);
const formVisible = ref(true);

const handleChange = (val: string) => {
  router.push({ query: { tabs: val } });
  tabType.value = val;
  handleSearch();
};

let treeFilterData = ref<any>({
  unitIds: '',
  levelCode: '',
});

// 组织结构切换
function updateVal(unitId: string, isBusinessUnit: boolean, treeData: any) {
  treeFilterData.value.unitIds = unitId;
  treeFilterData.value.levelCode = treeData.levelCode;
  if (tabType.value == '1') {
    tableCompRef1.value.pagination.pageNo = 1;
    // tableCompRef1.value.pagination.pageSize = 10;
  } else {
    tableCompRef2.value.pagination.pageNo = 1;
    // tableCompRef2.value.pagination.pageSize = 10;
  }
  handleSearch();
}
const tableCompRef1 = ref<any>(null);
const tableCompRef2 = ref<any>(null);
function handleSearch() {
  if (tabType.value == '1') {
    tableCompRef1.value?.getTableDataWrap(treeFilterData.value);
  } else {
    tableCompRef2.value?.getTableDataWrap(treeFilterData.value);
  }
}

onMounted(() => {
  // if (userInfo.unitOrgType != '2') handleSearch();
});
defineOptions({ name: 'planned-management' });
</script>

<style module lang="scss"></style>
