<template>
  <div class="right-table !p-[0]" :style="`height: calc(100% - ${toVw(40)})`">
    <div class="com-table-filter">
      <div class="mb-[20px] flex justify-start items-center w-full">
        <div class="trouble-number flex justify-start items-center mr-5">
          <div class="ml-3">
            <div>总数量（个）</div>
            <n-statistic tabular-nums style="--n-value-text-color: #">
              <n-number-animation :from="0" :to="statistics?.taskTotalNum" />
            </n-statistic>
          </div>
        </div>
        <div class="trouble-number flex justify-start items-center mr-5">
          <div class="ml-3">
            <div>进行中（个）</div>
            <n-statistic tabular-nums style="--n-value-text-color: #">
              <n-number-animation :from="0" :to="statistics?.taskingNum" />
            </n-statistic>
          </div>
        </div>
        <div class="trouble-number flex justify-start items-center mr-5">
          <div class="ml-3">
            <div>已结束（个）</div>
            <n-statistic tabular-nums style="--n-value-text-color: #">
              <n-number-animation :from="0" :to="statistics?.taskOverNum" />
            </n-statistic>
          </div>
        </div>
        <div class="trouble-number flex justify-start items-center mr-5">
          <div class="ml-3">
            <div>累计类比排查（项）</div>
            <n-statistic tabular-nums style="--n-value-text-color: #">
              <n-number-animation :from="0" :to="statistics?.oneToThreeNum" />
            </n-statistic>
          </div>
        </div>
      </div>

      <div class="flex items-center">
        <n-form
          style="border-radius: 5px 5px 0 0"
          :model="filterForm"
          label-placement="left"
          inline
          :show-feedback="false"
        >
          <n-flex :size="[20, 10]">
            <n-form-item label="检查类型:">
              <n-select
                v-model:value="filterForm.checkType"
                class="w-full flex items-center !w-[280px]"
                :options="sourceOptions"
                placeholder="全部"
                clearable
              />
            </n-form-item>
            <n-form-item label="任务状态:">
              <n-select
                v-model:value="filterForm.taskStatus"
                class="w-full flex items-center !w-[280px]"
                :options="taskStateOptions"
                placeholder="全部"
                clearable
              />
            </n-form-item>
          </n-flex>
        </n-form>
        <div class="flex gap-4 justify-end items-center">
          <n-button type="primary" @click="add()"> {{ ACTION_LABEL.ADD }} </n-button>
          <n-button @click="exportFn"> {{ ACTION_LABEL.EXPORT }} </n-button>
        </div>
      </div>
    </div>
    <n-data-table
      class="h-full com-table com-table-container !pt-[0]"
      remote
      striped
      :columns="columns"
      :data="tableData"
      :bordered="false"
      :flex-height="true"
      :row-key="(row: any) => row.planId"
      :loading="loading"
      :pagination="pagination"
      :render-cell="useEmptyCell"
      :scroll-x="1500"
    />
  </div>
</template>

<script setup lang="ts">
import { h, ref, VNode, watch } from 'vue';
import { DataTableColumns, NButton, useMessage } from 'naive-ui';
import { cols } from './columns';
import { ACTION_LABEL, sourceOptions, taskStateOptions } from '../../constant';
import { $dialog } from '@/common/shareContext/useDialogCtx.ts';

import { useRouter } from 'vue-router';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { IObj } from '@/types';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import {
  getOneToThreePage,
  oneToThreeStatistics,
  exportOneToThree,
  deleteOneToThree,
} from '@/views/takeOne-anitThree/fetchData';
import { fileDownloader } from '@/utils/fileDownloader';
import { toVw } from '@/utils/fit';
import dayjs from 'dayjs';

const props = defineProps({
  btnAuths: {
    type: Array,
    default: () => [],
  },
});
const router = useRouter();
const emits = defineEmits(['action']);
const [loading, search] = useAutoLoading(true);
const { pagination, updateTotal } = useNaivePagination(getTableData);
const filterForm = ref(initForm());
const unitId = ref('');
function initForm() {
  return {
    taskStatus: null, // 任务状态
    checkType: null, //检查类型
  };
}

const columns = ref<DataTableColumns>([]);
const tableData = ref<any[]>([]);

const add = () => {
  router.push({ name: 'takeOneAnitThreeAdd' });
};

function setColumns() {
  columns.value.push(...cols);
  // 添加操作栏 action
  columns.value.push({
    title: '操作',
    key: 'actions',
    align: 'left',
    fixed: 'right',
    width: 160,
    render(row: any) {
      return getActionBtn(row);
    },
  });
}
setColumns();
const message = useMessage();

function getActionBtn(row: any) {
  let acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          size: 'small',
          type: 'primary',
          ghost: true,
          onClick: () => {
            let data = {
              taskStartTime: dayjs(row.taskStartTime as string).format('YYYY-MM-DD HH:mm:ss'),
              taskEndTime: dayjs(row.taskEndTime as string).format('YYYY-MM-DD'),
              createByName: row.createByName,
              hazardRandomCheckEventId: row.hazardRandomCheckEventId,
            };
            router.push({
              name: 'takeOneAnitThreeDetail',
              // taskType 1 其他检查类型 2.频发典型问题
              query: { id: row.id, taskType: row.taskType, data: JSON.stringify(data) },
            });
          },
        },
        { default: () => '详情' }
      ),
    ],
    [
      h(
        NButton,
        {
          size: 'small',
          type: 'error',
          ghost: true,
          onClick: () => handleDelete(row),
        },
        { default: () => '删除' }
      ),
    ],
  ];
  // 已结束不予许删除 并且有删除权限
  if (row.taskStatus == '2' || !props.btnAuths.includes('del')) acList.splice(1, 1);
  return useActionDivider(acList);
}

// 删除
function handleDelete(data: any) {
  $dialog.warning({
    title: '提示',
    content: `确认要删除该类比排查任务吗？删除后不可恢复`,
    positiveButtonProps: { type: 'primary', color: '#527cff' },
    positiveText: '确定',
    negativeText: '取消',
    transformOrigin: 'center',
    onPositiveClick: async () => {
      await deleteOneToThree({ id: data.id });
      getTableData();
      message.success('删除成功');
    },
  });
}

const exportFn = async () => {
  const params = {
    pageNo: pagination.page,
    pageSize: -1,
    levelCode: levelCode.value,
    ...filterForm.value,
  };
  fileDownloader(exportOneToThree(), {
    method: 'POST',
    contentType: 'application/json',
    body: JSON.stringify(params),
  });
};

const statistics = ref<any>({});
const levelCode = ref<string>('');
function getTableData() {
  const params = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    levelCode: levelCode.value,
    ...filterForm.value,
  };
  search(getOneToThreePage(params)).then((res: any) => {
    tableData.value = res.data.rows || [];
    updateTotal(res.data.total ?? 0);
  });
  let data = {
    ...filterForm.value,
    levelCode: levelCode.value,
    pageSize: -1,
    pageNo: 1,
  };
  oneToThreeStatistics(data).then((res: any) => {
    statistics.value = res.data;
  });
}

function getTableDataWrap(data: IObj<any>) {
  if (data?.unitIds) unitId.value = data.unitIds;
  if (data?.levelCode) levelCode.value = data.levelCode;
  pagination.page = 1;
  getTableData();
}

watch(
  () => filterForm.value,
  () => {
    pagination.page = 1;
    getTableData();
  },
  { deep: true }
);

defineExpose({ getTableDataWrap, getTableData, pagination });
defineOptions({ name: 'myTable' });
</script>
<style lang="scss" scoped>
.trouble-number {
  width: 218px;
  height: 80px;
  padding: 15px 0 17px 32px;
  background: linear-gradient(180deg, #ffffff 0%, #e1e7fa 99%);
  box-shadow: 0px 2px 0px 0px rgba(0, 20, 82, 0.18);
  border-radius: 8px;
}

.com-table {
  height: calc(100% - 70px) !important;
  // border: 1px solid red;
}

.csBtn {
  position: absolute;
  left: 0;
  top: 0px;
  background-color: red;
  transform: translateX(-50%);
}
</style>
