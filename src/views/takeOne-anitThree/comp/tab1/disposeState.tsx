import { h } from 'vue';
import { defineComponent } from 'vue';

export const tagBgs = ['', '#E6A23C', '#00B578'];
export const tagName = ['', '进行中', '已结束'];

export const disposeState = defineComponent({
  props: {
    row: {
      type: Object,
      default: () => ({}),
    },
    field: {
      type: String,
      default: 'taskStatus',
    },
  },
  setup(props) {
    return () =>
      h(
        'div',
        {
          className: 'inline-block px-[10px] py-[4px] text-center rounded-[4px] text-white',
          style: `background-color: ${tagBgs[props.row.taskStatus]}`,
        },
        // { default: () => props.row[props.field] }
        { default: () => tagName[props.row.taskStatus] }
      );
  },
});
