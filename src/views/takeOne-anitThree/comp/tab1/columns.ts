import { DataTableColumn } from 'naive-ui';
import { disposeState } from './disposeState.tsx';
import { sourceOptions } from '../../constant';

import { h } from 'vue';
import dayjs from 'dayjs';

export const tagBgs = ['#F39600', '#89f12b', '#00B578', '#527CFF', '#f00'];

export const cols: DataTableColumn[] = [
  // {
  //   title: "序号",
  //   key: "index",
  //   width: 52,
  //   align: "left",
  //   ellipsis: {
  //     tooltip: true,
  //   },
  //   render: (_: any, index: number) => {
  //     return index + 1;
  //   },
  // },
  {
    title: '检查类型',
    key: 'checkType',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
    render(row) {
      return sourceOptions.find((i) => i.value == row.checkType)?.label || '--';
    },
  },
  {
    title: '隐患分类',
    key: 'essentialFactorClassName',
    align: 'left',
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
    render(row: any) {
      return row.essentialFactorClassName || '--';
    },
  },
  {
    title: '隐患描述',
    key: 'hazardDescribe',
    align: 'left',
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
    render(row) {
      return row.hazardDescribe || '--';
    },
  },
  {
    title: '任务状态',
    key: 'taskStatus',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
    render: (row: any) => h(disposeState, { row }),
  },
  {
    title: '发布单位',
    key: 'unitName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
    render(row) {
      return row.unitName || '--';
    },
  },
  {
    title: '任务起止日期',
    key: 'deadline',
    align: 'left',
    width: 190,
    ellipsis: {
      tooltip: true,
    },
    render(row) {
      return (
        dayjs(row.taskStartTime as string).format('YYYY-MM-DD') +
          '~' +
          dayjs(row.taskEndTime as string).format('YYYY-MM-DD') || '--'
      );
    },
  },
  {
    title: '类比排查情况(项)',
    key: 'hazardNum',
    align: 'left',
    width: 160,
    ellipsis: {
      tooltip: true,
    },
  },
];
