import { h } from 'vue';
import { defineComponent } from 'vue';

export const tagBgs = ['#527CFF', '#E87108', '#D9001B'];

export const disposeFrequency = defineComponent({
  props: {
    case: {
      type: Number,
      default: 0,
    },
    current: {
      type: Number,
      default: 0,
    },
  },
  setup(props) {
    return () =>
      h(
        'div',
        {
          className: 'inline-block w-[44px]  px-[10px] py-[4px] text-center rounded-[4px] text-white',
          style: `background-color: ${tagBgs[props.current]}`,
        },
        { default: () => props.case + '次' }
      );
  },
});
