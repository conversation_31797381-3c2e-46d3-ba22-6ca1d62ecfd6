import { DataTableColumn } from 'naive-ui';
import { disposeState } from './disposeState.tsx';
import { disposeFrequency } from './disposeFrequency.tsx';
import { h } from 'vue';

export const tagBgs = ['#F39600', '#89f12b', '#00B578', '#527CFF', '#f00'];

export const cols: DataTableColumn[] = [
  {
    type: 'selection',
  },
  {
    title: '隐患分类',
    key: 'hazardTypeName',
    align: 'left',
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
    render(row: any) {
      return row.hazardTypeName || '--';
    },
  },
  {
    title: '隐患等级',
    key: 'hazardLevelName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
    render: (row: any) => h(disposeState, { row }),
  },
  {
    title: '隐患描述',
    key: 'hazardDesc',
    align: 'left',
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
    render(row) {
      return row.hazardDesc || '--';
    },
  },
  {
    title: '出现频次',
    key: 'nums',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
    render: (row: any) => {
      const nums = Number(row.nums);
      let current = 0;
      if (nums >= 5 && nums <= 10) {
        current = 0;
      } else if (nums >= 11 && nums <= 20) {
        current = 1;
      } else {
        current = 2;
      }
      return h(disposeFrequency, { current, case: nums });
    },
  },
];
