<template>
  <div class="right-table !p-[0]" :style="`height: calc(100% - ${toVw(40)})`">
    <div class="com-table-filter flex">
      <n-form
        style="border-radius: 5px 5px 0 0"
        :model="filterForm"
        label-placement="left"
        inline
        :show-feedback="false"
      >
        <n-flex :size="[20, 10]">
          <n-form-item label="统计时段:">
            <div class="flex">
              <div
                v-for="(item, index) of dateList"
                :key="index"
                class="date-box"
                :class="current == item.value ? 'acitve_box' : ''"
                @click="deteClick(item)"
              >
                {{ item.label }}
              </div>
            </div>
          </n-form-item>
          <n-form-item>
            <n-date-picker class="!w-[360px]" v-model:value="rangeTime" type="daterange" @confirm="confirmRange" />
          </n-form-item>
        </n-flex>
      </n-form>

      <div class="flex gap-4 justify-end items-center">
        <n-button
          type="primary"
          @click="add()"
          :disabled="checkKeys.length == 0 ? true : false"
          v-if="btnAuths.includes('add')"
        >
          新增类比排查
        </n-button>
        <n-button @click="exportFn"> {{ ACTION_LABEL.EXPORT }} </n-button>
      </div>
    </div>
    <n-data-table
      class="h-full com-table com-table-container !pt-[0]"
      remote
      striped
      :columns="columns"
      :data="tableData"
      :bordered="false"
      :flex-height="true"
      :row-key="(row: any) => row.index"
      :loading="loading"
      :pagination="pagination"
      :render-cell="useEmptyCell"
      @update:checked-row-keys="handleCheck"
      :scroll-x="1500"
    />
  </div>
</template>

<script setup lang="ts">
import { h, ref, VNode } from 'vue';
import { DataTableColumns, NButton, useMessage } from 'naive-ui';
import { cols } from './columns';
import { ACTION_LABEL } from '../../constant';
import { $dialog } from '@/common/shareContext/useDialogCtx.ts';
import { useRouter } from 'vue-router';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { IObj } from '@/types';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { pageHaveOneFindThreeEvent, exportHighFrequencyEventList } from '../../fetchData';
import { fileDownloader } from '@/utils/fileDownloader';
import { useStore } from '@/store';
import { toVw } from '@/utils/fit';
import dayjs from 'dayjs';

const props = defineProps({
  btnAuths: {
    type: Array,
    default: () => [],
  },
});
const { userInfo } = useStore();
const router = useRouter();
const emits = defineEmits(['action']);
const [loading, search] = useAutoLoading(true);
const { pagination, updateTotal } = useNaivePagination(getTableData);

const current = ref(1);
const dateList = ref<any[]>([
  {
    label: '过去30天',
    value: 0,
    timestamp: dayjs(Date.now() - 30 * 24 * 60 * 60 * 1000).format('YYYY-MM-DD'), // 30天前的时间戳
  },
  {
    label: '过去90天',
    value: 1,
    timestamp: dayjs(Date.now() - 90 * 24 * 60 * 60 * 1000).format('YYYY-MM-DD'), // 90天前的时间戳
  },
  {
    label: '过去180天',
    value: 2,
    timestamp: dayjs(Date.now() - 180 * 24 * 60 * 60 * 1000).format('YYYY-MM-DD'), // 180天前的时间戳
  },
  {
    label: '过去360天',
    value: 3,
    timestamp: dayjs(Date.now() - 360 * 24 * 60 * 60 * 1000).format('YYYY-MM-DD'),
  },
]);

console.log(dateList.value, 'dateList');

const startTime = dateList.value[1].timestamp;
const endTime = dayjs(Date.now()).format('YYYY-MM-DD');
const rangeTime = ref<any>([new Date(startTime).getTime(), new Date(endTime).getTime()]);

const deteClick = (row: any) => {
  // if (current.value == row.value) return;
  current.value = row.value;
  rangeTime.value = [new Date(row.timestamp).getTime(), new Date(endTime).getTime()];
  filterForm.value.startTime = row.timestamp;
  filterForm.value.endTime = dayjs(Date.now()).format('YYYY-MM-DD');
  pagination.page = 1;
  getTableData();
};

function confirmRange(date: any) {
  filterForm.value.startTime = dayjs(date[0]).format('YYYY-MM-DD');
  filterForm.value.endTime = dayjs(date[1]).format('YYYY-MM-DD');
  if (isToday(date[1])) {
    current.value = -1;
    let days = getDaysBetweenTimestamps(date[0], date[1]);
    switch (days) {
      case 30:
        current.value = 0;
        break;
      case 90:
        current.value = 1;
        break;
      case 180:
        current.value = 2;
        break;
      case 360:
        current.value = 3;
        break;
      default:
    }
  } else {
    current.value = -1;
  }
  pagination.page = 1;
  getTableData();
}
// 获取两个时间戳之间的天数
function getDaysBetweenTimestamps(timestamp1: number, timestamp2: number) {
  // 计算时间戳差值（以毫秒为单位）
  const difference = Math.abs(timestamp1 - timestamp2);

  // 将差值转换为天数（1 天 = 24 小时 * 60 分钟 * 60 秒 * 1000 毫秒）
  const days = Math.floor(difference / (24 * 60 * 60 * 1000));

  return days;
}

// 判断是否是今天
function isToday(timestamp: number) {
  const date = new Date(timestamp);
  const today = new Date();

  return (
    date.getDate() === today.getDate() &&
    date.getMonth() === today.getMonth() &&
    date.getFullYear() === today.getFullYear()
  );
}

const filterForm = ref(initForm());
function initForm() {
  return {
    startTime: startTime,
    endTime: endTime,
  };
}

const columns = ref<DataTableColumns>([]);
const tableData = ref<any[]>([]);

const add = () => {
  router.push({ name: 'takeOneAnitThreeAddFrequently', query: { data: JSON.stringify(checkData.value) } });
};
const exportFn = async () => {
  const params = {
    pageNo: pagination.page,
    pageSize: -1,
    topUnitId: userInfo.topUnitId,
    levelCode: levelCode.value,
    ...filterForm.value,
  };
  fileDownloader(exportHighFrequencyEventList(), {
    method: 'POST',
    contentType: 'application/json',
    body: JSON.stringify(params),
  });
};
const checkKeys = ref<string[]>([]);
const checkData = ref<any[]>([]);
const handleCheck = (rowKey: string[], rowData: any[]) => {
  console.log(rowKey, rowData);
  checkKeys.value = rowKey;
  checkData.value = rowData;
};

function setColumns() {
  columns.value.push(...cols);
  // 添加操作栏 action
  columns.value.push({
    title: '操作',
    key: 'actions',
    align: 'center',
    fixed: 'right',
    width: 200,
    render(row: any) {
      return getActionBtn(row);
    },
  });
}
setColumns();
const currentTime = Date.now();
const message = useMessage();

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          size: 'small',
          type: 'primary',
          ghost: true,
          onClick: () =>
            router.push({
              name: 'takeOneAnitThreeAddFrequently',
              query: { data: JSON.stringify([row]) },
            }),
        },
        { default: () => '新增类比排查' }
      ),
    ],
  ];
  //有新增权限才可以新增
  if (!props.btnAuths.includes('add')) acList.splice(0, 1);
  return useActionDivider(acList);
}

// 删除
function handleDelete(data: any) {
  $dialog.warning({
    title: '提示',
    content: '确定删除计划吗？删除后不可恢复！',
    positiveButtonProps: { type: 'primary', color: '#527cff' },
    positiveText: '确定',
    negativeText: '取消',
    transformOrigin: 'center',
    onPositiveClick: async () => {
      // await deletePlan({ planId: data.planId });
      getTableData();
      message.success('删除成功');
    },
  });
}
const levelCode = ref<string>('');
// const unitId = ref<string>('');
function getTableData() {
  const params = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    // unitId: unitId.value ? unitId.value : userInfo.unitId, // 单位id
    levelCode: levelCode.value,
    ...filterForm.value,
  };
  search(pageHaveOneFindThreeEvent(params)).then((res: any) => {
    tableData.value =
      res.data.rows.map((item, index) => {
        return {
          ...item,
          index,
        };
      }) || [];
    updateTotal(res.data.total ?? 0);
  });
}

function getTableDataWrap(data: IObj<any>) {
  console.log(data, 'data');
  // if (data?.unitIds) unitId.value = data.unitIds;
  if (data?.levelCode) levelCode.value = data.levelCode;
  pagination.page = 1;
  getTableData();
}
defineExpose({ getTableDataWrap, getTableData, pagination });
// eslint-disable-next-line vue/multi-word-component-names
defineOptions({ name: 'tab2' });
</script>
<style lang="scss" scoped>
.date-box {
  padding: 8px 10px;
  width: 108px;
  text-align: center;
  margin-right: 4px;
  background-color: #fff;
  cursor: pointer;
}

.acitve_box {
  background-color: #527cff;
  color: #fff;
}

.com-table {
  height: calc(100% - 70px) !important;
  // border: 1px solid red;
}

.csBtn {
  position: absolute;
  left: 0;
  top: 0px;
  background-color: red;
  transform: translateX(-50%);
}
</style>
