import { h } from 'vue';
import { defineComponent } from 'vue';

export const tagBgs = ['', '#D9001B', '#E87108', '#FFDD00', '#527CFF', '#979797'];

export const tagName = ['', '重大隐患', '较大隐患', '一般隐患', '低隐患', '无隐患'];

export const disposeState = defineComponent({
  props: {
    row: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props) {
    return () =>
      h(
        'div',
        {
          className: 'inline-block px-[10px] py-[4px] text-center rounded-[4px] text-white',
          style: `background-color: ${tagBgs[props.row?.hazardLevelInfo?.gradeSort]}`,
        },
        // { default: () => tagName[props.row.state] }
        { default: () => props.row.hazardLevelName }
      );
  },
});
