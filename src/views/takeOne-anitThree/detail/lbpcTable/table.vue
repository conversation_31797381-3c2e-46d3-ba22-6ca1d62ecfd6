<template>
  <div class="table-box">
    <Header title="类比排查情况" :blueIcon="true"></Header>
    <n-data-table
      class="com-table"
      :scroll-x="800"
      remote
      :max-height="200"
      striped
      :columns="columns"
      :data="tableData"
      :bordered="false"
      :render-cell="useEmptyCell"
    />
  </div>
</template>

<script setup lang="ts">
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import Header from '@/components/header/ComHeaderB.vue';
import { ref } from 'vue';

const tableData = ref<any[]>([
  {
    unitName: '单位名称',
    essentialFactorClassName: '0',
    essentialFactorGradeName: '0',
    hazardDescribe: '0',
  },
  {
    unitName: '单位名称',
    essentialFactorClassName: '0',
    essentialFactorGradeName: '0',
    hazardDescribe: '0',
  },
  {
    unitName: '单位名称',
    essentialFactorClassName: '0',
    essentialFactorGradeName: '0',
    hazardDescribe: '0',
  },
  {
    unitName: '单位名称',
    essentialFactorClassName: '0',
    essentialFactorGradeName: '0',
    hazardDescribe: '0',
  },
  {
    unitName: '单位名称',
    essentialFactorClassName: '0',
    essentialFactorGradeName: '0',
    hazardDescribe: '0',
  },
  {
    unitName: '单位名称',
    essentialFactorClassName: '0',
    essentialFactorGradeName: '0',
    hazardDescribe: '0',
  },
  {
    unitName: '单位名称',
    essentialFactorClassName: '0',
    essentialFactorGradeName: '0',
    hazardDescribe: '0',
  },
  {
    unitName: '单位名称',
    essentialFactorClassName: '0',
    essentialFactorGradeName: '0',
    hazardDescribe: '0',
  },
  {
    unitName: '单位名称',
    essentialFactorClassName: '0',
    essentialFactorGradeName: '0',
    hazardDescribe: '0',
  },
  {
    unitName: '单位名称',
    essentialFactorClassName: '0',
    essentialFactorGradeName: '0',
    hazardDescribe: '0',
  },
  {
    unitName: '单位名称',
    essentialFactorClassName: '0',
    essentialFactorGradeName: '0',
    hazardDescribe: '0',
  },
  {
    unitName: '单位名称',
    essentialFactorClassName: '0',
    essentialFactorGradeName: '0',
    hazardDescribe: '0',
  },
]);

const columns = [
  {
    title: '单位名称',
    key: 'unitName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '类比排查隐患数量',
    key: 'essentialFactorClassName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '整改数量',
    key: 'essentialFactorGradeName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '逾期数量',
    key: 'hazardDescribe',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
];

defineOptions({ name: 'LbpcTable' }); //类比排查情况
</script>
<style scoped lang="scss">
.table-box {
  margin-top: 16px;
  width: 100%;
}
.com-table {
  margin-top: 16px;
}
</style>
