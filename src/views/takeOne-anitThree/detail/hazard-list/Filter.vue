<template>
  <n-form :show-feedback="false" label-placement="left">
    <div class="flex justify-between items-center">
      <n-grid :x-gap="12" :y-gap="8" :cols="4">
        <n-grid-item>
          <n-form-item label="隐患单位:">
            <n-select
              v-model:value="filterForm.unitId"
              placeholder="请选择"
              :options="unitOptions"
              clearable
              class="flex items-center"
            />
          </n-form-item>
        </n-grid-item>
        <n-grid-item>
          <n-form-item label="隐患等级:">
            <n-select
              v-model:value="filterForm.hazardLevel"
              placeholder="请选择"
              clearable
              :options="hazardLevelList"
              class="flex items-center"
            />
          </n-form-item>
        </n-grid-item>
        <n-grid-item>
          <n-form-item label="整改状态:">
            <n-select
              v-model:value="filterForm.disposeState"
              clearable
              :options="disposeStateOptions"
              class="flex items-center"
            />
          </n-form-item>
        </n-grid-item>
      </n-grid>
      <n-button type="primary" @click="exportEventListFn"> 导出 </n-button>
    </div>
  </n-form>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import { exportEventList, getHazardGradeList } from '@/views/hazard-management/fetchData';
import { getUnitListByTaskId } from '@/views/takeOne-anitThree/fetchData';
import { fileDownloader } from '@/utils/fileDownloader';
import { trimObjNull } from '@/utils/obj.ts';
import { ACTION } from '../type';
import { useStore } from '@/store';
import { useRoute } from 'vue-router';

const props = defineProps({
  value: {
    type: Object,
    default: () => {},
  },
  hazardReporters: {
    type: Object,
    default: () => {},
  },
  detailInfo: {
    type: Object,
    default: () => {},
  },
});

const router = useRoute();

const disposeStateOptions = [
  { label: '待整改', value: 0 },
  { label: '已整改', value: 1 },
  { label: '整改中', value: 2 },
];

const { userInfo } = useStore();
const emits = defineEmits(['action']);
const unitOptions = ref<any[]>([]);
const filterForm = computed(() => props.value);

// const filterForm = ref<any>({
//   haveOneFindThree: props.value.haveOneFindThree,
//   topUnitId: props.value.topUnitId,
//   unitId: null,
//   hazardLevel: null,
//   disposeState: null,
// });

function doHandle(action: ACTION) {
  emits('action', {
    action: action,
    data: trimObjNull(filterForm.value),
  });
}

const getPlanUnitByPlandApi = async () => {
  const { data } = await getUnitListByTaskId({ id: props.value.haveOneFindThree });
  unitOptions.value = data?.map((item: any) => ({ label: item.unitName, value: item.unitId }));
};
const hazardLevelList = ref<any[]>([]);
/** 获取下拉框数据 */
const getFilterFormData = async () => {
  getHazardGradeList({ delFlag: 0, unitId: userInfo.topUnitId }).then((res) => {
    hazardLevelList.value = res.data.map((item: any) => ({ label: item.gradeName, value: item.id }));
  });
};

watch(filterForm.value, () => {
  doHandle(ACTION.search);
});

const exportEventListFn = async () => {
  let data = {
    fileName: '举一反三隐患治理',
    ...filterForm.value,
    haveOneFindThreeUnitId: filterForm.value.unitId,
    // exportfields: [
    //   { key: 'eventFileUrlStr1', title: '隐患图片1' },
    //   { key: 'eventFileUrlStr2', title: '隐患图片2' },
    //   { key: 'eventFileUrlStr3', title: '隐患图片3' },
    //   { key: 'dealFileUrlStr1', title: '验收图片1' },
    //   { key: 'dealFileUrlStr2', title: '验收图片2' },
    //   { key: 'dealFileUrlStr3', title: '验收图片3' },
    // ],
    exportfields: [
      {
        key: 'unitName',
        title: '隐患单位',
      },
      {
        key: 'hazardSourceName',
        title: '隐患来源',
      },
      {
        key: 'hazardPosition',
        title: '隐患位置',
      },
      {
        key: 'hazardDesc',
        title: '隐患描述',
      },
      {
        key: 'hazardTypeName',
        title: '隐患分类',
      },
      {
        key: 'hazardLevelName',
        title: '隐患等级',
      },
      {
        key: 'eventTime',
        title: '上报时间',
      },
      {
        key: 'disposeStateName',
        title: '整改状态',
      },
      {
        key: 'timeoutDays',
        title: '是否超期',
      },
      { key: 'eventFileUrlStr1', title: '隐患图片1' },
      { key: 'eventFileUrlStr2', title: '隐患图片2' },
      { key: 'eventFileUrlStr3', title: '隐患图片3' },
      { key: 'dealFileUrlStr1', title: '验收图片1' },
      { key: 'dealFileUrlStr2', title: '验收图片2' },
      { key: 'dealFileUrlStr3', title: '验收图片3' },
    ],
  };
  fileDownloader(exportEventList(), {
    method: 'POST',
    contentType: 'application/json',
    body: JSON.stringify(data),
  });
};

watch(
  () => props.value,
  () => {
    // getPlanUnitByPlandApi();
  },
  {
    deep: true,
  }
);
watch(
  () => filterForm.value,
  () => {
    doHandle(ACTION.search);
  }
);
onMounted(() => {
  getFilterFormData();
  doHandle(ACTION.search);
  getPlanUnitByPlandApi();
});

const resetField = () => {
  filterForm.value.unitId = null;
  filterForm.value.likeFieldValue = '';
};

defineExpose({ resetField });
defineOptions({ name: 'checkTempFilterComp' });
</script>
<style module lang="scss"></style>
