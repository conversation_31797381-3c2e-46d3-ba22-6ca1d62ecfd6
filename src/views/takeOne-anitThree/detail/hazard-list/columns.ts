import { disposeState } from '@/components/table-col/disposeState';
import { DataTableColumn } from 'naive-ui';
import { h } from 'vue';

export const cols: DataTableColumn[] = [
  {
    title: '隐患单位',
    key: 'unitName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '隐患来源',
    key: 'hazardSourceName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '隐患位置',
    key: 'hazardPosition',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '隐患描述',
    key: 'hazardDesc',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '隐患分类',
    key: 'hazardTypeName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '隐患等级',
    key: 'hazardLevelName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '上报时间',
    key: 'eventTime',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '整改状态',
    key: 'disposeStateName',
    align: 'left',
    width: 120,
    render: (row) => h(disposeState, { row }),
  },
  {
    title: '是否超期',
    width: 150,
    key: 'timeoutDays',
    align: 'left',
    render: (row: any) => {
      if (row.timeoutDays == 0 || !row.timeoutDays) {
        return '否';
      } else {
        return `超期${row.timeoutDays}天`;
      }
    },
  },
];
