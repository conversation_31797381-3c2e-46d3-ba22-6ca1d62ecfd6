<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="hazard-capture com-g-row-a1">
    <com-bread :data="breadData"></com-bread>
    <detailsVue
      :id="route.query.id"
      :taskType="route.query.taskType"
      :routeData="JSON.parse(route.query.data as any)"
      class="p-[20px] bg-[#EEF7FF] border border-white rounded"
    />
  </div>
</template>

<script lang="ts" setup>
import ComBread from '@/components/breadcrumb/ComBread.vue';
import { IBreadData } from '@/components/breadcrumb/type';
import { useRoute } from 'vue-router';
import detailsVue from './details.vue';

const breadData: IBreadData[] = [
  { name: '隐患治理系统' },
  { name: '举一反三', routeRaw: { name: 'takeOneAnitThree' }, clickable: true },
  { name: '详情' },
];
const route = useRoute();
</script>

<style scoped>
.hazard-capture :deep(.n-data-table) {
  --n-merged-th-color: #bbccf3;
}
</style>
