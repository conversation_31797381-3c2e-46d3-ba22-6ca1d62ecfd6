<template>
  <div class="">
    <div class="relative">
      <Header :title="'隐患基本信息'" :blueIcon="true"></Header>
    </div>
    <n-descriptions class="mb-4 mt-4" separator=" " label-style="color: #909399;" :column="3" label-placement="left">
      <n-descriptions-item label="任务截止日期:">
        <span>{{ routeData?.taskEndTime || '--' }}</span>
      </n-descriptions-item>
      <n-descriptions-item label="任务发布人员:">
        <span>{{ routeData?.createByName || '--' }}</span>
      </n-descriptions-item>
      <n-descriptions-item label="任务发布时间:">
        <span>{{ routeData?.taskStartTime || '--' }}</span>
      </n-descriptions-item>
    </n-descriptions>

    <n-data-table
      class="com-table"
      :scroll-x="800"
      remote
      :min-height="100"
      striped
      :columns="props.taskType == '1' ? columns1 : columns2"
      :data="tableData"
      :bordered="false"
      :render-cell="useEmptyCell"
    />

    <!-- <LbpcTableComp /> -->
    <n-grid :x-gap="12" :y-gap="8" :cols="1" class="my-4">
      <n-grid-item>
        <Header title="类比排查情况" :blueIcon="true"></Header>

        <div class="flex justify-start items-center pt-5">
          <div class="trouble-number flex justify-start items-center">
            <img src="./assets/icon1.png" alt="" />
            <div class="ml-3">
              <div>隐患数量</div>
              <div style="color: #ff9500; font-size: 20px; margin-top: 10px">
                {{ statisticsData?.total || '0' }}
              </div>
            </div>
          </div>
          <div class="flex justify-start items-center other-card ml-5">
            <img src="./assets/bg1.png" alt="" style="position: absolute; top: 0; left: 0" />
            <div style="z-index: 2">
              <div>已整改</div>
              <div style="color: #00b578; font-size: 20px; margin-top: 10px">
                {{ statisticsData?.disposedNum || '0' }}
              </div>
            </div>
          </div>
          <div class="flex justify-start items-center other-card ml-5">
            <img src="./assets/bg1.png" alt="" style="position: absolute; top: 0; left: 0" />
            <div style="z-index: 2">
              <div>待整改</div>
              <div style="color: #fa5151; font-size: 20px; margin-top: 10px">
                {{ statisticsData?._num || '0' }}
              </div>
            </div>
          </div>
          <div class="flex justify-start items-center other-card ml-5">
            <img src="./assets/bg2.png" alt="" style="position: absolute; top: 0; left: 0" />
            <div style="z-index: 2">
              <div>超期数量</div>
              <div style="color: #ff9500; font-size: 20px; margin-top: 10px">
                {{ statisticsData?.timeoutNum || '0' }}
              </div>
            </div>
          </div>
        </div>
      </n-grid-item>
      <n-grid-item>
        <div class="gap-y-[8px] w-full h-full">
          <Filter
            ref="filterRef"
            class="com-table-filter !px-0 !pb-[0]"
            v-model:value="filterData"
            @action="actionFn"
          />
          <hazardListTable
            class="com-table-container !px-0"
            ref="hazardListTableRef"
            @showDetails="showHazardDetails"
          />
        </div>
      </n-grid-item>
    </n-grid>

    <hazardDetails ref="hazardDetailsRef" />
  </div>
</template>

<script lang="ts" setup>
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import Header from '@/components/header/ComHeaderB.vue';
import { disposeState } from '@/components/table-col/disposeState';
import hazardDetails from '@/views/hazard-management/hazard-details.vue';
import { getOneToThreeDetail, getStatisticsMeger, qeuryEventDetail } from '@/views/takeOne-anitThree/fetchData';
import { NButton } from 'naive-ui';
import { h, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import Filter from './hazard-list/Filter.vue';
import hazardListTable from './hazard-list/hazard-list-table.vue';
// import LbpcTableComp from './lbpcTable/table.vue';

import { useStore } from '@/store';
const { userInfo } = useStore();

interface IProps {
  id: string;
  routeData: any;
  taskType: string;
}

const props = withDefaults(defineProps<IProps>(), {});

const columns1 = [
  {
    title: '隐患单位',
    key: 'unitName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '隐患来源',
    key: 'hazardSourceName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '隐患位置',
    key: 'hazardPosition',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '隐患描述',
    key: 'hazardDesc',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '隐患分类',
    key: 'hazardTypeName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '隐患等级',
    key: 'hazardLevelName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '上报时间',
    key: 'eventTime',
    align: 'left',
    width: 180,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '整改状态',
    key: 'disposeStateName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
    render: (row: any) => h(disposeState, { row }),
  },
  {
    title: '是否超期',
    key: 'timeoutDays',
    align: 'left',
    width: 120,
    ellipsis: {
      tooltip: true,
    },
    render: (row: any) => {
      if (row.timeoutDays == 0 || !row.timeoutDays) {
        return '否';
      } else {
        return `超期${row.timeoutDays}天`;
      }
    },
  },
  {
    title: '操作',
    key: 'actions',
    align: 'left',
    fixed: 'right',
    width: 80,
    render: (row: any) => {
      return h(
        NButton,
        {
          size: 'small',
          type: 'primary',
          ghost: true,
          class: 'com-action-button',
          onClick: () => showHazardDetails(row, 2),
        },
        { default: () => '详情' }
      );
    },
  },
];

const columns2 = [
  {
    title: '隐患来源',
    key: 'checkTypeName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '隐患分类',
    key: 'essentialFactorClassName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '隐患等级',
    key: 'essentialFactorGradeName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '隐患描述',
    key: 'hazardDescribe',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
];
const tableData = ref<any[]>([]);

const route = useRoute();
const router = useRouter();
const filterRef = ref();
const hazardListTableRef = ref();

const hazardDetailsRef = ref();
const detailInfo = ref<any>({ planName: '' });
const statisticsData = ref<any>({});

const showHazardDetails = (row: any, type = 1) => {
  let data = {
    ...row,
    id: props.routeData.hazardRandomCheckEventId,
    flag: '2',
  };
  hazardDetailsRef.value.open(type == 1 ? row : data);
};

const filterData = ref({
  haveOneFindThree: route.query.id,
  topUnitId: userInfo.topUnitId,
  unitId: null,
  hazardLevel: null,
  disposeState: null,
});

const actionFn = (data: any) => {
  hazardListTableRef.value.getTableDataWrap(filterData.value);
  getHazardStatisticsFn(data.data);
};

const getDetail = async () => {
  if (props.taskType == '1') {
    const res = await qeuryEventDetail({ id: props.routeData.hazardRandomCheckEventId, flag: '2' });
    tableData.value = [res.data];
    detailInfo.value = res.data;
  } else {
    const res = await getOneToThreeDetail({ id: props.id });
    tableData.value = [res.data];
    detailInfo.value = res.data;
  }
};
const defaultPointTotal = ref(0);

// 统计数据
const getHazardStatisticsFn = async (data: any) => {
  let params = {
    haveOneFindThreeUnitId: null,
    haveOneFindThree: route.query.id,
    // unitId: userInfo.topUnitId,
    ...data,
  };
  console.log(data);
  if (data?.unitId) params.haveOneFindThreeUnitId = data.unitId;
  const res = await getStatisticsMeger(params);
  statisticsData.value = res.data;
  statisticsData.value._num = Number(statisticsData.value.unDisposedNum) + Number(statisticsData.value.disposingNum);
};

watch(
  props,
  async () => {
    if (!props.id) return;
    await getDetail();
  },
  {
    immediate: true,
  }
);

defineOptions({ name: 'hazard-details' });
</script>

<style scoped lang="scss">
.trouble-number {
  img {
    width: 66px;
    height: 66px;
  }

  width: 218px;
  height: 80px;
  padding: 15px 32px 17px 32px;
  background: linear-gradient(180deg, #ffffff 0%, #e1e7fa 99%);
  box-shadow: 0px 2px 0px 0px rgba(0, 20, 82, 0.18);
  border-radius: 8px;
}

.other-card {
  position: relative;
  width: 172px;
  height: 80px;
  padding: 15px 32px 17px 32px;
}
</style>
