export interface ITaskFilter {
  planStartDate?: string | null; // 计划开始时间
  planEndDate?: string | null; // 计划结束时间
  planType?: IPlanType | null; // "计划类型(1:督察检査;2:专项检査;3:自查检查)”
  taskState?: ITaskState | null; // 检査状态(1:待开始;2:进行中;3:已完成)
  keyWords?: string | null; // 关键字
  checkUnitId?: string | null; // 检查对象
  planUserId?: string | null; // 计划人员id
  userRoleCodes?: string | null; // 角色编码
  unitId?: string | null; // 单位id
  pageSize?: number | null; // 每页条数
  pageNo?: number | null; // 页码
}

export interface IHazardTask {
  planName: string;
  planType: IPlanType;
  unitName: string;
  planStartTime: string;
  planEndTime: string;
  taskState: ITaskState;
  timeState: ITimeState;
  createTime: string;
  createByName: string;
}

export enum ACTION {
  search,
  export,
}

export enum ITaskState {
  待开始 = 1,
  进行中,
  已完成,
  已停用,
}
export enum ITaskState2 {
  待开始 = 0,
  进行中,
  已完成,
  已停用,
}
export enum ITaskState3 {
  进行中 = 1,
  已完成,
  已停用,
}
export enum ITimeState {
  正常 = 1,
  逾期,
}

export enum IPlanType {
  督察检査 = 1,
  专项检査,
  自查检查,
}

export interface IClockData {
  id: string;
  clockInTime: string; // 打卡时间
  clockInAddress: string; // 打卡地点
  filePath: string; // 图片地址
  userName: string; // 打卡人
}

export interface IHazardTaskDetailsData {
  uncheckedItemNum?: number; // 未查数量
  checkItemedNum?: number; // 已查数量
  totalNum?: number; // 已查数量
  checkRange?: '1' | '2' | '3';
  isNeedClock?: '1' | '2'; // 是否需要检查人现场打卡(1:是;2:否)
  planType?: string; //检查状态
  planTypeName?: string; // 检查类型
  createByName?: string; // 创建人
  createTime?: string; // 创建时间
  frequency?: string; // 检查频次
  planStartDate?: string; // 计划开始时间
  planEndDate?: string; // 计划结束时间
  planStartTime?: string; //任务开始时间
  planEndTime?: string; //任务结束时间
  beginTime?: string; // 执行开始时间
  finishTime?: string; // 执行结束时间
  superviseUnitName?: string; // 分管单位
  unitNames?: string; // 检查对象
  timeStateName?: string; // 任务时效
  timeState?: 1 | 2; // 1 正常；2 逾期
  fzrs?: string; // 专家组组长
  cyrs?: string; // 专家组成员
  checkTable?: string; // 检查表
  checkItemNum?: number; // 检查表数量
  clockNum?: number; // 打卡
  planName: string; // 计划名称
  taskState?: 1 | 2 | 3;
  checkAreaFormGrade?: number; //检查表层级
  checkTableId?: string; //检查表id
  checkExecuteMethod?: '1' | '2'; // 2 需要逐一检查
  planCheckPointList?: any; // 巡查点位列表
}

/** 检查状态 */
export const taskStateOptions = [
  { label: ITaskState[1], value: 1 },
  { label: ITaskState[2], value: 2 },
  { label: ITaskState[3], value: 3 },
  // { label: ITaskState[4], value: 4 },
];

/** 隐患上报人员信息 */
export interface IHzaardReporter {
  userName: string;
  num: number;
  createBy: string;
}
