import { ICheckTempPageRes, FromData } from './type';
import { IObj } from '@/types';
import { api } from '@/api';
import { $http } from '@tanzerfe/http';

// 类比排查任务-分页查询
export function getOneToThreePage(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.cj.oneThree.getOneToThreePage, data);
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}
// 类比排查任务-详情
export function getOneToThreeDetail(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.cj.oneThree.getOneToThreeDetail, data);
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}

// 类比排查任务-数据统计
export function oneToThreeStatistics(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.cj.oneThree.oneToThreeStatistics, data);
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}
// 类比排查任务-导出
export function exportOneToThree() {
  const url = api.getUrl(api.type.hazard, api.cj.oneThree.exportOneToThree);
  return url;
}
// 新增举一反三类比排查任务
export function addOneToThree(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.cj.oneThree.addOneToThree);
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}

// 类比排查任务-删除
export function deleteOneToThree(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.cj.oneThree.deleteOneToThree, data);
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}
/** 隐患详情 */
export function qeuryEventDetail(params: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.hazardManagement.qeuryEventDetail, params);
  return $http.post(url, { data: { _cfg: { showTip: true } } });
}
/** 隐患治理已完成统计数据 */
export function getStatisticsMeger(params: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.hazardManagement.statisticsMeger);
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...params },
  });
}
// 获取列表排查单位列表
export function getUnitListByTaskId(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.cj.oneThree.getUnitListByTaskId, data);
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}

// 获取单位
export function getAllUnit(query: { orgCode: string; pageNo: number; pageSize: number; type?: string }) {
  const url = api.getUrl(api.type.hazard, api.public.getAllUnit);
  return $http.post<FromData>(url, {
    data: { _cfg: { showTip: true }, ...query },
  });
}

// 获取楼栋楼层树
export function getBuildingTreeByUnitId(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.getBuildingTreeByUnitId, data);
  return $http.post(url, {
    data: { _cfg: { showTip: true } },
  });
}

// 获取组织机构树
export function getOrgTree(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.public.getOrgTree, { ...query });
  return $http.post<any[]>(url, { data: { _cfg: { showTip: true } } });
}
// 获取组织人信息
export function getOrgUserList(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.public.getOrgUserPage);
  return $http.post(url, { data: { _cfg: { showTip: true }, ...query } });
}
// 获取隐患分类
export function getDangerLibrary(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.name.demoRequest.getDangerLibrary);
  return $http.post(url, { data: { _cfg: { showTip: true }, ...data } });
}

// 隐患等级
export function getDangerGrade(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.name.demoRequest.getDangerGrade);
  return $http.post(url, { data: { _cfg: { showTip: true }, ...data } });
}

// 频发典型问题
export function pageHaveOneFindThreeEvent(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.cj.oneThree.pageHaveOneFindThreeEvent, data);
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}

// 频发问题-导出
export function exportHighFrequencyEventList() {
  const url = api.getUrl(api.type.hazard, api.cj.oneThree.exportHighFrequencyEventList);
  return url;
}
