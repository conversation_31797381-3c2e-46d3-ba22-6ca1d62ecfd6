<template>
  <n-modal :show="props.showModal">
    <n-card class="n_card" :title="title" :bordered="false" size="huge" preset="dialog" role="dialog" aria-modal="true">
      <template #header-extra>
        <span class="sp" @click="cancel">×</span>
      </template>
      <div class="content">
        <!-- <div class="org">选择部门</div> -->
        <div class="my-[10px]">
          <n-input v-model:value="selectDept" clearable />
        </div>
        <n-scrollbar class="!h-[320px]">
          <n-tree
            ref="treeRef"
            block-line
            :data="filteredTreeDataList"
            :multiple="props.multiple"
            :key-field="props.keyField"
            :label-field="props.labelField"
            :children-field="props.childrenField"
            :render-switcher-icon="renderSwitcherIconWithExpaned"
            :render-label="renderLabel"
            :theme-overrides="themeOverrides"
            :default-expand-all="props.defaultExpandAll"
            :default-selected-keys="defaultSelectedkeys"
            :default-expanded-keys="defaultExpandedkeys"
            :pattern="selectDept"
            :show-irrelevant-nodes="false"
            @update:selected-keys="setSelectedKeys"
            @click:nodes="onNodeClick"
          />
          <!-- :node-props="nodeProps" -->
        </n-scrollbar>
      </div>

      <template #footer>
        <div class="flex items-center justify-end">
          <n-button class="!mr-[16px]" strong @click="cancel">取消</n-button>
          <n-button strong type="primary" @click="onConfirm">确定</n-button>
        </div>
      </template>
    </n-card>
  </n-modal>
</template>

<script setup lang="ts">
import { NIcon, TreeOption, createDiscreteApi } from 'naive-ui';
import { h, ref, watch } from 'vue';
import Dept from './assets/dept.png';
import { icon_chevron as iconChevronForward } from './assets/index';
import JianGuan from './assets/jianguan.png';
import YeWu from './assets/yewu.png';

import { useStore } from '@/store/index';
const { userInfo } = useStore();

const treeRef = ref(null);
const { message } = createDiscreteApi(['message']);
// 选中样式
const themeOverrides = {
  nodeHeight: '40px',
};
const props: any = defineProps({
  showModal: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: '选择单位',
  },
  dataList: {
    type: Array,
    default: () => [],
  },
  // 是否多选
  multiple: {
    type: Boolean,
    default: true,
  },
  keyField: {
    type: String,
    default: 'id',
  },
  labelField: {
    type: String,
    default: 'text',
  },
  childrenField: {
    type: String,
    default: 'children',
  },
  // 默认选中id
  defaultKeys: {
    type: Array,
    default: () => [],
  },
  // 禁用id
  disabledDeptIds: {
    type: Array,
    default: () => [],
  },
  // 是否展开所有节点
  defaultExpandAll: {
    type: Boolean,
    default: false,
  },
  // 1公司 2 部门
  orgType: {
    type: String,
    default: '2',
  },
  idd: {
    type: String,
  },
  flag: {
    type: Boolean,
    defalut: false,
  },
});

// 搜索框
const selectDept = ref('');
// 默认展开的节点
const defaultExpandedkeys = ref<string[]>([]);
// 默认选择节点
const defaultSelectedkeys = ref<string[]>([]);

const emits = defineEmits(['close', 'success']);
const optionN = ref();
const onNodeClick = (node: any) => {
  console.log(node, '>>>>>>>>>>>>>>>');
};
// #region 左侧树形结构
const nodeProps = ({ option }: { option: TreeOption }) => {
  return {
    onClick() {
      // console.log(option, option);
      // 这里写点击组组织后的逻辑
      optionN.value = option;
    },
  };
};
// 渲染图标
function renderSwitcherIconWithExpaned({ option }: any) {
  // if (option.root === 1) {
  //   return null;
  // } else {
  if (option.children && option.children.length > 0) {
    return h(
      NIcon,
      {
        // style: {
        //   width: '14px',
        //   height: '14px',
        // },
      },
      { default: () => h(iconChevronForward) }
    );
    // }
  }
}
function renderLabel(e: any) {
  // console.log(e, '数据渲染');

  // 如果节点没有子节点，不显示展开图标
  if (e.option.children && e.option.children.length === 0) {
    e.option.isLeaf = true;
    return h(
      'div',
      {
        style: {
          display: 'flex',
          alignItems: 'center',
          width: '100%',
          padding: '10px 0 10px 0',
        },
      },
      [
        h('img', {
          style: {
            width: '16px',
            height: '16px',
            marginRight: '5px',
          },
          src: e.option.attributes?.orgType === '2' ? JianGuan : e.option.attributes?.orgType === '1' ? YeWu : Dept,
        }),
        h(
          'div',
          {
            style: {
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            },
            title: e.option.text,
          },
          e.option.text
        ),
      ]
    );
  } else {
    // 如果节点有子节点，正常显示
    if (e.option.root === 1) {
      // e.option.isLeaf = true;
      return h(
        'div',
        {
          style: {
            display: 'flex',
            alignItems: 'center',
            padding: '10px 0 10px 0',
          },
        },
        [
          h('img', {
            style: {
              width: '16px',
              height: '16px',
              marginRight: '5px',
              // marginLeft: '24px',
            },
            // src: `https://agjp.tanzervas.com/aqsc/v1/img1/systemLogo/${userInfo.zhLogo}.png`,
            src: userInfo.logoPicUrl,
            // userInfo.zhLogo == 'angang'
            //   ? angang
            //   : userInfo.zhLogo == 'yanchang'
            //     ? yanchang
            //     : userInfo.zhLogo == 'zhongdanong'
            //       ? zhongdanong
            //       : changhang,
          }),
          h(
            'div',
            {
              style: {
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              },
              title: e.option.text,
            },
            e.option.text
          ),
        ]
      );
    } else {
      return h(
        'div',
        {
          style: {
            display: 'flex',
            alignItems: 'center',
            padding: '10px 0 10px 0',
            marginLeft: '-10px',
          },
        },
        [
          h('img', {
            style: {
              width: '16px',
              height: '16px',
              marginRight: '5px',
            },
            src: e.option.attributes?.orgType === '2' ? JianGuan : e.option.attributes?.orgType === '1' ? YeWu : Dept,
          }),
          h(
            'div',
            {
              style: {
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              },
              title: e.option.text,
            },
            e.option.text
          ),
        ]
      );
    }
  }
}

const setSelectedKeys = (rowKey: any, rowData: any) => {
  optionN.value = rowData;
};

const treeDataList = ref([]);
const filteredTreeDataList = ref([]);

// watch(
//   () => selectDept.value,
//   (val) => {
//     if (val) {
//       filteredTreeDataList.value = removeEmptyChildren(filterData(treeDataList.value, val));
//     } else {
//       filteredTreeDataList.value = treeDataList.value;
//     }
//   }
// );

function filterData(nodes: any[], filterText: string) {
  return nodes.reduce((acc: any, node: any) => {
    const newNode = { ...node }; // 创建新节点，避免修改原数据
    const filteredChildren = filterData(node.children || [], filterText); // 递归筛选子节点

    // 如果当前节点的text包含filterText或有符合条件的子节点，则保留该节点
    if ((newNode.text && newNode.text.includes(filterText)) || filteredChildren.length > 0) {
      newNode.children = filteredChildren; // 仅保留符合条件的子节点
      acc.push(newNode);
    }

    return acc;
  }, []);
}

watch(
  () => props.showModal,
  (nv) => {
    if (props.dataList?.length) {
      console.log(props.dataList, '>>>>>>>>>>>>>>>>>');
      selectDept.value = '';
      if (props.dataList?.length == 1) {
        let curKey = props.dataList[0].id as string;
        optionN.value = props.dataList[0];
        defaultExpandedkeys.value = [curKey];
        defaultSelectedkeys.value = props.defaultKeys;
      }

      const disabledIdsSet = new Set(props.disabledDeptIds);
      const _RES = ref([]);
      // 筛选公司还是部门
      // orgType: ,0 公司 1 业务单位 2 监管单位
      // if (props.orgType == '1') {
      //   _RES.value = filterByOrgTypeDeep(removeEmptyChildren(props.dataList));

      //   console.log(_RES.value, '>>>>>>>>>>>>>>>>>');
      // } else {
      //   _RES.value = removeEmptyChildren(props.dataList);
      // }
      _RES.value = removeEmptyChildren(addDisabledField(props.dataList, disabledIdsSet));

      filteredTreeDataList.value = treeDataList.value = _RES.value;

      if (filteredTreeDataList.value.length > 0) {
        filteredTreeDataList.value[0].root = 1;
      }
    }
  },
  { immediate: true }
);

// 递归过滤公司与部门
function filterByOrgTypeDeep(data: any) {
  return data
    .map((item: any) => {
      // 检查当前节点的unitStatus，并递归检查子节点
      const hasValidChildren = item.children
        ? item.children.some((child: any) => filterByOrgTypeDeep([child]).length > 0)
        : false;

      // 如果当前节点的unitStatus不为1，或者有有效的子节点
      if (item.attributes.orgType == 1 || hasValidChildren) {
        const newItem = { ...item }; // 创建当前项的副本

        // 如果当前节点有子节点，则递归过滤子节点，并移除空数组（如果没有有效子节点）
        if (newItem.children && newItem.children.length > 0) {
          newItem.children = filterByOrgTypeDeep(newItem.children).filter((child: any) => child !== null);

          // 如果没有子节点符合条件，则移除children属性
          if (newItem.children.length === 0) {
            delete newItem.children;
          }
        }

        return newItem;
      }
      // 如果当前节点的unitStatus为1且没有有效的子节点，则返回null
      return null;
    })
    .filter((item: any) => item !== null); // 过滤掉所有null值
}

function addDisabledField(data: any, disabledIdsSet: any) {
  return data.map((item: any) => {
    const newItem = { ...item }; // 创建当前项的副本以避免直接修改原始数据

    // 检查当前项的ID是否在禁用ID集合中
    if (disabledIdsSet.has(newItem.id)) {
      newItem.disabled = true; // 如果是，则添加disabled字段并设置为true
    } else {
      newItem.disabled = false; // 如果不是，则添加disabled字段并设置为false（这一步是可选的，因为未定义也可以表示false）
    }

    // 如果当前项有子项，则递归处理子项
    if (newItem.children && newItem.children.length > 0) {
      newItem.children = addDisabledField(newItem.children, disabledIdsSet);
    }

    return newItem;
  });
}

function removeEmptyChildren(array: any) {
  return array.map((item: any) => {
    // 创建一个新的对象，使用解构来复制原始属性
    const newItem = { ...item };
    // 检查是否存在children，并且是一个数组
    if (newItem.children && Array.isArray(newItem.children)) {
      newItem.children = removeEmptyChildren(newItem.children); // 递归调用
      // 如果children为空数组，则删除该属性
      if (newItem.children.length === 0) {
        delete newItem.children;
      }
    }

    return newItem; // 返回新对象
  });
}
const cancel = () => {
  emits('close', false);
};
const onConfirm = () => {
  // 有默认选中项，但再次点击没有更新数据，直接关闭
  if (!optionN.value.length && props.defaultKeys.length) return emits('close', false);
  if (!optionN.value.length) return message.error(`请${props.title}`);
  emits('success', optionN.value);
  emits('close', false);
};
</script>

<style lang="scss" scoped>
.sp {
  // font-family: '宋体';
  font-size: 24px;
  cursor: pointer;
}

.n_card {
  width: 600px;
  // height: 500px;
  background-color: #f8f8f8;

  :deep(.n-card-header) {
    // padding: 15px;
    padding-left: 20px;
    padding-right: 20px;
    padding-top: 10px;
    padding-bottom: 10px;
  }

  :deep(.n-card-header .n-card-header__main::before) {
    content: '';
    display: inline-block;
    width: 4px;
    height: 15px;
    margin-right: 5px;
    background-color: #527cff;
  }
}

.list {
  height: 320px !important;
  overflow: auto;
}

.content {
  height: 100%;

  .header {
    display: flex;
    margin-bottom: 10px;

    .text {
      color: #606266;
    }

    .box {
      flex: 1;
      margin-left: 10px;
      min-height: 30px;
      border-radius: 3px;
      border: 1px solid #dcdfe6;
      background-color: #fff;
    }
  }

  .main {
    height: 550px;
    display: flex;
    justify-content: space-between;

    .left {
      width: 280px;
      margin-right: 10px;
      background-color: #fff;
      border-radius: 3px;
      border: 1px solid #e1e1e1;

      .org {
        height: 50px;
        font-size: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f5f7fa;
      }
    }

    .right {
      flex: 1;
      padding: 10px 10px;
      background-color: #fff;
      border-radius: 3px;
      border: 1px solid #e1e1e1;

      .search-box {
        width: 100%;
        margin-bottom: 10px;
        display: flex;
        justify-content: end;
      }
    }
  }
}

// .n-tree-node-switcher {
//   margin-top: 5px;
// }
</style>
