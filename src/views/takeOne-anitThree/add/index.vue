<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="hazard-capture com-g-row-a1 relative">
    <ComBread :data="breadData" />

    <div class="p-[20px] bg-[#EEF7FF] border border-white rounded">
      <n-scrollbar class="h-[100vh] !pb-[0]">
        <Header :title="'隐患基本信息'" :blueIcon="true">
          <template #right>
            <n-button :style="`height: ${toVh(28)}`" type="primary" @click="add()">新增隐患</n-button>
          </template>
        </Header>
        <n-scrollbar class="!h-[600px] w-[100%]">
          <div v-for="(item, index) of formData" :key="index">
            <n-form :model="item" :rules="rules" ref="formInstRef" label-width="auto" require-mark-placement="left">
              <!-- label-placement="left" -->
              <div class="flex items-center px-[12px] mt-[16px]" style="justify-content: space-between">
                <div class="text-[18px] mb-[8px] mt-[-8px]" style="font-weight: bold">
                  {{ `隐患${index + 1}` }}
                </div>
                <div>
                  <n-button v-if="formData.length > 1" type="error" @click="formData.splice(index, 1)"
                    >删除任务</n-button
                  >
                </div>
              </div>

              <div class="flex flex-wrap -mx-4">
                <div class="w-full md:w-1/4 px-4">
                  <n-form-item label="检查类型：" path="checkType">
                    <n-select
                      v-model:value="item.checkType"
                      class="w-full flex items-center"
                      :options="sourceOptions"
                      placeholder="请选择"
                      clearable
                      @update:value="(rowKey: string, rowData: any) => (item.checkTypeName = rowData.label)"
                    />
                  </n-form-item>
                </div>
                <div class="w-full md:w-1/4 px-4">
                  <n-form-item label="所在单位：" path="objUnitId">
                    <n-tree-select
                      filterable
                      v-model:value="item.objUnitId"
                      :options="unitOptions"
                      placeholder="请选择"
                      label-field="text"
                      key-field="id"
                      children-field="children"
                      :default-expanded-keys="defaultExpandedKeys"
                      @update:value="(rowKey: string, rowData: any) => objUnitIdChange(rowData, index)"
                      :render-label="renderLabelIcon"
                    />
                  </n-form-item>
                </div>

                <div class="w-full md:w-1/4 px-4">
                  <n-form-item label="任务截止日期：" path="type">
                    <n-date-picker
                      class="w-full"
                      v-model:value="item.taskEndTime"
                      type="date"
                      :is-date-disabled="disablePreviousDate"
                      :actions="[]"
                    />
                  </n-form-item>
                </div>
                <div class="w-full md:w-1/4 px-4">
                  <n-form-item label="隐患分类：" path="essentialFactorClassId">
                    <n-cascader
                      class="essentialFactor-cascader"
                      :virtual-scroll="true"
                      :ellipsis-tag-popover-props="{ trigger: 'manual' }"
                      v-model:value="item.essentialFactorClassId"
                      placeholder="请选择"
                      :options="classificationOptions"
                      key-field="id"
                      check-strategy="child"
                      label-field="className"
                      value-field="id"
                      children-field="children"
                    />
                    <!-- <n-tree-select
                      class="essentialFactor-cascader"
                      :virtual-scroll="true"
                      :ellipsis-tag-popover-props="{ trigger: 'manual' }"
                      v-model:value="item.essentialFactorClassId"
                      placeholder="请选择"
                      :options="classificationOptions"
                      key-field="id"
                      check-strategy="child"
                      label-field="className"
                      value-field="id"
                      children-field="children"
                    /> -->
                  </n-form-item>
                </div>
                <div class="w-full md:w-1/4 px-4">
                  <n-form-item label="隐患等级:" path="essentialFactorGradeId">
                    <n-select
                      v-model:value="item.essentialFactorGradeId"
                      :render-label="renderLabel"
                      label-field="gradeName"
                      value-field="id"
                      clearable
                      :options="optionsGrade"
                    />
                  </n-form-item>
                </div>

                <div class="w-full md:w-1/4 px-4">
                  <n-form-item label="楼栋楼层：" path="floorId">
                    <n-tree-select
                      class="essentialFactor-cascader"
                      :virtual-scroll="true"
                      :ellipsis-tag-popover-props="{ trigger: 'manual' }"
                      v-model:value="item.floorId"
                      placeholder="请选择"
                      :options="buildingTree"
                      key-field="value"
                      check-strategy="child"
                      label-field="label"
                      value-field="value"
                      children-field="children"
                      @update:value="(rowKey: string, rowData: any) => buildingChange(rowData, index)"
                    />
                  </n-form-item>
                </div>

                <div class="w-full md:w-1/4 px-4">
                  <n-form-item label="隐患位置:" path="hazardPosition" ref="hazardPositionRef">
                    <n-input placeholder="请输入" v-model:value="item.hazardPosition" maxlength="50" class="w-[100%]">
                      <template #suffix>
                        <n-icon @click="chooseLocation(item, index)" size="20" color="#2d4ef0" class="cursor-pointer">
                          <!-- 禁用地图按钮 -->
                          <!-- <n-icon size="20" color="#9c9fa6" style="position: relative; left: -25px"> -->
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            xmlns:xlink="http://www.w3.org/1999/xlink"
                            viewBox="0 0 24 24"
                          >
                            <path
                              d="M20 1v3h3v2h-3v3h-2V6h-3V4h3V1h2zm-8 12c1.1 0 2-.9 2-2s-.9-2-2-2s-2 .9-2 2s.9 2 2 2zm2-9.75V7h3v3h2.92c.05.39.08.79.08 1.2c0 3.32-2.67 7.25-8 11.8c-5.33-4.55-8-8.48-8-11.8C4 6.22 7.8 3 12 3c.68 0 1.35.08 2 .25z"
                              fill="currentColor"
                            />
                          </svg>
                        </n-icon>
                      </template>
                    </n-input>
                  </n-form-item>
                </div>
                <div class="w-full md:w-1/1 px-4">
                  <n-form-item label="隐患整改人：" path="reformUserJson">
                    <n-tag
                      v-for="(person, perIdx) in item.reformUserJson"
                      :key="person.reformUserId"
                      closable
                      @close="delPerson(item.reformUserJson, perIdx)"
                      class="mr-1"
                    >
                      {{ person.reformUserName }}
                    </n-tag>
                    <n-icon size="30" @click="addUsers(index, item.objUnitId)">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                        <path
                          d="M368.5 240H272v-96.5c0-8.8-7.2-16-16-16s-16 7.2-16 16V240h-96.5c-8.8 0-16 7.2-16 16 0 4.4 1.8 8.4 4.7 11.3 2.9 2.9 6.9 4.7 11.3 4.7H240v96.5c0 4.4 1.8 8.4 4.7 11.3 2.9 2.9 6.9 4.7 11.3 4.7 8.8 0 16-7.2 16-16V272h96.5c8.8 0 16-7.2 16-16s-7.2-16-16-16z"
                        />
                      </svg>
                    </n-icon>
                  </n-form-item>
                </div>
                <div class="w-full md:w-1/1 px-4">
                  <n-form-item label="隐患描述：" path="hazardDescribe">
                    <n-input
                      v-model:value="item.hazardDescribe"
                      show-count
                      maxlength="200"
                      placeholder="请输入"
                      type="textarea"
                      :autosize="{
                        minRows: 3.5,
                        maxRows: 3.5,
                      }"
                    />
                  </n-form-item>
                </div>
                <div class="w-full md:w-1/1 px-4">
                  <n-form-item path="files" label="隐患图片：">
                    <ImgUpload
                      v-if="watermarkData"
                      :watermarkData="watermarkData"
                      :data="item.files"
                      @update="handleUpdate($event, index)"
                      :size="10"
                      :max="3"
                      :type="'image-card'"
                      :multiple="false"
                      :showBtn="false"
                      class="com-upload-img"
                    />
                    <!-- <ImgUpload v-else mode="detail" :data="form.files" :type="'image-card'" :showBtn="false" /> -->
                  </n-form-item>
                </div>
              </div>
            </n-form>
          </div>
        </n-scrollbar>
        <Header :title="'类比排查单位'" :blueIcon="true">
          <template #right>
            <n-button :style="`height: ${toVh(28)}`" type="primary" @click="showOrgTree = true">添加单位</n-button>
          </template>
        </Header>

        <n-data-table
          class="h-full com-table mt-[24px]"
          :max-height="180"
          remote
          striped
          :columns="columns"
          :data="tableData"
          :bordered="false"
        />

        <div class="p-[10px] flex justify-center w-full gap-[20px]">
          <n-button type="primary" size="small" ghost @click="goBack"> 取消 </n-button>
          <n-button type="primary" size="small" :loading="submitLoading" :disabled="submitLoading" @click="submit">
            发布
          </n-button>
        </div>
      </n-scrollbar>
    </div>
    <n-modal
      v-model:show="showMap"
      title="隐患位置"
      preset="dialog"
      :show-icon="false"
      style="width: 820px; background-color: #fff; height: 650px; overflow-y: auto"
      :auto-focus="false"
    >
      <mapFloor
        :floorInfo="floorInfo"
        @addMark="addMark"
        :pointer="pointer"
        :isAddMark="true"
        style="height: 500px"
        @cancel="cancel"
      />
    </n-modal>

    <selectTree
      :showModal="showOrgTree"
      :dataList="unitOptions"
      :disabledDeptIds="tableData.map((item) => item.unitId)"
      @close="showOrgTree = false"
      @success="selectOrgTree"
    />
    <SelectUser
      title="选择整改人员"
      :unitId="currentUnitId"
      :showSelectUserModal="showSelectUserModal"
      :userType="userType"
      :userlist="currentUserList"
      @close="showSelectUserModal = false"
      @success="confirmSelect"
    />
  </div>
</template>

<script lang="ts" setup>
import { $toast } from '@/common/shareContext/useToastCtx.ts';
import ComBread from '@/components/breadcrumb/ComBread.vue';
import { IBreadData } from '@/components/breadcrumb/type';
import Header from '@/components/header/ComHeaderB.vue';
import mapFloor from '@/components/indoorMap/index.vue';
import business from '@/components/unitSelect/business.png'; //业务单位
import section from '@/components/unitSelect/section.png'; //部门
import supervise from '@/components/unitSelect/supervise.png'; //监管单位
import { ImgUpload } from '@/components/upload';
import { useStore } from '@/store';
import { toVh } from '@/utils/fit';
import { getWatermarkBizDataApi } from '@/utils/getWatermarkBizData';
import SelectUser from '@/views/inspection-planning/planned-management/Add/SelectUser.vue';
import {
  addOneToThree,
  getBuildingTreeByUnitId,
  getDangerGrade,
  getDangerLibrary,
  getOrgTree,
} from '@/views/takeOne-anitThree/fetchData';
import dayjs from 'dayjs';
import { NButton, useMessage } from 'naive-ui';
import { h, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import selectTree from './selectTree.vue';
const message = useMessage();
// 获取水印配置
const watermarkData = ref<any>();
getWatermarkBizDataApi().then((data: any) => {
  watermarkData.value = data;
});

const { userInfo } = useStore();
// const logo_url = `https://agjp.tanzervas.com/aqsc/v1/img1/systemLogo/${userInfo.zhLogo}.png`;
const logo_url = userInfo.logoPicUrl;
const sourceOptions: { label: string; value: string }[] = [
  {
    label: '省级督导检查',
    value: '0',
  },
  {
    label: '市级督导检查',
    value: '1',
  },
  {
    label: '行业专家指导检查',
    value: '2',
  },
  {
    label: '集团督导检查',
    value: '3',
  },
  {
    label: '公司督导检查',
    value: '4',
  },
];

const breadData: IBreadData[] = [
  { name: '隐患治理系统' },
  { name: '举一反三', routeRaw: { name: 'takeOneAnitThree' }, clickable: true },
  { name: '新增' },
];
const router = useRouter();
const getFullYear7 = () => {
  let now = new Date();
  // 设置时间为当前时间的23:59:59ss
  now.setHours(23, 59, 59, 999);

  // 往后推7天
  now.setDate(now.getDate() + 7);
  // 获取时间戳（毫秒）
  return now.getTime();
};

const formData = ref<any>([
  {
    checkType: null, //检查类型: 0-省级督导检查;1-市级督导检查;2-行业专家指导检查;3-集团督导检查;4-公司督导检查;5-频发典型问题
    checkTypeName: '', // 检查类型名称
    objUnitId: null, //隐患单位
    objUnitName: '', //隐患单位名称
    taskEndTime: getFullYear7(), //任务结束时间
    taskStartTime: null, //任务开始时间
    taskType: 1, //任务类型: 任务类型:1-举一反三;2-频发典型

    essentialFactorClassId: null, // 隐患分类
    essentialFactorGradeId: null, // 隐患等级
    buildingId: null, // 楼栋id
    floorId: '', // 楼层id
    hazardPosition: '', // 隐患位置
    mapX: '', // 地图x坐标
    mapY: '', // 地图y坐标
    hazardDescribe: '', // 隐患描述
    reformUserJson: [], //隐患整改人员
    files: [],
    createBy: userInfo.id, //创建人
    createByName: userInfo.userName, //创建人名称
    unitId: userInfo.unitId, // 创建单位id
    unitName: userInfo.unitName, // 创建单位名称
  },
]);

function disablePreviousDate(ts: number) {
  return ts < Date.now();
}

const rules = {
  checkType: {
    required: true,
    message: '请选择检查类型',
    trigger: ['change', 'blur'],
  },
  source: {
    required: true,
    message: '请选择隐患来源',
    trigger: ['change', 'blur'],
  },
  objUnitId: {
    required: true,
    message: '请选择所在单位',
    trigger: ['change', 'blur'],
  },
  taskEndTime: {
    required: true,
    message: '请选择任务截止日期',
    trigger: ['change', 'blur'],
  },
  essentialFactorClassId: {
    required: true,
    message: '请选择隐患分类',
    trigger: ['change', 'blur'],
  },
  essentialFactorGradeId: {
    required: true,
    message: '请选择隐患等级',
    trigger: ['change', 'blur'],
  },
  floorId: {
    required: true,
    message: '请选择楼栋楼层',
    trigger: ['change', 'blur'],
  },
  hazardPosition: {
    required: true,
    trigger: ['blur', 'change'],
    message: '请输入隐患位置',
  },
  reformUserJson: {
    required: true,
    validator: (rule: any, value: any) => {
      if (!value || value.length === 0) {
        return new Error('请选择隐患整改人');
      }
      return true;
    },
  },
  hazardDescribe: {
    required: true,
    message: '请输入隐患描述',
    trigger: ['blur'],
  },
  files: {
    required: true,
    validator: (rule: any, value: any) => {
      if (!value.length) {
        return new Error('请上传图片');
      } else {
        return true;
      }
    },
  },
};

const columns = [
  {
    title: '序号',
    key: 'index',
    align: 'left',
    width: 100,
    render: (row: any, index: number) => index + 1,
  },
  {
    title: '单位名称',
    key: 'unitName',
    align: 'left',
  },
  {
    title: '操作',
    key: 'action',
    align: 'left',
    width: 120,
    render(row: any, index: number) {
      return h(
        NButton,
        {
          size: 'small',
          type: 'error',
          ghost: true,
          onClick: () => tableData.value.splice(index, 1),
        },
        { default: () => '删除' }
      );
    },
  },
];

const tableData = ref<any[]>([]);

// 所在单位
const unitOptions = ref<any[]>([]);
// 默认展开
const defaultExpandedKeys = ref<any[]>([]);

const currentUserIdx = ref(0);
const currentUnitId = ref('');
const showSelectUserModal = ref(false);
const userType = ref(1);
const currentUserList = ref([]);

// 确定选择人员
const confirmSelect = (users: any) => {
  if (!users.length) return;
  formData.value[currentUserIdx.value].reformUserJson = users.map((item: any) => {
    return {
      reformUserId: item.id,
      reformUserName: item.userName,
      zhId: userInfo.zhId,
      ...item,
    };
  });
};
// 删除人员
const delPerson = (reformUserJson: any, perIdx: number) => {
  reformUserJson.splice(perIdx, 1);
};
// 添加人员
const addUsers = (idx: number, objUnitId: any) => {
  console.log('🚀 ~ addUsers ~ objUnitId:', objUnitId);

  if (!objUnitId) return message.warning('请先选择所在单位');
  currentUnitId.value = objUnitId;
  currentUserIdx.value = idx;
  if (
    !formData.value[currentUserIdx.value].reformUserJson ||
    !formData.value[currentUserIdx.value].reformUserJson.length
  ) {
    currentUserList.value = [];
  } else {
    currentUserList.value = formData.value[currentUserIdx.value].reformUserJson.map((item: any) => {
      return {
        checkUserId: item.reformUserId,
        userName: item.reformUserName,
        ...item,
      };
    });
  }
  // console.log("currentUserList.value>>>", currentUserList.value)
  showSelectUserModal.value = true;
};

const getTreeItemIco = (row: any) => {
  if (row.parentId == -1) return logo_url;
  if (row.attributes.orgType == '0') {
    return section;
  } else if (row.attributes.orgType == '1') {
    return business;
  } else {
    return supervise;
  }
};

const renderLabelIcon = ({ option }) => {
  return h(
    'div',
    {
      class: 'flex flex-row',
      onClick: (e) => {
        // 自定义点击逻辑（可选）
        if (option.attributes.orgType == '2') {
          e.stopPropagation();
          return;
        }
      },
    },
    [
      // 使用 img 标签
      h('img', {
        class: option.attributes.orgType == '2' ? 'cursor-no-drop' : '',
        src: getTreeItemIco(option),
        alt: 'icon',
        style: { width: '20px', height: '20px' },
      }),
      h(
        'span',
        option.attributes.orgType == '2'
          ? { class: 'w-full text-[14px] pl-[12px] truncate cursor-no-drop' }
          : { class: 'w-full text-[14px] pl-[12px] truncate' },
        option.text
      ),
    ]
  );
};

const getUnitOptions = async () => {
  let res: any = await getOrgTree({ orgCode: userInfo.unitId, unitStatus: 1 });
  unitOptions.value = removeEmptyChildren(res.data) || [];
  if (res.data.length) defaultExpandedKeys.value.push(res.data[0].id);
};
function removeEmptyChildren(array: any) {
  return array.map((item: any) => {
    // 创建一个新的对象，使用解构来复制原始属性
    const newItem = { ...item };
    // 检查是否存在children，并且是一个数组
    if (newItem.children && Array.isArray(newItem.children)) {
      newItem.children = removeEmptyChildren(newItem.children); // 递归调用
      // 如果children为空数组，则删除该属性
      if (newItem.children.length === 0) {
        delete newItem.children;
      }
    }

    return newItem; // 返回新对象
  });
}
// 选中单位
const objUnitIdChange = (rowData: any, index: number) => {
  formData.value[index].reformUserJson = [];
  // 不展示全量的单位
  // const result = findTextPath(unitOptions.value, rowData['id']);
  const result = rowData.text;
  formData.value[index].objUnitName = result;
  // 清除楼栋楼层位置数据
  formData.value[index].floorId = '';
  formData.value[index].buildingId = '';
  formData.value[index].hazardPosition = '';
  formData.value[index].mapX = '';
  formData.value[index].mapY = '';
  // 获取楼栋树
  getBuildingTreeByUnitIdApi(rowData.id);
};
// 隐患分类
const classificationOptions = ref<any[]>([]);

function getLibrary() {
  let params = {
    // rootFlag: 1,
    unitId: userInfo.topUnitId,
    parentId: '0',
    delFlag: 0,
  };
  getDangerLibrary(params).then((res: any) => {
    classificationOptions.value = res.data || [];
  });
}

// 隐患级别
const optionsGrade = ref<any>([]);
const renderLabel = (option: any) => {
  return h(
    'div',
    {
      title: option.gradeName,
    },
    option.gradeName.length > 20 ? option.gradeName.substring(0, 20) + '...' : option.gradeName
  );
};

function getDangerGradeList() {
  let params = {
    delFlag: 0,
    unitId: userInfo.topUnitId,
  };
  getDangerGrade(params).then((res: any) => {
    optionsGrade.value = res.data;
  });
}

const buildingTree = ref<any[]>([]);
const showMap = ref(false);
const floorInfo = ref({
  unitId: '',
  buildingId: '',
  floorId: '',
  // unitId: '7f0624b79f8940bb98d6a0340bcb1a10',
  // buildingId: 'AHHF_QHHFY_20180408_002',
  // floorId: 'AHHF_QHHFY_20180408_002_U001',
  // floorAreaImg: 'image/floorImage/AHHF_QHHFY_20180408/002/U001/1.jpg',
});
const mapIndex = ref(0);

const pointer = ref<any>({ x: 0, y: 0 }); // 回显点位
/** 获取楼栋楼层树数据 */
const getBuildingTreeByUnitIdApi = async (unitId: string) => {
  const res: any = await getBuildingTreeByUnitId({ unitId });
  buildingTree.value = res.data.options;
};

const buildingChange = (row: any, index: number) => {
  floorInfo.value.floorId = row.value;
  floorInfo.value.buildingId = row.parentId;
  formData.value[index].buildingId = row.parentId;
  formData.value[index].mapX = 0;
  formData.value[index].mapY = 0;
  formData.value[mapIndex.value].hazardPosition = '';

  // if (!row) {
  //   formData.value[index].buildingId = null;
  //   formData.value[index].floorId = null;
  //   formData.value[index].floorAreaImg = null;
  //   return false;
  // }
  // formData.value[index].buildingId = row.id;

  // floorInfo.value.unitId = userInfo.unitId;
  // floorInfo.value.buildingId = row.parentId;
  // floorInfo.value.floorId = row.value;
};

function chooseLocation(row: any, index: number) {
  if (!row.floorId) return $toast.warning('请选择楼栋楼层');
  mapIndex.value = index;
  pointer.value.x = row.mapX;
  pointer.value.y = row.mapY;
  showMap.value = true;
}

const hazardPositionRef = ref<any>(null);

function addMark(data: any) {
  formData.value[mapIndex.value].mapX = data.x;
  formData.value[mapIndex.value].mapY = data.y;
  if (!formData.value[mapIndex.value].hazardPosition) formData.value[mapIndex.value].hazardPosition = data.text;
  hazardPositionRef.value[mapIndex.value].restoreValidation();
  mapIndex.value = 0;
  showMap.value = false;
}
const cancel = () => {
  showMap.value = false;
};

// 组织
const showOrgTree = ref(false);
const selectOrgTree = (row: any) => {
  let arr = row?.map((item: any) => {
    let name: any = '';

    // if (item.attributes.orgType === '0') {
    const result = findTextPath(unitOptions.value, item['id']);
    name = result;
    // }
    return {
      unitName: name,
      unitId: item.id,
      type: item.attributes.orgType,
    };
  });
  tableData.value = [...tableData.value, ...arr];
  showOrgTree.value = false;
};

function findNodeById(data: any, id: any) {
  for (const node of data) {
    if (node.id === id) {
      return node; // 找到目标节点，返回
    }

    if (node.children && node.children.length > 0) {
      const foundInChildren = findNodeById(node.children, id); // 递归查找子节点
      if (foundInChildren) {
        return foundInChildren; // 如果在子节点中找到了，返回
      }
    }
  }

  return null; // 如果没有找到，返回 null
}
function findTextPath(data: any, id: any) {
  const foundNode = findNodeById(data, id); // 使用递归查找节点

  if (!foundNode) {
    return null; // 如果没有找到返回 null
  }

  const pathElements = [foundNode.text]; // 保存节点的 text

  // 向上查找直到找到 orgType !== '0'
  let currentNode = foundNode;
  while (currentNode.parentId) {
    const parentNode = findNodeById(data, currentNode.parentId); // 查找父节点
    if (!parentNode) break; // 如果没有父节点则退出

    // 将父节点的 text 加入 path
    pathElements.unshift(parentNode.text);

    // 如果父节点的 attributes.orgType !== '0'，停止搜索
    // if (parentNode.attributes.orgType !== '0') {
    //   break;
    // }

    currentNode = parentNode; // 更新当前节点
  }

  return pathElements.join('/'); // 返回拼接的结果
}

const add = () => {
  formData.value.push(JSON.parse(JSON.stringify(formData.value[formData.value.length - 1])));
};

const handleUpdate = (res: any[], index: number) => {
  formData.value[index].files = res || [];
};

const goBack = () => router.push(breadData[1]['routeRaw'] as any);

const formInstRef = ref<any>();
const submitLoading = ref(false);
const submit = async () => {
  try {
    const allValid = formInstRef.value.map((item: any) => item.validate());
    await Promise.all(allValid);

    formData.value.forEach((item: any, idx: number) => {
      // 综合检查
      let flag = false;
      if (!item.mapX) flag = true;

      if (flag) {
        let msg = `隐患${idx + 1}请选择点位`;
        $toast.error(msg);
        throw new Error(msg);
      }
    });

    if (!tableData.value.length) return $toast.warning('请选择类比排查单位');
    submitLoading.value = true;
    let data = {
      addFormList: formData.value.map((item: any) => {
        return {
          ...item,
          taskStartTime: dayjs(new Date().getTime()).format('YYYY-MM-DD HH:mm:ss'),
          taskEndTime: dayjs(item.taskEndTime).format('YYYY-MM-DD HH:mm:ss'),
        };
      }),
      haveOneFindThreeUnitList: tableData.value,
    };
    addOneToThree(data)
      .then((res: any) => {
        if (res.code == 'success') {
          $toast.success('提交成功');
          goBack();
        } else {
          submitLoading.value = false;
        }
      })
      .catch(() => {
        submitLoading.value = false;
      });
  } catch (error: any) {
    if (error.length) {
      const first = error[0][0];
      if (first.message) {
        $toast.error(first.message);
      }
    }
  }
};

onMounted(() => {
  getUnitOptions();
  getLibrary();
  getDangerGradeList();
});
</script>

<style scoped lang="scss"></style>
