<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="hazard-capture com-g-row-a1 relative">
    <ComBread :data="breadData" />

    <div class="p-[20px] bg-[#EEF7FF] border border-white rounded">
      <n-scrollbar class="h-[100vh] !pb-[0]">
        <Header :title="'隐患基本信息'" :blueIcon="true">
          <template #right>
            <!-- <n-button type="primary" @click="add()">新增隐患</n-button> -->
          </template>
        </Header>
        <n-scrollbar class="!h-[380px] !pb-[0]">
          <div v-for="(item, index) of formData" :key="index">
            <n-form
              class="mt-[20px]"
              :model="item"
              :rules="rules"
              ref="formInstRef"
              label-width="auto"
              require-mark-placement="left"
            >
              <!-- label-placement="left" -->
              <div class="flex items-center" style="justify-content: space-between">
                <div class="text-[18px] mb-[8px]">{{ `隐患${index + 1}` }}</div>
                <div>
                  <!-- <n-bssutton v-if="index != 0" type="error" @click="formData.splice(index, 1)">删除任务</n-button> -->
                </div>
              </div>

              <div class="flex flex-wrap -mx-4">
                <div class="w-full md:w-1/4 px-4">
                  <n-form-item label="检查类型：" path="checkType">
                    <n-select
                      disabled
                      v-model:value="item.checkType"
                      class="w-full flex items-center"
                      :options="sourceOptions"
                      placeholder="请选择"
                      clearable
                      @update:value="(rowKey: string, rowData: any) => (item.checkTypeName = rowData.label)"
                    />
                  </n-form-item>
                </div>
                <div class="w-full md:w-1/4 px-4">
                  <n-form-item label="隐患分类：" path="essentialFactorClassId">
                    <n-cascader
                      disabled
                      class="essentialFactor-cascader"
                      :virtual-scroll="true"
                      :ellipsis-tag-popover-props="{ trigger: 'manual' }"
                      v-model:value="item.essentialFactorClassId"
                      placeholder="请选择"
                      :options="classificationOptions"
                      key-field="id"
                      check-strategy="child"
                      label-field="className"
                      value-field="id"
                      children-field="children"
                    />
                    <!-- <n-tree-select
                        class="essentialFactor-cascader"
                        :virtual-scroll="true"
                        :ellipsis-tag-popover-props="{ trigger: 'manual' }"
                        v-model:value="item.essentialFactorClassId"
                        placeholder="请选择"
                        :options="classificationOptions"
                        key-field="id"
                        check-strategy="child"
                        label-field="className"
                        value-field="id"
                        children-field="children"
                      /> -->
                  </n-form-item>
                </div>
                <div class="w-full md:w-1/4 px-4">
                  <n-form-item label="隐患等级:" path="essentialFactorGradeId">
                    <n-select
                      disabled
                      v-model:value="item.essentialFactorGradeId"
                      :render-label="renderLabel"
                      label-field="gradeName"
                      value-field="id"
                      clearable
                      :options="optionsGrade"
                    />
                  </n-form-item>
                </div>

                <div class="w-full md:w-1/4 px-4">
                  <n-form-item label="任务截止日期：" path="type">
                    <n-date-picker
                      class="w-full"
                      v-model:value="item.taskEndTime"
                      type="date"
                      :is-date-disabled="disablePreviousDate"
                      :actions="[]"
                    />
                  </n-form-item>
                </div>

                <div class="w-full h-[12px]"></div>

                <div class="w-full md:w-1/2 px-4">
                  <n-form-item label="隐患描述：" path="hazardDescribe">
                    <n-input
                      disabled
                      v-model:value="item.hazardDescribe"
                      show-count
                      maxlength="200"
                      placeholder="请输入隐患描述"
                      type="textarea"
                      :autosize="{
                        minRows: 3.5,
                        maxRows: 3.5,
                      }"
                    />
                  </n-form-item>
                </div>
              </div>
            </n-form>
          </div>
        </n-scrollbar>
        <Header :title="'类比排查单位'" :blueIcon="true">
          <template #right>
            <n-button type="primary" @click="showOrgTree = true">添加单位</n-button>
          </template>
        </Header>

        <n-data-table
          class="h-full com-table mt-[24px]"
          :max-height="180"
          remote
          striped
          :columns="columns"
          :data="tableData"
          :bordered="false"
        />

        <div class="absolute left-0 bottom-[-10px] p-[10px] flex justify-center w-full gap-[20px]">
          <n-button type="primary" size="small" ghost @click="goBack"> 取消 </n-button>
          <n-button type="primary" size="small" :loading="submitLoading" :disabled="submitLoading" @click="submit">
            发布
          </n-button>
        </div>
      </n-scrollbar>
    </div>

    <selectTree
      :showModal="showOrgTree"
      :dataList="unitOptions"
      :disabledDeptIds="tableData.map((item) => item.unitId)"
      @close="showOrgTree = false"
      @success="selectOrgTree"
    />
  </div>
</template>

<script lang="ts" setup>
import { h, ref, onMounted } from 'vue';
import { NButton } from 'naive-ui';
import ComBread from '@/components/breadcrumb/ComBread.vue';
import Header from '@/components/header/ComHeaderB.vue';
import { IBreadData } from '@/components/breadcrumb/type';
import selectTree from './selectTree.vue';
import { $toast } from '@/common/shareContext/useToastCtx.ts';

import {
  getOrgTree,
  getOrgUserList,
  getDangerLibrary,
  getDangerGrade,
  getBuildingTreeByUnitId,
  addOneToThree,
} from '@/views/takeOne-anitThree/fetchData';

import { useRoute, useRouter } from 'vue-router';
import dayjs from 'dayjs';
import { useStore } from '@/store';

const { userInfo } = useStore();

const sourceOptions: { label: string; value: string }[] = [
  {
    label: '省级督导检查',
    value: '0',
  },
  {
    label: '市级督导检查',
    value: '1',
  },
  {
    label: '行业专家指导检查',
    value: '2',
  },
  {
    label: '集团督导检查',
    value: '3',
  },
  {
    label: '公司督导检查',
    value: '4',
  },
  {
    label: '频发典型问题',
    value: '5',
  },
];

const breadData: IBreadData[] = [
  { name: '隐患治理系统' },
  { name: '举一反三', routeRaw: { name: 'takeOneAnitThree', query: { tabs: '2' } }, clickable: true },
  { name: '新增' },
];
const router = useRouter();
const route = useRoute();

const getFullYear7 = () => {
  let now = new Date();
  // 设置时间为当前时间的23:59:59ss
  now.setHours(23, 59, 59, 999);

  // 往后推7天
  now.setDate(now.getDate() + 7);
  // 获取时间戳（毫秒）
  return now.getTime();
};

const formData = ref<any>([
  {
    checkType: null, //检查类型: 0-省级督导检查;1-市级督导检查;2-行业专家指导检查;3-集团督导检查;4-公司督导检查;5-频发典型问题
    checkTypeName: '', // 检查类型名称
    objUnitId: null, //隐患单位
    objUnitName: '', //隐患单位名称
    reformUserId: null, //整改人员id
    reformUserName: '', //整改人员姓名
    taskEndTime: getFullYear7(), //任务结束时间
    taskStartTime: null, //任务开始时间
    taskType: 1, //任务类型: 任务类型:1-举一反三;2-频发典型

    essentialFactorClassId: null, // 隐患分类
    essentialFactorGradeId: null, // 隐患等级
    buildingId: null, // 楼栋id
    floorId: '', // 楼层id
    hazardPosition: '', // 隐患位置
    mapX: '', // 地图x坐标
    mapY: '', // 地图y坐标
    hazardDescribe: '', // 隐患描述
    files: [],
    createBy: userInfo.id, //创建人
    createByName: userInfo.userName, //创建人名称
    unitId: userInfo.unitId, // 创建单位id
    unitName: userInfo.unitName, // 创建单位名称
  },
]);

if (route.query?.data) {
  let data = JSON.parse(route.query.data as any);
  formData.value = data.map((item: any) => {
    item.taskType = 2;
    item.checkType = '5';
    item.checkTypeName = '频发典型问题';
    item.taskStartTime = null;
    item.taskEndTime = getFullYear7();
    item.createBy = userInfo.id; //创建人
    item.createByName = userInfo.userName; //创建人名称
    item.unitId = userInfo.unitId; // 创建单位id
    item.unitName = userInfo.unitName; // 创建单位名称
    item.essentialFactorClassId = item.hazardType;
    item.essentialFactorGradeId = item.hazardLevel;
    item.hazardDescribe = item.hazardDesc;
    return item;
  });
}

function disablePreviousDate(ts: number) {
  return ts < Date.now();
}

const rules = {
  source: {
    required: true,
    message: '请选择隐患来源',
    trigger: ['change', 'blur'],
  },
  objUnitId: {
    required: true,
    message: '请选择单位名称',
    trigger: ['change', 'blur'],
  },
  reformUserId: {
    required: true,
    message: '请选择隐患整改人',
    trigger: ['change', 'blur'],
  },
  taskEndTime: {
    required: true,
    message: '请选择任务截止日期',
    trigger: ['change', 'blur'],
  },
  essentialFactorClassId: {
    required: true,
    message: '请选择隐患分类',
    trigger: ['change', 'blur'],
  },
  essentialFactorGradeId: {
    required: true,
    message: '请选择隐患等级',
    trigger: ['change', 'blur'],
  },
  buildingId: {
    required: true,
    message: '请选择楼栋',
    trigger: ['change', 'blur'],
  },
  hazardPosition: {
    required: true,
    message: '请输入隐患位置',
    trigger: ['change', 'blur'],
  },
  hazardDescribe: {
    required: true,
    message: '请输入隐患描述',
    trigger: ['change', 'blur'],
  },
};

const columns = [
  {
    title: '序号',
    key: 'index',
    align: 'left',
    width: 100,
    render: (row: any, index: number) => index + 1,
  },
  {
    title: '单位名称',
    key: 'unitName',
    align: 'left',
  },
  {
    title: '操作',
    key: 'action',
    align: 'left',
    width: 120,
    render(row: any, index: number) {
      return h(
        NButton,
        {
          size: 'small',
          type: 'error',
          ghost: true,
          onClick: () => tableData.value.splice(index, 1),
        },
        { default: () => '删除' }
      );
    },
  },
];

const tableData = ref<any[]>([]);

// 所在单位
const unitOptions = ref<any[]>([]);
// 默认展开
const defaultExpandedKeys = ref<any[]>([]);
const getUnitOptions = async () => {
  let res: any = await getOrgTree({ orgCode: userInfo.unitId, unitStatus: 1 });
  unitOptions.value = removeEmptyChildren(res.data) || [];
  if (res.data.length) defaultExpandedKeys.value.push(res.data[0].id);
};
function removeEmptyChildren(array: any) {
  return array.map((item: any) => {
    // 创建一个新的对象，使用解构来复制原始属性
    const newItem = { ...item };
    // 检查是否存在children，并且是一个数组
    if (newItem.children && Array.isArray(newItem.children)) {
      newItem.children = removeEmptyChildren(newItem.children); // 递归调用
      // 如果children为空数组，则删除该属性
      if (newItem.children.length === 0) {
        delete newItem.children;
      }
    }

    return newItem; // 返回新对象
  });
}
// 隐患分类
const classificationOptions = ref<any[]>([]);

function getLibrary() {
  let params = {
    // rootFlag: 1,
    delFlag: 0,
  };
  getDangerLibrary(params).then((res: any) => {
    classificationOptions.value = buildTree(res.data);
    console.log(buildTree(res.data), '123');
  });
}
function buildTree(data: any[]): any[] {
  const map: Record<string, any> = {};
  const tree: any[] = [];

  // 将每个节点放入 map 中，以 id 为键
  data.forEach((item) => {
    map[item.id] = { ...item, children: [] }; // 初始化 children 为一个空数组
  });

  // 根据 parentId 将节点归纳到其父节点中
  data.forEach((item) => {
    if (item.parentId === '0') {
      // 如果 parentId 为 "0"，则为根节点，直接添加到树中
      tree.push(map[item.id]);
    } else {
      // 检查父节点是否存在
      const parent = map[item.parentId];
      if (parent) {
        // 安全地推送到父节点的 children 数组
        parent.children.push(map[item.id]);
      } else {
        // 如果父节点不存在，可以选择打印一个警告
        console.warn(`父节点 ${item.parentId} 不存在，无法将 ${item.id} 添加到其子节点中`);
      }
    }
  });

  // 可选：清理多余的属性，具体实现需要根据您的需求
  tree.forEach(cleanUp);

  return tree; // 返回构建好的树形结构
}

// 清理空 children 属性的递归函数
function cleanUp(node: any): void {
  if (node.children && node.children.length === 0) {
    delete node.children; // 删除空的 children 属性
  } else if (node.children) {
    node.children.forEach(cleanUp); // 递归处理子节点
  }
}

// 隐患级别
const optionsGrade = ref<any>([]);
const renderLabel = (option: any) => {
  return h(
    'div',
    {
      title: option.gradeName,
    },
    option.gradeName.length > 20 ? option.gradeName.substring(0, 20) + '...' : option.gradeName
  );
};

function getDangerGradeList() {
  let params = {
    delFlag: 0,
    unitId: userInfo.topUnitId,
  };
  getDangerGrade(params).then((res: any) => {
    optionsGrade.value = res.data;
  });
}

// 组织
const showOrgTree = ref(false);
const selectOrgTree = (row: any) => {
  let arr = row?.map((item: any) => {
    let name: any = '';

    // if (item.attributes.orgType === '0') {
    const result = findTextPath(unitOptions.value, item['id']);
    name = result;
    // }
    return {
      unitName: name,
      unitId: item.id,
      type: item.attributes.orgType,
    };
  });
  tableData.value = [...tableData.value, ...arr];
  showOrgTree.value = false;
  console.log(row, '123');
};

function findNodeById(data: any, id: any) {
  for (const node of data) {
    if (node.id === id) {
      return node; // 找到目标节点，返回
    }

    if (node.children && node.children.length > 0) {
      const foundInChildren = findNodeById(node.children, id); // 递归查找子节点
      if (foundInChildren) {
        return foundInChildren; // 如果在子节点中找到了，返回
      }
    }
  }

  return null; // 如果没有找到，返回 null
}
function findTextPath(data: any, id: any) {
  const foundNode = findNodeById(data, id); // 使用递归查找节点

  if (!foundNode) {
    return null; // 如果没有找到返回 null
  }

  const pathElements = [foundNode.text]; // 保存节点的 text

  // 向上查找直到找到 orgType !== '0'
  let currentNode = foundNode;
  while (currentNode.parentId) {
    const parentNode = findNodeById(data, currentNode.parentId); // 查找父节点
    if (!parentNode) break; // 如果没有父节点则退出

    // 将父节点的 text 加入 path
    pathElements.unshift(parentNode.text);

    // 如果父节点的 attributes.orgType !== '0'，停止搜索
    // if (parentNode.attributes.orgType !== '0') {
    //   break;
    // }

    currentNode = parentNode; // 更新当前节点
  }

  return pathElements.join('/'); // 返回拼接的结果
}

const add = () => {
  formData.value.push(formData.value[formData.value.length - 1]);
};

const handleUpdate = (res: any[], index: number) => {
  formData.value[index].files = res || [];
};

const goBack = () => router.push(breadData[1]['routeRaw'] as any);

const formInstRef = ref<any>();
const submitLoading = ref(false);
const submit = async () => {
  try {
    const allValid = formInstRef.value.map((item: any) => item.validate());
    await Promise.all(allValid);
    if (!tableData.value.length) return $toast.warning('请选择类比排查单位');
    submitLoading.value = true;
    let data = {
      addFormList: formData.value.map((item: any) => {
        return {
          ...item,
          taskStartTime: dayjs(new Date().getTime()).format('YYYY-MM-DD HH:mm:ss'),
          taskEndTime: dayjs(item.taskEndTime).format('YYYY-MM-DD HH:mm:ss'),
        };
      }),
      haveOneFindThreeUnitList: tableData.value,
    };
    addOneToThree(data)
      .then((res: any) => {
        if (res.code == 'success') {
          $toast.success('提交成功');
          goBack();
        } else {
          submitLoading.value = false;
        }
      })
      .catch(() => {
        submitLoading.value = false;
      });
  } catch (error: any) {
    if (error.length) {
      const first = error[0][0];
      if (first.message) {
        $toast.error(first.message);
      }
    }
  }
};

onMounted(() => {
  getUnitOptions();
  getLibrary();
  getDangerGradeList();
});
</script>

<style scoped lang="scss"></style>
