<template>
  <n-space vertical v-if="dispsoseNodeRecordData?.length">
    <n-timeline size="large">
      <n-timeline-item v-for="node of dispsoseNodeRecordData" :key="node.id" :title="node.nodeName">
        <template #footer>
          <div v-for="(item, index) of node.nodeInfo" :key="index" class="timeline__item">
            <span class="timeline__title">{{ item.description }}:</span>
            <span v-if="['string', 'String'].includes(item.webType)">{{ item.dataValue }}</span>
            <template v-else-if="item.webType === 'image'">
              <el-row :gutter="15" v-if="item.dataValue && item.dataValue.length" class="w-full">
                <el-col :span="8" v-for="src of item.dataValue" :key="src">
                  <n-image
                    class="w-[100px] rounded-[4px] inline-block"
                    :src="getFileURL(src, true)"
                    :preview-src="getFileURL(src)"
                    :initial-index="1"
                    object-fit="cover"
                  />
                </el-col>
              </el-row>
              <div v-else>{{ '--' }}</div>
            </template>
          </div>
        </template>
      </n-timeline-item>
    </n-timeline>
  </n-space>
  <div v-else class="flex justify-center items-center">
    <n-spin v-if="loading" />
    <empty v-else />
  </div>
</template>

<script setup lang="ts">
import { IHazardRecord } from './type';
import getFileURL from '@/utils/getFileURL';

interface Props {
  dispsoseNodeRecordData: IHazardRecord[] | null;
  loading: boolean;
}
const props = withDefaults(defineProps<Props>(), {
  dispsoseNodeRecordData: null,
});

function getFileUrlList(list: string[]) {
  return list.map((src: any) => getFileURL(src));
}
</script>

<style lang="scss" scoped>
.timeline__item {
  display: flex;
  margin: 10px 0;

  .timeline__title {
    margin-right: 10px;
    white-space: nowrap;
  }

  span {
    font-size: 14px;
    color: #333;
  }
}
</style>
