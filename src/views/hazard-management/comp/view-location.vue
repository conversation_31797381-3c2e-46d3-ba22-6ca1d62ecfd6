<template>
  <span>
    <!-- <n-button tertiary @click="showModal = true"> <span>查看位置</span></n-button> -->
    <span
      @click="showModal = true"
      style="
        height: 2vh;
        width: 4vw;
        text-align: center;
        display: inline-block;
        margin-top: 4px;
        line-height: 2vh;
        background-color: #2e33380d;
        user-select: none;
      "
      >查看位置</span
    >
    <!-- <n-modal v-model:show="showModal" title="隐患详情" preset="card" style="width: 800px; height: 600px">
      {{ floorData }}
      <indoorMap :floor-info="floorData" :pointer="pointer" :device-list="deviceList" />
    </n-modal> -->

    <!-- floorAreaImg 字段在hazard-details.vue 页面查询了接口获取 -->
    <n-modal
      class="models"
      v-model:show="showModal"
      preset="card"
      :show-icon="false"
      style="width: 900px"
      @negative-click="showModal = false"
    >
      <template #header>
        <div class="flex flex-row items-center">
          <img class="w-[17px] h-[12px] mr-[20px]" src="@/components/header/assets/icon-title-arrow3.png" />
          <div class="text-[16px] text-[#222222] font-bold">隐患位置</div>
        </div>
      </template>
      <!-- // 获取楼层图 通过这个判断是否有 gis 数据，因为外运长航的本地环境这个接口跨域问题，暂时屏蔽 -->
      <!-- <div class="h-[500px] pt-[100px]" v-if="!floorData.floorAreaImg">
        <n-empty description="暂无楼层图"></n-empty>
      </div> -->
      <!-- v-else -->
      <mapFloor
        :floorInfo="floorData"
        :isAddMark="true"
        :isShowBtn="false"
        @cancel="cancel"
        :pointer="pointer"
        style="height: 500px"
      ></mapFloor>
    </n-modal>
  </span>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import indoorMap from '../../../components/indoorMap/index.vue';
import mapFloor from '@/components/indoorMap/index.vue';
import { pointerList } from './data';
import { IMapFloorInfo } from '../type';

interface Props {
  floorData: IMapFloorInfo;
  deviceList: any[];
}
const props = withDefaults(defineProps<Props>(), {});
const pointer = { x: props.deviceList[0].mapX, y: props.deviceList[0].mapY };
const showModal = ref(false);
const cancel = () => {
  showModal.value = false;
};
</script>
