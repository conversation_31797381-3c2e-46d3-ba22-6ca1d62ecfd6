<template>
  <!-- new -->
  <div class="filter-box">
    <div class="collapse-btn" @click="isCollapse = !isCollapse">
      <span class="text-[#527cff]">{{ isCollapse ? '收起' : '展开' }}</span>
      <n-icon size="20">
        <component :is="isCollapse ? ArrowDown : ArrowRight" />
      </n-icon>
    </div>
    <n-form
      class="flex-1"
      :show-feedback="false"
      label-placement="left"
      label-width="90px"
    >
      <n-grid :x-gap="12" :y-gap="8" :cols="3">
        <n-gi>
          <n-form-item label="是否超期:">
            <n-select
              placeholder="请选择"
              v-model:value="filterForm.timeoutDays"
              clearable
              :options="hazardOverdueOptions"
              @update:value="doHandle"
            />
          </n-form-item>
        </n-gi>
        <n-gi>
          <n-form-item>
            <n-input
              placeholder="请输入隐患分类/隐患等级模糊搜索"
              v-model:value="filterForm.likeFieldValue"
              clearable
              @update:value="doHandle"
            >
              <template #suffix>
                <BsSearch />
              </template>
            </n-input>
          </n-form-item>
        </n-gi>
        <n-gi>
          <n-form-item label="隐患等级:">
            <n-select
              v-model:value="filterForm.hazardLevel"
              placeholder="请选择"
              clearable
              :options="hazardLevelList"
              @update:value="doHandle"
            />
          </n-form-item>
        </n-gi>
        <n-gi>
          <n-form-item label="上报人:">
            <n-input
              placeholder="请输入上报人"
              v-model:value="filterForm.createByName"
              clearable
              @update:value="doHandle"
            />
          </n-form-item>
        </n-gi>
        <n-gi>
          <n-form-item label="上报时间:">
            <n-date-picker
              v-model:value="timeRangeVal"
              type="daterange"
              class="flex items-center"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd"
              clearable
              @update:value="confirmRange"
            />
          </n-form-item>
        </n-gi>
        <n-gi v-if="isCollapse">
          <n-form-item label="隐患编号:">
            <n-input
              placeholder="请输入隐患编号"
              v-model:value="filterForm.rectificationNum"
              clearable
              @update:value="doHandle"
            />
          </n-form-item>
        </n-gi>
        <n-gi v-if="isCollapse">
          <n-form-item label="整改人:">
            <n-input
              placeholder="请输入整改人"
              v-model:value="filterForm.reformUserName"
              clearable
              @update:value="doHandle"
            />
          </n-form-item>
        </n-gi>
        <n-gi v-if="isCollapse">
          <n-form-item label="整改时间:">
            <n-date-picker
              v-model:value="ZGTimeRangeVal"
              type="daterange"
              class="flex items-center"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd"
              clearable
              @update:value="confirmZGTimeRange"
            />
          </n-form-item>
        </n-gi>
        <n-gi v-if="isCollapse">
          <n-form-item label="整改状态:">
            <n-select
              v-model:value="filterForm.disposeState"
              clearable
              :options="disposeStateOptions"
              @update:value="handleChange()"
            />
          </n-form-item>
        </n-gi>
        <n-gi :span="isCollapse ? '3' : '1'">
          <div class="flex justify-end w-full">
            <n-button type="primary" @click="exportEventListFn"> 导出</n-button>
          </div>
        </n-gi>
      </n-grid>
    </n-form>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';
import { BsSearch } from '@kalimahapps/vue-icons';
import { trimObjNull } from '@/utils/obj.ts';
import { SelectOption } from 'naive-ui';
import {
  exportEventList,
  getAllUnit,
  getHazardGradeList,
  getHazardOverdueList,
} from '../fetchData.ts';
import { IActionType } from '../type.ts';
import { computed } from 'vue';
import { fileDownloader } from '@/utils/fileDownloader';
import { throttle } from 'lodash-es';
import { useStore } from '@/store/index.ts';
import { getDefaultDateRange } from '@/utils/getDefaultDateRange.ts';
import dayjs from 'dayjs';
import { cols } from './table/columns.ts';
import {
  IcNavArrowRight as ArrowRight,
  IcNavArrowDown as ArrowDown,
} from '@kalimahapps/vue-icons';
import { useRoute, useRouter } from 'vue-router';

const props = defineProps({
  unitId: {
    type: String,
    default: '',
  },
});

const { userInfo } = useStore();

const route = useRoute();
const router = useRouter();
const emits = defineEmits(['action']);

const isCollapse = ref(false);

const defaultDateRange = [
  // 使用dayjs计算今天往前一个月的日期
  dayjs().add(-1, 'month').format('YYYY-MM-DD'),
  dayjs().format('YYYY-MM-DD'),
];

const hazardLevelList = ref<SelectOption[]>([]);
const hazardOverdueOptions = ref<SelectOption[]>([]);
// const unitOptions = ref<SelectOption[]>([]);

const disposeStateOptions = [
  { label: '待整改', value: 0 },
  { label: '已整改', value: 1 },
  { label: '整改中', value: 2 },
];

const filterForm = ref<any>({
  unitId: null,
  hazardType: null,
  likeFields: 'unitName,hazardTypeName,hazardLevelName',
  likeFieldValue: null,
  hazardLevel: null,
  disposeState: 0,
  timeoutDays: null,
  startTime: Object.keys(route.query).length > 0 ? null : defaultDateRange[0],
  endTime: Object.keys(route.query).length > 0 ? null : defaultDateRange[1],
  rectificationStartTime: null,
  rectificationEndTime: null,
  reformUserName: null,
  rectificationNum: null,
  createByName: null,
});

function getVal() {
  return filterForm.value;
}

function clearFrom() {
  if (Object.keys(route.query).length > 0) {
    filterForm.value = {
      hazardType: null,
      likeFields: 'unitName,hazardTypeName,hazardLevelName',
      likeFieldValue: null,
      hazardLevel: null,
      disposeState: null,
      timeoutDays: null,
      startTime: null,
      endTime: null,
      rectificationStartTime: null,
      rectificationEndTime: null,
      reformUserName: null,
      rectificationNum: null,
      createByName: null,
    };
  } else {
    filterForm.value = {
      hazardType: null,
      likeFields: 'unitName,hazardTypeName,hazardLevelName',
      likeFieldValue: null,
      hazardLevel: null,
      disposeState: null,
      timeoutDays: null,
      startTime:
        timeRangeVal.value && timeRangeVal.value[0] ? defaultDateRange[0] : '',
      endTime:
        timeRangeVal.value && timeRangeVal.value[1] ? defaultDateRange[1] : '',
      rectificationStartTime: null,
      rectificationEndTime: null,
      reformUserName: null,
      rectificationNum: null,
      createByName: null,
    };
  }
}

// 上报时间
const timeRangeVal = ref(
  Object.keys(route.query).length > 0
    ? null
    : [
        dayjs(defaultDateRange[0]).valueOf(),
        dayjs(defaultDateRange[1]).valueOf(),
      ]
);
function confirmRange() {
  if (!timeRangeVal.value) {
    console.log(333333333333);
    filterForm.value.startTime = undefined;
    filterForm.value.endTime = undefined;
    if (route.query) {
      route.query.starTime = '';
      route.query.endTime = '';
    }
    router.push({
      path: 'hazard-management',
      query: {},
    });
  } else {
    filterForm.value.startTime = dayjs(timeRangeVal.value[0]).format(
      'YYYY-MM-DD'
    );
    filterForm.value.endTime = dayjs(timeRangeVal.value[1]).format(
      'YYYY-MM-DD'
    );
  }
  doHandle();
}

// 整改时间
const ZGTimeRangeVal = ref<any>();
function confirmZGTimeRange() {
  if (!ZGTimeRangeVal.value) {
    filterForm.value.rectificationStartTime = undefined;
    filterForm.value.rectificationEndTime = undefined;
  } else {
    filterForm.value.rectificationStartTime = dayjs(
      ZGTimeRangeVal.value[0]
    ).format('YYYY-MM-DD');
    filterForm.value.rectificationEndTime = dayjs(
      ZGTimeRangeVal.value[1]
    ).format('YYYY-MM-DD');
  }
  doHandle();
}

const handleChange = () => {
  router.replace({ path: '/hazard-management', query: {} });
  doHandle();
};

const exportEventListFn = async () => {
  let data = {
    ...filterForm.value,
    unitId: props.unitId,
    exportfields: [
      ...cols.slice(1).map(({ key, title }: any) => {
        return { key, title };
      }),
      { key: 'eventFileUrlStr1', title: '隐患图片1' },
      { key: 'eventFileUrlStr2', title: '隐患图片2' },
      { key: 'eventFileUrlStr3', title: '隐患图片3' },
      { key: 'dealFileUrlStr1', title: '验收图片1' },
      { key: 'dealFileUrlStr2', title: '验收图片2' },
      { key: 'dealFileUrlStr3', title: '验收图片3' },
    ],
  };
  fileDownloader(exportEventList(), {
    method: 'POST',
    contentType: 'application/json',
    body: JSON.stringify(data),
  });
};

const doHandle = throttle(() => {
  emits('action', {
    action: IActionType.search,
    data: trimObjNull(filterForm.value),
  });
}, 1000);

if (route.query?.unitId) {
  let query = route.query;
  filterForm.value.unitId = query.unitId as null;
  filterForm.value.startTime = dayjs(query.eventTime as string).format(
    'YYYY-MM-DD'
  );
  filterForm.value.endTime = dayjs(query.eventTime as string).format(
    'YYYY-MM-DD'
  );
  filterForm.value.disposeState = 0;
  filterForm.value.hazardLevel = query.hazardLevel as null;
}

/** 获取下拉框数据 */
const getFilterFormData = async () => {
  getHazardGradeList({ delFlag: 0, unitId: userInfo.topUnitId }).then((res) => {
    hazardLevelList.value = res.data.map((item) => ({
      label: item.gradeName,
      value: item.id,
    }));
  });
  getHazardOverdueList({
    pageSize: -1,
    pageNo: 1,
    unitId: userInfo.topUnitId,
  }).then((res) => {
    hazardOverdueOptions.value = res.data.rows.map((item) => ({
      label: '超期' + item.overdueDay + '天',
      value: item.overdueDay,
    }));
    hazardOverdueOptions.value.unshift({ label: '否', value: 0 });
  });
  // await getAllUnit({ pageSize: -1, orgCode: userInfo.orgCode }).then((res) => {
  //   if (res.data.rows) {
  //     unitOptions.value = res.data.rows.map((item) => ({ label: item.unitName, value: item.id }));
  //   } else {
  //     unitOptions.value.push({
  //       label: userInfo.unitName,
  //       value: userInfo.unitId,
  //     });
  //   }
  // });
};

// watch(filterForm.value, doHandle);

function init() {
  if (route.query?.starTime || route.query?.endTime) {
    timeRangeVal.value = [
      dayjs(route.query?.starTime as string).valueOf(),
      dayjs(route.query?.endTime as string).valueOf(),
    ];
  }
}

init();

onMounted(async () => {
  getFilterFormData();
  // doHandle();
});

defineOptions({ name: 'checkTempFilterComp' });
defineExpose({ getVal, clearFrom });
</script>
<style scoped lang="scss">
:deep(.n-collapse-item__header-extra) {
  width: calc(100% - 60px);
}

:deep(.n-collapse-item__content-inner) {
  padding-left: 60px;
}
.n-date-picker {
  width: 100%;
}

.filter-box {
  @apply flex flex-row items-start;
  .collapse-btn {
    @apply w-[70px] mt-[4px] text-[18px] cursor-pointer flex flex-nowrap items-center gap-[8px];
  }
}
</style>
