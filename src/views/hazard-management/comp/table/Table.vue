<template>
  <n-data-table
    class="com-table"
    style="height: 100%"
    remote
    striped
    :columns="columns"
    :data="tableData"
    :bordered="false"
    :flex-height="true"
    :loading="loading"
    :pagination="pagination"
    :render-cell="useEmptyCell"
    :scroll-x="2040"
    :row-props="rowProps"
  />
</template>

<script setup lang="ts">
import { h, ref, VNode, toRaw, onMounted, nextTick } from 'vue';
import { DataTableColumns, NButton } from 'naive-ui';
import { cols } from './columns';
import { useRoute, useRouter } from 'vue-router';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { IObj } from '@/types';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { mergePageEvent } from '../../fetchData';
import { IActionType, IDisposeState, IHazardRowInfo } from '../../type';
import { useStore } from '@/store';
import dayjs from 'dayjs';

const route = useRoute();
// const message = useMessage();
const userInfo = useStore()?.userInfo;
const router = useRouter();
const emits = defineEmits(['action', 'showDetails']);

const [loading, search] = useAutoLoading(true);
const { pagination, updateTotal } = useNaivePagination(getTableData);

const columns = ref<DataTableColumns>([]);
const tableData = ref<any[]>([]);

const rowProps = (row: any) => {
  return {
    onContextmenu: (e: MouseEvent) => {
      // e.preventDefault();
      nextTick().then(async () => {
        console.log('contextMenuStyle>>>', row, e);
        try {
          await navigator.clipboard.writeText(row.disposeId);
          // message.success('处置ID已复制到剪贴板');
        } catch (err) {
          // message.error('复制失败');
        }
      });
    },
  };
};

function setColumns() {
  columns.value.push(...cols);
  columns.value.push({
    title: '操作',
    key: 'actions',
    align: 'left',
    fixed: 'right',
    width: 170,
    render: (row) => getActionBtn(row),
  });
}
setColumns();

function getActionBtn(row: IHazardRowInfo | any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          size: 'small',
          type: 'primary',
          ghost: true,
          class: 'com-action-button',
          onClick: () =>
            emits('action', {
              action: IActionType.showDetails,
              data: row,
            }),
        },
        { default: () => '详情' }
      ),
    ],
    [
      h(
        NButton,
        {
          size: 'small',
          type: 'primary',
          ghost: true,
          class: 'com-action-button',
          disabled: row.disposeState !== IDisposeState.待整改,
          onClick: () =>
            emits('action', {
              action: IActionType.hazardUrge,
              data: row,
            }),
        },
        { default: () => '催促' }
      ),
    ],
  ];
  return useActionDivider(acList);
}

let filterData: IObj<any> = {}; // 搜索条件
const paramsData = ref({});
function getTableData() {
  // 如果没选检查对象，默认登录用户自己的
  // if (!params.unitId) params.unitId = userInfo?.unitId;

  if (Object.keys(route.query).length > 0) {
    paramsData.value = {
      rectificationStartTime: null,
      rectificationEndTime: null,
      reformUserName: null,
      rectificationNum: null,
      createByName: null,
      startTime: dayjs(route.query.starTime).format('YYYY-MM-DD'),
      endTime: dayjs(route.query.endTime).format('YYYY-MM-DD'),
      pageNo: pagination.page,
      pageSize: pagination.pageSize,

      hazardType: null,
      likeFields: 'unitName,hazardTypeName,hazardLevelName',
      likeFieldValue: null,
      hazardLevel: null,
      disposeState: null,
      timeoutDays: null,
      unitId: route.query.unitId || filterData.unitId,
      // reformUserId: userInfo.id,
      roleCodes: userInfo.roleCodes,
    };
  } else {
    console.log(2222222222222);
    paramsData.value = {
      ...filterData,
      pageNo: pagination.page,
      pageSize: pagination.pageSize,
      // reformUserId: userInfo.id,
      roleCodes: userInfo.roleCodes,
    };
  }
  search(mergePageEvent(paramsData.value)).then((res) => {
    tableData.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
}

loading.value = false;
function getTableDataWrap(data: IObj<any>) {
  filterData = Object.assign({}, data) || {};
  pagination.page = 1;
  getTableData();
}
defineOptions({ name: 'checkTampTableComp' });
defineExpose({ getTableDataWrap, getTableData, pagination });
</script>
<style module lang="scss">
.context-menu {
  position: fixed;
  z-index: 9999;
  background: #fff;
  border: 1px solid #ddd;
  box-shadow: 2px 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  padding: 4px 0;
}

.context-menu-item {
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.context-menu-item:hover {
  background: #f0f0f0;
}
</style>
