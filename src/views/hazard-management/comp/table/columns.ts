import { disposeState } from '@/components/table-col/disposeState';
import { DataTableColumn, NImageGroup, NImage } from 'naive-ui';
import { h } from 'vue';
import getFileURL from '@/utils/getFileURL';

export const cols: DataTableColumn[] = [
  {
    title: '序号',
    key: 'index',
    width: 65,
    align: 'center',
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '隐患编号',
    key: 'rectificationNum',
    align: 'left',
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '隐患单位',
    key: 'unitName',
    align: 'left',
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
  },

  {
    title: '隐患描述',
    key: 'hazardDesc',
    align: 'left',
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '隐患位置',
    key: 'hazardPosition',
    align: 'left',
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '隐患图片',
    key: 'hazardRandomCheckEventFiles',
    width: 120,
    align: 'center',
    render: (row) => {
      const { hazardRandomCheckEventFiles } = row;
      if (Array.isArray(hazardRandomCheckEventFiles) && hazardRandomCheckEventFiles?.length > 0) {
        const imgList = hazardRandomCheckEventFiles.map((item, index) => {
          return h(
            'div',
            {
              style: { display: index > 0 ? 'none' : 'block' },
            },
            h(
              'div',
              {
                style: {
                  width: '80px',
                  height: '80px',
                  display: 'flex',
                  justifyContent: 'center',
                },
              },
              h(NImage, {
                objectFit: 'contain',
                src: getFileURL(item.fileUrl),
              })
            )
          );
        });
        return h(NImageGroup, null, () => imgList);
      } else {
        return h('span', null, '暂无图片');
      }
    },
  },
  {
    title: '隐患分类',
    key: 'hazardTypeName',
    align: 'left',
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '隐患等级',
    key: 'hazardLevelName',
    align: 'left',
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '隐患来源',
    key: 'hazardSourceName',
    align: 'left',
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '上报人',
    key: 'createByName',
    align: 'left',
    width: 100,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '上报时间',
    key: 'eventTime',
    align: 'left',
    width: 180,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '整改期限',
    key: 'correctionTime',
    align: 'left',
    width: 150,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '整改人',
    key: 'reformUserName',
    align: 'left',
    width: 180,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '整改时间',
    key: 'rectificationTime',
    align: 'left',
    width: 180,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '整改状态',
    key: 'disposeStateName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
    render: (row) => h(disposeState, { row }),
  },
  {
    title: '是否超期',
    key: 'timeoutDays',
    align: 'left',
    width: 120,
    ellipsis: {
      tooltip: true,
    },
    render: (row: any) => {
      if (row.timeoutDays == 0 || !row.timeoutDays) {
        return '否';
      } else {
        return `超期${row.timeoutDays}天`;
      }
    },
  },
];
