<template>
  <div class="gap-y-[20px] grid">
    <div class="border border-gray-300 rounded-[4px] p-[20px]">
      <h1 class="title">隐患基本信息</h1>
      <n-grid :x-gap="12" :y-gap="8" :cols="1" style="width: 89%">
        <n-grid-item v-for="item of basicInfo" :key="item.label" :span="item.span">
          <template v-if="item.key == 'createByName'">
            <template v-if="mapsFilter.includes(detailsData.hazardSource)">
              <span class="whitespace-nowrap">{{ item.label }}&nbsp; </span>
              <span>{{ detailsData['createByName'] }}</span>
            </template>
          </template>
          <template v-else>
            <span class="whitespace-nowrap">{{ item.label }}&nbsp; </span>
            <!-- <span v-if="item.key === 'reformUser'">
              {{
                (detailsData.users && detailsData.reformUser) ||
                (detailsData.users || []).map((item) => item.reformUserName).join(',')
              }}
            </span> -->
            <span v-if="item.key === 'reformUser'">
              {{
                detailsData.reformUser || (detailsData.users || []).map((item) => item.reformUserName).join(',') || '--'
              }}
            </span>
            <!-- {{
              detailsData.hazardPosition.length > 8
                ? detailsData.hazardPosition.slice(0, 8) + '...'
                : detailsData.hazardPosition
            }} -->
            <span class="single-line-ellipsis" v-else-if="item.key === 'hazardPosition'">
              {{ detailsData.hazardPosition }}
            </span>
            <!-- hazardPosition -->
            <span v-else>{{ detailsData[item.key as keyof IHazardInfo] || '--' }}</span>
          </template>
          <span class="relative" v-if="item.type === 'map' && showMap">
            <viewLocation
              :deviceList="deviceList"
              :floorData="floorData"
              class="absolute translate-y-[-6px] translate-x-[6px]"
            />
          </span>
        </n-grid-item>
      </n-grid>
    </div>
    <div class="border border-gray-300 rounded-[4px] p-[20px]">
      <h1 class="title">隐患详细信息</h1>
      <n-grid :x-gap="12" :y-gap="8" :cols="1">
        <n-grid-item v-for="item of detailedInfo" :key="item.label" :span="item.span" class="flex">
          <span class="whitespace-nowrap">{{ item.label }}&nbsp; </span>
          <template v-if="item.type === 'image'">
            <el-row :gutter="15" v-if="detailsData.files && detailsData.files.length" class="w-full">
              <el-col :span="8" v-for="src of detailsData.files" :key="src">
                <n-image
                  class="w-[100px] rounded-[4px] inline-block"
                  :src="getFileURL(src.fileUrl, true)"
                  :preview-src="getFileURL(src.fileUrl)"
                  object-fit="cover"
                />
              </el-col>
            </el-row>
            <div v-else>{{ '--' }}</div>
          </template>
          <span v-else>{{ detailsData[item.key as keyof IHazardInfo] || '--' }}</span>
        </n-grid-item>
      </n-grid>
    </div>
    <div class="border border-gray-300 rounded-[4px] p-[20px]" v-if="!deviceFilter.includes(detailsData.hazardSource)">
      <h1 class="title">检查信息</h1>
      <n-grid :x-gap="12" :y-gap="8" :cols="1">
        <n-grid-item v-for="item of inspectionInfo" :key="item.label" :span="item.span" class="flex">
          <span class="whitespace-nowrap">{{ item.label }}&nbsp; </span>
          <template v-if="item.type === 'image'">
            <!-- {{ detailsData.classItemVo.files }} -->
            <div v-if="!detailsData.classItemVo?.files?.length">--</div>
            <!-- <div v-if="!detailsData.classItemVo.listFile?.length">--</div> -->
            <n-image
              v-else
              v-for="img of detailsData.classItemVo?.files"
              :key="img.fileUrl"
              style="height: 60px; margin-right: 10px"
              :src="getFileURL(img.fileUrl)"
              :preview-src="getFileURL(img.fileUrl)"
            />
          </template>
          <span v-else>{{
            (detailsData.classItemVo && detailsData.classItemVo[item.key as keyof IHazardInfo['classItemVo']]) || '--'
          }}</span>
        </n-grid-item>
      </n-grid>
    </div>
  </div>
</template>

<script setup lang="ts">
import getFileURL from '@/utils/getFileURL';
import { computed } from 'vue';
import { isTopUnit } from '../inspection-planning/planned-management/Details/utils';
import viewLocation from './comp/view-location.vue';
import { IDetailsItem, IHazardInfo, IMapFloorInfo } from './type';

interface Props {
  detailsData: IHazardInfo;
  showMap: boolean;
}
const props = withDefaults(defineProps<Props>(), {});
const mapsFilter = [2, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13];
const deviceFilter = [1, 3, 14, 15, 16, 17, 18, 19, 20, 22];
const floorData = computed<IMapFloorInfo>(() => ({
  unitId: props.detailsData.unitId,
  buildingId: props.detailsData.buildingId,
  floorId: props.detailsData.floorId,
  floorAreaImg: props.detailsData.floorAreaImg,
}));
const deviceList = computed(() => [
  {
    mapX: props.detailsData.mapX,
    mapY: props.detailsData.mapY,
    mapZ: props.detailsData.mapZ,
  },
]);

const basicInfo = computed(() => {
  let result: IDetailsItem[] = [
    { label: '隐患单位:', key: 'unitName', span: 1 },
    { label: '隐患整改人员:', key: 'reformUser', span: 1 },
    { label: '隐患来源:', key: 'hazardSourceName', span: 1 },
    { label: '隐患位置:', key: 'hazardPosition', type: 'map', span: 2 },
    { label: '上报时间:', key: 'eventTime', span: 1 },
    { label: '检查时间:', key: 'createTime', span: 1 },
    { label: '检查人:', key: 'createByName', span: 1 },
    { label: '整改期限:', key: 'correctionTime', span: 1 },
  ];
  if (!isTopUnit()) result = result.filter((item: any) => item.key !== 'unitName');
  return result;
});

const detailedInfo: IDetailsItem[] = [
  { label: '隐患描述:', key: 'hazardDesc', span: 2 },
  { label: '隐患分类:', key: 'hazardTypeName', span: 1 },
  { label: '隐患等级:', key: 'hazardLevelName', span: 1 },
  { label: '备注:', key: 'remark', span: 1 },
  { label: '隐患图片:', key: 'files', type: 'image', span: 1 },
];

const inspectionInfo: IDetailsItem[] = [
  { label: '检查项:', key: 'inspectionItem' },
  { label: '检查内容:', key: 'inspectionDescribe' },
  { label: '法规依据:', key: 'inspectionItemBasis' },
  { label: '合规要求:', key: 'inspectionAsk' },
  { label: '法律原文:', key: 'legalText' },
  { label: '法律责任:', key: 'legalLiability' },
  { label: '图例:', key: 'files', type: 'image' },
];
function getFileUrlList(list: string[]) {
  return list.map((item: any) => getFileURL(item.fileUrl));
}
</script>

<style scoped>
.label {
  width: 100px;
  flex-shrink: 0;
}

.title {
  font-weight: bold;
  font-size: 18px;
  margin-bottom: 10px;
}
</style>
