<template>
  <div class="gap-y-[20px] grid">
    <div class="border border-gray-300 rounded-[4px] p-[20px]">
      <h1 class="title">隐患基本信息</h1>
      <n-grid :x-gap="12" :y-gap="8" :cols="1" style="width: 89%">
        <n-grid-item v-for="item of basicInfo" :key="item.label" :span="item.span">
          <template v-if="item.key == 'createByName'">
            <template v-if="mapsFilter.includes(detailsData.hazardSource)">
              <span class="whitespace-nowrap">{{ item.label }}&nbsp; </span>
              <span>{{ detailsData['createByName'] }}</span>
            </template>
          </template>
          <template v-if="item.key == 'hazardPosition'">
            <span class="whitespace-nowrap">{{ item.label }}&nbsp; </span>
            <span class="single-line-ellipsis" v-if="item.key === 'hazardPosition'">
              {{ detailsData.hazardPosition }}
            </span>
          </template>

          <template v-else>
            <span class="whitespace-nowrap">{{ item.label }}&nbsp; </span>
            <span>{{ detailsData[item.key as keyof IHazardInfo] }}</span>
          </template>
          <span class="relative" v-if="item.key === 'hazardPosition' && showMap">
            <viewLocation
              :deviceList="deviceList"
              :floorData="floorData"
              class="absolute translate-y-[-6px] translate-x-[6px]"
            />
          </span>
        </n-grid-item>
      </n-grid>
    </div>
    <div class="border border-gray-300 rounded-[4px] p-[20px]">
      <h1 class="title">隐患详细信息</h1>
      <n-grid :x-gap="12" :y-gap="8" :cols="1">
        <n-grid-item v-for="item of detailedInfo" :key="item.label" :span="item.span" class="flex">
          <span class="whitespace-nowrap">{{ item.label }}&nbsp; </span>
          <template v-if="item.type === 'image'">
            <el-row :gutter="15" v-if="detailsData.files && detailsData.files.length" class="w-full">
              <el-col :span="8" v-for="src of detailsData.files" :key="src">
                <n-image
                  class="w-[100px] rounded-[4px] inline-block"
                  :src="getFileURL(src.fileUrl, true)"
                  :preview-src="getFileURL(src.fileUrl)"
                  object-fit="cover"
                />
              </el-col>
            </el-row>
            <div v-else>{{ '--' }}</div>
          </template>
          <span v-else>{{ detailsData[item.key as keyof IHazardInfo] || '--' }}</span>
        </n-grid-item>
      </n-grid>
    </div>
    <div class="border border-gray-300 rounded-[4px] p-[20px]">
      <h1 class="title">设备信息</h1>
      <n-grid :x-gap="12" :y-gap="8" :cols="1">
        <n-grid-item v-for="item of inspectionInfo" :key="item.label" :span="item.span" class="flex">
          <span class="whitespace-nowrap">{{ item.label }}&nbsp; </span>
          <span>{{ detailsData.deviceInfo[item.key as keyof IHazardInfo['deviceInfo']] || '--' }}</span>
        </n-grid-item>
      </n-grid>
    </div>
  </div>
</template>

<script setup lang="ts">
import getFileURL from '@/utils/getFileURL';
import { computed } from 'vue';
import { isTopUnit } from '../inspection-planning/planned-management/Details/utils';
import ViewLocation from './comp/view-location.vue';
import { IDetailsItem, IHazardInfo, IMapFloorInfo } from './type';

interface Props {
  detailsData: IHazardInfo;
  showMap: boolean;
}
const props = withDefaults(defineProps<Props>(), {});
const mapsFilter = [2, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13];
const floorData = computed<IMapFloorInfo>(() => ({
  unitId: props.detailsData.unitId,
  buildingId: props.detailsData.buildingId,
  floorId: props.detailsData.floorId,
  floorAreaImg: props.detailsData.floorAreaImg,
}));
const deviceList = computed(() => [
  {
    mapX: props.detailsData.mapX,
    mapY: props.detailsData.mapY,
    mapZ: props.detailsData.mapZ,
  },
]);

const basicInfo = computed(() => {
  let result: IDetailsItem[] = [
    { label: '隐患单位:', key: 'unitName' },
    { label: '隐患位置:', key: 'hazardPosition' },
    { label: '隐患来源:', key: 'hazardSourceName' },
    { label: '上报时间:', key: 'eventTime' },
    { label: '检查人:', key: 'createByName' },
    { label: '整改期限:', key: 'correctionTime', span: 1 },
  ];
  if (!isTopUnit()) result = result.filter((item: any) => item.key !== 'unitName');
  return result;
});

const detailedInfo: IDetailsItem[] = [
  { label: '隐患描述:', key: 'hazardDesc', span: 2 },
  { label: '隐患分类:', key: 'hazardTypeName', span: 1 },
  { label: '隐患等级:', key: 'hazardLevelName', span: 1 },
  { label: '隐患图片:', key: 'files', type: 'image', span: 1 },
];

const inspectionInfo: IDetailsItem[] = [
  { label: '设备类型:', key: 'deviceTypeName' },
  { label: '系统类型:', key: 'deviceTypePname' },
  { label: '品牌型号:', key: 'brand' },
  { label: 'IMEI:', key: 'deviceNum' },
  { label: '安装日期:', key: 'createTime' },
];
function getFileUrlList(list: string[]) {
  return list.map((item: any) => getFileURL(item.fileUrl));
}
</script>

<style scoped>
.label {
  width: 100px;
  flex-shrink: 0;
}

.title {
  font-weight: bold;
  font-size: 1.5em;
  margin-bottom: 1em;
}
</style>
