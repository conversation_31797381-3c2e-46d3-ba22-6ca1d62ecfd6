<template>
  <div class="flex flex-col">
    <com-bread :data="breadData"></com-bread>

    <div class="item_content notVw flex-1">
      <UnitSelect class="unit-tree" @updateVal="updateVal" v-if="showTree" />
      <div class="right-table" :style="{ width: showTree ? '62vw' : '100%' }">
        <img
          src="@/assets/select_tree.png"
          class="select-tree"
          :style="{ width: toVw(30), left: `-${toVw(15)}` }"
          v-if="userInfo.unitOrgType == '2'"
          @click="showTree = !showTree"
          alt=""
        />
        <div style="display: flex; justify-content: space-between">
          <div></div>
          <n-button
            @click="router.back()"
            type="primary"
            v-if="Object.keys(route.query).length > 0"
            >返回</n-button
          >
        </div>
        <statistic ref="statisticRef" />
        <Filter @action="actionFn" ref="filterRef" :unitId="filterVal.unitId" />
        <TableList
          class="table-list mt-[20px]"
          ref="tableCompRef"
          @action="actionFn"
          @showDetails="showDetails"
        />
      </div>
    </div>
    <hazardDetails ref="hazardDetailsRef" />
  </div>
</template>

<script lang="ts" setup>
import ComBread from '@/components/breadcrumb/ComBread.vue';
import { IBreadData } from '@/components/breadcrumb/type.ts';
import UnitSelect from '@/components/unitSelect/index.vue';
import { useStore } from '@/store';
import { toVw } from '@/utils/fit';
import { useMessage } from 'naive-ui';
import { onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import Filter from './comp/Filter.vue';
import statistic from './comp/statistic.vue';
import TableList from './comp/table/Table.vue';
import { getHazardInventoryDetail, hazardUrge } from './fetchData';
import hazardDetails from './hazard-details.vue';
import { IActionType, IHazardRowInfo } from './type';

const message = useMessage();
const route = useRoute();
const router = useRouter();
const { userInfo } = useStore();
const showTree = ref(true);
console.log(route.params.status);
const hazardDetailsRef = ref();
const tableCompRef = ref();
const statisticRef = ref();
const breadData: IBreadData[] = [
  { name: '隐患治理系统' },
  { name: '隐患整改' },
];

const filterRef = ref();
const filterVal = ref<any>({
  unitId: userInfo.unitId,
});
const isFirstCall = ref(true);
// 组织结构切换
function updateVal(unitId: string) {
  filterRef.value?.clearFrom();
  if (!isFirstCall.value && Object.keys(route.query).length > 0) {
    router.push({
      path: 'hazard-management',
      query: {},
    });
  }
  isFirstCall.value = false; // 第一次调用后设为 false
  filterVal.value = { unitId, ...filterRef.value?.getVal() };
  console.log('filterVal.value>>>', filterVal.value);
  tableCompRef.value.pagination.pageNo = 1;
  // tableCompRef.value.pagination.pageSize = 10;
  tableCompRef.value.getTableDataWrap(filterVal.value);
  statisticRef.value.getData(filterVal.value);
}

// 查看详情
function getDetail(id: string) {
  getHazardInventoryDetail({ id }).then((res) => {
    res.data && hazardDetailsRef.value.open({ ...res.data, flag: 2 });
  });
}

const actionFn = ({
  action,
  data,
}: {
  action: IActionType;
  data: IHazardRowInfo;
}) => {
  filterVal.value = {
    unitId: filterVal.value.unitId,
    ...filterRef.value?.getVal(),
  };
  if (action === IActionType.search) {
    tableCompRef.value.getTableDataWrap(filterVal.value);
    statisticRef.value.getData(filterVal.value);
  } else if (action === IActionType.showDetails) {
    getDetail(data.id);
  } else if (action === IActionType.hazardUrge) {
    hazardUrgeFn(data);
  }
};

const hazardUrgeFn = async (row: any) => {
  const params = {
    disposeId: row.disposeId,
    eventType: 4,
    subCenterCode: row.zhId,
    operatorId: userInfo.id,
    operatorTel: userInfo.userTelphone,
    operatorName: userInfo.userName,
  };
  await hazardUrge(params);
  message.success('催促成功');
};
const showDetails = (row: any) => {};

onMounted(() => {
  route.query?.id && getDetail(route.query?.id);
});

defineOptions({ name: 'HazardManagement' });
</script>

<style scoped></style>
