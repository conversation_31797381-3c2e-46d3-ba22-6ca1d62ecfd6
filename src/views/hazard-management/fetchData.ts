// import { ICheckTempPageRes, ICheckItem, ICheckCategoryTree, IFormData } from './type';
import { IObj, IPageRes } from '@/types';
// import { PageList, statisticsData } from './type';
import { api } from '@/api';
import { $http } from '@tanzerfe/http';
import {
  IHazardGrade,
  IHazardInfo,
  IHazardRecord,
  IHazardRowInfo,
  IHazardStatistic,
  ILevelStatistic,
  IUnitInfo,
} from './type';

// 获取隐患等级
export function getHazardGradeList(params: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.hazardManagement.hazardGradeList);
  return $http.post<IHazardGrade[]>(url, {
    data: { _cfg: { showTip: true }, ...params },
  });
}

/** 隐患分类列表 */
export function getHazardEssentialFactorClassList(params: any) {
  const url = api.getUrl(api.type.hazard, api.hazardManagement.hazardEssentialFactorClassList, params);
  return $http.post<IHazardGrade[]>(url, {
    data: { _cfg: { showTip: true }, ...params },
  });
}

/** 隐患库-详情-编辑隐患分类 */
export function addRelation(params: any) {
  const url = api.getUrl(api.type.hazard, api.hazardManagement.addRelation, params);
  return $http.post<IHazardGrade[]>(url, {
    data: { _cfg: { showTip: true }, ...params },
  });
}

/** 隐患催促 */
export function hazardUrge(params: any) {
  const url = api.getUrl(api.type.hazard, api.hazardManagement.hazardUrge);
  return $http.post<IHazardGrade[]>(url, {
    data: { _cfg: { showTip: true }, ...params },
  });
}

/** 隐患整改记录 */
export function getDispsoseNodeRecord(params: any) {
  const url = api.getUrl(api.type.hazard, api.hazardManagement.getDispsoseNodeRecord);
  return $http.post<IPageRes<IHazardRecord>>(url, {
    data: { _cfg: { showTip: true }, ...params },
  });
}

/** 隐患处置详情 */
export function getHazardDetails(params: any) {
  const url = api.getUrl(api.type.hazard, api.hazardManagement.hazardDetails);
  return $http.post<IHazardGrade[]>(url, {
    data: { _cfg: { showTip: true }, ...params },
  });
}

// ================================== 隐患信息已完成数据接口 ==================================

export function pageEventMeger(params: IObj<any>) {
  if (!params.flag) params.flag = '02';
  const url = api.getUrl(api.type.hazard, api.hazardManagement.pageEventMeger);
  return $http.post<IPageRes<IHazardRowInfo>>(url, {
    data: { _cfg: { showTip: true }, ...params },
  });
}

export function mergePageEvent(params: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.hazardManagement.mergePageEvent);
  return $http.post<IPageRes<IHazardRowInfo>>(url, {
    data: { _cfg: { showTip: true }, ...params },
  });
}

/** 隐患治理已完成统计数据 */
export function getStatisticsMeger(params: IObj<any>) {
  if (!params.flag) params.flag = '02';
  const url = api.getUrl(api.type.hazard, api.hazardManagement.statisticsMeger);
  return $http.post<IHazardStatistic>(url, {
    data: { _cfg: { showTip: true }, ...params },
  });
}

// ================================== 隐患信息为完成数据接口 ==================================
/** 隐患治理未完成列表 */

/** 隐患详情 */
export function qeuryEventDetail(params: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.hazardManagement.qeuryEventDetail, params);
  return $http.post<IHazardInfo>(url, { data: { _cfg: { showTip: true } } });
}

export function getHazardOverdueList(params: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.hazardManagement.hazardOverdueList);
  return $http.post<IPageRes<{ overdueDay: number }>>(url, {
    data: { _cfg: { showTip: true }, ...params },
  });
}

export function exportEventList() {
  const url = api.getUrl(api.type.hazard, api.hazardManagement.exportEventList);
  return url;
}

/** 获取隐患单位列表 */
export function getAllUnit(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.hazardManagement.getAllUnit);
  return $http.post<IPageRes<IUnitInfo>>(url, {
    data: { _cfg: { showTip: true }, ...query },
  });
}

// 获取用户相关任务单位列表
export function getUserTaskUnitList(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.hazardManagement.getUserTaskUnitList, query);
  return $http.post<IPageRes<IUnitInfo>>(url, {
    data: { _cfg: { showTip: true }, ...query },
  });
}

// // 创建检查模板
// export function saveTemplate(data: IObj<any>) {
//   const url = api.getUrl(api.type.demo, api.hazardManagement.demo.saveTemplate, data);
//   return $http.post<IFormData>(url, { data: { _cfg: { showTip: true, showOkTip: true }, ...data } });
// }

export function levelStatisticsMeger(params: any) {
  if (!params.unitId || params.unitId == 'null') return Promise.reject(null);
  if (!params.flag) params.flag = '02';
  const url = api.getUrl(api.type.hazard, api.hazardManagement.levelStatisticsMeger);
  return $http.post<ILevelStatistic[]>(url, {
    data: { _cfg: { showTip: true }, ...params },
  });
}

// 获取计划下的检查对象
export function getPlanUnitByPland(params: any) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.getPlanUnitByPland, params);
  return $http.post<ILevelStatistic[]>(url, {
    data: { _cfg: { showTip: true } },
  });
}

// 获取楼层图
export async function queryFloorAreaImage(params: any) {
  const url = `${window.$SYS_CFG.INNER_BASE_URL}/bw-svc-indoor-gis-service/record/queryFloorAreaImage?floorId=${params}`;
  return $http.get<ILevelStatistic[]>(url, {
    data: { _cfg: { showTip: true } },
  });
}

//获取文件前缀
export function getFileConfig() {
  const url = api.getUrl(api.type.hazard, api.hazardManagement.getFileConfig);
  return $http.get<string>(url, {
    data: { _cfg: { showTip: true } },
  });
}

//获取隐患清单详情
export function getHazardInventoryDetail(data: any) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.getHazardInventoryDetail, data);
  return $http.post<ILevelStatistic[]>(url, {
    data: { _cfg: { showTip: true } },
  });
}
