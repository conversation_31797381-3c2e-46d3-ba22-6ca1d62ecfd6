<template>
  <n-drawer v-model:show="showModal" :style="{ width: toVw(500) }" class="models" :autoFocus="false">
    <n-drawer-content closable>
      <template #header>
        <div class="flex flex-row items-center">
          <img class="w-[17px] h-[12px] mr-[20px]" src="@/components/header/assets/icon-title-arrow3.png" />
          <div class="text-[16px] text-[#222222] font-bold">隐患详情</div>
        </div>
      </template>
      <!-- <template #header>
        <Header title="隐患详情" style="--bgc: rgba(255, 255, 255, 0)" :blueIcon="true"></Header>
      </template> -->
      <div class="com-g-row-a1 h-full gap-y-[20px]">
        <div class="tabs flex justify-center">
          <div
            @click="tab = 'info'"
            class="cursor-pointer left px-[20px] py-[6px] min-w-[125px] text-center rounded-l-[4px] border border-r-0"
            :class="[tab === 'info' ? 'select' : 'unselect']"
          >
            隐患信息
          </div>
          <div
            @click="tab = 'oasis'"
            :class="[tab === 'oasis' ? 'select' : 'unselect']"
            class="cursor-pointer right px-[20px] py-[6px] min-w-[125px] text-center rounded-r-[4px] border border-l-0"
          >
            隐患整改记录
          </div>
        </div>
        <div v-show="tab === 'info'" class="h-full overflow-y-scroll mx-[-24px]">
          <div v-if="detailsLoading" class="flex justify-center items-center">
            <n-spin />
          </div>
          <template v-if="detailsData">
            <hazardDetailsInfoOfIOT
              class="px-[24px]"
              :detailsData="detailsData"
              :showMap="showMap"
              v-if="detailsData.hazardSource === IHazardSource.物联网监测"
            />
            <!-- 除了物联网检查，其他来源都是用这个展示 -->
            <hazardDetailsInfo :showMap="showMap" class="px-[24px]" :detailsData="detailsData" v-else />
          </template>
        </div>
        <div v-show="tab === 'oasis'" class="h-full overflow-y-scroll mx-[-24px]">
          <hazardRectificationRecord
            class="px-[24px] pt-[10px]"
            :loading="recordLoading"
            :dispsoseNodeRecordData="dispsoseNodeRecordData"
          />
        </div>
      </div>
    </n-drawer-content>
  </n-drawer>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import hazardDetailsInfo from './hazard-details-info.vue';
import hazardDetailsInfoOfIOT from './hazard-details-info-of-IOT.vue';
import hazardRectificationRecord from './hazard-rectification-record.vue';
import { getDispsoseNodeRecord, qeuryEventDetail, queryFloorAreaImage } from './fetchData';
import { IHazardInfo, IHazardRecord, IHazardRowInfo, IHazardSource } from './type';
import { toVw, toVh } from '@/utils/fit';
import Header from '@/components/header/ComHeaderB.vue';
interface Props {
  showMap?: boolean;
}
const props = withDefaults(defineProps<Props>(), {
  showMap: true,
});

const tab = ref('info');
const showModal = ref(false);
const detailsData = ref<IHazardInfo | any>();
const dispsoseNodeRecordData = ref<IHazardRecord[] | null>(null);
/** 隐患信息加载状态 */
const detailsLoading = ref(false);
/** 整改记录加载状态 */
const recordLoading = ref(false);

const open = async (row: IHazardRowInfo) => {
  tab.value = 'info';
  showModal.value = true;
  if (row.flag !== '1') getDispsoseNodeRecordFn(row);
  await qeuryEventDetailFn(row.id, row.flag);

  // 获取楼层图
  // const foolImg = await queryFloorAreaImage(detailsData.value.floorId);
  // detailsData.value.floorAreaImg = foolImg.data;
};

/** 获取隐患信息 */
const qeuryEventDetailFn = async (id: string, flag: string) => {
  detailsLoading.value = true;
  detailsData.value = null;
  try {
    const res = await qeuryEventDetail({ id, flag });
    detailsData.value = res.data;
    let info = detailsData.value.deviceInfo.produceInfo;
    console.log(info);
    detailsData.value.deviceInfo.brand = info ? info.brand + '-' + (info.model ? info.model : '') : '--';
    console.log(detailsData.value, '======================detailsData');
  } catch (error) {}
  detailsLoading.value = false;
};

/** 获取隐患整改记录 */
const getDispsoseNodeRecordFn = async (row: IHazardRowInfo) => {
  dispsoseNodeRecordData.value = null;
  const params = {
    disposeId: row.disposeId,
    eventSourceId: row.eventSourceId,
    eventType: 4,
    subCenterCode: row.zhId,
  };
  recordLoading.value = true;
  try {
    const res = await getDispsoseNodeRecord(params);
    dispsoseNodeRecordData.value = res.data.rows;
  } catch (error) {}
  recordLoading.value = false;
};

defineExpose({ open });
</script>

<style scoped>
.select {
  color: #fff;
  background: #527cff;
  border-color: #527cff;
}

.unselect {
  border-color: #dcdfe6;
}
</style>
