import { IObj } from '@/types';
import { api } from '@/api';
import { $http } from '@tanzerfe/http';

export function getEssentialFactor(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.getEssentialFactor, data);
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}

export function saveOrUpdateHazard(data: IObj<any>, showTip: any) {
  console.log(showTip);

  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.saveOrUpdateHazard);
  return $http.post(url, {
    data: { _cfg: { showTip: showTip, showOkTip: showTip }, ...data },
  });
}

export function deleteEssentialFactor(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.deleteEssentialFactor, { ...query });
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: true, okTipContent: '删除成功' } },
  });
}

// 隐患分类-列表
// export function getTreeList(query: IObj<any>) {
//   const url = api.getUrl(api.type.hazard, api.ww.paramsConf.getTreeList, { ...query });
//   return $http.post(url, {
//     data: { _cfg: { showTip: true, showOkTip: false } },
//   });
// }
export function getTreeList(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.getTreeList);
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}

// 隐患分类-新增
export function saveBatch(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.saveBatch);
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}
// 隐患分类-编辑
export function updateBatch(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.updateBatch);
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}

// 隐患分类-删除
export function deleteEssentialFactorClass(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.deleteEssentialFactorClass, { ...query });
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: true } },
  });
}
// 隐患分类-全量名
export function saveFullName(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.saveFullName);
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}

// 隐患分类-移动
export function moveFactorClass(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.moveFactorClass);
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}

export function hazardEssentialFactorClassItemListPage(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.hazardEssentialFactorClassItemListPage);
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}
export function hazardEssentialFactorClassItemDetail(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.hazardEssentialFactorClassItemDetail, { ...query });
  return $http.post(url, {
    data: { _cfg: { showTip: true } },
  });
}
export function hazardEssentialFactorClassItemDetailOther(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.hazardEssentialFactorClassItemDetailOther, { ...query });
  return $http.post(url, {
    data: { _cfg: { showTip: true } },
  });
}

export function hazardEssentialFactorClassItemSaveOrUpdateHazard(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.hazardEssentialFactorClassItemSaveOrUpdateHazard);
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: true }, ...data },
  });
}

export function hazardEssentialFactorClassItemEexportTemplate(essentialFactorId: string) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.hazardEssentialFactorClassItemEexportTemplate, {
    essentialFactorId,
  });
  return url;
}

export function hazardEssentialFactorTemplate() {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.hazardEssentialFactorTemplate);
  return url;
}
