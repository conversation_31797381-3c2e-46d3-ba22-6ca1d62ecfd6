<template>
  <div class="com-g-row-a1">
    <com-bread :data="breadData"></com-bread>
    <TableList ref="tableCompRef" @action="actionFn" />
  </div>
</template>

<script setup lang="ts">
import TableList from './comp/table/Table.vue';
import ComBread from '@/components/breadcrumb/ComBread.vue';

import { ACTION } from './constant';
import { IActionData } from './type';
import { IObj } from '@/types';
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { IBreadData } from '@/components/breadcrumb/type.ts';

const currentAction = ref<IActionData>({ action: ACTION.ADD, data: {} });
// const breadData: IBreadData[] = [{ name: '隐患数据库' }, { name: '隐患基础库' }];
const breadData: IBreadData[] = [{ name: '隐患治理系统' }, { name: '隐患数据库' }];

const router = useRouter();
function actionFn(val: IActionData) {
  currentAction.value = val;
  if (val.action === ACTION.ADD) {
    return router.push({ name: 'checklistConfModify' });
  }
  if (val.action === ACTION.SEARCH) {
    return handleSearch(val.data);
  }
}

const tableCompRef = ref();
function handleSearch(data?: IObj<any>) {
  if (data) {
    tableCompRef.value?.getTableDataWrap(data);
  } else {
    tableCompRef.value?.getTableData();
  }
}

defineOptions({ name: 'planned-management' });
</script>

<style module lang="scss"></style>
