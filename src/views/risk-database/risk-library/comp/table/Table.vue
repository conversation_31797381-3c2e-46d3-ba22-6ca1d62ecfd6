<template>
  <div class="com-g-row-a1 com-table-container">
    <n-form label-placement="left" class="flex justify-between" :model="filterForm" inline label-width="90px">
      <n-grid :x-gap="12" :y-gap="8" :cols="4">
        <n-gi>
          <n-form-item label="编制时间:">
            <n-date-picker
              v-model:value="createTime"
              type="daterange"
              class="flex items-center w-full"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              value-format="yyyy-MM-dd"
              clearable
            />
          </n-form-item>
        </n-gi>
        <n-gi>
          <n-form-item label="">
            <n-input
              placeholder="请输入隐患库名称模糊搜索"
              maxlength="50"
              class="flex items-center w-full"
              show-count
              v-model:value="filterForm.elementName"
              clearable
            />
          </n-form-item>
        </n-gi>
        <n-gi span="2">
          <div class="flex gap-4 justify-end items-center">
            <n-button
              type="primary"
              v-if="isTopUnit()"
              @click="
                () => {
                  showModal = true;
                  modelTitle = '新建隐患库';
                }
              "
              >新建</n-button
            >
          </div>
        </n-gi>
      </n-grid>
      <!-- <n-form-item label="编制时间:">
        <n-date-picker
          v-model:value="createTime"
          type="daterange"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          value-format="yyyy-MM-dd HH:mm:ss"
          clearable
        />
        <n-input
          placeholder="请输入隐患库名称模糊搜索"
          style="width: 300px; margin-left: 10px"
          maxlength="50"
          show-count
          v-model:value="filterForm.elementName"
          clearable
        />
      </n-form-item>
      <div>
        <n-button
          type="primary"
          v-if="isTopUnit()"
          @click="
            () => {
              showModal = true;
              modelTitle = '新建隐患库';
            }
          "
          >新建</n-button
        > -->
      <!-- <n-button type="primary" @click="showExport = true" style="margin-left: 10px">导入</n-button> -->
      <!-- </div> -->
      <!-- <n-drawer v-model:show="showModal" :width="502">
        <n-drawer-content closable :title="modelTitle"> -->

      <ComDrawerA
        :title="modelTitle"
        :autoFocus="false"
        :footerPaddingBottom="25"
        :maskNeedClosable="true"
        :show="showModal"
        show-action
        @handleNegative="
          () => {
            showModal = false;
          }
        "
        class="!w-[430px]"
      >
        <n-form ref="formRef" :model="modelForm" :rules="rules" style="padding: 24px">
          <n-form-item path="elementName" label="隐患库名称">
            <n-input
              maxlength="50"
              show-count
              v-model:value="modelForm.elementName"
              clearable
              placeholder="请输入隐患库名称"
            />
          </n-form-item>
        </n-form>
        <template #footer>
          <div style="display: flex; justify-content: flex-end; gap: 12px; padding: 0 24px">
            <n-button
              @click="
                () => {
                  showModal = false;
                }
              "
              >取消</n-button
            >
            <n-button type="primary" @click="handleValidateClick"> 保存 </n-button>
          </div>
        </template>
      </ComDrawerA>
      <!-- </n-drawer-content>
      </n-drawer> -->
    </n-form>
    <n-data-table
      class="com-table"
      remote
      striped
      :columns="columns"
      :data="tableData"
      :bordered="false"
      :flex-height="true"
      :loading="loading"
      :pagination="pagination"
      :render-cell="useEmptyCell"
      :min-height="550"
      :max-height="550"
      :scroll-x="1200"
    />
    <n-modal
      v-model:show="showExport"
      title="导入隐患库"
      style="width: 600px"
      @negative-click="showExport = false"
      preset="card"
    >
      <n-upload
        ref="uploadRef"
        directory-dnd
        :action="actionUrl"
        :data="{ unitId: topUnitId }"
        show-retry-button
        with-credentials
        :max="1"
        accept=".xlsx"
        :is-error-state="isErrorState"
      >
        <n-upload-dragger>
          <div style="margin-bottom: 12px">
            <n-icon size="48" :depth="3">
              <ArchiveIcon />
            </n-icon>
          </div>
          <n-text style="font-size: 16px"> 点击或者拖动文件到该区域来上传 </n-text>
        </n-upload-dragger>
      </n-upload>
      <div class="flex justify-end download mt-5" @click="downloadTemplate">下载导入模版</div>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { h, ref, VNode, watchEffect, toRaw, watch } from 'vue';
import { DataTableColumns, NButton, FormInst, useMessage } from 'naive-ui';
import { cols } from './columns';
import { useRouter } from 'vue-router';

import {
  getEssentialFactor,
  deleteEssentialFactor,
  saveOrUpdateHazard,
  hazardEssentialFactorTemplate,
} from '@/views/risk-database/risk-library/fetchData';
import ComDrawerA from '@/components/drawer/ComDrawerA.vue';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { IObj } from '@/types';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { $dialog } from '@/common/shareContext/useDialogCtx.ts';
import dayjs from 'dayjs';
import { api } from '@/api';
import { fileDownloader } from '@/utils/fileDownloader';
import { useStore } from '@/store/index';
import { getDefaultDateRange } from '@/utils/getDefaultDateRange';
import { throttle } from 'lodash-es';
import { isTopUnit } from '@/views/inspection-planning/planned-management/Details/utils';

const defaultDateRange = getDefaultDateRange();
const message = useMessage();
const createTime = ref<any>(undefined);

const { topUnitId } = useStore().$state.userInfo;
const filterForm = ref({
  startTime: '',
  endTime: '',
  elementName: null,
});
const showModal = ref(false);
const modelTitle = ref('新建隐患库');
const router = useRouter();
const [loading, search] = useAutoLoading(true);
const formRef = ref<FormInst | null>(null);
const { pagination, updateTotal } = useNaivePagination(getTableData);
const columns = ref<DataTableColumns>([]);
const tableData = ref<any[]>([]);
// 导入
let showExport = ref<boolean>(false);
// 导入接口
const actionUrl = api.getUrl(api.type.hazard, api.ww.paramsConf.hazardEssentialFactorReportData);
const modelForm = ref({
  id: null,
  elementName: null,
  status: 0, // 0启用 1停用
  unitId: topUnitId,
});
const rules: any = {
  elementName: [
    {
      required: true,
      message: '请输入隐患库名称',
      trigger: ['blur'],
    },
  ],
};
function resetFrom() {
  modelForm.value = {
    id: null,
    elementName: null,
    status: 0, // 0启用 1停用
    unitId: topUnitId,
  };
}
watchEffect(() => {
  if (!showModal.value) {
    resetFrom();
  }
});

watch(filterForm.value, () => {
  doSearch();
});

const doSearch = throttle(() => {
  getTableData();
}, 1000);

function downloadTemplate() {
  fileDownloader(hazardEssentialFactorTemplate(), {
    filename: '隐患库导入模板.xlsx',
    method: 'POST',
    contentType: 'application/json',
  });
}
const uploadRef = ref();
// (xhr: XMLHttpRequest) => boolean 判断请求是否为异常状态
function isErrorState(e: any) {
  let res = JSON.parse(e.response);
  if (res.code == 'success') {
    message.success('导入成功');
    showExport.value = false;
    getTableData();
  } else {
    message.error(res.message);
    if (uploadRef.value) uploadRef.value.clear(); //清除上传列表
  }
}

function setColumns() {
  columns.value.push(...cols);
  // 添加操作栏 action
  columns.value.push({
    fixed: 'right',
    width: '300px',
    title: '操作',
    key: 'actions',
    align: 'center',
    render(row) {
      return getActionBtn(row);
    },
  });
}
setColumns();

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          size: 'small',
          type: 'primary',
          ghost: true,
          onClick: () =>
            router.push({
              name: 'riskLibraryDetail',
              query: { id: row.id, name: row.elementName },
            }),
        },
        { default: () => '详情' }
      ),
    ],
  ];
  if (isTopUnit()) {
    acList.push(
      [
        h(
          NButton,
          {
            size: 'small',
            type: 'primary',
            ghost: true,
            disabled: row.status == '1',
            onClick: () => {
              let { id, elementName, status } = toRaw(row);
              modelForm.value = {
                id,
                elementName,
                unitId: topUnitId,
                status: status || 0, // 0启用 1停用
              };
              showModal.value = true;
              modelTitle.value = '编辑隐患库';
            },
          },
          { default: () => '编辑' }
        ),
        // row.used != null && row.used == 0, //隐患库数据未被使用才可编辑
      ],
      [
        h(
          NButton,
          {
            size: 'small',
            type: 'warning',
            ghost: true,
            onClick: () => handleStop(row),
          },
          {
            default: () => {
              return row.status == 0 ? '停用' : '启用';
            },
          }
        ),
      ],
      [
        h(
          NButton,
          {
            size: 'small',
            type: 'error',
            ghost: true,
            disabled: row.status == '1',
            onClick: () => handleDelete(row),
          },
          { default: () => '删除' }
        ),
        // row.used != null && row.used == 0, //隐患库数据未被使用才可编辑
      ]
    );
  }
  return useActionDivider(acList);
}

// 停用 or 启用
function handleStop(row: any) {
  let { id, status } = toRaw(row);
  $dialog.warning({
    title: '系统提示',
    content: `确定${status == 0 ? '停用' : '启用'}吗？`,
    positiveText: '确定',
    negativeText: '取消',
    positiveButtonProps: { type: 'primary', color: '#527cff' },
    transformOrigin: 'center',
    onPositiveClick: async () => {
      saveOrUpdateHazard({ id, status: status == 0 ? 1 : 0 }, false).then((res: any) => {
        message.success(status == 0 ? '停用成功' : '启用成功');
        getTableData();
      });
    },
  });
}

// 删除隐患库
function handleDelete(row: any) {
  $dialog.warning({
    title: '删除隐患库',
    // content: '确认要删除该隐患库吗？删除后不可恢复',
    content: () =>
      h('div', {}, [
        h('div', { style: 'text-align: center;' }, '确认要删除该隐患库吗？'),
        h('div', { style: 'text-align: center;' }, '删除后不可恢复'),
      ]),
    // action: () => h('div', {}, [h(NButton, {}, '取消')]),
    positiveButtonProps: { type: 'primary', color: '#527cff' },
    // positiveButtonProps: { type: 'primary', style: { '--n-color': '#3e62eb' } },
    positiveText: '确定',
    negativeText: '取消',
    transformOrigin: 'center',
    onPositiveClick: async () => {
      await deleteEssentialFactor({ id: row.id });
      getTableData();
    },
  });
}

function handleValidateClick(e: Event) {
  e.preventDefault();
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      saveOrUpdateHazard(modelForm.value, true).then((res: any) => {
        if (res.code === 'success') {
          showModal.value = false;
          resetFrom();
        }
        getTableData();
      });
    } else {
      console.log(errors);
    }
  });
}
function timeChange(date: any) {
  if (date) {
    filterForm.value.startTime = dayjs(date[0]).format('YYYY-MM-DD');
    filterForm.value.endTime = dayjs(date[1]).format('YYYY-MM-DD');
  } else {
    filterForm.value.startTime = '';
    filterForm.value.endTime = '';
  }
}

watch(createTime, (date) => {
  timeChange(date);
});

function getTableData() {
  const params = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    unitId: topUnitId,
    ...filterForm.value,
  };
  search(getEssentialFactor(params)).then((res: any) => {
    tableData.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
  loading.value = false;
}

function getTableDataWrap(data: IObj<any>) {
  pagination.page = 1;
  getTableData();
}
getTableData();
defineOptions({ name: 'checkTampTableComp' });
defineExpose({ getTableDataWrap, getTableData });
</script>
<style lang="scss" scoped>
.download {
  color: #3e62eb;
  text-decoration: underline;
  cursor: pointer;
}
</style>
