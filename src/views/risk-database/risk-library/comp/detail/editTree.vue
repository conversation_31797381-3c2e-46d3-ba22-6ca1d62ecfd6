<template>
  <n-drawer v-model:show="show" :width="502" :mask-closable="false">
    <n-drawer-content title="隐患分类">
      <!-- 设定 cascade 进行级联选择。 -->
      <n-tree
        class="tree-scroll mt-5"
        ref="treeRef"
        block-line
        cascade
        checkable
        :selectable="false"
        :data="treeData"
        checkbox-placement="right"
        key-field="id"
        label-field="label"
        children-field="children"
        :render-prefix="renderPrefix"
        :default-checked-keys="defaultCheckedKeys"
        :renderLabel="renderLabel"
        @update:checked-keys="updateCheckedKeys"
      />
      <template #footer>
        <div class="flex gap-2">
          <n-button @click="show = false">取消</n-button>
          <n-button type="primary" @click="addRelationFn">保存</n-button>
        </div>
      </template>
      <div
        v-show="loading"
        class="absolute left-0 right-0 bottom-0 top-0 flex justify-center items-center bg-black bg-opacity-30 z-[99]"
      >
        <n-space>
          <n-spin />
        </n-space>
      </div>
    </n-drawer-content>
  </n-drawer>
</template>

<script setup lang="ts">
import { computed, h, ref } from 'vue';
import parentIco from './parent.png';
import childrenIco from './children.png';
import type { TreeOption } from 'naive-ui';
import { addRelation, getHazardEssentialFactorClassList } from '@/views/hazard-management/fetchData';
import { useStore } from '@/store';
import { NTooltip } from 'naive-ui';

const userStore = useStore();
const emits = defineEmits(['update']);

function renderPrefix({ option }: { option: TreeOption }) {
  return h('img', {
    src: option.children ? parentIco : childrenIco,
    style: {
      minWidth: '16px',
      maxWidth: '16px',
    },
  });
}
const show = ref(false);

const treeData = ref([]);
// const defaultCheckedKeys = ref<string[]>([]);
// let CheckedKeys: Set<string> = new Set();
const CheckedKeys = ref<Set<string>>(new Set());
const defaultCheckedKeys = computed(() => [...CheckedKeys.value]);
const essentialFactorId = ref('');
const loading = ref(false);

const openDrawer = (data: any) => {
  show.value = true;
  essentialFactorId.value = data.essentialFactorId;
  getList(data);
};

const addRelationFn = async () => {
  console.log(defaultCheckedKeys.value);
  loading.value = true;
  try {
    await addRelation({ essentialFactorId: essentialFactorId.value, classIds: [...CheckedKeys.value] });
    emits('update');
    show.value = false;
  } catch (error) {}
  loading.value = false;
};
const updateCheckedKeys = (keys, option, meta) => {
  // 【测试环境web】隐患库检查项，隐患分类第一层级勾选时，建议二三层级同步勾选
  CheckedKeys.value = keys;
  // console.log(keys, option, meta);
  // if(meta.checked) {
  // let nodeId = meta.node.id;
  // if (meta.action === 'check') {
  //   let node: ITreeNode | null = nodeMap[nodeId];
  //   while (node) {
  //     CheckedKeys.value.add(node.id);
  //     node = node.parent;
  //   }
  // } else if (meta.action === 'uncheck') {
  //   const fn = (node: ITreeNode | null) => {
  //     if (!node) return;
  //     CheckedKeys.value.delete(node.id);
  //     if (node.children && node.children.length) {
  //       node.children.forEach((child) => fn(child));
  //     }
  //   };
  //   fn(nodeMap[nodeId]);
  // }
};

interface ITreeNode {
  parent: ITreeNode | null;
  label: string;
  disabled?: boolean;
  id: string;
  children: ITreeNode[] | null;
}

let nodeMap: { [key: string]: ITreeNode } = {};
function createData(data: any, parent: ITreeNode | null) {
  return data.map((item: any, index: any) => {
    const node: ITreeNode = {
      parent,
      label: item.className,
      disabled: item.haveData,
      id: item.id,
      children: null,
    };
    nodeMap[node.id] = node;
    if (item.children && item.children.length) node.children = createData(item.children, node);
    return node;
  });
}
const getList = async (data: { defaultCheckedKeys: string[] }) => {
  loading.value = true;
  try {
    treeData.value = [];
    CheckedKeys.value = new Set(data.defaultCheckedKeys);
    nodeMap = {};
    const res = await getHazardEssentialFactorClassList({
      unitId: userStore.userInfo.topUnitId,
      parentId: '0',
      essentialFactorId: essentialFactorId.value,
    });
    treeData.value = createData(res.data, null);
  } catch (error) {}
  loading.value = false;
};

const disabled = ref(false);
function renderLabel({ option }) {
  //超出显示tooltip
  return h(
    NTooltip,
    {
      placement: 'left-start',
      disabled: disabled.value,
    },
    {
      trigger: () => {
        return h(
          'div',
          {
            style: {
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            },
            onMouseover: (e: any) => {
              if (e.target.clientWidth < e.target.scrollWidth) {
                disabled.value = false;
              } else {
                disabled.value = true;
              }
            },
          },
          option.label
        );
      },
      default: () => option.label,
    }
  );
}

defineExpose({ openDrawer });
</script>
<style lang="scss" scoped>
:deep(.n-tree .n-tree-node-content .n-tree-node-content__text) {
  overflow: hidden;
  text-wrap: nowrap;
  text-overflow: ellipsis;
}
</style>
