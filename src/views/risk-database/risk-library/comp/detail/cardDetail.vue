<!--
 * @Author: fan<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-11-20 14:22:00
 * @LastEditors: fangweiwei <EMAIL>
 * @LastEditTime: 2024-12-05 16:54:43
 * @FilePath: \ehs-hazard-mgr\src\views\risk-database\risk-library\comp\detail\cardDetail.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <n-form
    style="padding: 24px"
    ref="formRef"
    :model="form"
    require-mark-placement="right-hanging"
    label-width="auto"
    class="max-w-[1400px] mb-[60px]"
    :rules="modalMode != 'detail' ? rules : ''"
  >
    <n-form-item path="inspectionItem" label="检查项：">
      <!-- 检查项长度50字，检查内容限制200个字 -->
      <n-input
        :disabled="modalMode === 'detail'"
        placeholder="请输入检查项"
        type="textarea"
        show-count
        :autosize="{ minRows: 3, maxRows: 8 }"
        maxlength="50"
        v-model:value="form.inspectionItem"
        clearable
      />
    </n-form-item>

    <n-form-item path="inspectionDescribe" label="检查内容：">
      <n-input
        :disabled="modalMode === 'detail'"
        placeholder="请输入检查内容"
        type="textarea"
        show-count
        :autosize="{ minRows: 3, maxRows: 8 }"
        maxlength="200"
        v-model:value="form.inspectionDescribe"
        clearable
      />
    </n-form-item>

    <n-form-item path="inspectionItemBasis" label="法规依据：">
      <n-input
        :disabled="modalMode === 'detail'"
        :placeholder="modalMode === 'detail' ? '' : '请输入法规依据'"
        type="textarea"
        show-count
        :autosize="{ minRows: 3, maxRows: 8 }"
        maxlength="200"
        v-model:value="form.inspectionItemBasis"
        clearable
      />
    </n-form-item>

    <n-form-item path="inspectionAsk" label="合规要求：">
      <n-input
        :disabled="modalMode === 'detail'"
        :placeholder="modalMode === 'detail' ? '' : '请输入合规要求'"
        type="textarea"
        show-count
        :autosize="{ minRows: 3, maxRows: 8 }"
        maxlength="200"
        v-model:value="form.inspectionAsk"
        clearable
      />
    </n-form-item>

    <n-form-item path="legalText" label="法律原文：">
      <n-input
        :disabled="modalMode === 'detail'"
        :placeholder="modalMode === 'detail' ? '' : '请输入法律原文'"
        type="textarea"
        show-count
        :autosize="{ minRows: 3, maxRows: 8 }"
        maxlength="200"
        v-model:value="form.legalText"
        clearable
      />
    </n-form-item>

    <n-form-item path="legalLiability" label="法律责任：">
      <n-input
        :disabled="modalMode === 'detail'"
        :placeholder="modalMode === 'detail' ? '' : '请输入法律责任'"
        type="textarea"
        show-count
        :autosize="{ minRows: 3, maxRows: 8 }"
        maxlength="200"
        v-model:value="form.legalLiability"
        clearable
      />
    </n-form-item>

    <n-form-item path="files" label="图例：">
      <ImgUpload
        v-if="modalMode != 'detail'"
        :data="form.files"
        @update="handleUpdate"
        :size="15"
        :max="10"
        :watermarkData="watermarkData"
        :type="'image-card'"
        :showBtn="false"
        class="com-upload-img"
      />
      <ImgUpload v-else mode="detail" :data="form.files" :type="'image-card'" :showBtn="false" />
    </n-form-item>
  </n-form>

  <!-- 常见隐患 -->
  <div class="px-[24px] pb-[88px] bt">
    <ComHeaderB title="常见隐患" />

    <n-data-table
      class="pt-[24px]"
      style="--n-merged-th-color: rgb(187, 204, 243, 0.5)"
      remote
      striped
      :columns="columns"
      ref="tableData"
      :data="form.classItemCommons"
      :min-height="260"
      :max-height="260"
      :scroll-x="1300"
    />
    <n-button
      style="margin: 20px 0; width: 100%; background: rgba(82, 124, 255, 0.08); color: #527cff"
      class="w-full my-[20px]"
      dashed
      @click="addColumns"
      v-if="modalMode != 'detail'"
    >
      <template #icon>
        <IconAdd />
      </template>
      新增一行
    </n-button>
  </div>

  <div class="flex justify-end items-center pr-[24px] h-[58px] absolute bottom-0 left-0 w-full bds" v-if="!falseBtn">
    <n-button @click="closeFn" v-if="!fromCheckList" tertiary>取消</n-button>
    <n-button type="primary" v-if="modalMode != 'detail'" @click="submit" style="margin-left: 10px"> 保存 </n-button>
    <n-button
      type="primary"
      v-if="modalMode === 'detail' && isTopUnit() && !fromCheckList"
      @click="editData"
      style="margin-left: 10px"
    >
      编辑
    </n-button>
  </div>
</template>
<script setup lang="ts">
import { h, VNode, ref, defineProps, computed, watch, reactive, nextTick } from 'vue';
import type { FormInst, FormRules } from 'naive-ui';
import { hazardEssentialFactorClassItemSaveOrUpdateHazard } from '@/views/risk-database/risk-library/fetchData';
import { useRouter, useRoute } from 'vue-router';
import { IUploadRes } from '@/components/upload/type';
import { ImgUpload } from '@/components/upload';
import { isTopUnit } from '@/views/inspection-planning/planned-management/Details/utils';
import { FlFilledAdd as IconAdd } from '@kalimahapps/vue-icons';
import { DataTableColumns, NButton, NInput, NSelect, NTreeSelect } from 'naive-ui';
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import ComHeaderB from '@/components/header/ComHeaderB.vue';
import { $toast } from '@/common/shareContext/useToastCtx.ts';
import { $dialog } from '@/common/shareContext/useDialogCtx.ts';
import { ElMessage, ElMessageBox } from 'element-plus';

const router = useRouter();
const route = useRoute();

const themeOverrides = {
  tdColorStriped: '#dfeefc',
  tdColorStripedModal: 'rgba(223,238,252,0.99)',
  thColor: '#DFEEFC',
  thTextColor: '#222',
  // tdColorHoverModal: 'rgba(18, 83, 123, 0.35)',
  // tdColorHoverPopover: 'rgba(18, 83, 123, 0.35)',
};

const props: any = defineProps({
  modalMode: {
    type: String,
    default: '',
  },
  treeData: {
    type: Array,
    default: () => [],
  },
  gradeOpt: {
    type: Array,
    default: () => [],
  },
  rows: {
    type: Object,
    default: () => {},
  },
  // 取消页面上按钮
  falseBtn: {
    type: Boolean,
    default: false,
  },
  fromCheckList: {
    //是否是检查表页面引用 是的话不显示编辑和取消按钮
    type: Boolean,
    default: false,
  },
  watermarkData: {
    type: Object,
    default: null,
  },
});
const essentialFactorId = ref<string>(route.query?.id as string).value || props.id;
const essentialFactorName = ref<string>(route.query?.name as string).value || '';
const formRef = ref<FormInst | null>(null);
const tableData = ref();
const isFirstValue = ref(false);
const form = ref<any>({
  id: null,
  essentialFactorClassId: null,
  inspectionItem: null,
  inspectionDescribe: null,
  inspectionItemBasis: null,
  inspectionAsk: null,
  legalText: null,
  legalLiability: null,
  files: [],
  classItemCommons: [], // 隐患列表
});

const rules: FormRules = {
  essentialFactorClassId: [
    {
      required: true,
      message: '请选择隐患分类',
      trigger: 'blur',
    },
  ],
  inspectionItem: [
    {
      required: true,
      message: '请输入检查项',
      trigger: 'blur',
    },
  ],
  inspectionDescribe: [
    {
      required: true,
      message: '请输入检查内容',
      trigger: 'blur',
    },
  ],
};

const emits = defineEmits(['handleNegative', 'edit', 'uploadData']);

const closeFn = () => {
  emits('handleNegative');
};

const uploadData = () => {
  emits('uploadData');
};

const handleUpdate = (res: any) => {
  form.value.files = res || [];
};

const editData = () => {
  emits('edit');
};

const submit = () => {
  formRef.value?.validate((errors) => {
    if (!errors) {
      // 校验常见隐患
      const _arrayCs: any = form.value.classItemCommons;
      // if (!_arrayCs.length) return $toast.error(`请新增常见隐患!`);
      if (_arrayCs.length) {
        for (let index = 0; index < _arrayCs.length; index++) {
          const element = _arrayCs[index];
          if (!element.essentialFactorClassId) return $toast.error(`请选择隐患分类!`);
          if (!element.hazardDesc) return $toast.error(`请输入隐患描述!`);
          if (!element.hazardGradeId) return $toast.error(`请选择隐患等级!`);

          element.classItemCommonFileVo = [
            ...(element.correctImg || []).map((item: any) => {
              item.fileCategory = 1;
              item.id = null;
              return item;
            }),
            ...(element.errorImg || []).map((item: any) => {
              item.fileCategory = 2;
              item.id = null;
              return item;
            }),
          ];
          element.serialNumber = index + 1;
        }
      }
      hazardEssentialFactorClassItemSaveOrUpdateHazard({
        ...form.value,
        essentialFactorId,
        files: form.value.files.map((item: any) => {
          return {
            ...item,
            id: null,
          };
        }),
      }).then((res: any) => {
        if (res.code == 'success') {
          closeFn();
          uploadData();
        }
      });
    } else {
      // console.log(errors);
    }
  });
};

watch(
  () => props.rows,
  (newVal) => {
    if (newVal.id && props.modalMode != 'add' && !isFirstValue.value) {
      form.value = newVal;
      const _list = newVal.classItemCommonList;
      if (_list?.length) {
        for (let index = 0; index < _list.length; index++) {
          const element = _list[index];
          if (JSON.stringify(element) == '{}') return;
          if (element.classItemCommonFileVo && element.classItemCommonFileVo.length) {
            element.correctImg = element.classItemCommonFileVo.filter((item: any) => item.fileCategory == '1');
            element.errorImg = element.classItemCommonFileVo.filter((item: any) => item.fileCategory == '2');
          }
        }
      }

      form.value.classItemCommons = _list;
      console.log(form.value.classItemCommons, '======form.value.classItemCommons');
      isFirstValue.value = true;
    }
  },
  {
    deep: true,
    immediate: true,
  }
);

// 常见隐患内容
function getActionBtn(index: number) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          ghost: true,
          type: 'error',
          size: 'small',
          onClick: () => deleteItems(index),
        },
        { default: () => '删除' }
      ),
    ],
  ];
  return useActionDivider(acList);
}
const createColumns = (): DataTableColumns<any> => [
  {
    title: '序号',
    key: 'index',
    width: 60,
    align: 'center',
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '隐患分类',
    key: 'essentialFactorClassId',
    width: 200,
    render(row: any, index: any) {
      return h(NTreeSelect, {
        value: row.essentialFactorClassId,
        placeholder: '请选择隐患分类',
        disabled: props.modalMode === 'detail',
        options: props.treeData,
        labelField: 'label',
        childrenFiled: 'children',
        valueField: 'id',
        keyField: 'id',
        onUpdateValue(v) {
          form.value.classItemCommons[index].essentialFactorClassId = v;
        },
      });
    },
  },
  {
    title: '隐患描述',
    key: 'hazardDesc',
    width: 200,
    render(row, index) {
      return h(NInput, {
        maxlength: 50,
        placeholder: '请输入隐患描述',
        disabled: props.modalMode === 'detail',
        // type: 'textarea',
        // autosize: { minRows: 2, maxRows: 3 },
        showCount: true,
        value: row.hazardDesc,
        title: row.hazardDesc,
        clearable: true,
        onUpdateValue(v) {
          form.value.classItemCommons[index].hazardDesc = v;
        },
      });
    },
  },
  {
    title: '隐患等级',
    key: 'hazardGradeId',
    width: 200,
    render(row, index) {
      return h(NSelect, {
        placeholder: '请选择隐患等级',
        disabled: props.modalMode === 'detail',
        options: props.gradeOpt,
        labelField: 'gradeName',
        valueField: 'id',
        value: row.hazardGradeId,
        clearable: true,
        onUpdateValue(v) {
          form.value.classItemCommons[index].hazardGradeId = v;
        },
      });
    },
  },
  {
    title: '正确图例',
    key: 'correctImg',
    resizable: true,
    minWidth: 120,
    render(row, index) {
      console.log(row.correctImg, '======row.correctImg');
      console.log(props.watermarkData, '======props.watermarkData');
      return (
        props.watermarkData &&
        h(ImgUpload, {
          data: row.correctImg,
          width: 60,
          max: 3,
          rowNumber: 3,
          watermarkData: props.watermarkData,
          mode: props.modalMode === 'detail' ? 'detail' : '',
          showBtn: false,
          type: 'image-card',
          onUpdate: (res: IUploadRes[]) => {
            form.value.classItemCommons[index].correctImg = res || [];
          },
        })
      );
    },
  },
  {
    title: '错误图例',
    key: 'errorImg',
    resizable: true,
    minWidth: 200,
    render(row, index) {
      return (
        props.watermarkData &&
        h(ImgUpload, {
          data: row.errorImg,
          width: 60,
          max: 3,
          rowNumber: 3,
          watermarkData: props.watermarkData,
          mode: props.modalMode === 'detail' ? 'detail' : '',
          showBtn: false,
          type: 'image-card',
          onUpdate: (res: IUploadRes[]) => {
            form.value.classItemCommons[index].errorImg = res || [];
          },
        })
      );
    },
  },
  {
    title: '操作',
    key: 'actions',
    align: 'center',
    fixed: 'right',
    width: 80,
    render(_, index) {
      return props.modalMode === 'detail' ? '--' : getActionBtn(index);
    },
  },
];
const columns = createColumns();
const addColumns = () => {
  form.value.classItemCommons.push({});
  nextTick(() => {
    tableData.value.scrollTo({
      top: form.value.classItemCommons.length * 121,
      behavior: 'smooth',
    });
  });
};

const deleteItems = (index: number) => {
  const _items = form.value.classItemCommons[index];
  if (JSON.stringify(_items) === '{}') {
    form.value.classItemCommons.splice(index, 1);
  } else {
    if (_items?.hazardDesc || _items?.hazardGradeId || _items?.correctImg?.length || _items?.errorImg?.length) {
      ElMessageBox.confirm('确认要删除该隐患吗？', '系统提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        form.value.classItemCommons.splice(index, 1);
        $toast.success(`删除成功!`);
      });
    } else {
      form.value.classItemCommons.splice(index, 1);
    }
  }
};

defineOptions({ name: 'cardDetail' });
</script>
<style lang="scss" scoped>
::v-deep .n-data-table .n-data-table-th.n-data-table-th--fixed-right {
  background: rgb(187, 204, 243);
}

// .com-upload-img :deep(.n-upload-file-list) {
//   width: calc(136px * 4 + 60px) !important;
// }

// .com-upload-img :deep(.n-upload-file) {
//   width: 136px !important;
//   height: 96px !important;
// }

// .com-upload-img :deep(.n-upload-dragger) {
//   width: 136px !important;
//   height: 96px !important;
// }

// :deep(.n-upload-file-list) {
//   width: 200px !important;
// }

// :deep(.n-upload-file) {
//   width: 60px !important;
//   height: 36px !important;
// }

// :deep(.n-upload-dragger) {
//   width: 60px !important;
//   height: 36px !important;
// }

// :deep(.n-upload-trigger) {
//   height: 36px !important;
// }

.bds {
  border-top: 1px solid #ebeef5;
  background: rgb(244, 249, 255);
}

// .bt {
//   :deep(.title) {
//     &::after {
//       content: '*';
//       color: #f56c6c;
//       padding-left: 8px;
//     }
//   }
// }
</style>
