import { DataTableColumn, NInput, NRadioGroup, NRadio, NSpace } from 'naive-ui';
import { h } from 'vue';

export const cols: DataTableColumn[] = [
  {
    title: '序号',
    key: 'index',
    width: 52,
    align: 'center',
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '检查项名称',
    key: 'itemName',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '检查项类型',
    key: 'itemTypeName',
    align: 'center',
  },
  {
    title: '选项',
    key: 'optionVoList',
    align: 'left',
    render(row: any) {
      if (row.itemType === '1') {
        if (!row.optionVoList) return;
        return h(NRadioGroup, { disabled: true }, () => [
          h(NSpace, { vertical: true }, () =>
            row?.optionVoList.map((item: any) => {
              if (item.keyType !== '2') {
                return h(NRadio, {}, () => item.optionTitle);
              } else {
                return h(NSpace, { align: 'center' }, () => [
                  h(NRadio, {}, () => item.optionTitle),
                  h(NInput, { disabled: true }),
                ]);
              }
            })
          ),
        ]);
      } else {
        return h(NInput, { disabled: true, placeholder: '请输入，不超过50个字符' });
      }
    },
  },
];
