<template>
  <div>
    <ComBread :data="breadData" v-if="!props.isChoose" />
    <div class="com-table-filter">
      <n-grid :x-gap="12" :cols="12">
        <n-gi :span="6">
          <span class="text-[16px] font-[500] flex items-center h-full">{{ essentialFactorName }}</span>
        </n-gi>
        <n-gi :span="6">
          <n-flex justify="end">
            <div class="flex items-center">
              <n-input-group>
                <n-input
                  placeholder="请输入隐患描述/检查内容模糊搜索"
                  style="width: 300px"
                  v-model:value="hazardDescribe"
                  maxlength="50"
                  show-count
                  clearable
                />
                <n-button type="primary" @click="getTableData"> 搜索 </n-button>
              </n-input-group>
            </div>
            <template v-if="isTopUnit()">
              <n-button v-if="!props.isChoose" type="primary" @click="editForm(null, 'add', '新增隐患')">
                新增
              </n-button>
              <n-button v-if="!props.isChoose" type="primary" @click="showExport = true"> 导入 </n-button>
              <n-button v-if="!props.isChoose" type="primary" @click="router.go(-1)"> 返回 </n-button>
            </template>
          </n-flex>
        </n-gi>
      </n-grid>
    </div>

    <el-row class="mt-5">
      <el-col :span="5" class="com-table-container">
        <div class="flex justify-between">
          <n-button type="primary" disabled> 隐患分类 </n-button>
          <n-button text type="primary" @click="editTreeFn" v-if="isTopUnit()"> 编辑 </n-button>
        </div>
        <n-input v-model:value="pattern" v-if="treeData.length" placeholder="请输入隐患分类" class="mt-5" />
        <n-tree
          class="tree-scroll mt-5"
          ref="treeRef"
          :pattern="pattern"
          block-line
          default-expand-all
          :data="treeData"
          show-line
          selectable
          key-field="id"
          label-field="label"
          children-field="children"
          :show-irrelevant-nodes="false"
          :renderLabel="renderLabel"
          :render-prefix="renderPrefix"
          :on-update:selected-keys="handleUpdateSelectedKeys"
        />
      </el-col>
      <el-col :span="19">
        <div class="table-scroll com-table-filter h-full ml-4" style="background-color: #fff">
          <div v-if="tableData.length" :style="`height: calc(100vh - ${toVw(300)}); overflow-y: auto`">
            <div
              v-for="(item, index) in tableData"
              :key="item.id"
              class="mt-4 table-item"
              :style="{ 'margin-top': index == 0 ? index : '' }"
            >
              <n-flex
                justify="space-between"
                align="center"
                class="p-2 title"
                style="color: #527cff; background-color: #edf1ff; border-radius: 4px 4px 0px 0px"
              >
                <div class="font-bold mt-2 max-w-[1000px] text-[15px]">{{ item.inspectionItem }}</div>
                <div>
                  <n-button v-if="props.isChoose" type="primary" size="small" @click="emits('getDesc', item)">
                    选择
                  </n-button>
                  <n-button
                    type="primary"
                    size="small"
                    @click="editForm(item, 'detail', '隐患详情')"
                    style="margin-left: 10px"
                  >
                    查看详情
                  </n-button>
                  <n-button
                    v-if="!props.isChoose && isTopUnit()"
                    type="primary"
                    size="small"
                    @click="editForm(item, 'edit', '编辑隐患')"
                    style="margin-left: 10px"
                  >
                    编辑
                  </n-button>
                </div>
              </n-flex>
              <div class="flex flex-row border-solid border-[1px] border-[#dde0e6]">
                <!-- 检查内容 -->
                <div class="p-3" :class="item.classItemCommonList.length ? 'w-[50%]' : 'w-[100%]'">
                  <div class="flex justify-start items-center">
                    <div class="w-[2px] h-[13px] bg-[#527cff] rounded-[0px]"></div>
                    <div class="ml-3">检查内容：</div>
                  </div>
                  <div class="mt-[11px]">{{ item.inspectionDescribe }}</div>
                </div>
                <!-- 常见隐患 -->
                <div
                  class="w-[50%] p-3 border-l-[1px] border-solid border-[#dde0e6]"
                  v-if="item.classItemCommonList.length"
                >
                  <div class="flex justify-start items-center">
                    <div class="w-[2px] h-[13px] bg-[#527cff] rounded-[0px]"></div>
                    <div class="ml-3">
                      常见隐患（<span>{{ item.classItemCommonList.length }}条</span>）
                    </div>
                  </div>
                  <div
                    class="flex justify-start mt-2"
                    style="align-items: flex-start"
                    v-for="(ks, index) in item.classItemCommonList && item.classItemCommonList.length > 3
                      ? item.classItemCommonList.slice(0, 3)
                      : item.classItemCommonList"
                    :key="index"
                  >
                    <div class="mr-[10px] mt-[4px]">
                      <n-button
                        class="min-w-[84px]"
                        size="small"
                        strong
                        secondary
                        type="warning"
                        :title="ks.hazardGradeName && ks.hazardGradeName.length > 4 ? ks.hazardGradeName : ''"
                        >{{
                          ks.hazardGradeName && ks.hazardGradeName.length > 4
                            ? `${ks.hazardGradeName.slice(0, 4)}...`
                            : ks.hazardGradeName
                        }}</n-button
                      >
                    </div>

                    <div>
                      <div class="text-gray-400">{{ ks.essentialFactorClassFullName || '--' }}</div>
                      <div>{{ index + 1 }}.{{ ks.hazardDesc }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <n-empty style="position: relative; top: 200px" v-else description="暂无数据" />
          <div class="flex justify-end items-end mt-4" v-if="total">
            <div class="mr-3">共 {{ total }} 条</div>
            <n-pagination
              v-model:page="pagination.page"
              v-model:page-size="pagination.pageSize"
              :item-count="total"
              show-size-picker
              :page-sizes="[10, 20, 30, 40, 50, 100]"
              :on-update:page-size="(pageSize: number) => getTableData({ pageSize })"
              :on-update:page="(page: number) => getTableData({ page })"
            />
          </div>
        </div>
      </el-col>
    </el-row>
    <!-- 改侧滑弹窗 V1.0.4 -->
    <ComDrawerA
      :title="modalTitle"
      :autoFocus="false"
      :footerPaddingBottom="25"
      :maskNeedClosable="true"
      :show="modalShow"
      @handleNegative="handleClose"
      class="!w-[1260px]"
    >
      <CardDetail
        v-if="watermarkData"
        :watermarkData="watermarkData"
        :modalMode="modalMode"
        :treeData="treeData"
        :gradeOpt="gradeOpt"
        :rows="rowsData"
        @edit="changeEdit"
        @handleNegative="handleClose"
        @uploadData="getTreeData"
      />
    </ComDrawerA>

    <!-- 旧版弹窗 导入 -->
    <n-modal
      v-model:show="showExport"
      title="导入"
      style="width: 600px"
      @negative-click="showExport = false"
      preset="card"
    >
      <n-upload
        ref="uploadRef"
        directory-dnd
        :action="actionUrl"
        show-retry-button
        with-credentials
        :max="1"
        accept=".xlsx"
        :is-error-state="isErrorState"
      >
        <n-upload-dragger>
          <div style="margin-bottom: 12px">
            <n-icon size="48" :depth="3">
              <ArchiveIcon />
            </n-icon>
          </div>
          <n-text style="font-size: 16px"> 点击或者拖动文件到该区域来上传 </n-text>
        </n-upload-dragger>
      </n-upload>
      <div class="flex justify-end download mt-5" @click="downloadTemplate">下载导入模版</div>
    </n-modal>
    <editTree ref="editTreeRef" @update="getTreeData" />
  </div>
</template>

<script setup lang="ts">
import CardDetail from './cardDetail.vue';
import ComDrawerA from '@/components/drawer/ComDrawerA.vue';
import ComBread from '@/components/breadcrumb/ComBread.vue';
import { h, computed, ref, Ref, watch, toRef } from 'vue';
import { useRoute } from 'vue-router';
import { IBreadData } from '@/components/breadcrumb/type.ts';
import type { FormInst, TreeOption, FormRules } from 'naive-ui';
import { useMessage, NButton, NTooltip } from 'naive-ui';
import {
  getTreeList,
  hazardEssentialFactorClassItemListPage,
  hazardEssentialFactorClassItemDetail,
  hazardEssentialFactorClassItemSaveOrUpdateHazard,
  hazardEssentialFactorClassItemEexportTemplate,
} from '@/views/risk-database/risk-library/fetchData';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { getGradeList } from '@/views/paramsConf/comp/fetchData';
import { api } from '@/api';
import { fileDownloader } from '@/utils/fileDownloader';
import childrenIco from './children.png';
import { ArchiveOutline as ArchiveIcon } from '@vicons/ionicons5';
import parentIco from './parent.png';
import { useStore } from '@/store';
import editTree from './editTree.vue';
import { unref } from 'vue';
import { isTopUnit } from '@/views/inspection-planning/planned-management/Details/utils';
import { toVw, toVh } from '@/utils/fit';
import { useRouter } from 'vue-router';
import { nextTick } from 'vue';
import { getWatermarkBizDataApi } from '@/utils/getWatermarkBizData';

// 获取水印配置
const watermarkData = ref<any>();
getWatermarkBizDataApi().then((data: any) => {
  watermarkData.value = data;
});

const router = useRouter();
const route = useRoute();
const editTreeRef = ref();
let defaultCheckedKeys: string[] = [];
const editTreeFn = () => {
  editTreeRef.value.openDrawer({ defaultCheckedKeys, essentialFactorId: unref(essentialFactorId) });
};

const { userInfo } = useStore();

const changeEdit = () => {
  modalMode.value = 'edit';
  modalTitle.value = '编辑隐患';
};

interface Props {
  isChoose?: boolean; //是否作为选择组件
  id: any;
}
const props = withDefaults(defineProps<Props>(), {
  isChoose: false,
  id: '',
});

const essentialFactorId = ref<string>(route.query?.id as string).value || props.id;
const essentialFactorName = ref<string>(route.query?.name as string).value || '';
const pattern = ref('');
const emits = defineEmits(['getDesc']);
const formRef = ref<FormInst | null>(null);
const treeData = ref([]);
const total = ref();
const parentId = ref();
const form = ref<any>({
  id: null,
  hazardDescribe: null,
  essentialFactorClassId: null,
  hazardGradeId: null,
  inspectionItem: null,
  inspectionDescribe: null,
  inspectionItemBasis: null,
  inspectionAsk: null,
  legalText: null,
  legalLiability: null,
  files: [],
});
const modalShow = ref(false);
const modalTitle = ref();
const modalMode = ref<any>('detail');
const gradeOpt = ref<any[]>([]);
const defaultIds = ref<any[]>([]);
const rowsData = ref({});
// 隐患库ID
// 导入
let showExport = ref<boolean>(false);
// 导入接口
const actionUrl =
  api.getUrl(api.type.hazard, api.ww.paramsConf.hazardEssentialFactorClassItemUploadAdd) +
  `?essentialFactorId=${essentialFactorId}`;

const { pagination } = useNaivePagination(getTableData);
const tableData = ref<any[]>([]);
const childrenIds = ref<any[]>([]);
const message = useMessage();

const rules: FormRules = {
  hazardDescribe: [
    {
      required: true,
      message: '请输入隐患描述',
      trigger: 'blur',
    },
  ],
  essentialFactorClassId: [
    {
      required: true,
      message: '请选择隐患分类',
      trigger: 'blur',
    },
  ],
  hazardGradeId: [
    {
      required: true,
      message: '请选择隐患等级',
      trigger: ['blur', 'change'],
    },
  ],
  inspectionItem: [
    {
      required: true,
      message: '请输入检查内容',
      trigger: 'blur',
    },
  ],
  inspectionDescribe: [
    {
      required: true,
      message: '请输入检查详情',
      trigger: 'blur',
    },
  ],
  inspectionItemBasis: [
    {
      required: true,
      message: '请输入法规依据',
      trigger: 'blur',
    },
  ],
  inspectionAsk: [
    {
      required: true,
      message: '请输入合规要求',
      trigger: 'blur',
    },
  ],
  legalText: [
    {
      required: true,
      message: '请输入法律原文',
      trigger: 'blur',
    },
  ],
  legalLiability: [
    {
      required: true,
      message: '请输入法律责任',
      trigger: 'blur',
    },
  ],
};

const hazardDescribe = ref<string>('');
const breadData: Ref<IBreadData[]> = computed(() => [
  { name: '隐患治理系统' },
  {
    name: '隐患数据库',
    clickable: true,
    routeRaw: {
      name: 'riskDatabase',
    },
  },
  {
    name: '隐患库检查项',
  },
]);

function renderPrefix({ option }: { option: TreeOption }) {
  return h('img', {
    src: option.children ? parentIco : childrenIco,
    style: {
      minWidth: '16px',
      maxWidth: '16px',
    },
  });
}

let disabled = ref(false);
function renderLabel({ option }) {
  //超出显示tooltip
  return h(
    NTooltip,
    {
      placement: 'top-start',
      disabled: disabled.value,
    },
    {
      trigger: () => {
        return h(
          'div',
          {
            style: {
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            },
            onMouseover: (e: any) => {
              if (e.target.clientWidth < e.target.scrollWidth) {
                disabled.value = false;
              } else {
                disabled.value = true;
              }
            },
          },
          option.label
        );
      },
      default: () => option.label,
    }
  );
}

function downloadTemplate() {
  fileDownloader(hazardEssentialFactorClassItemEexportTemplate(route.query?.id), {
    filename: '隐患库检查项-数据导入模板.xlsx',
    method: 'POST',
    contentType: 'application/json',
  });
}

const uploadRef = ref();
// (xhr: XMLHttpRequest) => boolean 判断请求是否为异常状态
function isErrorState(e: any) {
  let res = JSON.parse(e.response);
  if (res.code == 'success') {
    defaultIds.value = []; // 清空默认值
    message.success('导入成功');
    showExport.value = false;
    getTableData();
    getTreeData();
  } else {
    message.error(res.message);
    if (uploadRef.value) uploadRef.value.clear(); //清除上传文件
  }
}

function resetForm() {
  form.value = {
    id: null,
    hazardDescribe: null,
    essentialFactorClassId: null,
    hazardGradeId: null,
    inspectionItem: null,
    inspectionDescribe: null,
    inspectionItemBasis: null,
    inspectionAsk: null,
    legalText: null,
    legalLiability: null,
    files: [],
  };
}
// 检查项详情
function editForm(item: any, mode = 'detail', title = '隐患库详情') {
  modalTitle.value = title;
  if (!item) {
    resetForm();
    modalShow.value = true;
    modalMode.value = mode;
    return;
  }
  hazardEssentialFactorClassItemDetail({ classItemId: item.id }).then((res) => {
    form.value = res.data || null;
    if (form.value) {
      modalShow.value = true;
      modalMode.value = mode;
      // 文件类别：1正确图例 2错误图例
      form.value.correctFiles = form.value.files.filter((item: any) => item.fileCategory == 1);
      form.value.errorFiles = form.value.files.filter((item: any) => item.fileCategory == 2);
      rowsData.value = form.value;
    }
  });
}

// 节点选中项发生变化时的回调函数
function handleUpdateSelectedKeys(keys: any) {
  if (keys.length == 0 || keys[0] != parentId.value) {
    childrenIds.value = [];
  }
  parentId.value = (keys.length && keys[0]) || '';
  // treeData是树状结构且多级 需要递归获取parentId对应的 item
  let node = findNodeById(treeData.value, parentId.value);
  getChildrenIds(node);
  pagination.page = 1;
  // pagination.pageSize = 10;
  getTableData();
}

// 递归遍历树，并找到具有指定ID的节点
function findNodeById(tree: any, id: any) {
  // 遍历当前节点的所有子节点
  for (let i = 0; i < tree.length; i++) {
    // 如果找到匹配的ID，则返回该节点
    if (tree[i].id === id) {
      return tree[i];
    }
    // 如果当前节点有子节点，则递归查找
    if (tree[i].children && tree[i].children.length > 0) {
      const found: any = findNodeById(tree[i].children, id);
      if (found) {
        return found;
      }
    }
  }
  // 如果没有找到，则返回null
  return null;
}

// 获取当前id下的所有子id
function getChildrenIds(node: any) {
  // 递归获取子级id
  if (node && node.children && node.children.length) {
    node.children.forEach((item: any) => {
      childrenIds.value.push(item.id);
      item.children && item.children.length && getChildrenIds(item);
    });
  } else {
    childrenIds.value = [];
  }
}

//获取隐患级别
function getGradeOpt() {
  const params = {
    pageNo: 1,
    pageSize: 9999,
    unitId: userInfo.topUnitId,
  };
  getGradeList(params).then((res: any) => {
    gradeOpt.value = res.data.rows || [];
  });
}

function getTableData({ page, pageSize }: any = {}) {
  if (page) pagination.page = page;
  if (pageSize) {
    pagination.pageSize = pageSize;
    pagination.page = 1;
  }
  if (!page && !pageSize) pagination.page = 1;
  setTimeout(() => {
    const params = {
      pageNo: pagination.page,
      pageSize: pagination.pageSize,
      hazardDescribe: hazardDescribe.value,
      essentialFactorClassId:
        parentId.value || childrenIds.value.length ? [parentId.value, ...childrenIds.value].join(',') : '',
      essentialFactorId,
    };
    hazardEssentialFactorClassItemListPage(params).then((res: any) => {
      tableData.value = res.data.rows || [];
      total.value = res.data.total || 0;
    });
  });
}

function createData(data: any) {
  return data.map((item: any, index: any) => {
    defaultCheckedKeys.push(item.id);
    return {
      label: item.className,
      id: item.id,
      children: item.children && item.children.length ? createData(item.children) : null,
    };
  });
}

function traverse(items: any) {
  items.forEach((item: any) => {
    defaultIds.value.push(item.id); // 将当前项的id添加到ids数组中
    if (item.children && item.children.length > 0) {
      // 如果当前项有children，则递归遍历它们
      traverse(item.children);
    }
  });
}

function handleClose() {
  modalShow.value = false;
}

function getTreeData() {
  getTreeList({ essentialFactorId, parentId: '0' }).then((res: any) => {
    res.data && res.data.length ? traverse(res.data) : [];
    defaultCheckedKeys = [];
    treeData.value = createData(res.data);
    getTableData();
  });
}
watch(
  () => hazardDescribe.value,
  () => getTableData()
);
getTreeData();
getGradeOpt();
defineOptions({ name: 'checklistConfComp' });
</script>

<style lang="scss" scoped>
::v-deep .n-tree-node--selected {
  background: rgba(82, 124, 255, 0.1);

  .n-tree-node-content__text {
    color: #527cff;
  }
}

::v-deep .n-tree .n-tree-node-content {
  height: 30px;
}

.tree-scroll {
  height: calc(100vh - 370px);
  overflow: auto;
}

.table-scroll {
  // 隐藏高度
  // height: calc(100vh - 220px);

  .table-item {
    border-radius: 6px;
    border-color: #e1e3e9;

    .title {
      background-color: #f5f7fa;
    }
  }
}

.download {
  color: #3e62eb;
  text-decoration: underline;
  cursor: pointer;
}

:deep(.n-tree .n-tree-node-content .n-tree-node-content__text) {
  overflow: hidden;
  text-wrap: nowrap;
  text-overflow: ellipsis;
}
</style>
