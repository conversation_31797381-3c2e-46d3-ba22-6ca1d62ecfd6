<template>
  <ComDialog ref="checkDialog" :maskClosable="false" modelColor="rgba(255,255,255,1)" @handleClose="handleClose">
    <template #header>
      <div>添加检查人员</div>
    </template>
    <div class="flex-1 w-full h-full">
      <radio-tab class="" :tab-list="tabList" :tab="timeType" @change="handleChange"></radio-tab>
      <n-p>你选中了 {{ checkedRowKeys.length }} 行。</n-p>
      <n-data-table
        class="com-table"
        :max-height="400"
        v-model:checked-row-keys="checkedRowKeys"
        :columns="getColumns()"
        :data="tableData"
        :pagination="pagination"
      />
    </div>
    <template #action>
      <div class="flex justify-end">
        <div class="mr-[15px]">
          <n-button type="primary" @click="submit"> 确定 </n-button>
        </div>
        <div>
          <n-button> 取消 </n-button>
        </div>
      </div>
    </template>
  </ComDialog>
</template>
<script setup lang="ts">
import { computed, ref, watch, nextTick } from 'vue';
import ComDialog from '@/components/dialog/ComDialog.vue';
import type { DataTableColumns } from 'naive-ui';
import RadioTab from '@/components/tab/ComRadioTabA.vue';
import { ITabItem } from '@/components/tab/type';

const tabList: any = [
  { createTime: '2024-01-01 23:23:23', name: '隐患库1' },
  { createTime: '2024-01-01 23:23:23', name: '隐患库2' },
];
const timeType = ref('2');
const handleChange = (name: string) => {
  console.log('🚀 ~ name:', name);
};

const props = defineProps<{
  modelValue: boolean;
  dftSel?: number[];
}>();
const emit = defineEmits(['update:modelValue', 'upSleTableData']);
watch(
  () => props.dftSel,
  (val) => {
    checkedRowKeys.value = props.dftSel ?? [];
  }
);
const checkDialog = ref();
interface RowData {
  name: string;
  age: number;
  address: string;
  key: number;
}
const tableData = ref(
  new Array(77).fill('').map<RowData>((_, index) => ({
    name: `Edward King ${index}`,
    age: 32,
    address: `London, Park Lane no. ${index}`,
    key: index,
  }))
);
console.log('🚀 ~ data ~ data:', tableData.value);

const pagination = {
  pageSize: 10,
};
const checkedRowKeys = ref<number[]>([]);

const submit = () => {
  console.log('🚀 ~ submit ~ checkedRowKeys:', checkedRowKeys.value);
  const data = tableData.value.filter((i) => {
    return checkedRowKeys.value?.includes(i.key);
  });
  emit('upSleTableData', data);
  console.log(
    '🚀 ~ submit ~ checkedRowKeys:',
    tableData.value.filter((i) => {
      return checkedRowKeys.value?.includes(i.key);
    })
  );
};

const getColumns = () => {
  return [
    {
      type: 'selection',
      options: [
        'all',
        'none',
        {
          label: '选中前 2 行',
          key: 'f2',
          onSelect: (pageData: any) => {
            checkedRowKeys.value = pageData.map((row: any) => row.key).slice(0, 2);
          },
        },
      ],
      disabled(row: any) {
        return row.name === 'Edward King 3';
      },
    },
    {
      title: 'Name',
      key: 'name',
    },
    {
      title: 'Age',
      key: 'age',
    },
    {
      title: 'Address',
      key: 'address',
    },
  ];
};

// emit('update:modelValue', nVal)
watch(
  () => props.modelValue,
  (val) => {
    if (val) {
      nextTick(() => {
        console.log('🚀 ~ nextTick ~ checkDialog:', checkDialog);

        checkDialog.value.open();
      });
    } else {
      checkDialog.value.close();
    }
  }
);

const handleClose = () => {
  emit('update:modelValue', false);
};
defineOptions({ name: 'AddPersonnel' });
</script>
