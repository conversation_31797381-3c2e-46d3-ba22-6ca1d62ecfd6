<template>
  <n-layout has-sider class="com-menu notVw">
    <n-layout-sider
      :class="{ [$style.wrap]: true, [$style.expand]: isExpand }"
      :collapsed-width="48"
      :collapsed="isExpand"
      :width="240"
      @collapse="setExpand(false)"
      @expand="setExpand(true)"
      bordered
      collapse-mode="width"
    >
      <div
        class="header_btn"
        :style="{
          'padding-left': isExpand ? '14px' : '18px',
          fontSize: '16px',
          height: '48px',
        }"
        :class="{
          [$style['btn-trigger']]: true,
          ['retract']: isExpand,
          ['overflow-hidden']: true,
          relative: true,
        }"
        @click="setExpand(!isExpand)"
        text
      >
        <!-- :style="{ 'font-size': isExpand ? '22px' : '20px' }" -->
        <IconExpand
          style="color: #fff"
          :class="{
            [$style['notVw-icon-expand']]: true,
            [$style['re']]: isExpand,
          }"
        />

        <span class="text-[#fff] whitespace-nowrap" :style="`font-size: 16px;margin-left:8px`" v-show="!isExpand">
          隐患治理系统
        </span>
        <!-- <span class="text-[#fff] whitespace-nowrap absolute left-[45px]" v-show="!isExpand">隐患治理系统</span> -->
      </div>

      <!-- <n-scrollbar :style="`max-height: ${scrollMaxH}`"> -->
      <!-- 本地菜单 -->
      <!-- <n-menu
          class="com-menu menu-item-style"
          v-model:value="activeKey"
          :collapsed="isExpand"
          :options="menuList"
          :inverted="isExpand"
          accordion
          @update:value="handleUpdateValue"
        /> -->
      <!-- 动态菜单 -->
      <n-menu
        class="com-menu menu-item-style"
        v-model:value="activeKey"
        :collapsed="isExpand"
        :options="menuRouterList"
        :inverted="isExpand"
        @update:value="handleUpdateValue"
        :collapsed-icon-size="18"
        :accordion="false"
        :root-indent="18"
        :style="{ 'font-size': '16px' }"
      />
      <!-- </n-scrollbar> -->
    </n-layout-sider>
  </n-layout>
</template>

<script lang="ts" setup>
/* eslint-disable */
/* eslint-enable */
import { computed, ref, h } from 'vue';
import { icon_expand as IconExpand } from './assets/icon/index';
import { loginApi } from '@/api/login';
import { useMenu } from '@/views/menu/useMenu';
import { useState } from '@/common/hooks/useState.ts';

import misIcon from './assets/icon/隐患治理一张图.png';
import plannedManagementIndexIcon from './assets/icon/计划管理.png';
import taskManagementIcon from './assets/icon/任务管理.png';
import randomCheckIcon from './assets/icon/随机检查.png';
import hiddenDangersPhotoIcon from './assets/icon/随手拍.png';
import hazardManagementIcon from './assets/icon/隐患治理.png';
import checkListIcon from './assets/icon/检查表.png';
import riskDatabaseIcon from './assets/icon/隐患数据库.png';
import paramsConfIcon from './assets/icon/参数配置.png';
import inspectionPointIcon from './assets/icon/巡查点位.png';
import takeOneAnitThreeIcon from './assets/icon/举一反三.png';
import dataStatisticsIcon from './assets/icon/data_statistics.png';

import { useRoute } from 'vue-router';
import { useStore } from '@/store';
import { toVw, toVh } from '@/utils/fit';
const { userInfo } = useStore();
const route = useRoute();

const { menuList, activeKey, handleUpdateValue } = useMenu();

const [isExpand, setExpand] = useState(false);

const props = defineProps({
  headless: Boolean,
});

const scrollMaxH = computed(() => {
  return props.headless ? `calc(100vh - ${toVh(48)})` : `calc(100vh - ${toVh(112)})`;
});

const iconMap = {
  misIcon,
  plannedManagementIndexIcon,
  taskManagementIcon,
  randomCheckIcon,
  hiddenDangersPhotoIcon,
  hazardManagementIcon,
  checkListIcon,
  riskDatabaseIcon,
  paramsConfIcon,
  inspectionPointIcon,
  takeOneAnitThreeIcon,
  dataStatisticsIcon,
};

const menuRouterList = ref([]);
if (route.query?.token) {
  if (route.query.token && route.query.sysCode) {
    const { token, sysCode } = route.query;
    loginApi({
      token,
      sysCode,
    }).then((res: any) => {
      if (res.code == 200) {
        menuRouterList.value = res.data.resourceVoList?.map((item: any) => {
          return {
            label: item.resName,
            key: item.resUrl,
            routeName: item.resUrl,
            resIconUrl: item.resIconUrl,
            icon: () =>
              h('img', {
                src: window.$SYS_CFG.base_url + '/' + item.resIconUrl,
                width: '24px',
                height: '24px',
              }),
          };
        });
        localStorage.setItem('ehs-hazard-mgr_menuList', JSON.stringify(menuRouterList.value));
      }
    });
  }
} else {
  menuRouterList.value =
    JSON.parse(localStorage.getItem('ehs-hazard-mgr_menuList') as string)?.map((item: any) => {
      item.icon = () =>
        h('img', {
          src: window.$SYS_CFG.base_url + '/' + item.resIconUrl,
          width: '24px',
          height: '24px',
        });
      return item;
    }) || [];
}
defineOptions({ name: 'MenuIndex' });
</script>
<style lang="scss" scoped>
.notVw .com-menu {
  --n-item-height: 48px !important;
}

.n-menu {
  background: #252843 !important;
}

/* .menu-item-style.menu-item-style {
  font-size: 16px;
} */

/* :deep(.n-menu .n-menu-item) {
  height: 42px;
} */

.header_btn {
  width: 100%;
  text-align: left;
  display: flex;
  align-items: center;
  padding-left: 18px;
  cursor: pointer;
  transition:
    background-color 0.3s var(--n-bezier),
    padding-left 0.3s var(--n-bezier),
    border-color 0.3s var(--n-bezier);
  // background-color: #313652 !important;
  background-color: #252843 !important;
}

.retract {
  padding-left: 14px;
}
</style>
<style module lang="scss">
.wrap {
  background-color: #252843 !important;

  &.expand {
    background: var(--com-primary-color);
  }
}

.btn-trigger {
  width: 100%;
  /* height: ; */
  justify-content: flex-start;
  padding-left: 14px;
  font-size: 20px;

  .notVw-icon-expand {
    font-size: 20px;

    &.re {
      transform-origin: center center;
      transform: rotate(-180deg);
      color: #fff;
    }
  }
}
</style>
