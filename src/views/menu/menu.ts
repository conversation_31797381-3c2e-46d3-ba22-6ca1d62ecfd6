import type { IMenu } from "@/views/menu/type";
import { h } from "vue";
import icon from "./assets/icon/容器.png";
import hiddenDanger from "./assets/icon/隐患治理一张图.png";
import plannedManagementIcon from "./assets/icon/计划管理.png";
import taskManagementIcon from "./assets/icon/任务管理.png";
import randomCheckIcon from "./assets/icon/随机检查.png";
import hiddenDangersPhotoIcon from "./assets/icon/随手拍.png";
import hazardManagementIcon from "./assets/icon/隐患治理.png";
import jcbIcon from "./assets/icon/检查表.png";
import riskDatabaseIcon from "./assets/icon/隐患数据库.png";
import paramsConfIcon from "./assets/icon/参数配置.png";

import { createApp } from "vue";
import { setupStore } from "@/plugins";
import App from "@/App.vue";
const app = createApp(App);
async function setupApp() {
  // register store
  setupStore(app);
  app.mount("#app");
}
setupApp();
import { useStore } from "@/store";
const { userInfo } = useStore();
// console.log(userInfo.resourceVoList, 'resourceVoList');

/**
 *
 *
 * 菜单 - 支队、大队
 */
export const menuDataList: IMenu[] = [
  // ...userInfo.resourceVoList,
  {
    label: "隐患治理一张图",
    key: "home",
    routeName: "home",
    icon: () =>
      h("img", {
        src: hiddenDanger,
        width: "20px",
        height: "20px",
      }), // 图标名
  },
  {
    label: "mis",
    key: "mis",
    routeName: "mis",
    icon: () =>
      h("img", {
        src: hiddenDanger,
        width: "20px",
        height: "20px",
      }), // 图标名
  },
  {
    label: "计划管理",
    key: "plannedManagementIndex",
    routeName: "plannedManagementIndex",
    icon: () =>
      h("img", {
        src: plannedManagementIcon,
        width: "20px",
        height: "20px",
      }), // 图标名
  },
  {
    label: "任务管理",
    key: "taskManagement",
    routeName: "taskManagement",
    icon: () =>
      h("img", {
        src: taskManagementIcon,
        width: "20px",
        height: "20px",
      }), // 图标名
    // icon: 'pzgl',
  },
  {
    label: "随机检查",
    key: "randomCheck", // 一般同路由名
    // icon: 'pzgl', // 图标名
    icon: () =>
      h("img", {
        src: randomCheckIcon,
        width: "20px",
        height: "20px",
      }), // 图标名
    children: [
      {
        label: "随机检查任务管理",
        key: "checkTaskManage",
        routeName: "checkTaskManage",
      },
    ],
  },
  {
    label: "隐患随手拍",
    key: "hiddenDangersPhoto", // 一般同路由名
    // icon: 'sy', // 图标名
    routeName: "hiddenDangersPhoto",
    icon: () =>
      h("img", {
        src: hiddenDangersPhotoIcon,
        width: "20px",
        height: "20px",
      }), // 图标名
  },
  {
    label: "隐患治理",
    key: "hazardManagement",
    routeName: "hazardManagement",
    icon: () =>
      h("img", {
        src: hazardManagementIcon,
        width: "20px",
        height: "20px",
      }),
    // icon: 'pzgl',
  },
  // {
  //   label: '检查表',
  //   key: 'jcb',
  //   // icon: 'pzgl',
  //   children: [
  //     {
  //       label: '检查表管理',
  //       key: 'inspectMgrList',
  //       routeName: 'inspectMgrList',
  //     },
  //   ],
  // },

  {
    label: "检查表",
    key: "jcb",
    // icon: 'pzgl',
    icon: () =>
      h("img", {
        src: jcbIcon,
        width: "20px",
        height: "20px",
      }), // 图标名
    children: [
      {
        label: "检查表管理",
        key: "inspectMgrList",
        routeName: "inspectMgrList",
      },
    ],
  },
  {
    label: "巡查点位管理",
    key: "inspection_point",
    routeName: "inspectionPoint",
    icon: () =>
      h("img", {
        src: plannedManagementIcon,
        width: "20px",
        height: "20px",
      }), // 图标名
  },
  {
    label: "隐患数据库",
    key: "riskDatabase", // 一般同路由名
    // icon: 'pzgl', // 图标名
    icon: () =>
      h("img", {
        src: riskDatabaseIcon,
        width: "20px",
        height: "20px",
      }), // 图标名
    children: [
      {
        label: "隐患基础库",
        key: "riskLibrary",
        routeName: "riskLibrary",
      },
    ],
  },
  {
    label: "参数配置",
    key: "paramsConf",
    routeName: "paramsConf",
    icon: () =>
      h("img", {
        src: paramsConfIcon,
        width: "20px",
        height: "20px",
      }), // 图标名
    // icon: 'pzgl',
  },
];
