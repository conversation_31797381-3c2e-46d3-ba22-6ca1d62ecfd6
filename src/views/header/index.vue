<!--
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-10-12 09:24:03
 * @LastEditors: fangweiwei <EMAIL>
 * @LastEditTime: 2024-12-04 15:46:10
 * @FilePath: /隐患一张图/ehs-hazard/src/views/header/index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <header class="com-header" :class="$style['header']">
    <div :class="$style['area']">
      <div class="flex items-center" :class="$style['area-l']">
        <img
          :style="{ width: toVw(40), height: toVw(40), cursor: 'pointer' }"
          :src="logo_url"
          alt=""
          @click="goWorkbench"
        />
        <span :class="$style['title']" :style="{ fontSize: toVw(30) }" @click="goWorkbench">
          {{ userInfo.zhName || userInfo.unitName }}
        </span>
      </div>
      <div :class="$style['area-r']" class="items-center">
        <avatar />
      </div>
    </div>
  </header>
</template>

<script lang="ts" setup>
import Avatar from './comp/Avatar.vue';
import { toVw, toVh } from '@/utils/fit';
import { useStore } from '@/store/index';
const { userInfo } = useStore();

//  logo图片  测试环境图片还没有  先用生产环境的图片 后面有了替换下面注释代码即可
const logo_url = userInfo.logoPicUrl;

const goWorkbench = () => {
  window.open(userInfo.zhPlatformUrl);
};

defineOptions({ name: 'MisHeaderComp' });
</script>
<style scoped></style>

<style module lang="scss">
.header {
  position: relative;
  overflow: hidden;
  background: #252843;
}

.area {
  position: relative;
  height: 100%;
  display: grid;
  grid-template-columns: 1fr auto;
  justify-content: space-between;
  align-items: center;
  z-index: 2;
}

.area-l {
  position: relative;
  font-size: 24px;
}

.area-r {
  display: grid;
  grid-template-columns: auto 1fr;
  column-gap: 20px;
}

.title {
  margin-left: 10px;
  font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
  font-weight: 400;
  color: #ffffff;
  text-shadow: 0px 2px 8px rgba(41, 47, 58, 0.2016);
  font-style: normal;
  text-transform: none;
  cursor: pointer;
}

.logo {
  width: 40px;
  height: 40px;
  cursor: pointer;
}
</style>
