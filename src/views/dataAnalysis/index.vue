<template>
  <div>
    <com-bread :data="breadData" />
    <div class="top1"><DataAnalysisTop1Comp></DataAnalysisTop1Comp></div>
    <div class="top2"><DataAnalysisTop2Comp @action="action"></DataAnalysisTop2Comp></div>
    <div class="top3"><DataAnalysisTop3Comp ref="top3Ref"></DataAnalysisTop3Comp></div>
  </div>
</template>

<script setup lang="ts">
import ComBread from '@/components/breadcrumb/ComBread.vue';
import DataAnalysisTop1Comp from './comp/top1/index.vue';
import DataAnalysisTop2Comp from './comp/top2/index.vue';
import DataAnalysisTop3Comp from '@/views/dataAnalysis/comp/top3/top3.vue';
import { ref } from 'vue';

const breadData = [{ name: '隐患治理系统' }, { name: '数据分析' }];
const top3Ref = ref<any>();

function action(obj: any) {
  top3Ref.value?.setData(obj.data);
}

defineOptions({ name: 'DataAnalysisIndex' });
</script>

<style scoped lang="scss">
.top1 {
  padding: 20px;
  width: 100%;
  height: 372px;
  background: linear-gradient(180deg, #f6f9ff 0%, #ffffff 99%);
  border-radius: 4px 4px 4px 4px;
  margin-bottom: 20px;
}

.top2 {
  padding: 20px;
  width: 100%;
  height: 439px;
  background: linear-gradient(180deg, #f6f9ff 0%, #ffffff 99%);
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #ffffff;
  margin-bottom: 20px;
}

.top3 {
  width: 100%;
  height: 550px;
  margin-bottom: 18px;
}
</style>
