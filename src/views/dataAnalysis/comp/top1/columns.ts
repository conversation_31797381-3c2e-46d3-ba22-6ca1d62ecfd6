import { DataTableColumn, NImage } from 'naive-ui';
import { h } from 'vue';
import top1 from './assets/1.png';
import top2 from './assets/2.png';
import top3 from './assets/3.png';

export const cols: DataTableColumn[] = [
  {
    title: 'TOP',
    key: '_sort',
    align: 'center',
    render: (row: any, index: number) => {
      if (index === 0) return h(NImage, { src: top1, width: 24, height: 23, alt: 'top' });
      if (index === 1) return h(NImage, { src: top2, width: 24, height: 23, alt: 'top' });
      if (index === 2) return h(NImage, { src: top3, width: 24, height: 23, alt: 'top' });
      return index + 1;
    },
  },
  {
    title: '单位名称',
    key: 'unitName',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '单位任务数',
    key: 'rwzs',
    align: 'center',
  },
  {
    title: '任务完成率',
    key: 'rcwcl',
    render: (row: any) => {
      return row.rcwcl + '%';
    },
  },
];
