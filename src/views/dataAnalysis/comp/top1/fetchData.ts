import { api } from '@/api';
import { $http } from '@tanzerfe/http';
import { IObj } from '@/types';

/**
 * 综合分析
 * @param query
 */
export function getComprehensiveAnalysis(query: IObj<any>) {
  const url = api.getUrl(api.type.eahs, api.name.eahs.comprehensiveAnalysis, query);
  return $http.get<any>(url);
}
/**
 * 近一年隐患整改情况
 * @param query
 */
export function getHarazdDetail(query: IObj<any>) {
  const url = api.getUrl(api.type.eahs, api.name.eahs.harazdDetail, query);
  return $http.get<any>(url);
}

/**
 * 近30天排查任务TOP5
 * @param query
 */
export function getCheckTaskTop5(query: IObj<any>) {
  const url = api.getUrl(api.type.eahs, api.name.eahs.checkTaskTop5, query);
  return $http.get<any>(url);
}
