<template>
  <div class="h-[132px]">
    <comTitleB title="近一年隐患整改情况"></comTitleB>
    <n-grid class="mt-[14px]" :x-gap="12" :cols="3" layout-shift-disabled>
      <n-gi v-for="(item, i) in data" :key="i">
        <div class="item">
          <div class="m-auto">
            <div class="value">{{ item.value }}</div>
            <div class="name">{{ item.name }}</div>
          </div>
        </div>
      </n-gi>
    </n-grid>
  </div>
  <div class="custom-bottom w-full com-g-row-a1">
    <comTitleB title="隐患等级分布">
      <template #right>
        <div class="flex">
          <n-radio-group
            @change="getData"
            class="ml-auto"
            v-model:value="disposeState"
            :theme-overrides="themeOverrides"
          >
            <n-radio-button v-for="song in songs" :key="song.value" :value="song.value" :label="song.label" />
          </n-radio-group>
        </div>
      </template>
    </comTitleB>
    <bar-chart
      v-if="echartsData.label.length > 0"
      :extra="extra"
      :echartsData="echartsData"
      :graphicColor="graphicColor"
      :levelIndex="3"
      :levelColor="graphicColor"
    ></bar-chart>
    <div v-else class="relative">
      <n-empty size="large" class="mt-[8%]"></n-empty>
    </div>
  </div>
</template>

<script setup lang="ts">
import ComTitleB from '@/components/HeadTitle/comTitleB.vue';
import { ref } from 'vue';
import BarChart from '@/components/charts/barChart.vue';
import { getHarazdDetail } from '@/views/dataAnalysis/comp/top1/fetchData.ts';
import { useStore } from '@/store';

const data = ref<any>([
  {
    name: '已整改隐患',
    value: 0,
  },
  {
    name: '待整改隐患',
    value: 0,
  },
  {
    name: '逾期隐患',
    value: 0,
  },
]);

const disposeState = ref('0');
const themeOverrides = {
  buttonHeightMedium: '28px',
  fontSizeMedium: '12px',
  buttonTextColorActive: '#ffffff',
  buttonColorActive: '#0080FF',
};
const songs = ref([
  {
    label: '待整改',
    value: '0',
  },
  {
    label: '已整改',
    value: '1',
  },
  {
    label: '全部',
    value: '',
  },
]);

const echartsData = ref<any>({
  label: [],
  data: [
    {
      name: '',
      value: [],
    },
  ],
});

const extra = ref({
  barWidth: '40px',
});

const graphicColor = [
  ['#FF7272', '#ED0D0D'],
  ['#FF9C6A', '#E74D00'],
  ['#FFC44D', '#DC8B00'],
  ['#58ACFF', '#0072E4'],
];

function getData() {
  const params = {
    unitId: useStore().userInfo.unitId,
    disposeState: disposeState.value,
  };
  getHarazdDetail(params).then((res: any) => {
    data.value[0].value = res.data.disposedNum;
    data.value[1].value = res.data.unDisposedNum;
    data.value[2].value = res.data.timeoutNum;
    echartsData.value.label = res.data.dataX;
    echartsData.value.data[0].value = res.data.dataY;
  });
}

getData();

defineOptions({ name: 'rectificationSituation' });
</script>

<style scoped lang="scss">
.custom-bottom {
  height: calc(100% - 132px);
}
.item {
  display: flex;
  height: 78px;
  background: rgba(195, 199, 208, 0.2);
}

.value {
  font-family: D-DIN-PRO, D-DIN-PRO;
  font-weight: 500;
  font-size: 24px;
  color: #222222;
  line-height: 34px;
  text-align: center;
  font-style: normal;
  text-transform: none;
}

.name {
  font-family: AlibabaPuHuiTi, AlibabaPuHuiTi;
  font-weight: 400;
  font-size: 14px;
  color: #222222;
  line-height: 19px;
  text-align: center;
  font-style: normal;
  text-transform: none;
}
</style>
