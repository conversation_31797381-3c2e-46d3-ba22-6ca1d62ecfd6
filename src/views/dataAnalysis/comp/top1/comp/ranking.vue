<template>
  <div class="pl-[32px] h-full com-g-row-a1">
    <comTitleB title="近30天排查任务TOP5">
      <template #right>
        <div class="flex">
          <n-radio-group
            @change="getData"
            class="ml-auto mr-[20px]"
            v-model:value="sort"
            :theme-overrides="themeOverrides"
          >
            <n-radio-button
              v-for="song in songs"
              :key="song.value"
              :value="song.value"
              :label="song.label"
            />
          </n-radio-group>
          <div @click="more" class="more">更多>></div>
        </div>
      </template>
    </comTitleB>
    <div class="pt-[14px]">
      <n-data-table
        size="small"
        :columns="cols"
        :data="tableData"
        :pagination="false"
        :bordered="false"
        :max-height="230"
        :striped="false"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import ComTitleB from '@/components/HeadTitle/comTitleB.vue';
import { ref } from 'vue';
import { cols } from '../columns.ts';
import { useStore } from '@/store';
import { getCheckTaskTop5 } from '@/views/dataAnalysis/comp/top1/fetchData.ts';
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';

const router = useRouter();
const sort = ref('1');
const themeOverrides = {
  buttonHeightMedium: '28px',
  fontSizeMedium: '12px',
  buttonTextColorActive: '#ffffff',
  buttonColorActive: '#0080FF',
};
const songs = ref([
  {
    label: '前5',
    value: '1',
  },
  {
    label: '后5',
    value: '2',
  },
]);

const tableData = ref<any[]>([]);

function getData() {
  const params = {
    unitId: useStore().userInfo.unitId,
    sort: +sort.value, // 排序 1倒序，2顺序
  };
  getCheckTaskTop5(params).then((res: any) => {
    tableData.value = res.data;
  });
}

function more() {
  const end = new Date();
  const start = end.setMonth(end.getMonth() - 1);
  const starTime = dayjs(start).format('YYYY-MM-DD' + ' 00:00:00');
  const endTime = dayjs(new Date()).format('YYYY-MM-DD' + ' 23:59:59');
  router.push({
    path: 'task-management',
    query: {
      tab: 1,
      starTime,
      endTime,
      unitId: useStore().userInfo.unitId,
    },
  });
}

getData();

defineOptions({ name: 'RankingComp' });
</script>

<style scoped lang="scss">
.more {
  cursor: pointer;
  line-height: 26px;
  font-weight: 400;
  font-size: 14px;
  color: #0080ff;
  text-align: right;
  font-style: normal;
  text-transform: none;
}
</style>
