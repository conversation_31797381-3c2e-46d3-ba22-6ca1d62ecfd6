<template>
  <div class="h-full w-full com-g-row-a1">
    <comTitleB title="综合分析"></comTitleB>
    <RadarChart :chartData="chartData" compare-mark="集团环比均值"></RadarChart>
  </div>
</template>

<script setup lang="ts">
import ComTitleB from '@/components/HeadTitle/comTitleB.vue';
import RadarChart from '@/components/charts/detailEcharts.vue';
import { ref } from 'vue';
import { useStore } from '@/store';
import { getComprehensiveAnalysis } from '@/views/dataAnalysis/comp/top1/fetchData.ts';
const chartData = ref({
  indicator: [],
  data: [],
});

function getData() {
  const params = {
    unitId: useStore().userInfo.unitId,
  };
  getComprehensiveAnalysis(params).then((res: any) => {
    chartData.value.data = res.data.data;
    chartData.value.indicator = res.data.indicator;
  });
}

getData();

defineOptions({ name: 'ComprehensiveAnalysis' });
</script>

<style scoped lang="scss"></style>
