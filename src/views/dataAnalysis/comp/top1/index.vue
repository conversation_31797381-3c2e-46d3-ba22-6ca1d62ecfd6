<template>
  <div class="w-full h-full flex">
    <div class="w-[346px] h-full">
      <ComprehensiveAnalysis></ComprehensiveAnalysis>
    </div>
    <!--    <div class="ml-[16px] w-[234px]">-->
    <!--      <div class="dataReady">-->
    <!--        <div class="dataReady1">数据解读</div>-->
    <!--        <div class="dataReady2">-->
    <!--          排查任务较上周期减少38%，原因为外运合肥、外运天竺排查计划未执行。-->
    <!--          <br />-->
    <!--          整改周期增加21%，原因为外运黄埔、外运马驹桥整改复查周期较长。-->
    <!--          <br />-->
    <!--          整改完成率较同期增加15%，原因为外运空运、外运合肥隐患整改率较低。-->
    <!--        </div>-->
    <!--        <div class="img">-->
    <!--          <img alt="sjjd" :src="sjjd" class="dataReady3" />-->
    <!--        </div>-->
    <!--      </div>-->
    <!--    </div>-->
    <div class="flex-1 flex ml-[32px]">
      <div class="w-[480px]">
        <rectificationSituation></rectificationSituation>
      </div>
      <div class="flex-1">
        <RankingComp></RankingComp>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import ComprehensiveAnalysis from './comp/comprehensiveAnalysis.vue';
import RectificationSituation from './comp/rectificationSituation.vue';
import RankingComp from './comp/ranking.vue';
import sjjd from './assets/sjjd-bj.png';

defineOptions({ name: 'DataAnalysisTop1Comp' });
</script>

<style scoped lang="scss">
.dataReady {
  margin-top: 30px;
  width: 234px;
  height: 294px;
  background: linear-gradient(90deg, #ebf5ff 0%, #d6ebff 100%);
  border-radius: 4px 4px 4px 4px;
  padding: 10px 11px 10px 16px;
}

.dataReady1 {
  font-weight: 600;
  font-size: 14px;
  color: #0761bb;
  line-height: 20px;
  font-style: normal;
  text-transform: none;
}

.dataReady2 {
  margin-top: 10px;
  font-weight: 400;
  font-size: 12px;
  color: #0761bb;
  line-height: 17px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

.img {
  position: relative;
}

.dataReady3 {
  position: absolute;
  right: -14px;
  top: 58px;
}
</style>
