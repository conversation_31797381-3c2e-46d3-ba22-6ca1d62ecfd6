<template>
  <div class="com-g-row-a1 h-full">
    <div>
      <n-radio-group
        @change="valueChange"
        class="ml-auto mr-[20px]"
        v-model:value="value"
        :theme-overrides="themeOverrides"
      >
        <n-radio-button
          v-for="song in songs"
          :key="song.value"
          :value="song.value"
          :label="song.label"
        />
      </n-radio-group>
    </div>
    <div class="char">
      <div class="com-g-row-a1 h-full w-full left2">
        <div>
          <com-title-b title="">
            <template #right>
              <div @click="more" class="more">更多>></div>
            </template>
          </com-title-b>
        </div>
        <div>
          <StackingChart
            :color="['#FD2727', '#11D0CB', '#0080FF', '#FD9905', '#11D0CB']"
            :extra="extra1"
            :echartsData="echartsData1"
            v-if="value === '1'"
          ></StackingChart>
          <BarLineChart
            :color="['#4070FF', '#11D0CB', '#FF6A00']"
            :extra="extra2"
            :echartsData="echartsData2"
            v-if="value === '2'"
          ></BarLineChart>
          <LineChart
            v-if="value === '3'"
            :extra="extra"
            :echartsData="echartsData"
            :color="[
              'rgba(255, 0, 32, 1)',
              'rgba(17, 208, 203, 1)',
              'rgba(64, 112, 255, 1)',
              'rgba(235,58,177,1)',
              'rgba(105,120,240,1)',
              'rgba(255, 106, 0, 1)',
              'rgba(35,177,255, 1)',
              'rgba(254,146,2, 1)',
              'rgba(255,96,93, 1)',
              'rgba(234,202,51, 1)',
              'rgba(125,190,125, 1)',
              'rgba(55,165,149, 1)',
            ]"
            :graphicColor="[
              ['rgba(255, 0, 32, 0.6)', 'rgba(255, 0, 32, 0.1)'],
              ['rgba(17, 208, 203, 0.6)', 'rgba(17, 208, 203, 0.1)'],
              ['rgba(64, 112, 255, 0.6)', 'rgba(64, 112, 255, 0.1)'],
              ['rgba(235,58,177,0.6)', 'rgba(235,58,177, 0.1)'],
              ['rgba(105,120,240, 0.6)', 'rgba(105,120,240, 0.1)'],
              ['rgba(255, 106, 0, 0.6)', 'rgba(255, 106, 0, 0.1)'],
              ['rgba(35,177,255, 0.6)', 'rgba(35,177,255, 0.1)'],
              ['rgba(254,146,2, 0.6)', 'rgba(254,146,2, 0.1)'],
              ['rgba(255,96,93, 0.6)', 'rgba(255,96,93, 0.1)'],
              ['rgba(234,202,51, 0.6)', 'rgba(234,202,51, 0.1)'],
              ['rgba(125,190,125, 0.6)', 'rgba(125,190,125, 0.1)'],
              ['rgba(55,165,149, 0.6)', 'rgba(55,165,149, 0.1)'],
            ]"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import ComTitleB from '@/components/HeadTitle/comTitleB.vue';
import LineChart from '@/components/charts/LineChart.vue';
import BarLineChart from '@/components/charts/BarLineChart.vue';
import StackingChart from '@/components/charts/StackingChart.vue';
import { useRouter } from 'vue-router';
import { useStore } from '@/store/index';
const userInfo = useStore()?.userInfo;
const router = useRouter();
const startDate = ref();
const endDate = ref();
const tab = ref();
const value = ref('1');
const themeOverrides = {
  buttonHeightMedium: '32px',
  fontSizeMedium: '12px',
  buttonTextColorActive: '#ffffff',
  buttonColorActive: '#0080FF',
};
const songs = ref([
  {
    label: '任务进度分析',
    value: '1',
  },
  {
    label: '任务完成率分析',
    value: '2',
  },
  {
    label: '任务趋势分析',
    value: '3',
  },
]);
const echartsData1 = ref<any>({
  label: ['任务完成总进度', '任务逾期完成进度'],
  data: [
    {
      name: '逾期完成数',
      stack: '总量',
      type: 'bar',
      data: ['', 0],
    },
    {
      name: '逾期总数',
      stack: '总量',
      type: 'bar',
      data: ['', 0],
    },
    {
      name: '进行中',
      stack: '总量',
      type: 'bar',
      data: [0, ''],
    },
    {
      name: '待开始',
      stack: '总量',
      type: 'bar',
      data: [0, ''],
    },
    {
      name: '已完成',
      stack: '总量',
      type: 'bar',
      data: [0, ''],
    },
  ],
});
const extra1 = ref({
  barWidth: '18px',
  series: {
    dataZoom: [
      {
        type: 'inside', // 内置型数据区域缩放组件
        show: false, // 显示 组件
      },
    ],
  },
  tooltip: {
    formatter: function (params: any) {
      if (params.length <= 0) {
        return '';
      }
      let html = `${params[0].name}<br/>`;
      for (let i = 0; i < params.length; i++) {
        const name = params[i].name;
        const marker = params[i].marker;
        const seriesName = params[i].seriesName;
        if (name === '任务完成总进度') {
          if (['进行中', '已完成', '待开始'].includes(seriesName)) {
            const value = params[i].value;
            html += `<div style="height:0;justify-content: space-between;display: flex"><div>${marker}${seriesName}</div> <div>${value}</div></div><br/>`;
          }
        } else if (name === '任务逾期完成进度') {
          if (['逾期完成数', '逾期总数'].includes(seriesName)) {
            const value = params[i].value || 0;
            html += `<div style="height:0;justify-content: space-between;display: flex"><div>${marker}${seriesName}</div> <div>${value}</div></div><br/>`;
          }
        }
      }
      return html;
    },
  },
});
const extra = {
  smooth: true,
  maxLabel: 4,
  legend: {
    itemWidth: 20, // 长方形的宽度
    itemHeight: 5, // 长方形的高度
  },
};
const echartsData = ref<any>({
  label: [],
  data: [],
});
const echartsData2 = ref<any>({
  label: [],
  data: [],
});
const extra2 = ref({
  barWidth: '18px',
  series: {
    dataZoom: [
      {
        type: 'inside', // 内置型数据区域缩放组件
        show: false, // 显示 组件
      },
    ],
  },
});

const emits = defineEmits(['action']);

function valueChange() {
  emits('action', value.value);
}

function setData(
  data: any,
  starTime: string,
  endTime: string,
  dateType: string
) {
  startDate.value = starTime;
  endDate.value = endTime;
  tab.value = dateType;
  if (value.value === '1') {
    echartsData1.value.data[0].data[1] = data.yqwcs;
    echartsData1.value.data[1].data[1] = data.yqrwzs;
    echartsData1.value.data[2].data[0] = data.jxzzs;
    echartsData1.value.data[3].data[0] = data.dkszs;
    echartsData1.value.data[4].data[0] = data.ywczs;
  } else if (value.value === '2') {
    echartsData2.value.label = data.dataX;
    const dataY1 = {
      name: '检查完成数',
      stack: '总量',
      type: 'bar',
      data: data.dataY1,
    };
    const dataY2 = {
      name: '检查任务总数',
      stack: '总量',
      type: 'bar',
      data: data.dataY2,
    };
    const dataY3 = {
      name: '任务完成率',
      type: 'line',
      yAxisIndex: 1, //使用的 y 轴的 index，在单个图表实例中存在多个 y轴的时候有用
      smooth: false, //平滑曲线显示
      symbol: 'circle', //标记的图形为实心圆
      symbolSize: 8, //标记的大小
      itemStyle: {
        normal: {
          borderColor: 'rgba(114, 169, 254, 0.5)', //圆点透明 边框
          borderWidth: 7,
        },
      },
      data: data.dataY3,
    };
    echartsData2.value.data = [dataY1, dataY2, dataY3];
  } else if (value.value === '3') {
    echartsData.value.label = data.dataX;
    echartsData.value.data = data.taskUnitVoList.map((item: any) => {
      return {
        name: item.itemName,
        value: item.dataLists,
      };
    });
  }
}

function more() {
  router.push({
    path: 'task-management',
    query: {
      tab: 1,
      starTime: startDate.value,
      endTime: endDate.value,
      unitId: userInfo.unitId,
    },
  });
}

defineExpose({
  setData,
});

defineOptions({ name: 'TroubleshootingTasks' });
</script>

<style scoped lang="scss">
.more {
  cursor: pointer;
  font-weight: 400;
  font-size: 14px;
  color: #0080ff;
  line-height: 20px;
  text-align: right;
  font-style: normal;
  text-transform: none;
}

.left2 {
  background-image: url('../assets/left2.png');
  background-repeat: no-repeat; /* 不平铺 */
  background-position: top right; /* 定位在右上角 */
}

.char {
  margin-top: 12px;
  padding: 16px 20px 8px 20px;
  background: linear-gradient(180deg, #f1f5ff 21%, rgba(255, 255, 255, 0) 100%);
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #e5e8f0;
}
</style>
