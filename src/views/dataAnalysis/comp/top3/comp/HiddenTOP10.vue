<template>
  <n-data-table
    size="small"
    :columns="cols"
    :data="tableData"
    :pagination="false"
    :bordered="false"
    :max-height="450"
    :striped="false"
  />
</template>

<script setup lang="ts">
import { cols } from '../columns.ts';
import { ref } from 'vue';
import { IHiddenDangerTop10 } from '../type.ts';

const tableData = ref<IHiddenDangerTop10[]>([]);

function setData(data: IHiddenDangerTop10[]) {
  tableData.value = data;
}

defineExpose({
  setData,
});

defineOptions({ name: 'HiddenTOP10' });
</script>

<style scoped lang="scss">
:deep(.n-data-table-empty) {
  height: 450px !important;
}
</style>
