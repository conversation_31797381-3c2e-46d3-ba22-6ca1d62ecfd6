<template>
  <div class="com-g-row-a1 h-full">
    <div>
      <n-radio-group
        @change="valueChange"
        class="ml-auto mr-[20px]"
        v-model:value="value"
        :theme-overrides="themeOverrides"
      >
        <n-radio-button v-for="song in songs" :key="song.value" :value="song.value" :label="song.label" />
      </n-radio-group>
    </div>
    <div class="char">
      <div class="com-g-row-a1 h-full w-full left1">
        <div>
          <com-title-b title="">
            <template #right>
              <div @click="more" class="more">更多>></div>
            </template>
          </com-title-b>
        </div>
        <div>
          <LineChart
            v-if="value === '1'"
            :extra="extra"
            :echartsData="echartsData"
            :color="[
              'rgba(255, 0, 32, 1)',
              'rgba(17, 208, 203, 1)',
              'rgba(64, 112, 255, 1)',
              'rgba(235,58,177,1)',
              'rgba(105,120,240,1)',
              'rgba(255, 106, 0, 1)',
              'rgba(35,177,255, 1)',
              'rgba(254,146,2, 1)',
              'rgba(255,96,93, 1)',
              'rgba(234,202,51, 1)',
              'rgba(125,190,125, 1)',
              'rgba(55,165,149, 1)',
            ]"
            :graphicColor="[
              ['rgba(255, 0, 32, 0.6)', 'rgba(255, 0, 32, 0.1)'],
              ['rgba(17, 208, 203, 0.6)', 'rgba(17, 208, 203, 0.1)'],
              ['rgba(64, 112, 255, 0.6)', 'rgba(64, 112, 255, 0.1)'],
              ['rgba(235,58,177,0.6)', 'rgba(235,58,177, 0.1)'],
              ['rgba(105,120,240, 0.6)', 'rgba(105,120,240, 0.1)'],
              ['rgba(255, 106, 0, 0.6)', 'rgba(255, 106, 0, 0.1)'],
              ['rgba(35,177,255, 0.6)', 'rgba(35,177,255, 0.1)'],
              ['rgba(254,146,2, 0.6)', 'rgba(254,146,2, 0.1)'],
              ['rgba(255,96,93, 0.6)', 'rgba(255,96,93, 0.1)'],
              ['rgba(234,202,51, 0.6)', 'rgba(234,202,51, 0.1)'],
              ['rgba(125,190,125, 0.6)', 'rgba(125,190,125, 0.1)'],
              ['rgba(55,165,149, 0.6)', 'rgba(55,165,149, 0.1)'],
            ]"
          />
          <BarLineChart
            :color="['#11D0CB', '#FF6A00', '#4070FF']"
            :extra="extra2"
            :echartsData="echartsData2"
            v-if="value === '2'"
          ></BarLineChart>
          <bar-chart
            :color="['#11D0CB']"
            v-if="value === '3'"
            :extra="extra3"
            :echartsData="echartsData3"
            :graphicColor="graphicColor"
            :barBorderRadius="[20, 20, 20, 20]"
            :levelColor="graphicColor"
          ></bar-chart>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import ComTitleB from '@/components/HeadTitle/comTitleB.vue';
import LineChart from '@/components/charts/LineChart.vue';
import BarChart from '@/components/charts/barChartNew.vue';
import BarLineChart from '@/components/charts/BarLineChart.vue';
import { useRouter } from 'vue-router';

const startDate = ref();
const endDate = ref();
const tab = ref();
const router = useRouter();
const value = ref('1');
const themeOverrides = {
  buttonHeightMedium: '32px',
  fontSizeMedium: '12px',
  buttonTextColorActive: '#ffffff',
  buttonColorActive: '#0080FF',
};
const songs = ref([
  {
    label: '隐患上报数量',
    value: '1',
  },
  {
    label: '隐患整改完成率',
    value: '2',
  },
  {
    label: '隐患整改周期',
    value: '3',
  },
]);
const extra = {
  smooth: true,
  maxLabel: 4,
  legend: {
    itemWidth: 20, // 长方形的宽度
    itemHeight: 5, // 长方形的高度
  },
};
const echartsData = ref<any>({
  label: [],
  data: [],
});
const echartsData2 = ref<any>({
  label: [],
  data: [],
});
const extra2 = ref({
  barWidth: '18px',
  series: {
    dataZoom: [
      {
        type: 'inside', // 内置型数据区域缩放组件
        show: false, // 显示 组件
      },
    ],
  },
});
const echartsData3 = ref<any>({
  label: [],
  data: [
    {
      name: '整改完成数',
      value: [],
    },
  ],
});
const extra3 = ref({
  barWidth: '18px',
  series: {
    grid: {
      top: '40',
      left: '8',
      right: '8',
      bottom: '70',
    },
    dataZoom: [
      {
        type: 'inside', // 内置型数据区域缩放组件
        show: false, // 显示 组件
      },
    ],
  },
  part: {
    label: {
      normal: {
        show: true,
        position: 'top',
        color: '#000000',
        fontSize: 14,
        backgroundColor: '#FFFFFF',
        padding: [9, 13, 9, 14],
        borderRadius: 4,
        shadowColor: 'rgba(0, 0, 0, 0.1)',
        shadowBlur: 10,
        formatter: function (params: any) {
          return (params.value = params.value || '');
        },
        rich: {
          leftStr: {
            marginRight: 5,
            color: '#F2F4F7',
          },
        },
      },
    },
  },
});
const graphicColor = [['rgba(17, 208, 203, 0.5)', 'rgba(17, 208, 203, 1)']];
const emits = defineEmits(['action']);

function valueChange() {
  emits('action', value.value);
}

function setData(data: any, starTime: string, endTime: string, dateType: string) {
  startDate.value = starTime;
  endDate.value = endTime;
  tab.value = dateType;
  if (value.value === '1') {
    echartsData.value.label = data.dataX;
    echartsData.value.data = data.hazardNumberVoList.map((item: any) => {
      return {
        name: item.hazardLevelName,
        value: item.dataLists,
      };
    });
  } else if (value.value === '2') {
    echartsData2.value.label = data.dataX;
    const dataY1 = {
      name: '已整改',
      stack: 'one',
      type: 'bar',
      data: data.dataY1,
    };
    const dataY2 = {
      name: '隐患数',
      stack: 'one',
      type: 'bar',
      data: data.dataY2,
    };
    const dataY3 = {
      name: '整改完成率',
      type: 'line',
      yAxisIndex: 1, //使用的 y 轴的 index，在单个图表实例中存在多个 y轴的时候有用
      symbol: 'circle', //标记的图形为实心圆
      itemStyle: {
        normal: {
          borderColor: 'rgba(114, 169, 254, 0.5)', //圆点透明 边框
          borderWidth: 7,
        },
      },
      data: data.dataY3,
    };
    echartsData2.value.data = [dataY1, dataY2, dataY3];
  } else {
    echartsData3.value.label = data.dataX;
    const total = data.dataY1.reduce((a: any, b: any) => a + b, 0);
    extra3.value.part.label.normal.formatter = function (params: any) {
      if (total > 0) {
        return Math.round((params.value / total) * 10000) / 100 + '% \ ' + params.value + '条';
      } else {
        return '{leftStr|0%} \ {rightStr||0条}';
      }
    };
    echartsData3.value.data[0].value = data.dataY1;
  }
}

function more() {
  router.push({
    path: 'hazard-management',
    query: {
      starTime: startDate.value,
      endTime: endDate.value,
      tab: tab.value,
    },
  });
}

defineExpose({
  setData,
});

defineOptions({ name: 'HiddenDangerRectification' });
</script>

<style scoped lang="scss">
.more {
  cursor: pointer;
  font-weight: 400;
  font-size: 14px;
  color: #0080ff;
  line-height: 20px;
  text-align: right;
  font-style: normal;
  text-transform: none;
}

.char {
  margin-top: 12px;
  padding: 16px 20px 8px 20px;
  background: linear-gradient(180deg, #f1f5ff 21%, rgba(255, 255, 255, 0) 100%);
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #e5e8f0;
}

.left1 {
  background-image: url('../assets/left1.png');
  background-repeat: no-repeat; /* 不平铺 */
  background-position: top right; /* 定位在右上角 */
}
</style>
