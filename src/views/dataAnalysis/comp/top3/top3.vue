<template>
  <n-grid
    class="mt-[14px] w-full h-full"
    :x-gap="20"
    :cols="3"
    layout-shift-disabled
  >
    <n-gi class="ngi com-g-row-a1">
      <comTitleA title="隐患整改数据分析"></comTitleA>
      <!--      <div class="description">-->
      <!--        本期隐患上报{curr_cnt}条，较上期{prev_cnt}条{pct_change}%，上报的{一般隐患}为{XX}条，占比51%。-->
      <!--        整改完成率{comp_rate}%，A、B、C单位隐患整改率较低，请敦促单位重视，加强隐患整改闭环。-->
      <!--        整改周期集中在{最大的整改周期区间}，建议加强隐患整改时效。-->
      <!--      </div>-->
      <div class="pt-[16px]">
        <HiddenDangerRectification
          ref="hiddenDangerRectificationRef"
          @action="action1"
        ></HiddenDangerRectification>
      </div>
    </n-gi>
    <n-gi class="ngi com-g-row-a1">
      <comTitleA title="排查任务执行分析"></comTitleA>
      <!--      <div class="description">-->
      <!--        本期隐患排查任务{curr_cnt}条，较上期{prev_cnt}条增加/减少{pct_change}%-->
      <!--        任务完成率78%，A、B、C单位完成率相对较低。-->
      <!--        当前存在XX个逾期待执行任务，建议敦促相关单位重视，加强隐患排查任务执行。-->
      <!--      </div>-->
      <div class="pt-[16px]">
        <TroubleshootingTasks
          ref="troubleshootingTasksRef"
          @action="action2"
        ></TroubleshootingTasks>
      </div>
    </n-gi>
    <n-gi class="ngi com-g-row-a1">
      <comTitleA title="单位发生隐患TOP10">
        <template #right>
          <div @click="more" class="more">更多>></div>
        </template>
      </comTitleA>
      <div class="mt-[16px]">
        <HiddenTOP10 ref="hiddenTOP10Ref"></HiddenTOP10>
      </div>
    </n-gi>
  </n-grid>
</template>

<script setup lang="ts">
import ComTitleA from '@/components/HeadTitle/comTitleA.vue';
import HiddenTOP10 from './comp/HiddenTOP10.vue';
import HiddenDangerRectification from './comp/HiddenDangerRectification.vue';
import TroubleshootingTasks from './comp/TroubleshootingTasks.vue';
import {
  getHazardDisposeAnalysis,
  getHazardDisposeTimeAnalysis,
  getHazardNumberAnalysis,
  taskCompleteAnalysis,
  taskTrendAnalysis,
  taskProgessAnalysis,
  unitHazardTop10,
} from './fetchData.ts';
import { ref } from 'vue';
import { useStore } from '@/store/index';
import { useRouter } from 'vue-router';
const userInfo = useStore()?.userInfo;
const router = useRouter();
const hiddenDangerRectificationRef = ref<any>();
const troubleshootingTasksRef = ref<any>();
const hiddenTOP10Ref = ref<any>();
const action1Value = ref<any>('1');
const action2Value = ref<any>('1');
const params = ref<any>({});
const dateType = ref<any>({});

function setData(param: any) {
  params.value = {
    ...param,
    dateType: undefined,
  };
  dateType.value = param.dateType;
  action1Fun();
  action2Fun();
  unitHazardTop10(params.value).then((res: any) => {
    hiddenTOP10Ref.value?.setData(
      res.data,
      params.value.starTime,
      params.value.endTime,
      dateType.value
    );
  });
}

function action1Fun() {
  if (action1Value.value === '1') {
    getHazardNumberAnalysis({
      ...params.value,
      dateType: dateType.value === '' ? '' : +dateType.value + 1,
    }).then((res: any) => {
      hiddenDangerRectificationRef.value?.setData(
        res.data,
        params.value.starTime,
        params.value.endTime,
        dateType.value
      );
    });
  } else if (action1Value.value === '2') {
    getHazardDisposeAnalysis({
      ...params.value,
      dateType: dateType.value === '' ? '' : +dateType.value + 1,
    }).then((res: any) => {
      hiddenDangerRectificationRef.value?.setData(
        res.data,
        params.value.starTime,
        params.value.endTime,
        dateType.value
      );
    });
  } else {
    getHazardDisposeTimeAnalysis(params.value).then((res: any) => {
      hiddenDangerRectificationRef.value?.setData(
        res.data,
        params.value.starTime,
        params.value.endTime,
        dateType.value
      );
    });
  }
}

function action2Fun() {
  if (action2Value.value === '1') {
    taskProgessAnalysis(params.value).then((res: any) => {
      troubleshootingTasksRef.value?.setData(
        res.data,
        params.value.starTime,
        params.value.endTime,
        dateType.value
      );
    });
  } else if (action2Value.value === '2') {
    taskCompleteAnalysis({
      ...params.value,
      dateType: dateType.value === '' ? '' : +dateType.value + 1,
    }).then((res: any) => {
      troubleshootingTasksRef.value?.setData(
        res.data,
        params.value.starTime,
        params.value.endTime,
        dateType.value
      );
    });
  } else {
    taskTrendAnalysis(params.value).then((res: any) => {
      troubleshootingTasksRef.value?.setData(
        res.data,
        params.value.starTime,
        params.value.endTime,
        dateType.value
      );
    });
  }
}

function action1(str: string) {
  action1Value.value = str;
  action1Fun();
}

function action2(str: string) {
  action2Value.value = str;
  action2Fun();
}

function more() {
  router.push({
    path: 'hazard-management',
    query: {
      starTime: params.value.starTime,
      endTime: params.value.endTime,
      tab: params.value.dateType,
      unitId: userInfo.unitId,
    },
  });
}

defineExpose({
  setData,
});

defineOptions({ name: 'DataAnalysisTop3Comp' });
</script>

<style scoped lang="scss">
.more {
  cursor: pointer;
  font-weight: 400;
  font-size: 14px;
  color: #0080ff;
  line-height: 20px;
  text-align: right;
  font-style: normal;
  text-transform: none;
}

.ngi {
  padding: 20px;
  background: linear-gradient(180deg, #f6f9ff 0%, #ffffff 99%), #ffffff;
  border-radius: 4px 4px 4px 4px;
}

.description {
  margin-top: 16px;
  padding: 10px 0 0 5px;
  height: 84px;
  background: linear-gradient(90deg, #ebf5ff 0%, #d6ebff 100%);
  border-radius: 4px 4px 4px 4px;
  font-weight: 400;
  font-size: 12px;
  color: #0761bb;
  line-height: 21px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
</style>
