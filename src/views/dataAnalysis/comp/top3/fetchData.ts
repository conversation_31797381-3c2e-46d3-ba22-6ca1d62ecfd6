import { api } from '@/api';
import { $http } from '@tanzerfe/http';
import { IObj } from '@/types';

/**
 * 隐患整改数据分析-隐患上报数量
 * @param query
 */
export function getHazardNumberAnalysis(query: IObj<any>) {
  const url = api.getUrl(api.type.eahs, api.name.eahs.hazardNumberAnalysis, query);
  return $http.get<any>(url);
}

/**
 * 隐患整改数据分析-隐患整改完成率
 * @param query
 */
export function getHazardDisposeAnalysis(query: IObj<any>) {
  const url = api.getUrl(api.type.eahs, api.name.eahs.hazardDisposeAnalysis, query);
  return $http.get<any>(url);
}

/**
 * 隐患整改数据分析-隐患整改周期
 * @param query
 */
export function getHazardDisposeTimeAnalysis(query: IObj<any>) {
  const url = api.getUrl(api.type.eahs, api.name.eahs.hazardDisposeTimeAnalysis, query);
  return $http.get<any>(url);
}

/**
 * 排查任务执行分析-任务进度分析
 * @param query
 */
export function taskProgessAnalysis(query: IObj<any>) {
  const url = api.getUrl(api.type.eahs, api.name.eahs.taskProgessAnalysis, query);
  return $http.get<any>(url);
}

/**
 * 排查任务执行分析-任务完成率分析
 * @param query
 */
export function taskCompleteAnalysis(query: IObj<any>) {
  const url = api.getUrl(api.type.eahs, api.name.eahs.taskCompleteAnalysis, query);
  return $http.get<any>(url);
}

/**
 * 排查任务执行分析-任务趋势分析
 * @param query
 */
export function taskTrendAnalysis(query: IObj<any>) {
  const url = api.getUrl(api.type.eahs, api.name.eahs.taskTrendAnalysis, query);
  return $http.get<any>(url);
}

/**
 * 单位发生隐患Top10
 * @param query
 */
export function unitHazardTop10(query: IObj<any>) {
  const url = api.getUrl(api.type.eahs, api.name.eahs.unitHazardTop10, query);
  return $http.get<any>(url);
}
