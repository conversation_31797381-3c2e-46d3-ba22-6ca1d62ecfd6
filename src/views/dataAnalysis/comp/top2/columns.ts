import { DataTableColumn, NImage } from 'naive-ui';
import { h } from 'vue';
import top1 from './assets/1.png';
import top2 from './assets/2.png';
import top3 from './assets/3.png';

export const cols: DataTableColumn[] = [
  {
    title: 'TOP',
    key: '_sort',
    align: 'center',
    render: (row: any, index: number) => {
      if (index === 0) return h(NImage, { src: top1, width: 24, height: 23, alt: 'top' });
      if (index === 1) return h(NImage, { src: top2, width: 24, height: 23, alt: 'top' });
      if (index === 2) return h(NImage, { src: top3, width: 24, height: 23, alt: 'top' });
      return index + 1;
    },
  },
  {
    title: '隐患描述',
    key: 'hazardDesc',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '隐患数量',
    key: 'total',
    align: 'center',
  },
  {
    title: '占比',
    key: 'rate',
    render: (row: any) => {
      return row.rate + '%';
    },
  },
];
