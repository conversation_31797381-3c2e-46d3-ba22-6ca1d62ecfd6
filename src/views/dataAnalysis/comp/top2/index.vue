<template>
  <div class="com-g-row-a1 h-full w-full">
    <comTitleA title="高频隐患数据分析">
      <template #right>
        <div class="flex">
          <n-radio-group
            @change="dateTypeChange"
            class="ml-auto"
            v-model:value="dateType"
            :theme-overrides="themeOverrides"
          >
            <n-radio-button v-for="song in songs" :key="song.value" :value="song.value" :label="song.label" />
          </n-radio-group>
          <n-date-picker
            v-model:value="timeRangeVal"
            type="daterange"
            size="small"
            class="ml-[20px] w-[238px]"
            format="yyyy-MM-dd"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            clearable
            @update:value="timeRangeValChange"
          />
        </div>
      </template>
    </comTitleA>
    <n-grid class="pt-[18px]" :x-gap="21" :cols="3" layout-shift-disabled>
      <n-gi class="bg1 ngi com-g-row-a1">
        <com-title-b title="高频隐患分类">
          <template #right>
            <div @click="more" class="more">更多>></div>
          </template>
        </com-title-b>
        <ringDiagram
          v-if="certificateCompliance.length > 0"
          :extra="extra"
          :echartsData="certificateCompliance"
        ></ringDiagram>
        <div v-else class="relative">
          <n-empty size="large" class="mt-[20%]"></n-empty>
        </div>
      </n-gi>
      <n-gi class="bg2 ngi">
        <TestItems ref="testItemsRef"></TestItems>
      </n-gi>
      <n-gi class="bg2 ngi">
        <DetailsTop5 ref="detailsTop5Ref"></DetailsTop5>
      </n-gi>
    </n-grid>
  </div>
</template>

<script setup lang="ts">
import ComTitleA from '@/components/HeadTitle/comTitleA.vue';
import { ref, toRaw, nextTick } from 'vue';
import RingDiagram from '@/components/charts/ringDiagram.vue';
import ComTitleB from '@/components/HeadTitle/comTitleB.vue';
import TestItems from './comp/testItems.vue';
import DetailsTop5 from '@/views/dataAnalysis/comp/top2/comp/DetailsTop5.vue';
import { useStore } from '@/store';
import { getHighCheckDetailAnalysisTop10, getHighHarazdAnalysis, highCheckAnalysisTop10 } from './fetchData.ts';
import dayjs from 'dayjs';

import { useRouter } from 'vue-router';

const userInfo = useStore()?.userInfo;
const router = useRouter();
const emits = defineEmits(['action']);
const testItemsRef = ref<any>();
const detailsTop5Ref = ref<any>();
// 默认给时间范围赋值最近一周
const end = new Date(); // 获取当前时间
const start = new Date();
// 当前时间往前推7天就是最近一周开始的日期
start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
const timeRangeVal = ref([start, end]);
const dateType = ref('2');
const themeOverrides = {
  buttonHeightMedium: '28px',
  fontSizeMedium: '12px',
  buttonTextColorActive: '#ffffff',
  buttonColorActive: '#0080FF',
};
const songs = ref([
  {
    label: '今天',
    value: '0',
    getStart() {
      return new Date();
    },
  },
  {
    label: '昨天',
    value: '1',
    getStart() {
      return new Date().setTime(new Date().getTime() - 3600 * 1000 * 24);
    },
  },
  {
    label: '近一周',
    value: '2',
    getStart() {
      return new Date().setTime(new Date().getTime() - 3600 * 1000 * 24 * 6);
    },
  },
  {
    label: '近一个月',
    value: '3',
    getStart() {
      const pastDate = new Date();
      return pastDate.setMonth(pastDate.getMonth() - 1);
    },
  },
  {
    label: '近三个月',
    value: '4',
    getStart() {
      const pastDate = new Date();
      return pastDate.setMonth(pastDate.getMonth() - 3);
    },
  },
]);
const certificateCompliance = ref<any[]>([]);
const extra = {
  legend: {
    backgroundColor: 'rgba(255,255,255,0.8)',
    right: '8%',
  },
  tooltip: {
    formatter: '{b}<br /> {d}% {c}条',
  },
  series: {
    center: ['30%', '50%'],
  },
};

function dateTypeChange() {
  const obj: any = songs.value.find((item: any) => item.value == dateType.value);
  let startTime = obj.getStart();
  timeRangeVal.value = [startTime, new Date()];
  if (dateType.value === '1') {
    timeRangeVal.value = [startTime, startTime];
  }
  console.log(timeRangeVal.value, '>>>>');
  getData();
}

function timeRangeValChange(value: any, formatValue: any) {
  dateType.value = '';
  getData();
}

function getData() {
  console.log('2025-05-10', 'timeRnageVal');
  const params = {
    unitId: useStore().userInfo.unitId,
    // dateType: dateType.value,
    starTime: dayjs(timeRangeVal.value[0]).format('YYYY-MM-DD' + ' 00:00:00'),
    endTime: dayjs(timeRangeVal.value[1]).format('YYYY-MM-DD' + ' 23:59:59'),
  };

  getHighHarazdAnalysis(params).then((res: any) => {
    certificateCompliance.value = res.data.highHarazdList.map((item: any) => {
      return {
        name: item.className,
        value: item.total,
      };
    });
  });

  highCheckAnalysisTop10(params).then((res: any) => {
    testItemsRef.value?.setData(res.data, params.starTime, params.endTime, dateType.value);
  });

  getHighCheckDetailAnalysisTop10(params).then((res: any) => {
    detailsTop5Ref.value?.setData(res.data, params.starTime, params.endTime, dateType.value);
  });
  nextTick(() => {
    emits('action', {
      action: 'query',
      data: {
        ...params,
        dateType: dateType.value,
      },
    });
  });
}

function more() {
  router.push({
    path: 'data_statistics',
    query: {
      starTime: dayjs(timeRangeVal.value[0]).format('YYYY-MM-DD' + ' 00:00:00'),
      endTime: dayjs(timeRangeVal.value[1]).format('YYYY-MM-DD' + ' 23:59:59'),
      tab: dateType.value,
      tab1: 2,
      tab2: 2,
      unitId: userInfo.unitId,
    },
  });
}

getData();

defineOptions({ name: 'DataAnalysisTop2Comp' });
</script>

<style scoped lang="scss">
.bg1 {
  background-image: url('./assets/bg1.png');
  background-repeat: no-repeat; /* 不平铺 */
  background-position: top right; /* 定位在右上角 */
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #e5e8f0;
}

.bg2 {
  background: linear-gradient(180deg, #f1f5ff 21%, rgba(255, 255, 255, 0) 100%);
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #e5e8f0;
}

.ngi {
  padding: 15px 21px 15px 21px;
}

.more {
  cursor: pointer;
  font-weight: 400;
  font-size: 14px;
  color: #0080ff;
  line-height: 20px;
  text-align: right;
  font-style: normal;
  text-transform: none;
}
</style>
