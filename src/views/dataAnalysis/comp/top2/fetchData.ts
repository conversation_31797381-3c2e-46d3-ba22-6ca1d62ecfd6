import { api } from '@/api';
import { $http } from '@tanzerfe/http';
import { IObj } from '@/types';

/**
 * 高频隐患分类
 * @param query
 */
export function getHighHarazdAnalysis(query: IObj<any>) {
  const url = api.getUrl(api.type.eahs, api.name.eahs.highHarazdAnalysis, query);
  return $http.get<any>(url);
}
/**
 * 高频隐患检查项TOP10
 * @param query
 */
export function highCheckAnalysisTop10(query: IObj<any>) {
  const url = api.getUrl(api.type.eahs, api.name.eahs.highCheckAnalysisTop10, query);
  return $http.get<any>(url);
}

/**
 * 高频隐患详情TOP5
 * @param query
 */
export function getHighCheckDetailAnalysisTop10(query: IObj<any>) {
  const url = api.getUrl(api.type.eahs, api.name.eahs.highCheckDetailAnalysisTop10, query);
  return $http.get<any>(url);
}
