<!--
 * @Author: xginger <EMAIL>
 * @Date: 2025-06-09 14:27:04
 * @LastEditors: xginger <EMAIL>
 * @LastEditTime: 2025-06-11 15:02:36
 * @FilePath: \ehs-hazard-mgr\src\views\dataAnalysis\comp\top2\comp\DetailsTop5.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="com-g-row-a1 h-full top5bj">
    <com-title-b title="高频隐患详情top5">
      <template #right>
        <div @click="more" class="more">更多>></div>
      </template>
    </com-title-b>
    <div class="pt-[14px]">
      <n-data-table
        size="small"
        :columns="cols"
        :data="tableData"
        :pagination="false"
        :bordered="false"
        :max-height="250"
        :striped="false"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import ComTitleB from '@/components/HeadTitle/comTitleB.vue';
import { cols } from '../columns.ts';
import { ref } from 'vue';
import { IHiddenDangerTop5 } from '../type.ts';
import { useRouter } from 'vue-router';
import { useStore } from '@/store/index';
const userInfo = useStore()?.userInfo;
const router = useRouter();
const tableData = ref<any[]>([]);
const startDate = ref('');
const endDate = ref('');
const tab = ref('');

function setData(
  data: IHiddenDangerTop5[],
  starTime: string,
  endTime: string,
  dateType: string
) {
  tableData.value = data;
  startDate.value = starTime;
  endDate.value = endTime;
  tab.value = dateType;
}

function more() {
  router.push({
    path: 'hazard-management',
    query: {
      starTime: startDate.value,
      endTime: endDate.value,
      tab: tab.value,
      unitId: userInfo.unitId,
    },
  });
}

defineExpose({
  setData,
});

defineOptions({ name: 'DetailsTop5' });
</script>

<style scoped lang="scss">
.top5bj {
  background-image: url('../assets/top5-bj.png');
  background-repeat: no-repeat; /* 不平铺 */
  background-position: top right; /* 定位在右上角 */
}

.more {
  cursor: pointer;
  font-weight: 400;
  font-size: 14px;
  color: #0080ff;
  line-height: 20px;
  text-align: right;
  font-style: normal;
  text-transform: none;
}
</style>
