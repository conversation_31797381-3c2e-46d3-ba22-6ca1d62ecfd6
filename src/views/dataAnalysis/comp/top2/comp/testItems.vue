<template>
  <div class="com-g-row-a1 h-full top10bj">
    <com-title-b title="高频隐患检查项目top10">
      <template #right>
        <div @click="more" class="more">更多>></div>
      </template>
    </com-title-b>
    <bar-chart
      v-if="echartsData.label.length > 0"
      :extra="extra"
      :provideNumber="3"
      :echartsData="echartsData"
      :graphicColor="graphicColor"
      :levelIndex="3"
      :barBorderRadius="[20, 20, 20, 20]"
      :levelColor="graphicColor"
    ></bar-chart>
    <div v-else class="relative">
      <n-empty size="large" class="mt-[20%]"></n-empty>
    </div>
  </div>
</template>

<script setup lang="ts">
import ComTitleB from '@/components/HeadTitle/comTitleB.vue';
import BarChart from '@/components/charts/barChartNew.vue';
import { ref } from 'vue';
import { IHiddenDangerTop10 } from '@/views/dataAnalysis/comp/top2/type.ts';
import { useRouter } from 'vue-router';
import { useStore } from '@/store/index';
const userInfo = useStore()?.userInfo;
const startDate = ref('');
const endDate = ref('');
const tab = ref('');
const router = useRouter();
const echartsData = ref<any>({
  label: ['重大隐患', '较大隐患', '一般隐患', '轻微隐患'],
  data: [
    {
      name: '',
      value: [5, 22, 29, 16],
    },
  ],
});

const extra = ref({
  barGap: '50px',
  barWidth: '18px',
  series: {
    dataZoom: [
      {
        type: 'inside', // 内置型数据区域缩放组件
        show: false, // 显示 组件
      },
    ],
  },
});

const graphicColor = [
  ['#58ACFF', '#0072E4'],
  ['rgba(2, 191, 127, 1)', 'rgba(28, 229, 152, 1)'],
  ['#FFC44D', '#DC8B00'],
  ['#FF9C6A', '#E74D00'],
  ['#FF7272', '#ED0D0D'],
];

function setData(
  data: IHiddenDangerTop10,
  starTime: string,
  endTime: string,
  dateType: string
) {
  echartsData.value.label = data.dataX;
  echartsData.value.data[0].value = data.dataY;
  startDate.value = starTime;
  endDate.value = endTime;
  tab.value = dateType;
}

function more() {
  router.push({
    path: 'hazard-management',
    query: {
      starTime: startDate.value,
      endTime: endDate.value,
      tab: tab.value,
      unitId: userInfo.unitId,
    },
  });
}

defineExpose({
  setData,
});

defineOptions({ name: 'TestItems' });
</script>

<style scoped lang="scss">
.top10bj {
  background-image: url('../assets/top10-bj.png');
  background-repeat: no-repeat; /* 不平铺 */
  background-position: top right; /* 定位在右上角 */
}

.more {
  cursor: pointer;
  font-weight: 400;
  font-size: 14px;
  color: #0080ff;
  line-height: 20px;
  text-align: right;
  font-style: normal;
  text-transform: none;
}
</style>
