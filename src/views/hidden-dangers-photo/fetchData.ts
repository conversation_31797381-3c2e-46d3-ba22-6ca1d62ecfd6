import {
  ICheckTempPageRes,
  ICheckItem,
  ICheckCategoryTree,
  IFormData,
  Item,
  InspectionResponse,
  FromData,
  TreeData,
} from './type';
import { IObj } from '@/types';
import { PageList, statisticsData, TreeRes, PaginatedData } from './type';
import { api } from '@/api';
import { $http } from '@tanzerfe/http';

// 随手拍列表页
export function getDataList(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.name.demoRequest.getDataList);
  return $http.post<PageList>(url, { data: { _cfg: { showTip: true }, ...data } });
}

// 随手拍统计
export function getStatisticsData(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.name.demoRequest.getStatisticsData);
  return $http.post<statisticsData>(url, { data: { _cfg: { showTip: true }, ...query } });
}

// 随手拍详情
export function getDangerDetail(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.name.demoRequest.getDangerDetail, query);
  return $http.post<statisticsData>(url, { data: { _cfg: { showTip: true } } });
}

// 隐患等级筛选列表
export function getDangerGrade(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.name.demoRequest.getDangerGrade);
  return $http.post(url, { data: { _cfg: { showTip: true }, ...data } });
}

// 隐患分类库左
export function getDangerLibrary(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.name.demoRequest.getDangerLibrary);
  return $http.post<TreeRes>(url, { data: { _cfg: { showTip: true }, ...data } });
}

// 隐患分类库右
export function getDangerCheckList(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.name.demoRequest.getDangerCheckList);
  return $http.post<InspectionResponse>(url, { data: { _cfg: { showTip: true }, ...data } });
}

// 隐患分类库右 2
export function getDangerCheckListOther(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.name.demoRequest.getDangerCheckListOther);
  return $http.post<InspectionResponse>(url, { data: { _cfg: { showTip: true }, ...data } });
}

// 根据隐患库id获取隐患库常见隐患
export function getClassItemAndCommonByFactorId(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.name.demoRequest.getClassItemAndCommonByFactorId);
  return $http.post<InspectionResponse>(url, { data: { _cfg: { showTip: true }, ...data } });
}

// 隐患检查列表项详情
export function getListPageDetail(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.name.demoRequest.getListPageDetail, query);
  return $http.post<statisticsData>(url, { data: { _cfg: { showTip: true } } });
}

export function getListPageDetailFile(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.name.demoRequest.getListPageDetailFile, query);
  return $http.post<statisticsData>(url, { data: { _cfg: { showTip: true } } });
}

// 随后拍-审核
export function checkDangerConfirm(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.name.demoRequest.checkDangerConfirm);
  return $http.post<TreeRes>(url, { data: { _cfg: { showTip: true }, ...data } });
}

// 获取人员列表
export function getUsersList(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.name.demoRequest.getUsersList);
  return $http.post<TreeRes>(url, { data: { _cfg: { showTip: true }, ...data } });
}
// 获取结构列表
export function getOrgList(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.name.demoRequest.getOrgList);
  return $http.post<TreeData>(url, { data: { _cfg: { showTip: true }, ...data } });
}

export function getListPage(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.name.demoRequest.getListPage);
  return $http.post<PageList>(url, { data: { _cfg: { showTip: true }, ...data } });
}

// 删除模板
export function deleteTemp(query: IObj<any>) {
  const url = api.getUrl(api.type.demo, api.name.demo.tempDelete, query);
  return $http.get(url, { data: { _cfg: { showTip: true, showOkTip: true } } });
}

// 获取检查类别树
export function queryCategoryTree(query?: IObj<any>) {
  const url = api.getUrl(api.type.demo, api.name.demo.queryCategoryTree, query);
  return $http.get<ICheckCategoryTree[]>(url, { data: { _cfg: { showTip: true } } });
}

// 获取检查模板项
export function queryInspectTemplateItems(query: IObj<any>) {
  const url = api.getUrl(api.type.demo, api.name.demo.queryInspectItems, query);
  return $http.get<ICheckItem[]>(url, { data: { _cfg: { showTip: true } } });
}

// 创建检查模板
export function saveTemplate(data: IObj<any>) {
  const url = api.getUrl(api.type.demo, api.name.demo.saveTemplate, data);
  return $http.post<IFormData>(url, { data: { _cfg: { showTip: true, showOkTip: true }, ...data } });
}

// 模板详情
export function getTemplateDetail(query: IObj<any>) {
  const url = api.getUrl(api.type.demo, api.name.demo.queryDetailTemplate, query);
  return $http.get<IFormData>(url, { data: { _cfg: { showTip: true } } });
}

// 模板详情已选类别树
export function detailCategoryTree(query: IObj<any>) {
  const url = api.getUrl(api.type.demo, api.name.demo.detailCategoryTree, query);
  return $http.get<ICheckCategoryTree[]>(url, { data: { _cfg: { showTip: true } } });
}

// 模板详情——检查项
export function queryTemplateDetailItems(query: IObj<any>) {
  const url = api.getUrl(api.type.demo, api.name.demo.queryTemplateDetailItems, query);
  return $http.get<ICheckItem[]>(url, { data: { _cfg: { showTip: true } } });
}

// 编辑 模板详情
export function queryUpdateTemplateDetail(query: IObj<any>) {
  const url = api.getUrl(api.type.demo, api.name.demo.queryUpdateTemplateDetail, query);
  return $http.get<IFormData>(url, { data: { _cfg: { showTip: true } } });
}

// 更新检查模板
export function updateTemplate(data: IObj<any>) {
  const url = api.getUrl(api.type.demo, api.name.demo.updateTemplate, data);
  return $http.post<IFormData>(url, { data: { _cfg: { showTip: true, showOkTip: true }, ...data } });
}
