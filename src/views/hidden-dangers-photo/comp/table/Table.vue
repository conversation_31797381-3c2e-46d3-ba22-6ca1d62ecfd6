<template>
  <n-data-table
    class="com-table"
    style="height: 100%; width: 100%"
    remote
    striped
    :columns="columns"
    :data="tableData"
    :bordered="false"
    :flex-height="true"
    :loading="loading"
    :pagination="pagination"
    nzShowCustomEmpty="--"
    :scroll-x="1500"
  />
</template>

<script setup lang="ts">
import { h, ref, VNode } from 'vue';
import { useStore } from '@/store';
import { DataTableColumns, NButton } from 'naive-ui';
import { cols } from '@/views/hidden-dangers-photo/comp/table/columns';
import { ACTION } from '@/views/hidden-dangers-photo/constant';
import { getDataList } from '@/views/hidden-dangers-photo/fetchData';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
const emits = defineEmits(['action']);
const [loading, search] = useAutoLoading(true);
const { pagination, updateTotal } = useNaivePagination(getTableData);

const columns = ref<DataTableColumns>([]);
const tableData = ref<any[]>([]);
function setColumns() {
  columns.value.push(...cols);
  // 添加操作栏 action
  columns.value.push({
    title: '操作',
    key: 'actions',
    align: 'left',
    fixed: 'right',
    width: 160,
    render(row) {
      return getActionBtn(row);
    },
  });
}
setColumns();

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          size: 'small',
          type: 'primary',
          ghost: true,
          onClick: () => getDetailData(row),
        },
        { default: () => '详情' }
      ),
    ],
    [
      h(
        NButton,
        {
          size: 'small',
          type: 'primary',
          ghost: true,
          disabled: row.approveStateName === '已审核',
          onClick: () => checkedOrder(row),
        },
        { default: () => '审核' }
      ),
    ],
  ];
  return useActionDivider(acList);
}

let filterData = {}; // 搜索条件
function getTableData() {
  const params = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    ...filterData,
  };

  search(getDataList(params)).then((res) => {
    tableData.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
  loading.value = false;
}

function getTableDataWrap(data) {
  filterData = data;
  pagination.page = 1;
  getTableData();
}

const getDetailData = (row: any) => {
  emits('action', { action: ACTION.DETAIL, data: row });
};

const checkedOrder = (row: any) => {
  emits('action', { action: ACTION.CHECKED, data: row });
};

defineOptions({ name: 'checkTampTableComp' });
defineExpose({ getTableDataWrap, getTableData, pagination });
</script>
<style module lang="scss"></style>
