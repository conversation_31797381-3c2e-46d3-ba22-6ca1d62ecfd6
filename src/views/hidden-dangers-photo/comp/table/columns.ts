import { approveState } from '@/components/table-col/approveState';
import { confirmState } from '@/components/table-col/confirmState';
import { DataTableColumn, NTag, NButton } from 'naive-ui';
import { h } from 'vue';
export const cols: DataTableColumn[] = [
  {
    title: '上报人员',
    key: 'createByName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '隐患单位',
    key: 'unitName',
    align: 'left',
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '隐患位置',
    key: 'hazarePlace',
    resizable: true,
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
    render: (row: any) => {
      return row.hazarePlace ? row.hazarePlace : '--';
    },
  },
  {
    title: '隐患描述',
    key: 'hazardDescribe',
    resizable: true,
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '隐患分类',
    key: 'hazardCategoryName',
    resizable: true,
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
    render: (row: any) => {
      return row.hazardCategoryName ? row.hazardCategoryName : '--';
    },
  },
  {
    title: '隐患等级',
    key: 'gradeName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
    render: (row: any) => {
      return row.gradeName ? row.gradeName : '--';
    },
  },
  {
    title: '上报时间',
    key: 'createTime',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '审核状态',
    key: 'approveStateName',
    align: 'left',
    render: (row) => h(approveState, { row }),
  },
  {
    title: '隐患确认',
    key: 'confirmStateName',
    align: 'left',
    render: (row) => h(confirmState, { row }),
  },
  {
    title: '审核人员',
    key: 'approveUserName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
    render: (row: any) => {
      return row.approveUserName ? row.approveUserName : '--';
    },
  },
];
