<template>
  <n-form label-placement="left" label-align="left" :model="detailData">
    <el-row :gutter="20">
      <el-col :span="24">
        <ComHeaderB title="随手拍信息" :hasIcon="false" class="my-5" />
      </el-col>
      <el-col :span="8">
        <n-form-item label="上报人员:">
          <span class="n-form-item-span"> {{ detailData.createByName }}</span>
        </n-form-item>
      </el-col>
      <el-col :span="8">
        <n-form-item label="上报人员组织机构:">
          <span class="n-form-item-span">
            {{ detailData.createUserDepartmentName ? detailData.createUserDepartmentName : '--' }}
          </span>
        </n-form-item>
      </el-col>
      <el-col :span="8">
        <n-form-item label="隐患单位:">
          <span class="n-form-item-span">
            {{ detailData.unitName || '--' }}
          </span>
        </n-form-item>
      </el-col>
      <el-col :span="8">
        <n-form-item label="隐患分类:">
          <span class="n-form-item-span"> {{ detailData.hazardCategoryName || '--' }} </span>
        </n-form-item>
      </el-col>
      <el-col :span="8">
        <n-form-item label="隐患描述:">
          <span class="n-form-item-span"> {{ detailData.hazardDescribe || '--' }}</span>
        </n-form-item>
      </el-col>
      <el-col :span="8">
        <n-form-item label="上报时间:"
          ><span class="n-form-item-span">{{ detailData.createTime || '--' }}</span>
        </n-form-item>
      </el-col>
      <el-col :span="8">
        <n-form-item label="楼栋:"
          ><span class="n-form-item-span">{{ detailData.buildingName || '--' }} </span>
        </n-form-item>
      </el-col>
      <el-col :span="8">
        <n-form-item label="楼层:"
          ><span class="n-form-item-span">{{ detailData.floorName || '--' }}</span>
        </n-form-item>
      </el-col>
      <el-col :span="24">
        <n-form-item label="隐患图片:" class="flex justify-start items-center">
          <n-image
            class="w-[100px] rounded-[4px] mx-[10px]"
            v-for="item in detailData.hazardEventAttachmentPoList"
            :key="item.attachPath"
            :src="getFileURL(item.attachPath, true)"
            :preview-src="getFileURL(item.attachPath)"
            object-fit="cover"
          />
        </n-form-item>
      </el-col>
      <el-col :span="8">
        <n-form-item label="隐患位置:">
          <div class="flex justify-between items-center w-full">
            <el-tooltip class="box-item" effect="dark" :content="detailData.hazardPlace" placement="top-start">
              <div class="ellipsis n-form-item-span">{{ detailData.hazardPlace || '--' }}</div>
            </el-tooltip>
            <el-button
              v-if="detailData.approveStateName == '已审核'"
              type="primary"
              plain
              size="small"
              @click="showImage = true"
              >查看位置</el-button
            >
          </div>
        </n-form-item>
      </el-col>
      <el-col :span="24">
        <ComHeaderB title="审核信息" :hasIcon="false" class="my-5" />
      </el-col>
      <el-col :span="8">
        <n-form-item label="审核状态:">
          <span class="n-form-item-span">{{ detailData.approveStateName || '--' }}</span>
        </n-form-item>
      </el-col>
      <el-col :span="8">
        <n-form-item label="隐患确认:">
          <span class="n-form-item-span"> {{ detailData.confirmStateName || '--' }}</span>
        </n-form-item>
      </el-col>
      <el-col :span="8">
        <n-form-item label="审核人员:"
          ><span class="n-form-item-span"> {{ detailData.approveUserName || '--' }}</span>
        </n-form-item>
      </el-col>
      <el-col :span="8">
        <n-form-item label="审核时间:"
          ><span class="n-form-item-span">{{ detailData.approveTime || '--' }} </span>
        </n-form-item>
      </el-col>
      <template v-if="detailData.confirmStateName != '已拒绝'">
        <el-col :span="24">
          <ComHeaderB title="隐患信息" :hasIcon="false" class="my-5" />
        </el-col>
        <el-col :span="8">
          <n-form-item label="隐患整改人员:">
            <span v-if="detailData.hazardRandomCheckEventUsers?.length">
              <span class="mr-1 n-form-item-span" v-for="item in detailData.hazardRandomCheckEventUsers" :key="item.id">
                {{ item.reformUserName || '--' }}
              </span>
            </span>
            <!-- <span v-else>--</span> -->
          </n-form-item>
        </el-col>
        <el-col :span="8">
          <n-form-item label="隐患位置:">
            <div class="flex justify-between items-center w-full">
              <el-tooltip class="box-item" effect="dark" :content="detailData.hazardPlace" placement="top-start">
                <div class="ellipsis n-form-item-span">
                  {{ detailData.hazardPlace || '--' }}
                </div>
              </el-tooltip>
              <el-button
                v-if="detailData.approveStateName == '已审核'"
                type="primary"
                plain
                size="small"
                @click="showImage = true"
                >查看位置</el-button
              >
            </div>
          </n-form-item>
        </el-col>
        <el-col :span="8">
          <n-form-item label="隐患等级:">
            <span class="n-form-item-span">{{ detailData.gradeName || '--' }}</span>
          </n-form-item>
        </el-col>
      </template>
      <template v-if="detailData.confirmStateName != '已拒绝'">
        <el-col :span="24">
          <n-form-item label="法规依据:">
            <span class="n-form-item-span">{{ detailData.inspectionItemBasis || '--' }}</span>
          </n-form-item>
        </el-col>
        <el-col :span="24">
          <n-form-item label="合规要求:">
            <span class="n-form-item-span">{{ detailData.inspectionAsk || '--' }} </span>
          </n-form-item>
        </el-col>
        <el-col :span="24">
          <n-form-item label="法律原文:">
            <span class="n-form-item-span">{{ detailData.legalText || '--' }} </span>
          </n-form-item>
        </el-col>
        <el-col :span="24">
          <n-form-item label="法律责任:">
            <span class="n-form-item-span">{{ detailData.legalLiability || '--' }}</span>
          </n-form-item>
        </el-col>
        <!-- <el-col :span="8">
        <n-form-item label="法律责任:"> {{ detailData.legalLiability || '--' }} </n-form-item>
      </el-col> -->
        <el-col :span="24">
          <n-form-item label="图例:" class="flex justify-start items-center">
            <n-image
              v-for="item in detailData.hazardEssentialFactorClassItemFiles"
              :key="item.id"
              style="height: 40px; width: 40px"
              :src="getFileURL(item.fileUrl)"
            />
            <span v-if="!detailData?.hazardEssentialFactorClassItemFiles?.length">--</span>
          </n-form-item>
        </el-col>
      </template>
      <template v-else>
        <el-col :span="24">
          <n-form-item label="说明:">
            <span class="n-form-item-span">{{ detailData.remark || '--' }} </span>
          </n-form-item>
        </el-col>
      </template>
    </el-row>
  </n-form>

  <n-modal
    class="models"
    v-model:show="showImage"
    preset="card"
    :show-icon="false"
    style="width: 900px"
    @negative-click="showImage = false"
  >
    <template #header>
      <div class="flex flex-row items-center">
        <img class="w-[17px] h-[12px] mr-[20px]" src="@/components/header/assets/icon-title-arrow3.png" />
        <div class="text-[16px] text-[#222222] font-bold">隐患位置</div>
      </div>
    </template>
    <!-- <div class="h-[500px] pt-[100px]" v-if="!floorInfo.floorAreaImg">
      <n-empty description="暂无楼层图"></n-empty>
    </div> -->
    <mapFloor
      :floorInfo="floorInfo"
      :isAddMark="true"
      :isShowBtn="false"
      @cancel="cancel"
      :pointer="pointer"
      style="height: 500px"
    ></mapFloor>
  </n-modal>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import mapFloor from '@/components/indoorMap/index.vue';
import ComHeaderB from '@/components/header/ComHeaderB.vue';
import { ACTION } from '@/views/hidden-dangers-photo/constant';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { getDangerDetail } from '@/views/hidden-dangers-photo/fetchData';
import { DetailData } from '@/views/hidden-dangers-photo/type';
import getFileURL from '@/utils/getFileURL';
import { queryFloorAreaImage } from '../../../hazard-management/fetchData.ts';
const emits = defineEmits(['action']);
const [loading, search] = useAutoLoading(true);
const checkMsg = ref(true);
const showModal = ref(false);
const checkedValue = ref<string | null>(null);
const showImage = ref(false);
const props = defineProps({
  orderId: String,
});
const pointer = ref({
  x: 0,
  y: 0,
});
const floorInfo = ref<any>({
  unitId: '',
  buildingId: '',
  floorId: '',
  floorAreaImg: '',
});
const detailData = ref<DetailData>({});
const checkSecurity = () => {
  emits('action', { action: ACTION.CHECKED });
};

const checkForm = ref(initForm());
function initForm() {
  return { templateName: null };
}
function getDetailData() {
  const params = {
    id: props.orderId,
  };
  search(getDangerDetail(params)).then(async (res) => {
    detailData.value = res.data;
    floorInfo.value.unitId = String(res.data.unitId);
    floorInfo.value.floorId = String(res.data.floorId);
    floorInfo.value.buildingId = String(res.data.buildingId);
    floorInfo.value.floorAreaImg = String(res.data.floorAreaImg);
    pointer.value.x = Number(res.data.mapX);
    pointer.value.y = Number(res.data.mapY);
    // 获取楼层图 通过这个判断是否有 gis 数据，因为外运长航的环境这个接口跨域问题，暂时屏蔽
    // const foolImg = await queryFloorAreaImage(floorInfo.value.floorId);
    // floorInfo.value.floorAreaImg = foolImg.data ? foolImg.data : '';
  });
  loading.value = false;
}

getDetailData();

const open = ({ type }: any) => {
  showModal.value = true;
};
const cancel = () => {
  showImage.value = false;
};
function changeCheck() {
  checkMsg.value = false;
}

const handleClose = () => {
  // message.info('tag close')
};
function handleChange(e: Event) {
  checkedValue.value = (e.target as HTMLInputElement).value;
}
defineOptions({ name: 'detailPage' });
defineExpose({ changeCheck, getDetailData });
</script>

<style scoped lang="scss">
.n-form-item-span {
  font-size: 0.875rem;
}

.ellipsis {
  white-space: nowrap;
  /* 确保文本在一行内显示 */
  overflow: hidden;
  /* 隐藏溢出的内容 */
  text-overflow: ellipsis;
  /* 使用省略号表示文本溢出 */
  width: 200px;
  /* 设置容器宽度 */
}
</style>
