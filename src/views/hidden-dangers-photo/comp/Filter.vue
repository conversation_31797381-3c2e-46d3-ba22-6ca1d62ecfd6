<template>
  <n-form :show-feedback="false" label-placement="left" label-width="90px">
    <!-- <n-form-item label="隐患单位:" style="flex: 13%">
          <n-space vertical style="width: 100%">
            <n-select clearable v-model:value="filterForm.checkUnitId" label-field="unitName" value-field="id"
              placeholder="请选择" :options="dataList" />
          </n-space>
        </n-form-item> -->

    <!-- <n-form-item label="隐患类别:" style="flex: 1 1 30%">
          <n-space vertical style="width: 100%">
            <n-input
              placeholder="请输入隐患类别模糊搜索"
              v-model:value="filterForm.essentialFactorClassId"
              clearable
            ></n-input>
            <n-select v-model:value="filterForm.essentialFactorClassId" :options="options" />
          </n-space>
        </n-form-item> -->
    <n-grid :x-gap="12" :y-gap="8" :cols="3">
      <n-gi>
        <n-form-item label="隐患等级:">
          <n-select
            v-model:value="filterForm.hazardGradeId"
            :render-label="renderLabel"
            class="w-full flex items-center"
            label-field="gradeName"
            value-field="id"
            clearable
            placeholder="请选择"
            :options="optionsGrade"
          />
        </n-form-item>
      </n-gi>
      <n-gi>
        <n-form-item>
          <n-input
            placeholder="请输入隐患分类/隐患等级/上报人员模糊搜索"
            class="w-full flex items-center"
            v-model:value="filterForm.likeParam"
            clearable
          >
            <template #suffix>
              <BsSearch />
            </template>
          </n-input>
        </n-form-item>
      </n-gi>
    </n-grid>
  </n-form>
</template>

<script setup lang="ts">
import { ref, watch, h } from 'vue';
import { useStore } from '@/store';
import { ACTION } from '../constant';
import { getDangerGrade, getOrgList } from '@/views/hidden-dangers-photo/fetchData';
import { trimObjNull } from '@/utils/obj.ts';
import { useRouter } from 'vue-router';
import { Item } from '@/views/hidden-dangers-photo/type';
import { BsSearch } from '@kalimahapps/vue-icons';
const router = useRouter();
const { userInfo } = useStore();
const emits = defineEmits(['action']);
const dataList = ref<any>([]);
const optionsGrade = ref<Item[]>([]); // 用于存储数据

const filterForm = ref({ hazardGradeId: null, likeParam: null, roleCodes: userInfo.roleCodes });

const renderLabel = (option: any) => {
  return h(
    'div',
    {
      title: option.gradeName,
    },
    option.gradeName.length > 12 ? option.gradeName.substring(0, 12) + '...' : option.gradeName
  );
};

const add = () => {
  router.push({ name: 'plannedManagementAdd' });
};

function doHandle(action: ACTION) {
  emits('action', {
    action: action,
    data: trimObjNull(filterForm.value),
  });
}

function getVal() {
  return filterForm.value;
}

function clearFrom() {
  filterForm.value.hazardGradeId = null;
  filterForm.value.likeParam = null;
  filterForm.value.roleCodes = userInfo.roleCodes;
  // 不要修改源数据，创建一个新的对象
  // filterForm.value = { hazardGradeId: null, likeParam: null, roleCodes: userInfo.roleCodes };
}

watch(
  filterForm.value,
  () => {
    doHandle(ACTION.SEARCH);
  },
  {
    deep: true,
  }
);

function getDangerGradeList() {
  let params = {
    delFlag: 0,
    unitId: userInfo.topUnitId,
  };
  getDangerGrade(params).then((res: unknown) => {
    const data = res as { code: string; message: string; data: Item[] };
    optionsGrade.value = data.data; // 使用类型断言
  });
}
getDangerGradeList();
// const getOrg = async () => {
//   let params = {
//     keyWord: '',
//     pageNo: '',
//     pageSize: -1,
//     orgCode: userInfo.unitId,
//     type: '',
//   };
//   const res: any = await getOrgList(params);
//   dataList.value = res.data.rows || [];
// };
// getOrg();
// onMounted(() => {
//   doHandle(ACTION.SEARCH);
// });

defineOptions({ name: 'checkTempFilterComp' });
defineExpose({ getVal, clearFrom });
</script>
<style module lang="scss">
.form-content {
  // @apply w-full ;
  display: grid;
  grid-template-columns: repeat(auto-fill, 300px);
  grid-row-gap: 10px;
  grid-column-gap: 10px;
}
</style>
