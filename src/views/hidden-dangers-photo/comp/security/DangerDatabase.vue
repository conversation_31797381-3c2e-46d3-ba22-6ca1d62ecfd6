<template>
  <div class="flex justify-between">
    <div class="flex justify-between items-center">
      <span style="margin-right: 15px">编制时间:</span>
      <n-date-picker
        style="width: 280px"
        v-model:value="range"
        type="daterange"
        clearable
        @update:value="confirmRange"
      />
    </div>
    <n-input style="width: 315px" placeholder="请输隐患库名称模糊搜索" v-model:value="filterForm.elementName" clearable>
      <template #suffix>
        <n-icon size="20">
          <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24">
            <path
              d="M15.5 14h-.79l-.28-.27A6.471 6.471 0 0 0 16 9.5A6.5 6.5 0 1 0 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5S14 7.01 14 9.5S11.99 14 9.5 14z"
              fill="currentColor"
            ></path>
          </svg>
        </n-icon>
      </template>
    </n-input>
  </div>
  <div class="mt-5">
    <Table ref="tableDanger" @chooseBase="chooseBase"></Table>
  </div>

  <n-modal
    class="models"
    :show-icon="false"
    v-model:show="showModalSecurity"
    preset="card"
    style="width: 1070px"
    @negative-click="showModalSecurity = false"
  >
    <template #header>
      <div class="flex flex-row items-center">
        <img class="w-[17px] h-[12px] mr-[20px]" src="@/components/header/assets/icon-title-arrow3.png" />
        <div class="text-[16px] text-[#222222] font-bold">隐患选择</div>
      </div>
    </template>
    <Security
      ref="checkCompRef"
      @closeFactory="closeFactory"
      @getDesc="getDesc"
      :isChoose="true"
      @sendChooseId="sendChooseId"
    ></Security>
  </n-modal>
</template>

<script setup lang="ts">
type TableInstance = InstanceType<typeof Table>;
import { ref, defineEmits, watch } from 'vue';
import Table from '../security/table/Table.vue';
import Security from '@/views/hidden-dangers-photo/comp/security/Security.vue';
import arrow3Icon from '@/components/header/assets/icon-title-arrow3.png';

const emits = defineEmits(['filterData', 'getParentId']);
// const tableDanger = ref(null);
const checkCompRef = ref(null);
// 获取当前日期
const currentDate = new Date();

// 获取当前月的第一天
const firstDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);

// 获取当前月的最后一天
const lastDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);

// 获取时间戳
const firstDayTimestamp = firstDayOfMonth.setHours(0, 0, 0, 0); // 设置为当天的开始
const lastDayTimestamp = lastDayOfMonth.setHours(23, 59, 59, 999); // 设置为当天的结束

console.log('本月第一天的时间戳:', firstDayTimestamp);
console.log('本月最后一天的时间戳:', lastDayTimestamp);
// const range = ref([firstDayTimestamp, lastDayTimestamp]);
const range = ref(null);
const showModalSecurity = ref(false);
const tableDanger = ref<TableInstance | null>(null); // 定义类型
const filterForm = ref(initForm());
function initForm() {
  return { startTime: null as string | null, endTime: null as string | null, elementName: null };
}
// 格式化为 YYYY-MM-DD
const formatDate = (timestamp: number) => {
  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

function confirmRange() {
  if (!range.value) {
    filterForm.value.startTime = null;
    filterForm.value.endTime = null;
  } else {
    filterForm.value.startTime = formatDate(range.value[0]);
    filterForm.value.endTime = formatDate(range.value[1]);
  }
}
watch(
  filterForm.value,
  () => {
    tableDanger.value?.getTableDataWrap(filterForm.value);
  },
  { deep: true }
);
function chooseBase(data: any) {
  showModalSecurity.value = true;
}

function sendChooseId(data: any) {
  showModalSecurity.value = false;
  console.log(emits);

  emits('getParentId', data);
}

function closeFactory() {
  showModalSecurity.value = false;
}
function getDesc(data: any) {}
confirmRange();
</script>

<style module lang="scss"></style>
