<template>
  <div>
    <n-descriptions label-placement="left" :column="1">
      <n-descriptions-item label="隐患描述"> {{ detailData.hazardDescribe }} </n-descriptions-item>
      <n-descriptions-item label="隐患分类"> {{ detailData.inspectionItem }} </n-descriptions-item>
      <n-descriptions-item label="隐患等级"> {{ detailData.hazardGradeName }} </n-descriptions-item>
      <n-descriptions-item label="检查内容"> {{ detailData.inspectionAsk }} </n-descriptions-item>
      <n-descriptions-item label="检查详情"> {{ detailData.legalLiability }} </n-descriptions-item>
      <n-descriptions-item label="法规依据"> {{ detailData.inspectionItemBasis }} </n-descriptions-item>
      <n-descriptions-item label="合规要求"> {{ detailData.inspectionAsk }} </n-descriptions-item>
      <n-descriptions-item label="法律原文"> {{ detailData.legalText }} </n-descriptions-item>
      <n-descriptions-item label="法律责任"> {{ detailData.legalLiability }} </n-descriptions-item>
      <n-descriptions-item label="图例">
        <n-image-group>
          <n-space>
            <n-image
              v-for="item in detailData2"
              :key="item.id"
              style="height: 50px; width: 50px"
              :src="getFileURL(item.fileUrl)"
            />
          </n-space>
        </n-image-group>
      </n-descriptions-item>
    </n-descriptions>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { getListPageDetail, getListPageDetailFile } from '@/views/hidden-dangers-photo/fetchData';
import { DetailData } from '@/views/hidden-dangers-photo/type';
import getFileURL from '@/utils/getFileURL';

const detailData = ref<DetailData>({});
const detailData2 = ref<DetailData>({});
const [loading, search] = useAutoLoading(true);
const props = defineProps({
  order: Object,
  orderId: String,
});
function getDetailData() {
  const params = {
    classItemId: props.orderId,
  };
  search(getListPageDetail(params)).then((res) => {
    detailData.value = res.data;
  });
  loading.value = false;
}
function getFile() {
  let params = {
    classItemId: props.orderId,
  };
  getListPageDetailFile(params).then((res) => {
    detailData2.value = res.data;
  });
}
getFile();
getDetailData();
defineOptions({ name: 'dangerDetail' });
</script>

<style scoped></style>
