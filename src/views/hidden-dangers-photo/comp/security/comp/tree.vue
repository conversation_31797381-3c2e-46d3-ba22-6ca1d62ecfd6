<template>
  <div
    class="tc text-center h-34 leading-34"
    style="
      height: 40px;
      background-color: #deeaff;
      line-height: 40px;
      border: 1px solid #e6e8ed;
      border-radius: 5px 5px 0 0;
      color: #527cff;
    "
  >
    隐患分类
  </div>
  <n-tree
    block-line
    :data="treeData"
    :default-expanded-keys="defaultExpandedKeys"
    :default-selected-keys="defaultSelectedKeys"
    key-field="id"
    label-field="className"
    children-field="children"
    @update:selected-keys="handleNodeSelect"
    selectable
  />
</template>

<script setup lang="ts">
import { ref, defineEmits } from 'vue';
import { getDangerLibrary } from '@/views/hidden-dangers-photo/fetchData';
import { repeat } from 'seemly';
import type { TreeOption } from 'naive-ui';
const emit = defineEmits(['filterList']);
import { TreeNode } from '@/views/hidden-dangers-photo/type';
const defaultSelectedKeys = ref<string[]>([]);
function createData(level = 4, baseKey = ''): TreeOption[] | undefined {
  if (!level) return undefined;
  return repeat(6 - level, undefined).map((_, index) => {
    const key = `${baseKey}${level}${index}`;
    return {
      whateverLabel: createLabel(level),
      whateverKey: key,
      whateverChildren: createData(level - 1, key),
    };
  });
}
const treeData = ref<TreeNode[]>([]);
// 定义 createLabel 函数
function createLabel(level: number): string {
  if (level === 4) return '道生一';
  if (level === 3) return '一生二';
  if (level === 2) return '二生三';
  if (level === 1) return '三生万物';
  return '';
}

const data = createData();
function getLibrary() {
  let params = {
    rootFlag: 1,
    delFlag: 0,
    essentialFactorId: sessionStorage.getItem('securityId'),
  };
  getDangerLibrary(params).then((res: any) => {
    treeData.value = buildTree(res.data);
  });
}

// 构建树形结构的函数
function buildTree(data: TreeNode[]): TreeNode[] {
  const map: Record<string, TreeNode> = {};
  const tree: TreeNode[] = [];

  // 将每个节点放入 map 中，以 id 为键
  data.forEach((item) => {
    map[item.id] = { ...item, children: [] }; // 初始化 children 为一个空数组
  });

  // 根据 parentId 将节点归纳到其父节点中
  data.forEach((item) => {
    if (item.parentId === '0') {
      // 如果 parentId 为 "0"，则为根节点，直接添加到树中
      tree.push(map[item.id]);
    } else {
      // 检查父节点是否存在
      const parent = map[item.parentId];
      if (parent) {
        // 安全地推送到父节点的 children 数组
        parent.children.push(map[item.id]);
      } else {
        // 如果父节点不存在，可以选择打印一个警告
        console.warn(`父节点 ${item.parentId} 不存在，无法将 ${item.id} 添加到其子节点中`);
      }
    }
  });

  // 可选：清理多余的属性，具体实现需要根据您的需求
  tree.forEach(cleanUp);
  const firstNode = tree[0].id;
  firstClick(tree[0].id);
  defaultSelectedKeys.value = [firstNode];
  defaultExpandedKeys.value = [firstNode];
  return tree; // 返回构建好的树形结构
}

// 清理空 children 属性的递归函数
function cleanUp(node: TreeNode): void {
  if (node.children && node.children.length === 0) {
    delete node.children; // 删除空的 children 属性
  } else if (node.children) {
    node.children.forEach(cleanUp); // 递归处理子节点
  }
}

getLibrary();
function handleNodeSelect(selectedKeys: string[]) {
  // 处理点击事件
  if (selectedKeys.length === 0) return;
  sessionStorage.setItem('treeLeftId', selectedKeys[0]);
  emit('filterList', selectedKeys);
}

function firstClick(data: any) {
  emit('filterList', data);
}

defineExpose({ getLibrary });
const defaultExpandedKeys = ref<string[]>([]);
defineOptions({ name: 'securityTree' });
</script>

<style module lang="scss"></style>
