<template>
  <n-data-table
    class="h-full com-table"
    style="height: 500px"
    remote
    striped
    :columns="columns"
    :data="tableData"
    :bordered="false"
    :flex-height="true"
    :loading="loading"
    :pagination="pagination"
  />
</template>

<script setup lang="ts">
import { h, ref, VNode, toRaw, defineProps } from 'vue';
import { DataTableColumns, NButton } from 'naive-ui';
import { cols } from '@/views/hidden-dangers-photo/comp/security/table/columns';
import { ACTION } from '@/views/hidden-dangers-photo/constant';
import { useRouter } from 'vue-router';
// // 接口获取数据fetchData文件
import { getListPage } from '@/views/hidden-dangers-photo/fetchData';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { IObj } from '@/types';
import { useStore } from '@/store';
import { IForm } from '@/views/hidden-dangers-photo/type';
const emits = defineEmits(['action', 'chooseBase']);
const [loading, search] = useAutoLoading(true);
const { pagination, updateTotal } = useNaivePagination(getTableData);
const userInfo = useStore()?.userInfo;
console.log(userInfo);
const { topUnitId } = useStore().$state.userInfo;
const columns = ref<DataTableColumns>([]);
const tableData = ref<any[]>([]);

function setColumns() {
  columns.value.push(...cols);
  // 添加操作栏 action
  columns.value.push({
    title: '操作',
    key: 'actions',
    align: 'center',
    width: 120,
    render(row) {
      return getActionBtn(row);
    },
  });
}
setColumns();

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          type: 'primary',
          ghost: true,
          size: 'small',
          class: 'com-action-button',
          onClick: () => getDetailData(row),
        },
        { default: () => '选择' }
      ),
    ],
  ];
  return useActionDivider(acList);
}

let filterData: IObj<any> = {}; // 搜索条件
function getTableData() {
  const params = {
    status: 0,
    delFlag: 0,
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    ...filterData,
    unitId: topUnitId,
    valid: 1, // 隐患数据库有效，非空表
  };
  search(getListPage(params)).then((res) => {
    tableData.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
  loading.value = false;
}

function getTableDataWrap(data: any) {
  filterData = Object.assign({}, data) || {};
  pagination.page = 1;
  getTableData();
}
getTableData();

const getDetailData = (row: any) => {
  sessionStorage.setItem('securityId', row.id);
  emits('chooseBase');
};

defineOptions({ name: 'checkTampTableCompForFactory' });
defineExpose({ getTableDataWrap, getTableData });
</script>
<style module lang="scss"></style>
