<template>
  <div class="box">
    <div class="box-left">
      <n-input v-model:value="pattern" v-if="treeData.length" placeholder="请输入隐患分类" />
      <n-scrollbar class="mt-[18px]" :style="{ height: '50vh' }">
        <n-tree
          class="tree"
          :pattern="pattern"
          block-line
          default-expand-all
          :data="treeData"
          show-line
          selectable
          key-field="id"
          label-field="label"
          children-field="children"
          :show-irrelevant-nodes="false"
          :render-prefix="renderPrefix"
          :on-update:selected-keys="handleUpdateSelectedKeys"
        />
      </n-scrollbar>
    </div>
    <div class="box-right">
      <div class="mb-[14px]" style="display: flex; justify-content: flex-end">
        <n-input
          placeholder="请输入隐患描述模糊搜索"
          style="width: 300px"
          v-model:value="hazardDescribe"
          maxlength="50"
          clearable
        >
          <template #suffix>
            <BsSearch style="cursor: pointer" />
          </template>
        </n-input>
      </div>

      <!-- <n-data-table
        remote
        :columns="columns"
        :data="tableData"
        class="com-table"
        :min-height="400"
        :max-height="400"
        virtual-scroll
        :loading="loading"
        :row-key="(row: any) => row.id"
        :default-checked-row-keys="checkedRowKeys"
      /> -->

      <n-scrollbar :style="{ height: '50vh' }">
        <div v-if="tableData.length">
          <div v-for="item in tableData" :key="item.id" class="mb-[18px] table-item">
            <div class="top pl-[20px] pr-[12px]">
              <div class="font-bold leading-[40px] truncate text-[16px] text-[#666]">
                {{ item.inspectionItem }}
              </div>
              <div class="flex items-center">
                <n-button type="primary" size="small" @click="editForm(item)" style="margin-left: 10px">
                  查看详情
                </n-button>
              </div>
            </div>

            <div class="w-[100%] content" v-if="item.classItemCommonList?.length">
              <!-- 检查详情 -->
              <div class="flex justify-start items-center border-b-[1px] border-dashed border-[#979797]">
                <div class="text-[#222222] text-[16px] font-bold leading-[20px] pb-[10px]">检查内容</div>
              </div>
              <div class="mt-[11px]">
                <div class="flex flex-row pb-[8px] justify-between">
                  <div class="flex flex-row">
                    <div class="leading-[26px] text-[#222222] line-clamp-2" :title="item.inspectionDescribe">
                      {{ item.inspectionDescribe }}
                    </div>
                  </div>
                </div>
              </div>
              <!-- 常见隐患 -->
              <div class="flex justify-start items-center border-b-[1px] border-dashed border-[#979797]">
                <div class="text-[#222222] text-[16px] font-bold leading-[20px] pb-[10px]">
                  常见隐患（<span class="text-[red]">{{ item.classItemCommonList.length }}条</span>）：
                </div>
              </div>
              <div class="mt-[11px]">
                <div
                  class="flex flex-row pb-[8px] justify-between"
                  v-for="(ks, index) in item.classItemCommonList"
                  :key="index"
                >
                  <div class="flex flex-row">
                    <n-button
                      strong
                      secondary
                      type="primary"
                      class="min-w-[84px]"
                      size="small"
                      :title="ks.hazardGradeName.length > 4 ? ks.hazardGradeName : ''"
                      >{{
                        ks.hazardGradeName.length > 4 ? `${ks.hazardGradeName.slice(0, 4)}...` : ks.hazardGradeName
                      }}</n-button
                    >
                    <div class="leading-[28px] pl-3">{{ index + 1 }}、{{ ks.hazardDesc }}</div>
                  </div>
                  <div class="mx-[4px]">
                    <n-button type="primary" ghost size="small" @click="chooseCheckItem(ks, item.id)"> 选择 </n-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <n-empty
          style="display: flex; justify-content: center; padding-top: 200px; transform: translateY(-50%)"
          v-if="!tableData.length"
          description="暂无数据"
        />
      </n-scrollbar>

      <div class="flex justify-end items-end mt-[14px]">
        <div class="mr-3 whitespace-nowrap" v-if="total">共 {{ total }} 条</div>
        <n-pagination
          v-if="total"
          v-model:page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :item-count="total"
          show-size-picker
          show-quick-jumper
          :page-sizes="[10, 20, 30, 40, 50, 100]"
          :on-update:page-size="
            (pageSize: number) => {
              (pagination.pageSize = pageSize), getTableData();
            }
          "
          :on-update:page="
            (page: number) => {
              (pagination.page = page), getTableData();
            }
          "
        />
      </div>
    </div>

    <!-- 侧滑 -->
    <ComDrawerA
      :title="'隐患详情'"
      :autoFocus="false"
      :footerPaddingBottom="25"
      :maskNeedClosable="true"
      :show="modalShow"
      @handleNegative="modalShow = false"
      class="!w-[70%]"
    >
      <CardDetail
        :title="'隐患详情'"
        :watermarkData="watermarkData"
        :modalMode="'detail'"
        :treeData="treeData"
        :gradeOpt="gradeOpt"
        :rows="rowsData"
        :falseBtn="true"
        @handleNegative="modalShow = false"
      />
    </ComDrawerA>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, h } from 'vue';
import type { TreeOption } from 'naive-ui';
import { NButton } from 'naive-ui';
import { BsSearch } from '@kalimahapps/vue-icons';
import { useMessage } from 'naive-ui';
import { getDangerCheckListOther, getClassItemAndCommonByFactorId } from '@/views/hidden-dangers-photo/fetchData';
import { getTreeList, hazardEssentialFactorClassItemDetailOther } from '@/views/risk-database/risk-library/fetchData';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import childrenIco from '@/views/risk-database/risk-library/comp/detail/children.png';
import parentIco from '@/views/risk-database/risk-library/comp/detail/parent.png';
import { getGradeList } from '@/views/paramsConf/comp/fetchData';
import { useStore } from '@/store';
import CardDetail from '@/views/risk-database/risk-library/comp/detail/cardDetail.vue';
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { getWatermarkBizDataApi } from '@/utils/getFileURL';
const [loading, search] = useAutoLoading(false);

const { userInfo } = useStore();

interface Props {
  id?: null; //id
}
const props = withDefaults(defineProps<Props>(), {
  id: null,
});

// 获取水印配置
const watermarkData = ref<any>();
getWatermarkBizDataApi().then((data: any) => {
  watermarkData.value = data;
  console.log(watermarkData.value, '水印配置');
});

const emits = defineEmits(['sendChooseId', 'closeFactory']);
const treeData = ref<any[]>([]);
const total = ref();
const parentId = ref();
const form = ref<any>({
  id: null,
  hazardDescribe: null,
  essentialFactorClassId: null,
  hazardGradeId: null,
  inspectionItem: null,
  inspectionDescribe: null,
  inspectionItemBasis: null,
  inspectionAsk: null,
  legalText: null,
  legalLiability: null,
  files: [],
});
const modalShow = ref(false);
const modalTitle = ref();
const defaultIds = ref<any[]>([]);
// 隐患库ID
// 导入
let showExport = ref<boolean>(false);

const pattern = ref('');
const { pagination } = useNaivePagination(getTableData);
const tableData = ref<any[]>([]);
const childrenIds = ref<any[]>([]);
const message = useMessage();

const hazardDescribe = ref();
const gradeOpt = ref<any[]>([]);

watch([hazardDescribe], async ([newHazardDescribe]) => {
  try {
    // 当页面或每页条目数变化时调用接口
    const response = await getTableData();
    // 在这里可以处理 API 返回的数据
  } catch (error) {
    console.error('Error fetching data:', error);
  }
});

//获取隐患级别
function getGradeOpt() {
  const params = {
    pageNo: 1,
    pageSize: 9999,
    unitId: userInfo.topUnitId,
  };
  getGradeList(params).then((res: any) => {
    gradeOpt.value = res.data.rows || [];
  });
}

const rowsData = ref<any>({});
// 检查项详情
function editForm(item: any) {
  hazardEssentialFactorClassItemDetailOther({ classItemId: item.id }).then((res) => {
    form.value = res.data || null;
    if (form.value) {
      modalShow.value = true;
      rowsData.value = res.data;
    }
  });
}
function renderPrefix({ option }: { option: TreeOption }) {
  return h('img', {
    src: option.children ? parentIco : childrenIco,
    style: {
      width: '16px',
    },
  });
}

// 节点选中项发生变化时的回调函数
function handleUpdateSelectedKeys(keys: any) {
  if (keys.length == 0 || keys[0] != parentId.value) {
    childrenIds.value = [];
  }
  parentId.value = (keys.length && keys[0]) || '';
  sessionStorage.setItem('treeLeftId', keys[0]);

  // treeData是树状结构且多级 需要递归获取parentId对应的 item
  let node = findNodeById(treeData.value, parentId.value);
  getChildrenIds(node);
  console.log(parentId.value);

  console.log(childrenIds.value);
  pagination.page = 1;
  pagination.pageSize = 10;
  if (parentId.value) {
    getTableData();
  }
}

// 递归遍历树，并找到具有指定ID的节点
function findNodeById(tree: any, id: any) {
  // 遍历当前节点的所有子节点
  for (let i = 0; i < tree.length; i++) {
    // 如果找到匹配的ID，则返回该节点
    if (tree[i].id === id) {
      return tree[i];
    }
    // 如果当前节点有子节点，则递归查找
    if (tree[i].children && tree[i].children.length > 0) {
      const found: any = findNodeById(tree[i].children, id);
      if (found) {
        return found;
      }
    }
  }
  // 如果没有找到，则返回null
  return null;
}

// 获取当前id下的所有子id
function getChildrenIds(node: any) {
  // 递归获取子级id
  if (node && node.children && node.children.length) {
    node.children.forEach((item: any) => {
      childrenIds.value.push(item.id);
      item.children && item.children.length && getChildrenIds(item);
    });
  } else {
    childrenIds.value = [];
  }
}

const columns = [
  {
    title: '序号',
    key: 'index',
    width: 60,
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '隐患分类',
    key: 'essentialFactorClassFullName',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '隐患描述',
    key: 'hazardDescribe',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '隐患等级',
    key: 'hazardGradeName',
    render(row: any) {
      return h(
        NButton,
        {
          size: 'small',
          type: 'primary',
          strong: true,
          secondary: true,
          class: 'min-w-[80px]',
        },
        () => row.hazardGradeName
      );
    },
  },
  {
    title: '操作',
    key: 'actions',
    render(row: any) {
      return useActionDivider([
        [
          h(
            NButton,
            {
              size: 'small',
              type: 'info',
              ghost: true,
              onClick: () => editForm(row),
            },
            { default: () => '详情' }
          ),
        ],
        [
          h(
            NButton,
            {
              size: 'small',
              type: 'primary',
              ghost: true,
              onClick: () => chooseCheckItem(row, row.id),
            },
            { default: () => '选择' }
          ),
        ],
      ]);
    },
  },
];

const checkedRowKeys = ref([]);

function getTableData() {
  // search(
  //   getClassItemAndCommonByFactorId({
  //     factorId: props.id ? props.id : sessionStorage.getItem('securityId'), //隐患库id
  //     classId:
  //       parentId.value || childrenIds.value.length
  //         ? [parentId.value, ...childrenIds.value].join(',')
  //         : defaultIds.value.join(','), //隐患分类id
  //     likeParam: hazardDescribe.value,
  //   })
  // ).then((res: any) => {
  //   tableData.value = res.data.factorClassItemCommonList || [];
  // });

  const params = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    hazardDescribe: hazardDescribe.value,
    essentialFactorId: props.id ? props.id : sessionStorage.getItem('securityId'),
    essentialFactorClassId:
      parentId.value || childrenIds.value.length ? [parentId.value, ...childrenIds.value].join(',') : '',
  };

  getDangerCheckListOther(params).then((res: any) => {
    tableData.value = res.data.rows;
    total.value = res.data.total;
  });
}

function createData(data: any) {
  return data.map((item: any, index: any) => {
    return {
      label: item.className,
      id: item.id,
      children: item.children && item.children.length ? createData(item.children) : null,
    };
  });
}

function traverse(items: any) {
  items.forEach((item: any) => {
    defaultIds.value.push(item.id); // 将当前项的id添加到ids数组中
    if (item.children && item.children.length > 0) {
      // 如果当前项有children，则递归遍历它们
      traverse(item.children);
    }
  });
  console.log(defaultIds.value);
}

function getTreeData() {
  let params = {
    rootFlag: 1,
    parentId: '0',
    // delFlag: 0,
    essentialFactorId: props.id ? props.id : sessionStorage.getItem('securityId'),
  };
  // { essentialFactorId, parentId: '0' }
  getTreeList(params).then((res: any) => {
    res.data && res.data.length ? traverse(res.data) : [];
    treeData.value = createData(res.data);
    sessionStorage.setItem('treeLeftId', treeData.value[0].id);

    getTableData();
  });
}

function chooseCheckItem(data: any, id?: string) {
  modalShow.value = false;
  data._id = id;
  emits('sendChooseId', data);
}

getTreeData();
getGradeOpt();
defineOptions({ name: 'checklistConfComp' });

function then(arg0: (res: any) => void) {
  throw new Error('Function not implemented.');
}
</script>
<style lang="scss" scoped>
.box {
  height: 73vh;
  background: #f3f4f5;
  display: flex;
  justify-content: space-between;
  padding: 20px 24px;

  .box-left {
    min-width: 206px;
    background: #ffffff;
    border-radius: 4px 4px 4px 4px;
    padding: 12px 20px;
  }

  .box-right {
    margin-left: 20px;
    padding: 16px 20px;
    width: calc(100% - 226px);
    background: #ffffff;
    border-radius: 4px 4px 4px 4px;

    .table-item {
      .top {
        width: 100%;
        // padding: 15px 20px;
        // height: 60px;
        background: rgba(82, 124, 255, 0.1);
        border-radius: 4px 4px 0px 0px;
        display: flex;
        justify-content: space-between;
        color: #527cff;

        .title {
          width: 450px;
          //超出一行省略号
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        .mt-2 {
          width: 450px;
          font-weight: 400;
        }
      }

      .content {
        padding: 10px 20px;
        background: #f2f9ff;
        border: 1px solid #dde0e6;
      }
    }
  }
}

.tree ::v-deep .n-tree-node {
  padding: 6px 0 !important;
}

.line-clamp-2 {
  overflow: hidden;
  overflow-wrap: break-word;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
</style>
