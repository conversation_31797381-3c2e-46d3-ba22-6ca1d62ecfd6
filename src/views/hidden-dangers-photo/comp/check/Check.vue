<template>
  <n-form label-placement="left" label-align="left" :model="detailData">
    <n-grid :x-gap="8" :cols="18">
      <n-gi :span="18">
        <ComHeaderB title="随手拍信息" :hasIcon="false" class="my-5" />
      </n-gi>
      <n-gi :span="6">
        <n-form-item label="上报人员:">
          <span class="n-form-item-span">{{ detailData.createByName }}</span>
        </n-form-item>
      </n-gi>
      <n-gi :span="6">
        <n-form-item label="上报人员组织机构:">
          <span class="n-form-item-span">
            {{ detailData.createUserDepartmentName ? detailData.createUserDepartmentName : '--' }}
          </span>
        </n-form-item>
      </n-gi>
      <n-gi :span="6">
        <n-form-item label="隐患单位:">
          <span class="n-form-item-span">
            {{ detailData.unitName || '--' }}
          </span>
        </n-form-item>
      </n-gi>
      <n-gi :span="6">
        <n-form-item label="隐患分类:">
          <span class="n-form-item-span">
            {{ detailData.hazardCategoryName || '--' }}
          </span>
        </n-form-item>
      </n-gi>
      <n-gi :span="6">
        <n-form-item label="隐患描述:">
          <span class="n-form-item-span">
            {{ detailData.hazardDescribe || '--' }}
          </span>
        </n-form-item>
      </n-gi>
      <n-gi :span="6">
        <n-form-item label="上报时间:"
          ><span class="n-form-item-span">{{ detailData.createTime || '--' }}</span></n-form-item
        >
      </n-gi>
      <n-gi :span="6">
        <n-form-item label="楼栋:"
          ><span class="n-form-item-span">{{ detailData.buildingName || '--' }}</span></n-form-item
        >
      </n-gi>
      <n-gi :span="6">
        <n-form-item label="楼层:"
          ><span class="n-form-item-span">{{ detailData.floorName || '--' }}</span></n-form-item
        >
      </n-gi>
      <n-gi :span="6">
        <n-form-item label="隐患图片:">
          <n-image
            class="w-[100px] rounded-[4px] mx-[10px]"
            v-for="item in detailData.hazardEventAttachmentPoList"
            :key="item.attachPath"
            :src="getFileURL(item.attachPath, true)"
            :preview-src="getFileURL(item.attachPath)"
            object-fit="cover"
          />
        </n-form-item>
      </n-gi>
      <n-gi :span="6">
        <n-form-item label="隐患位置:">
          <div class="flex justify-between items-center w-full">
            <el-tooltip class="box-item" effect="dark" :content="detailData.hazardPlace" placement="top-start">
              <div class="ellipsis n-form-item-span">
                {{ detailData.hazardPlace || '--' }}
              </div>
            </el-tooltip>
            <el-button type="primary" plain size="small" @click="chooseLocation(1)"> 查看位置 </el-button>
          </div>
        </n-form-item>
      </n-gi>
      <n-gi :span="18">
        <ComHeaderB title="随手拍审核" :hasIcon="false" class="my-5">
          <template #right>
            <el-radio-group v-model="checkedValue" @change="radioChange">
              <el-radio
                :value="item.value"
                v-for="(item, index) in radios"
                :key="index"
                :label="item.label"
                class="ml-8"
              />
            </el-radio-group>
            <div class="w-1/4 p-4 underline cursor-pointer" style="color: #0000aad8">
              <span v-if="checkedValue == '1'" @click="open">隐患库查询</span>
            </div>
            <!-- </div> -->
          </template>
        </ComHeaderB>
      </n-gi>
    </n-grid>
  </n-form>
  <n-form
    label-placement="left"
    label-width="110px"
    label-align="right"
    ref="formRef"
    :model="checkForm"
    :rules="rules"
  >
    <n-grid :x-gap="8" :cols="18">
      <template v-if="checkedValue == '1'">
        <n-gi :span="18">
          <n-form-item label="隐患描述:">
            <n-input
              placeholder="此处填写隐患相关描述的内容"
              maxlength="500"
              type="textarea"
              show-count
              v-model:value="checkForm.hazardDescribe"
              clearable
            />
          </n-form-item>
        </n-gi>
        <n-gi :span="9">
          <n-form-item label="隐患分类:" path="essentialFactorClassId">
            <n-cascader
              class="essentialFactor-cascader flex items-center"
              :virtual-scroll="true"
              :ellipsis-tag-popover-props="{ trigger: 'manual' }"
              v-model:value="checkForm.essentialFactorClassId"
              placeholder="请选择"
              :options="treeData"
              key-field="id"
              check-strategy="child"
              label-field="className"
              value-field="id"
              children-field="children"
            />
          </n-form-item>
        </n-gi>
        <n-gi :span="9">
          <n-form-item label="隐患等级:" label-align="right" path="hazardGradeId">
            <n-select
              v-model:value="checkForm.hazardGradeId"
              :render-label="renderLabel"
              class="flex items-center"
              label-field="gradeName"
              value-field="id"
              clearable
              :options="optionsGrade"
            />
          </n-form-item>
        </n-gi>
        <n-gi :span="9">
          <n-form-item label="隐患位置:" path="hazardPlace">
            <n-input
              :placeholder="detailData.hazardPlace"
              v-model:value="checkForm.hazardPlace"
              maxlength="50"
            ></n-input>
            <n-icon
              @click="chooseLocation(2)"
              size="20"
              color="#2d4ef0"
              class="cursor-pointer"
              style="position: relative; left: -25px"
            >
              <!-- 禁用地图按钮 -->
              <!-- <n-icon size="20" color="#9c9fa6" style="position: relative; left: -25px"> -->
              <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24">
                <path
                  d="M20 1v3h3v2h-3v3h-2V6h-3V4h3V1h2zm-8 12c1.1 0 2-.9 2-2s-.9-2-2-2s-2 .9-2 2s.9 2 2 2zm2-9.75V7h3v3h2.92c.05.39.08.79.08 1.2c0 3.32-2.67 7.25-8 11.8c-5.33-4.55-8-8.48-8-11.8C4 6.22 7.8 3 12 3c.68 0 1.35.08 2 .25z"
                  fill="currentColor"
                />
              </svg>
            </n-icon>
          </n-form-item>
        </n-gi>
        <n-gi :span="18">
          <n-form-item label="隐患整改人员:" class="relative" path="fixUser" style="width: 370px">
            <n-tag v-for="item in userList" :key="item.userId" closable @close="handleClose(item)" class="mr-1">
              {{ item.userName }}
            </n-tag>
            <n-input
              class="absolute"
              style="width: 0px; height: 0px; opacity: 0"
              placeholder="请选择隐患整改人员"
              v-model:value="checkForm.fixUser"
              clearable
            ></n-input>
            <n-icon size="30" @click="addUsers">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                <path
                  d="M368.5 240H272v-96.5c0-8.8-7.2-16-16-16s-16 7.2-16 16V240h-96.5c-8.8 0-16 7.2-16 16 0 4.4 1.8 8.4 4.7 11.3 2.9 2.9 6.9 4.7 11.3 4.7H240v96.5c0 4.4 1.8 8.4 4.7 11.3 2.9 2.9 6.9 4.7 11.3 4.7 8.8 0 16-7.2 16-16V272h96.5c8.8 0 16-7.2 16-16s-7.2-16-16-16z"
                />
              </svg>
            </n-icon>
          </n-form-item>
        </n-gi>
      </template>
      <n-gi :span="18">
        <n-form-item v-if="checkedValue == '1'" label="备注:" path="remark">
          <n-input
            maxlength="500"
            type="textarea"
            placeholder="此处填写隐患相关备注的内容"
            v-model:value="checkForm.remark"
            clearable
          />
        </n-form-item>
        <n-form-item v-else label="说明:" path="remark">
          <n-input
            maxlength="500"
            type="textarea"
            placeholder="此处填写隐患相关备注的内容"
            v-model:value="checkForm.remark"
            clearable
          />
        </n-form-item>
      </n-gi>
    </n-grid>
  </n-form>
  <div class="flex justify-end w-full mt-5 pr-5">
    <n-button type="info" @click="cancelDetail()" text-color="#fff" style="margin-right: 20px; width: 92px">
      取 消
    </n-button>
    <n-button @click="checkApply(true)" type="primary" style="width: 92px">确 定</n-button>
  </div>
  <n-modal
    class="models"
    v-model:show="showModalDatabase"
    preset="card"
    :show-icon="false"
    style="width: 1070px"
    @negative-click="showModalDatabase = false"
  >
    <template #header>
      <div class="flex flex-row items-center">
        <img class="w-[17px] h-[12px] mr-[20px]" src="@/components/header/assets/icon-title-arrow3.png" />
        <div class="text-[16px] text-[#222222] font-bold">选择隐患库</div>
      </div>
    </template>
    <div class="p-[24px]">
      <DangerDatabase @sendChooseId="sendChooseId" @getParentId="getParentId"></DangerDatabase>
    </div>
  </n-modal>
  <n-modal
    v-model:show="showModalConfirm"
    style="background-color: #fff; width: 350px; height: 240px; border: 1px solid #7c7c7c; border-radius: 10px"
  >
    <div class="h-full w-full text-center pt-6">
      <div>提示</div>
      <div class="mx-auto mt-6" style="width: 90%; height: 0.5px; background-color: #7c7c7c"></div>
      <!-- 13662 【web隐患随手拍】-随手拍审核，选择非隐患，点击确定按钮,页面提示字段与原型图不符 -->
      <div class="mt-10">确认要提交审核结果吗？</div>
      <div class="mt-10 flex w-full justify-center">
        <n-button @click="closeSubmit" style="width: 30%; border-radius: 10px; border: 1px solid #7c7c7c" strong>
          取消
        </n-button>
        <n-button
          @click="submitForm"
          color="#1e98d7"
          style="width: 30%; border-radius: 10px; margin-left: 10px"
          strong
          :loading="loadingSubmit"
          type="primary"
        >
          确认
        </n-button>
      </div>
    </div>
  </n-modal>
  <n-modal
    v-model:show="showMap"
    title="隐患位置"
    preset="dialog"
    :show-icon="false"
    style="width: 820px; background-color: #fff; height: 650px; overflow-y: auto"
  >
    <mapFloor
      :floorInfo="floorInfo"
      @addMark="addMark"
      :pointer="pointer"
      :isAddMark="true"
      :isShowBtn="showBtn"
      style="height: 500px"
      @cancel="cancel"
    ></mapFloor>
  </n-modal>
  <!-- <selectUsers :showModal="showUsers" :useArray="userList" :unitCode="detailData.unitId" @close="showUsers = false"
    @success="confirmSelect" /> -->
  <SelectUser
    title="选择整改人员"
    :unitId="detailData.unitId"
    :showSelectUserModal="showUsers"
    :userType="userTypein || 1"
    :userlist="userList"
    @close="showUsers = false"
    @success="confirmSelect"
  />
</template>

<script setup lang="ts">
import { useStore } from '@/store';
import ComHeaderB from '@/components/header/ComHeaderB.vue';
import type { FormRules, FormInst } from 'naive-ui';
import { ref, defineProps, defineEmits, onMounted, h } from 'vue';
import { ACTION } from '@/views/hidden-dangers-photo/constant';
import { getDangerLibrary, getDangerDetail } from '@/views/hidden-dangers-photo/fetchData';
import { TreeNode } from '@/views/hidden-dangers-photo/type';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { checkDangerConfirm, getDangerGrade } from '@/views/hidden-dangers-photo/fetchData';
import DangerDatabase from '../security/DangerDatabase.vue';
import { DetailData } from '@/views/hidden-dangers-photo/type';
import { useMessage } from 'naive-ui';
import mapFloor from '@/components/indoorMap/index.vue';
import SelectUser from '@/views/inspection-planning/planned-management/Add/SelectUser.vue';
import getFileURL from '@/utils/getFileURL';

const auth = useStore();
const emits = defineEmits(['action']);
const [loading, search] = useAutoLoading(true);
const treeData = ref<TreeNode[]>([]);
const showModal = ref(false);
const showModalDatabase = ref(false);
const showModalConfirm = ref(false);
const formRef = ref<FormInst | null>(null);
const message = useMessage();
const checkedValue = ref('1');
const optionsGrade = ref<any>([]);
const renderLabel = (option: any) => {
  return h(
    'div',
    {
      title: option.gradeName,
    },
    option.gradeName.length > 20 ? option.gradeName.substring(0, 20) + '...' : option.gradeName
  );
};

const radios = [
  {
    value: '1',
    label: '隐患',
  },
  {
    value: '0',
    label: '非隐患',
  },
];

const parentId = ref(null);
const detailData = ref<DetailData>({});
const showMap = ref(false);
const showUsers = ref(false);
const userList = ref<any>([]);
const loadingSubmit = ref(false);
const showBtn = ref(true);
const showMapTitle = ref('选择隐患位置');
const floorInfo = ref({
  unitId: '',
  buildingId: '',
  floorId: '',
  floorAreaImg: '',
});
const props = defineProps({
  onlyCheck: Boolean,
  listId: String,
});
const pointer = ref<any>({ x: '', y: '' }); // 回显点位
const rules: FormRules = {
  hazardDescribe: [
    {
      required: true,
      message: '请输入隐患描述',
      trigger: 'blur',
    },
  ],
  fixUser: [
    {
      required: true,
      message: '请选择隐患整改人员',
      trigger: ['input', 'blur'],
    },
  ],
  essentialFactorClassId: [
    {
      required: true,
      message: '请选择隐患分类',
      trigger: ['blur', 'change'],
    },
  ],
  hazardGradeId: [
    {
      required: true,
      message: '请选择隐患等级',
      trigger: ['blur', 'change'],
    },
  ],
  hazardPlace: [
    {
      required: true,
      message: '请选择隐患位置',
      trigger: 'change',
    },
  ],
  remark: [
    {
      required: true,
      message: '请输入相关备注的内容',
      trigger: 'change',
    },
  ],
};
function addMark(x: any) {
  // checkForm.value.hazardPlace = detailData.value.hazardPlace;
  checkForm.value.mapX = x.x;
  checkForm.value.mapY = x.y;
  pointer.value.x = x.x;
  pointer.value.y = x.y;
  showMap.value = false;
}
const cancel = () => {
  showMap.value = false;
};
const cancelDetail = () => {
  emits('action', { action: ACTION.CANCEL });
};
const checkForm = ref({
  hazardDescribe: '',
  essentialFactorClassId: null,
  hazardGradeId: null,
  hazardPlace: '',
  remark: '',
  essentialFactorClassItemId: '',
  fixUser: '',
  mapX: '',
  mapY: '',
});
function getDetailData() {
  const params = {
    id: props.listId,
  };
  search(getDangerDetail(params)).then((res: any) => {
    detailData.value = res.data;
    checkForm.value.hazardPlace = detailData.value.hazardPlace;
    floorInfo.value.unitId = String(res.data.unitId);
    floorInfo.value.floorId = String(res.data.floorId);
    floorInfo.value.buildingId = String(res.data.buildingId);
    floorInfo.value.floorAreaImg = String(res.data.floorAreaImg);
    pointer.value.x = res.data.mapX;
    pointer.value.y = res.data.mapY;
  });
  loading.value = false;
}

getDetailData();
function getLibrary() {
  let params = {
    unitId: auth.$state.userInfo.topUnitId,
    delFlag: 0,
  };
  getDangerLibrary(params).then((res: any) => {
    treeData.value = buildTree(res.data);
  });
}
function sendChooseId(id: any) {
  showModal.value = false;
  parentId.value = id;
  getLibrary();
}
getLibrary();

const open = ({ type }: any) => {
  showModalDatabase.value = true;
};

const handleClose = (val: any) => {
  let users = [];
  users = userList.value.filter((item: { id: number }) => item.id !== val.id);
  if (users.length === 0) {
    checkForm.value.fixUser = '';
  }
  userList.value = users;
  checkApply(false);
};
const addUsers = () => {
  showUsers.value = true;
};
// const successFn = (val: any, userType: any) => {
//   groupData.value.userlist = [...val];
//   groupData.value.userType = userType;
// };
const userTypein = ref(1);
const confirmSelect = (val: any, userType: any) => {
  userList.value = val;
  console.log('🚀 ~ render ~ confirmSelect:', userList.value);
  checkForm.value.fixUser = userList.value.map((item: any) => item.userName).join(',');
  userTypein.value = userType;
};
function radioChange(value: any) {
  checkForm.value = {
    hazardDescribe: '',
    essentialFactorClassId: null,
    hazardGradeId: null,
    hazardPlace: '',
    remark: '',
    essentialFactorClassItemId: '',
    fixUser: '',
    mapX: '',
    mapY: '',
  };
}
function buildTree(data: TreeNode[]): TreeNode[] {
  const map: Record<string, TreeNode> = {};
  const tree: TreeNode[] = [];

  // 将每个节点放入 map 中，以 id 为键
  data.forEach((item) => {
    map[item.id] = { ...item, children: [] }; // 初始化 children 为一个空数组
  });

  // 根据 parentId 将节点归纳到其父节点中
  data.forEach((item) => {
    if (item.parentId === '0') {
      // 如果 parentId 为 "0"，则为根节点，直接添加到树中
      tree.push(map[item.id]);
    } else {
      // 检查父节点是否存在
      const parent = map[item.parentId];
      if (parent) {
        // 安全地推送到父节点的 children 数组
        parent.children.push(map[item.id]);
      } else {
        // 如果父节点不存在，可以选择打印一个警告
        console.warn(`父节点 ${item.parentId} 不存在，无法将 ${item.id} 添加到其子节点中`);
      }
    }
  });

  // 可选：清理多余的属性，具体实现需要根据您的需求
  tree.forEach(cleanUp);

  return tree; // 返回构建好的树形结构
}
// 清理空 children 属性的递归函数
function cleanUp(node: TreeNode): void {
  if (node.children && node.children.length === 0) {
    delete node.children; // 删除空的 children 属性
  } else if (node.children) {
    node.children.forEach(cleanUp); // 递归处理子节点
  }
}
function closeSubmit() {
  showModalConfirm.value = false;
  loadingSubmit.value = false;
}
function submitForm() {
  loadingSubmit.value = true;
  let user: any = [];
  userList.value.forEach((el: any) => {
    let obj = {
      userId: el.id,
      userName: el.userName,
    };
    user.push(obj);
  });
  let params = {
    id: props.listId, // 列表id
    essentialFactorClassItemId: checkForm.value.essentialFactorClassItemId, //隐患库检查项id
    essentialFactorId: Number(checkedValue.value) == 1 ? sessionStorage.getItem('securityId') : '', //隐患库id
    approveState: 1, // 状态
    confirmState: Number(checkedValue.value), // 0 非 1 是
    hazardDescribe: checkForm.value.hazardDescribe, //隐患描述
    essentialFactorClassId: checkForm.value.essentialFactorClassId, //隐患分类
    hazardGradeId: checkForm.value.hazardGradeId, //隐患等级
    hazardPlace: checkForm.value.hazardPlace, //隐患位置
    mapX: checkForm.value.mapX,
    mapY: checkForm.value.mapY,
    hazardRandomCheckEventUsers: user, //整改人员
    userIdList: user, //整改人员
    remark: checkForm.value.remark, //备注、说明
    approveUserName: auth.$state.userInfo.userName, // 审核人
    confirmUserName: auth.$state.userInfo.userName, // 审核人
    updateByName: auth.$state.userInfo.userName, // 审核人
    approveUserId: auth.$state.userInfo.id, // 审核人id
    confirmUserId: auth.$state.userInfo.id, // 审核人id
    updateBy: auth.$state.userInfo.id, // 审核人id
  };
  // 非隐患提交审核
  if (checkedValue.value == '0') {
    params.hazardDescribe = detailData.value.hazardDescribe;
    params.hazardPlace = detailData.value.hazardPlace;
  }
  checkDangerConfirm(params)
    .then((res: any) => {
      if (res.code == 'success') {
        message.success('审核成功');
        showModalConfirm.value = false;
        emits('action', { action: ACTION.SUCCESS });
      }
    })
    .catch(() => {
      loadingSubmit.value = false;
    });
}
function getDangerGradeList() {
  let params = {
    delFlag: 0,
    unitId: auth.$state.userInfo.topUnitId,
  };
  getDangerGrade(params).then((res) => {
    optionsGrade.value = res.data;
  });
}
getDangerGradeList();
function getParentId(data: any) {
  checkForm.value.essentialFactorClassItemId = data._id;
  checkForm.value.hazardDescribe = data.hazardDesc;
  checkForm.value.hazardGradeId = data.hazardGradeId;

  checkForm.value.essentialFactorClassId = sessionStorage.getItem('treeLeftId');
  showModalDatabase.value = false;
}

function checkApply(submit: boolean) {
  console.log(detailData.value);
  formRef.value?.validate((errors: any) => {
    if (!errors && submit) {
      showModalConfirm.value = true;
    }
  });
}

function chooseLocation(id: number) {
  if (id === 1) {
    showMapTitle.value = '';
    showBtn.value = false;
  } else if (id === 2) {
    showMapTitle.value = '选择隐患位置';
    showBtn.value = true;
  }
  showMap.value = true;
}

defineOptions({ name: 'detailPage' });
// defineExpose({ changeCheck });
onMounted(() => {});
</script>

<style lang="scss" scoped>
.n-form-item-span {
  font-size: 0.875rem;
}

.ellipsis {
  white-space: nowrap;
  /* 确保文本在一行内显示 */
  overflow: hidden;
  /* 隐藏溢出的内容 */
  text-overflow: ellipsis;
  /* 使用省略号表示文本溢出 */
  width: 200px;
  /* 设置容器宽度 */
}

::v-deep .n-cascader-menu {
  .n-cascader-submenu {
    max-width: 180px !important;
  }
}
</style>
