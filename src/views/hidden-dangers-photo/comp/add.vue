<template>
  <div class="com-g-row-a1">
    <com-bread :data="breadData"></com-bread>
    <div class="bg-[#fff] rounded overflow-y-auto py-[20px]">
      <n-form
        ref="formRef"
        :model="model"
        :rules="rules"
        label-placement="left"
        label-width="200px"
        require-mark-placement="right-hanging"
        :size="size"
        :style="{
          maxWidth: '640px',
        }"
      >
        <n-form-item label="计划名称" path="inputValue">
          <n-input v-model:value="model.inputValue" placeholder="计划名称" />
        </n-form-item>
        <n-form-item label="检查类型" path="textareaValue">
          <n-input
            v-model:value="model.textareaValue"
            placeholder="检查类型"
            type="textarea"
            :autosize="{
              minRows: 3,
              maxRows: 5,
            }"
          />
        </n-form-item>
        <n-form-item label="计划起止时间" path="selectValue">
          <n-select v-model:value="model.selectValue" placeholder="Select" :options="generalOptions" />
        </n-form-item>
        <n-form-item label="检查频次" path="multipleSelectValue">
          <n-select v-model:value="model.multipleSelectValue" placeholder="Select" :options="generalOptions" multiple />
        </n-form-item>
        <n-form-item label="检查执行方式" path="datetimeValue">
          <n-date-picker v-model:value="model.datetimeValue" type="datetime" />
        </n-form-item>
        <n-form-item label="检查对象" path="switchValue">
          <n-switch v-model:value="model.switchValue" />
        </n-form-item>
        <n-form-item label="检查要求" path="checkboxGroupValue">
          <n-checkbox-group v-model:value="model.checkboxGroupValue">
            <n-space>
              <n-checkbox value="Option 1"> Option 1 </n-checkbox>
              <n-checkbox value="Option 2"> Option 2 </n-checkbox>
              <n-checkbox value="Option 3"> Option 3 </n-checkbox>
            </n-space>
          </n-checkbox-group>
        </n-form-item>
        <n-form-item label="附件" path="radioGroupValue">
          <n-radio-group v-model:value="model.radioGroupValue" name="radiogroup1">
            <n-space>
              <n-radio value="Radio 1"> Radio 1 </n-radio>
              <n-radio value="Radio 2"> Radio 2 </n-radio>
              <n-radio value="Radio 3"> Radio 3 </n-radio>
            </n-space>
          </n-radio-group>
        </n-form-item>
        <n-form-item label="检查人员" path="radioGroupValue">
          <n-data-table class="com-table" :columns="columns" :data="data" :pagination="pagination" :bordered="false" />
        </n-form-item>
        <!-- :class="$style['form-item-block']" -->
        <n-form-item label="检查结束操作" path="inputNumberValue">
          <div>
            <div class="pb-[10px]">检查任务是否只允许检查负责人结束</div>
            <div>
              <n-radio-group v-model:value="model.radioGroupValue" name="radiogroup1">
                <n-space>
                  <n-radio value="Radio 1"> 是</n-radio>
                  <n-radio value="Radio 2">否 </n-radio>
                </n-space>
              </n-radio-group>
            </div>
            <div class="pt-[10px] text-[#999]">
              选择是，只有检查负责人可以结束检查任务。<br />
              选择否，检查负责人和参与人都可以结束检查任务
            </div>
          </div>
        </n-form-item>
        <n-form-item label="是否需要检查人现场打卡" path="timePickerValue">
          <div>
            <div class="pb-[10px]">选择后需要检查人员现场拍照打卡才可以开始检查</div>
            <div>
              <n-radio-group v-model:value="model.inputNumberValue" name="radiogroup2">
                <n-space>
                  <n-radio value="Radio 1">是 </n-radio>
                  <n-radio value="Radio 2">否 </n-radio>
                </n-space>
              </n-radio-group>
            </div>
          </div>
        </n-form-item>
        <!--
      <n-form-item label="Nested Path" :show-feedback="false">
        <n-grid :cols="2" :x-gap="24">
          <n-form-item-gi path="nestedValue.path1">
            <n-input v-model:value="model.nestedValue.path1" placeholder="Nested Path 1" />
          </n-form-item-gi>
          <n-form-item-gi path="nestedValue.path2">
            <n-select v-model:value="model.nestedValue.path2" placeholder="Nested Path 2" :options="generalOptions" />
          </n-form-item-gi>
        </n-grid>
      </n-form-item> -->
        <div style="display: flex; justify-content: flex-end">
          <n-button round type="primary" @click="handleValidateButtonClick"> 验证 </n-button>
        </div>
      </n-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, h, watch } from 'vue';
import type { FormInst, FormItemRule } from 'naive-ui';
import { useMessage } from 'naive-ui';
import ComBread from '@/components/breadcrumb/ComBread.vue';
import { IBreadData } from '@/components/breadcrumb/type.ts';
import { NButton, NRadioGroup, NRadio } from 'naive-ui';

import type { DataTableColumns } from 'naive-ui';
const breadData: IBreadData[] = [{ name: '计划管理' }, { name: '新增' }];

interface Song {
  no: number;
  title: string;
  length: string;
  checked: string;
}
const formRef = ref<FormInst | null>(null);
// const message = useMessage();
const emits = defineEmits(['action']);
const data = ref<Song[]>([
  { no: 3, title: 'Wonderwall', length: '4:18', checked: '' },
  { no: 4, title: "Don't Look Back in Anger", length: '4:48', checked: '' },
  { no: 12, title: 'Champagne Supernova', length: '7:27', checked: '' },
]);

const columns = createColumns({
  play(row: Song) {
    // message.info(`Play ${row.title}`);
    console.log('🚀 ~ play ~ row.title:', row.title);
  },
});
const pagination = false as const;
const size = ref('medium');

const model = ref({
  inputValue: null,
  textareaValue: null,
  selectValue: null,
  multipleSelectValue: null,
  datetimeValue: null,
  nestedValue: {
    path1: null,
    path2: null,
  },
  switchValue: false,
  checkboxGroupValue: null,
  radioGroupValue: null,
  radioButtonGroupValue: null,
  inputNumberValue: null,
  timePickerValue: null,
  sliderValue: 0,
  transferValue: null,
});

const generalOptions = ref(
  ['groode', 'veli good', 'emazing', 'lidiculous'].map((v) => ({
    label: v,
    value: v,
  }))
);

const rules = {
  inputValue: {
    required: true,
    trigger: ['blur', 'input'],
    message: '请输入 inputValue',
  },
  textareaValue: {
    required: true,
    trigger: ['blur', 'input'],
    message: '请输入 textareaValue',
  },
  selectValue: {
    required: true,
    trigger: ['blur', 'change'],
    message: '请选择 selectValue',
  },
  multipleSelectValue: {
    type: 'array',
    required: true,
    trigger: ['blur', 'change'],
    message: '请选择 multipleSelectValue',
  },
  datetimeValue: {
    type: 'number',
    required: true,
    trigger: ['blur', 'change'],
    message: '请输入 datetimeValue',
  },
  nestedValue: {
    path1: {
      required: true,
      trigger: ['blur', 'input'],
      message: '请输入 nestedValue.path1',
    },
    path2: {
      required: true,
      trigger: ['blur', 'change'],
      message: '请输入 nestedValue.path2',
    },
  },
  checkboxGroupValue: {
    type: 'array',
    required: true,
    trigger: 'change',
    message: '请选择 checkboxGroupValue',
  },
  radioGroupValue: {
    required: true,
    trigger: 'change',
    message: '请选择 radioGroupValue',
  },
  radioButtonGroupValue: {
    required: true,
    trigger: 'change',
    message: '请选择 radioButtonGroupValue',
  },
  inputNumberValue: {
    type: 'number',
    required: true,
    trigger: ['blur', 'change'],
    message: '请输入 inputNumberValue',
  },
  timePickerValue: {
    type: 'number',
    required: true,
    trigger: ['blur', 'change'],
    message: '请输入 timePickerValue',
  },
  sliderValue: {
    validator(rule: FormItemRule, value: number) {
      return value > 50;
    },
    trigger: ['blur', 'change'],
    message: 'sliderValue 需要大于 50',
  },
  transferValue: {
    type: 'array',
    required: true,
    trigger: 'change',
    message: '请输入 transferValue',
  },
};

const options = ref([
  {
    label: "Everybody's Got Something to Hide Except Me and My Monkey",
    value: 'song0',
    disabled: true,
  },
  {
    label: 'Drive My Car',
    value: 'song1',
  },
]);

const handleValidateButtonClick = (e: MouseEvent) => {
  e.preventDefault();
  formRef.value?.validate((errors) => {
    if (!errors) {
      // message.success('验证成功');
      console.log('🚀 ~ formRef.value?.validate ~ 验证成功:', '验证成功');
    } else {
    }
  });
};

function createColumns({ play }: { play: (row: Song) => void }): DataTableColumns<Song> {
  return [
    {
      title: 'No',
      key: 'no',
    },
    {
      title: 'Title',
      key: 'title',
    },
    {
      title: '检查人类型',
      key: 'length',
      render(row: any) {
        return h(
          NRadioGroup,
          {
            // checked: '001',
            value: row.checked,
            onUpdateValue: (val) => {
              console.log('🚀 ~ render ~ onUpdate:', val);
              row.checked = val;
              console.log(data.value);
            },
          },
          [
            { value: '001', label: '检查负责人' },
            { value: '002', label: '检查参与人' },
          ].map((i) => {
            return h(NRadio, {
              label: i.label,
              value: i.value,
            });
          })
          // NRadio
          // { default: () => '删除' }
        );
      },
    },
    {
      title: '操作',
      key: 'actions',
      align: 'left',
      width: 100,
      render(row: any) {
        return h(
          NButton,
          {
            strong: true,
            tertiary: true,
            size: 'small',
            onClick: () => play(row),
          },
          { default: () => '删除' }
        );
      },
    },
  ];
}

const filterForm = ref(initForm());
function initForm() {
  return { templateName: '', value: '' };
}
onMounted(() => {});

defineOptions({ name: 'plannedManagementAdd' });
</script>
<style module lang="scss">
.form-content {
  // @apply w-full ;
  display: grid;
  grid-template-columns: repeat(auto-fill, 300px);
  grid-row-gap: 10px;
  grid-column-gap: 10px;
  // .form-item-block {
  //   display: block;
}
</style>
