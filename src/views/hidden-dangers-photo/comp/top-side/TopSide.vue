<template>
  <div class="flex justify-start items-center w-full">
    <div class="trouble-number flex justify-start items-center mr-5">
      <img src="./随手拍.png" alt="" />
      <div class="ml-3">
        <div>隐患随手拍数量</div>
        <n-statistic tabular-nums style="--n-value-text-color: #ff9500">
          <n-number-animation :from="0" :to="total" />
        </n-statistic>
      </div>
    </div>
    <div class="trouble-number flex justify-start items-center mr-5">
      <img src="./已审批.png" alt="" />
      <div class="ml-3">
        <div>已审核</div>
        <n-statistic tabular-nums style="--n-value-text-color: #ff9500">
          <n-number-animation :from="0" :to="approveSum" />
        </n-statistic>
      </div>
    </div>
    <div class="trouble-number flex justify-start items-center mr-5">
      <img src="./未审批.png" alt="" />
      <div class="ml-3">
        <div>未审核</div>
        <n-statistic tabular-nums style="--n-value-text-color: #ff9500">
          <n-number-animation :from="0" :to="noApproveSum" />
        </n-statistic>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineComponent, ref } from 'vue';
import { getStatisticsData } from '../../fetchData';
import { useStore } from '@/store';
const { userInfo } = useStore();
const total = ref(0);
const approveSum = ref(0);
const noApproveSum = ref(0);
const getCount = async (data = {}) => {
  const params = {
    pageNo: 1,
    pageSize: -1,
    orgCode: userInfo.unitId,
  };
  let filterData = Object.assign(params, data, { pageNo: null, pageSize: null });
  const res = await getStatisticsData(filterData);
  total.value = res.data.total || 0;
  approveSum.value = res.data.approveSum || 0;
  noApproveSum.value = res.data.noApproveSum || 0;
};
// getCount();
defineExpose({ getCount });
defineOptions({ name: 'checkTampTopSideComp' });
</script>

<style scoped lang="scss">
.trouble-number {
  img {
    width: 66px;
    height: 66px;
  }

  width: 218px;
  height: 80px;
  padding: 15px 0 17px 32px;
  background: linear-gradient(180deg, #ffffff 0%, #e1e7fa 99%);
  box-shadow: 0px 2px 0px 0px rgba(0, 20, 82, 0.18);
  border-radius: 8px;
}

.other-card {
  position: relative;
  width: 172px;
  height: 80px;
  padding: 15px 32px 17px 32px;
}
</style>
