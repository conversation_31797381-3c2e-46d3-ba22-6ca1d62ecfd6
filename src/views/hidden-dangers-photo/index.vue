<template>
  <div class="flex flex-col">
    <com-bread :data="breadData"></com-bread>
    <div class="item_content notVw flex-1" style="" v-if="showList == 'list'">
      <UnitSelect class="unit-tree" @updateVal="updateVal" v-if="showTree" />
      <div class="right-table" :style="{ width: showTree ? '62vw' : '100%' }">
        <img
          src="@/assets/select_tree.png"
          class="select-tree"
          :style="{ width: toVw(30), left: `-${toVw(15)}` }"
          v-if="userInfo.unitOrgType == '2'"
          @click="showTree = !showTree"
          alt=""
        />
        <TopSide ref="topSideComp" />
        <Filter @action="actionFn" ref="filterRef" class="mt-4" />
        <TableList class="mt-4" ref="tableCompRef" @action="actionFn" />
      </div>
    </div>
    <ComDrawerA
      title="详情"
      :autoFocus="false"
      :footerPaddingBottom="25"
      :maskNeedClosable="true"
      :show="showModalDetail"
      style="background-color: #fff; width: 1000px"
      @handleNegative="showModalDetail = false"
    >
      <n-button
        v-if="showCheckBtn"
        @click="showModalCheck = true"
        text-color="#ffffff"
        type="primary"
        class="handle-btn"
      >
        隐患审核
      </n-button>
      <div style="padding: 16px 24px">
        <Detail :orderId="orderId" @action="actionFn" ref="detailComp" />
      </div>
    </ComDrawerA>
    <ComDrawerA
      title="隐患审核"
      :autoFocus="false"
      :footerPaddingBottom="25"
      :maskNeedClosable="true"
      :show="showModalCheck"
      @handleNegative="actionFn({ action: ACTION.CANCEL })"
      style="background-color: #fff; width: 1000px"
    >
      <div style="padding: 16px 24px">
        <Check @action="actionFn" :listId="listId" ref="checkComp" />
      </div>
    </ComDrawerA>
  </div>
</template>

<script setup lang="ts">
import ComDrawerA from '@/components/drawer/ComDrawerA.vue';
import { ref } from 'vue';
import Filter from './comp/Filter.vue';
import TopSide from './comp/top-side/TopSide.vue';
import TableList from './comp/table/Table.vue';
import Detail from './comp/detail/Detail.vue';
import Check from './comp/check/Check.vue';
import { ACTION } from './constant';
import { IActionData } from './type';
import { IBreadData } from '@/components/breadcrumb/type.ts';
import ComBread from '@/components/breadcrumb/ComBread.vue';
import { getStatisticsData } from './fetchData';
import { useStore } from '@/store/index';
import { toVw } from '@/utils/fit';
const { userInfo } = useStore();
const breadData: IBreadData[] = [{ name: '隐患治理系统' }, { name: '隐患随手拍 ' }];
const currentAction = ref<IActionData>({ action: ACTION.ADD, data: {} });
const listId = ref('');
const topSideComp = ref();
const showList = ref('list');
const detailComp = ref();
const showModalCheck = ref(false);
const showCheckBtn = ref(false);
const filterRef = ref();
const showTree = ref(true);
let filterVal = ref({ orgCode: userInfo.unitId });

// 组织结构切换
function updateVal(orgCode: any) {
  filterRef.value?.clearFrom();
  filterVal.value = {
    orgCode: orgCode,
    ...filterRef.value?.getVal(),
    userId: userInfo.id,
    roleCodes: userInfo.roleCodes,
  };
  tableCompRef.value.pagination.pageNo = 1;
  // tableCompRef.value.pagination.pageSize = 10;
  handleSearch(filterVal.value);
  topSideComp.value?.getCount(filterVal.value);
}

function actionFn(val: any) {
  currentAction.value = val;
  if (val.action === ACTION.DETAIL) {
    if (val.data.approveStateName == '已审核') {
      showCheckBtn.value = false;
    } else {
      showCheckBtn.value = true;
    }
    listId.value = val.data.id;
    orderId.value = val.data.id;
    showModalDetail.value = true;
  }
  if (val.action === ACTION.SEARCH) {
    filterVal.value = { orgCode: filterVal.value.orgCode, ...filterRef.value?.getVal(), userId: userInfo.id };
    handleSearch(filterVal.value);
    topSideComp.value?.getCount(filterVal.value);
    return (showList.value = 'list');
  }
  if (val.action === ACTION.CANCEL) {
    showModalCheck.value = false;
    // showModalDetail.value = false;
  }
  if (val.action === ACTION.CHECKED) {
    listId.value = val.data.id;
    showModalCheck.value = true;
  }
  if (val.action === ACTION.SUCCESS) {
    showModalCheck.value = false;
    showModalDetail.value = false;
    filterVal.value = { orgCode: filterVal.value.orgCode, ...filterRef.value?.getVal(), userId: userInfo.id };
    handleSearch(filterVal.value);
    topSideComp.value?.getCount(filterVal.value);
  }
}
const tabType = ref('1');
const tabList: any = ref([
  { name: '1', label: '隐患随手拍数量' },
  { name: '2', label: '已审批' },
  { name: '3', label: '未审批' },
]);
const handleChange = (tab: string) => {
  if (tab === tabType.value) return;
  if (tab === '2') {
    // defaultTableParams.planUserId = userInfo.userId as string;
  } else {
    // defaultTableParams.planUserId = '';
  }
  tabType.value = tab;
  // filterRef.value.resetField();
  // tableCompRef.value.getTableDataWrap(defaultTableParams);
};
const tableCompRef = ref();
const orderId = ref('');
const showModalDetail = ref(false);

function handleSearch(data: any) {
  tableCompRef.value?.getTableDataWrap(data);
}
// const getCount = async () => {
//   const params = {
//     pageNo: -1,
//     pageSize: 10,
//   };
//   const res = await getStatisticsData(params);
//   tabList.value[0].label = '隐患随手拍数量 (' + (res.data.total || 0) + ')';
//   tabList.value[1].label = '已审批 (' + (res.data.approveSum || 0) + ')';
//   tabList.value[2].label = '未审批 (' + (res.data.noApproveSum || 0) + ')';
// };
// getCount();

defineOptions({ name: 'hiddenDangersPhoto' });
</script>
<style lang="scss" scoped>
.handle-btn {
  position: fixed;
  top: 1%;
  right: 5%;
}
</style>
<style module lang="scss">
.n-modal {
  .n-dialog__title {
    display: none;

    .n-dialog__icon {
      display: none;
    }
  }
}
</style>
