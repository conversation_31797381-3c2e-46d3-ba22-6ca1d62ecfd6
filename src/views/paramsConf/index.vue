<!--
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-10-12 09:24:03
 * @LastEditors: 悦悦 <EMAIL>
 * @LastEditTime: 2024-10-21 11:11:04
 * @FilePath: /隐患一张图/ehs-hazard/src/views/paramsConf/index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <com-bread :data="breadData"></com-bread>
    <div class="com-table-container">
      <!-- 检查类型设定 -->
      <InspectList :unitId="topUnitId" />
      <!-- 隐患等级设置 -->
      <RiskList class="mt-5" :unitId="topUnitId" />
      <!-- 隐患分类配置 -->
      <hazardClassify class="mt-5" title="隐患分类" :unitId="topUnitId" />
      <!-- 隐患超期时间设置 -->
      <TimeoutLIist class="mt-5" :unitId="topUnitId" />
      <!-- 物联网信号设置 -->
      <InternetLIist class="mt-5" :unitId="topUnitId" />
      <!--打卡配置-->
      <ClockIn />
      <!-- 设备默认检查项配置 -->
      <!-- <DefaultConfig class="mt-5" :unitId="topUnitId" title="设备默认检查项配置" checkTableType="device" /> -->
      <!-- 点位默认检查项配置 -->
      <!-- <DefaultConfig class="mt-5" placeholder="请输入点位默认检查项" :unitId="topUnitId" title="点位默认检查项配置"
        checkTableType="point" /> -->
      <misGisConfig />
      <!-- 隐患来源设置 -->
      <hazardSource :unitId="topUnitId" />
    </div>
  </div>
</template>
<script setup lang="ts">
import { useStore } from '@/store/index';
import InspectList from './comp/Inspect/Table.vue';
import RiskList from './comp/risk/Table.vue';
import TimeoutLIist from './comp/timeout/Table.vue';
import InternetLIist from './comp/Internet/Table.vue';
import DefaultConfig from './comp/DefaultConfig/Table.vue';
import { IBreadData } from '@/components/breadcrumb/type.ts';
import ComBread from '@/components/breadcrumb/ComBread.vue';
import hazardClassify from './comp/HazardClassify/hazardTree.vue';
import ClockIn from './comp/ClockIn/index.vue';
import misGisConfig from './comp/misGisConfig/index.vue';
import hazardSource from './comp/hazardSource/Table.vue';
const breadData: IBreadData[] = [{ name: '隐患治理系统' }, { name: '参数配置' }];
const { $state } = useStore();
// 单位ID入参全部修改为集团ID
const { topUnitId } = $state.userInfo;

defineOptions({ name: 'paramsConf' });
</script>

<style module lang="scss"></style>
