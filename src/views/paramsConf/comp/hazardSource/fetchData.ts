import { IObj } from '@/types';
import { api } from '@/api';
import { $http } from '@tanzerfe/http';

// 添加隐患来源配置
export function addHazardSourceConfigAPI(data: any) {
  console.log(data);
  const url = api.getUrl(api.type.hazard, api.public.addHazardSourceConfig);
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}
// updateHazardSourceConfig
export function updateHazardSourceConfigAPI(data: any) {
  console.log(data);
  const url = api.getUrl(api.type.hazard, api.public.updateHazardSourceConfig);
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}
// 查询隐患来源配置
export function HazardSourcelistAPI(data: any) {
  const url = api.getUrl(api.type.hazard, api.public.HazardSourcelist, data);
  return $http.post(url, {
    data: { _cfg: { showTip: true } },
  });
}
// 删除隐患来源配置
export function deleteHazardSourceConfigAPI(data: any) {
  const url = api.getUrl(api.type.hazard, api.public.deleteHazardSourceConfig, data);
  return $http.post(url, {
    data: { _cfg: { showTip: true } },
  });
}
// 隐患来源详情
export function azardSourcegetInfoAPI(data: any) {
  const url = api.getUrl(api.type.hazard, api.public.azardSourcegetInfo, data);
  return $http.post(url, {
    data: { _cfg: { showTip: true } },
  });
}
