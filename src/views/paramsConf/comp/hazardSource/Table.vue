<template>
  <AppHeader title="隐患来源配置" blueIcon>
    <template #content>
      <span style="color: #527cff; font-weight: bold">(仅针对手动上报、单独隐患上报和随手拍功能)</span>
    </template>
    <template #right>
      <div class="flex justify-end items-center">
        <n-button v-if="isTopUnit()" type="primary" @click="addModal" :style="{ height: toVh(30), fontSize: toVh(16) }"
          >新增</n-button
        >
      </div>
    </template>
  </AppHeader>

  <n-data-table
    class="com-table mt-5"
    remote
    striped
    :bordered="false"
    :columns="columns"
    :data="tableData"
    :pagination="false"
    :min-height="150"
    :max-height="500"
    :render-cell="useEmptyCell"
  />

  <n-drawer v-model:show="showModal" :style="{ width: toVw(500) }" class="models" :autoFocus="false">
    <n-drawer-content closable>
      <template #header>
        <div class="flex flex-row items-center">
          <img class="w-[17px] h-[12px] mr-[20px]" src="@/components/header/assets/icon-title-arrow3.png" />
          <div class="text-[16px] text-[#222222] font-bold">
            <!-- {{ isDetail ? '隐患来源详情' : modelForm.id ? '编辑隐患来源' : '新增隐患来源' }} -->
            {{ modelForm.id ? '编辑隐患来源' : '新增隐患来源' }}
          </div>
        </div>
      </template>
      <n-form ref="formRef" :model="modelForm" :rules="rules">
        <n-form-item path="hazardSourceName" label="隐患来源名称">
          <!-- <div v-if="isDetail">{{ modelForm.hazardSourceName }}</div> -->
          <n-input
            v-model:value="modelForm.hazardSourceName"
            maxlength="8"
            show-count
            clearable
            placeholder="请输入隐患来源名称"
          />
        </n-form-item>
        <n-form-item path="sourceType" label="内外部隐患">
          <n-radio :checked="checkedValue === '1'" value="1" name="sourceType" @change="handleChange">
            内部隐患
          </n-radio>
          <n-radio :checked="checkedValue === '2'" value="2" name="sourceType" @change="handleChange">
            外部隐患
          </n-radio>
        </n-form-item>
      </n-form>
      <template #footer>
        <div class="flex justify-end items-center">
          <n-button type="primary" @click="showModal = false" ghost>取消</n-button>
          <!-- v-if="!isDetail" 20866【参数配置】编辑隐患来源页面没有保存按钮 -->
          <n-button type="primary" @click="handleValidateClick" style="margin-left: 10px"> 保存 </n-button>
        </div>
      </template>
    </n-drawer-content>
  </n-drawer>
</template>

<script setup lang="ts">
import { h, ref, VNode, watchEffect, toRaw, watch, computed } from 'vue';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { DataTableColumns, NButton, FormInst, FormRules, FormItemRule, useMessage } from 'naive-ui';
import { cols } from './columns';
import { useRouter } from 'vue-router';
import {
  addHazardSourceConfigAPI,
  HazardSourcelistAPI,
  deleteHazardSourceConfigAPI,
  updateHazardSourceConfigAPI,
  azardSourcegetInfoAPI,
} from './fetchData';
import {
  getCheckSettingList,
  addCheckSetting,
  deleteCheckSetting,
  stopCheckSetting,
  updateCheckSetting,
} from '@/views/paramsConf/comp/fetchData';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { $dialog } from '@/common/shareContext/useDialogCtx.ts';
import { useStore } from '@/store/index';
import { isTopUnit } from '@/views/inspection-planning/planned-management/Details/utils';
import { toVw, toVh } from '@/utils/fit';

const { $state } = useStore();
const { topUnitId } = $state.userInfo;
const { userInfo } = $state;
console.log(userInfo);

const router = useRouter();
const emits = defineEmits(['action']);
const props = defineProps({
  unitId: {
    type: String,
    default: '',
  },
});
const title = ref('编辑检查类型');
const isEdit = computed(() => {
  return topUnitId == userInfo.unitId;
});

const modelForm: any = ref({
  id: null,
  status: 0,
  hazardSourceName: '',
  sourceType: '1',
  createBy: userInfo.userName,
  updateBy: userInfo.userName,
});
const message = useMessage();
const formRef = ref<any | null>(null);
const rules: any = {
  hazardSourceName: [
    {
      required: true,
      message: '请输入隐患来源名称',
      trigger: ['blur'],
    },
  ],
  sourceType: [
    {
      required: true,
      message: '请选择内外部隐患',
      trigger: ['blur'],
    },
  ],
};

const [loading, search] = useAutoLoading(true);
const { pagination, updateTotal } = useNaivePagination(getTableData);

const columns = ref<DataTableColumns>([]);
const planTypeName = ref('');
const tableData = ref<any[]>();
const showModal = ref(false);
const isDetail = ref(false);
const checkedValue = ref('1');

// 选择内部外部
function handleChange(e: Event) {
  console.log(e);
  checkedValue.value = (e.target as HTMLInputElement).value;
  modelForm.value.sourceType = checkedValue.value;
}
watchEffect(() => {
  if (!showModal.value) {
    resetFrom();
  }
});
// watch(
//   () => modelForm.value.planTypeConfig.area,
//   (newValue, oldValue) => {
//     if (!newValue) {
//       modelForm.value.planTypeConfig.checkEnd = false;
//       modelForm.value.planTypeConfig.clockIn = false;
//     }
//     // 在此处添加针对变化的处理逻辑
//   }
// );
// watch(
//   () => isDetail.value,
//   (newValue, oldValue) => {
//     console.log(newValue, '================newValue');
//     if (newValue) {
//       title.value = '隐患来源详情';
//     } else {
//       title.value = '编辑隐患来源';
//     }
//     // 在此处添加针对变化的处理逻辑
//   }
// );

function addModal() {
  // if (tableData.value?.length >= 10 && title.value == '新增隐患来源') {
  //   message.error('最多只能添加10条数据');
  //   return;
  // }
  title.value = '新增隐患来源';
  showModal.value = true;
}

function handleValidateClick(e: Event) {
  e.preventDefault();
  formRef.value?.validate((errors: any) => {
    console.log(errors);
    if (!errors) {
      let _api = modelForm.value.id ? updateHazardSourceConfigAPI : addHazardSourceConfigAPI;
      let { id, hazardSourceName, sourceType, createBy, updateBy } = modelForm.value;
      let params = {
        id: id || null,
        hazardSourceName,
        sourceType,
        createBy,
        updateBy,
      };
      console.log(params, '=====modelForm.value');
      _api(params).then((res: any) => {
        if (res.code === 'success') {
          showModal.value = false;
          resetFrom();
        }
        getTableData();
      });
    } else {
      console.log(errors);
    }
  });
}

function resetFrom() {
  checkedValue.value = '1';
  modelForm.value = {
    hazardSourceName: null,
    sourceType: '1',
    id: null,
    createBy: userInfo.userName,
    updateBy: userInfo.userName,
  };
  isDetail.value = false;
}

function setColumns() {
  columns.value.push(...cols);
  // 添加操作栏 action
  // if (isTopUnit())
  if (isTopUnit()) {
    columns.value.push({
      fixed: 'right',
      width: '230px',
      title: '操作',
      key: 'actions',
      align: 'left',
      render(row) {
        return getActionBtn(row);
      },
    });
  }
}
setColumns();

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    // [
    //   h(
    //     NButton,
    //     {
    //       size: 'small',
    //       type: 'primary',
    //       ghost: true,
    //       class: 'com-action-button',
    //       onClick: () => {
    //         getDetail(row, 'detail');
    //       },
    //     },
    //     { default: () => '详情' }
    //   ),
    // ],
  ];

  if (isTopUnit()) {
    acList.push([
      h(
        NButton,
        {
          size: 'small',
          type: 'primary',
          ghost: true,
          onClick: () => {
            getDetail(row, 'edit');
          },
        },
        { default: () => '编辑' }
      ),
    ]);

    // acList.push([
    //   h(
    //     NButton,
    //     {
    //       size: 'small',
    //       type: 'warning',
    //       ghost: true,
    //       onClick: () => handleStop(row),
    //     },
    //     {
    //       default: () => {
    //         return row.planTypeStatus == '2' ? '启用' : '停用';
    //       },
    //     }
    //   ),
    // ]);

    acList.push([
      h(
        NButton,
        {
          // disabled: row.checkTypeInuse,
          size: 'small',
          type: 'error',
          ghost: true,
          onClick: () => handleDelete(row),
        },
        { default: () => '删除' }
      ),
    ]);
  }

  return useActionDivider(acList);
}

// 停用 or 启用
// function handleStop(row: any) {
//   $dialog.warning({
//     title: '提示',
//     content: `确定${row.planTypeStatus == '2' ? '启用' : '停用'}吗？`,
//     positiveText: '确定',
//     negativeText: '取消',
//     transformOrigin: 'center',
//     onPositiveClick: async () => {
//       await stopCheckSetting({
//         planTypeId: row.planTypeId,
//         planTypeStatus: row.planTypeStatus == '1' ? '2' : '1',
//       }).then((res: any) => {
//         if (res.code === 'success') {
//           message.success(row.planTypeStatus == '1' ? '停用成功' : '启用成功');
//           getTableData();
//         }
//       });
//     },
//   });
// }
const typeId = ref('');
function getDetail(row: any, type: string) {
  console.log('Row data:', type);
  resetFrom();
  // 初始化默认值
  typeId.value = row.id;
  // let { planTypeId, planTypeName, planTypeSort, planTypeRemark } = toRaw(row);
  azardSourcegetInfoAPI({ id: row.id }).then((res: any) => {
    modelForm.value = res.data;
    checkedValue.value = modelForm.value.sourceType;
    // console.log(modelForm.value, "======编辑");
  });
  // modelForm.value = row;
  if (type == 'edit') {
    isDetail.value = true;
    title.value = '编辑隐患来源';
  } else {
    isDetail.value = false;
    title.value = '新增隐患来源';
  }
  showModal.value = true;
}

// 删除检查项
function handleDelete(row: any) {
  $dialog.warning({
    title: '提示',
    content: '确定删除隐患来源吗？删除后不可恢复！',
    positiveButtonProps: { type: 'primary', color: '#527cff' },
    positiveText: '确定',
    negativeText: '取消',
    transformOrigin: 'center',
    onPositiveClick: async () => {
      await deleteHazardSourceConfigAPI({ id: row.id });
      getTableData();
    },
  });
}

function getTableData() {
  const params = {};
  search(HazardSourcelistAPI(params)).then((res: any) => {
    tableData.value = res.data || [];
    console.log(tableData.value, '================tableData.value');
  });
}

watch(
  () => planTypeName.value,
  () => {
    getTableData();
  }
);

function getTableDataWrap(data: any) {
  pagination.page = 1;
  getTableData();
}
getTableData();

defineOptions({ name: 'checkTampTableComp' });
defineExpose({ getTableDataWrap, getTableData });
</script>
<style module lang="scss">
.dialog_btn {
  --n-color: #527cff;
  --n-color-hover: #7aa0ff;
  --n-color-pressed: #3b5dd9;
  --n-color-focus: #7aa0ff;
  --n-color-disabled: #527cff;
  --n-ripple-color: #527cff;
  --n-text-color: #fff;
  --n-text-color-hover: #fff;
  --n-text-color-pressed: #fff;
  --n-text-color-focus: #fff;
  --n-text-color-disabled: #fff;
  --n-border: 1px solid #527cff;
  --n-border-hover: 1px solid #7aa0ff;
  --n-border-pressed: 1px solid #3b5dd9;
  --n-border-focus: 1px solid #7aa0ff;
  --n-border-disabled: 1px solid #527cff;
}
</style>
