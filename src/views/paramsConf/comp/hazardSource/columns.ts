import { planTypeStatus } from '@/components/table-col/planTypeStatus';
import { DataTableColumn, NRadio } from 'naive-ui';
import { h } from 'vue';
const list = [
  {
    label: '内部',
    value: '1',
  },
  {
    label: '外部',
    value: '2',
  },
];
export const cols: DataTableColumn[] = [
  {
    title: '隐患来源名称',
    key: 'hazardSourceName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '内外部',
    key: 'sourceType',
    align: 'left',
    // render(row) {
    //   console.log(row);
    //   // return row.planTypeStatus == '2' ? '启用' : '停用';
    // },
    render: (row) => {
      return list.map((item: any) => {
        return h(NRadio, {
          value: item.value,
          label: item.label,
          checked: row.sourceType == item.value,
          disabled: true,
          // onupdatechecked: async (val: any) => {
          //   console.log(val);
          // },
        });
      });
    },
  },
];
