import { planTypeStatus } from '@/components/table-col/planTypeStatus';
import { DataTableColumn } from 'naive-ui';
import { h } from 'vue';

export const cols: DataTableColumn[] = [
  {
    title: '排序',
    key: 'planTypeSort',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '检查类型',
    key: 'planTypeName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },

  {
    title: '备注',
    key: 'planTypeRemark',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
    render: (row) => {
      return row.planTypeRemark || '--';
    },
  },
  {
    title: '状态',
    key: 'planTypeStatus',
    align: 'left',
    // render(row) {
    //   console.log(row);
    //   // return row.planTypeStatus == '2' ? '启用' : '停用';
    // },
    render: (row) => h(planTypeStatus, { row }),
  },
];
