<template>
  <AppHeader title="检查类型设置" blueIcon>
    <template #right>
      <div class="flex justify-end items-center">
        <n-input-group class="mr-5">
          <n-input
            placeholder="请输入检查类型进行模糊查询"
            v-model:value="planTypeName"
            clearable
            :style="{ height: toVh(30), width: '300px' }"
            maxlength="50"
            show-count
          />
          <n-button :style="{ height: toVh(30), fontSize: toVh(16) }" type="primary">搜索</n-button>
        </n-input-group>
        <!-- <n-button type="primary" @click="showModal = true">创建检查类型</n-button> -->
        <n-button v-if="isTopUnit()" type="primary" @click="addModal" :style="{ height: toVh(30), fontSize: toVh(16) }"
          >创建检查类型</n-button
        >
      </div>
    </template>
  </AppHeader>

  <n-data-table
    class="com-table mt-5"
    remote
    striped
    :bordered="false"
    :columns="columns"
    :data="tableData"
    :pagination="false"
    :min-height="150"
    :max-height="500"
    :render-cell="useEmptyCell"
  />

  <n-drawer v-model:show="showModal" :style="{ width: toVw(500) }" class="models" :autoFocus="false">
    <n-drawer-content closable>
      <template #header>
        <div class="flex flex-row items-center">
          <img class="w-[17px] h-[12px] mr-[20px]" src="@/components/header/assets/icon-title-arrow3.png" />
          <div class="text-[16px] text-[#222222] font-bold">
            {{ isDetail ? '检查类型详情' : modelForm.planTypeId ? '编辑检查类型' : '新增检查类型' }}
          </div>
        </div>
      </template>
      <n-form ref="formRef" :model="modelForm" :rules="rules">
        <n-form-item path="planTypeName" label="检查类型">
          <div v-if="isDetail">{{ modelForm.planTypeName }}</div>
          <n-input
            v-else
            v-model:value="modelForm.planTypeName"
            maxlength="15"
            show-count
            clearable
            placeholder="请输入检查类型"
          />
        </n-form-item>
        <n-form-item path="planTypeSort" label="排序">
          <div v-if="isDetail">{{ modelForm.planTypeSort }}</div>
          <n-input-number
            v-else
            maxlength="50"
            show-count
            v-model:value="modelForm.planTypeSort"
            placeholder="请输入"
            clearable
            :min="1"
            max="99"
            style="width: 100%"
            @input="handleInput"
          />
        </n-form-item>
        <!-- <n-form-item label="权限配置" path="planTypeConfig">
          <div class="pl-[25px]">
            <div>
              <n-checkbox :disabled="isDetail" v-model:checked="modelForm.planTypeConfig.area"> 位置 </n-checkbox>
              <div class="pl-[25px]" v-if="modelForm.planTypeConfig.area">
                <div>
                  <n-checkbox :disabled="isDetail" v-model:checked="modelForm.planTypeConfig.checkEnd">
                    检查结束操作
                  </n-checkbox>
                </div>
                <div>
                  <n-checkbox :disabled="isDetail" v-model:checked="modelForm.planTypeConfig.clockIn">
                    是否需要检查人现场打卡
                  </n-checkbox>
                </div>
              </div>
            </div>
            <div>
              <n-checkbox :disabled="isDetail" v-model:checked="modelForm.planTypeConfig.device"> 设备 </n-checkbox>
            </div>
            <div>
              <n-checkbox :disabled="isDetail" v-model:checked="modelForm.planTypeConfig.pointer">
                巡查点位
              </n-checkbox>
            </div>
          </div>
        </n-form-item> -->

        <!-- <div>
          <n-form-item path="planTypeConfig.area">
            <n-checkbox v-model:checked="modelForm.planTypeConfig.area"> 区域 </n-checkbox>
          </n-form-item>
        </div>
        <div>
          <n-form-item path="planTypeConfig.device">
            <n-checkbox v-model:checked="modelForm.planTypeConfig.device"> 设备 </n-checkbox>
          </n-form-item>
        </div>
        <div>
          <n-form-item path="planTypeConfig.pointer">
            <n-checkbox v-model:checked="modelForm.planTypeConfig.pointer"> 点位 </n-checkbox>
          </n-form-item>
        </div> -->

        <!-- modelForm.planTypeConfig -->
        <n-form-item path="planTypeRemark" label="备注">
          <div v-if="isDetail">{{ modelForm.planTypeRemark ? modelForm.planTypeRemark : '--' }}</div>
          <n-input
            v-else
            maxlength="200"
            show-count
            v-model:value="modelForm.planTypeRemark"
            placeholder="请输入备注"
            clearable
            type="textarea"
            :autosize="{
              minRows: 3,
              maxRows: 5,
            }"
          />
        </n-form-item>
      </n-form>
      <template #footer>
        <div class="flex justify-end items-center">
          <n-button type="primary" @click="showModal = false" ghost>取消</n-button>
          <n-button type="primary" @click="handleValidateClick" v-if="!isDetail" style="margin-left: 10px">
            保存
          </n-button>
        </div>
      </template>
    </n-drawer-content>
  </n-drawer>
</template>

<script setup lang="ts">
import { h, ref, VNode, watchEffect, toRaw, watch, computed } from 'vue';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { DataTableColumns, NButton, FormInst, FormRules, FormItemRule, useMessage } from 'naive-ui';
import { cols } from './columns';
import { useRouter } from 'vue-router';
import {
  getCheckSettingList,
  addCheckSetting,
  deleteCheckSetting,
  stopCheckSetting,
  updateCheckSetting,
} from '@/views/paramsConf/comp/fetchData';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { $dialog } from '@/common/shareContext/useDialogCtx.ts';
import { useStore } from '@/store/index';
import { isTopUnit } from '@/views/inspection-planning/planned-management/Details/utils';
import { toVw, toVh } from '@/utils/fit';

const { $state } = useStore();
const { topUnitId } = $state.userInfo;
const { userInfo } = $state;
console.log(userInfo);

const router = useRouter();
const emits = defineEmits(['action']);
const props = defineProps({
  unitId: {
    type: String,
    default: '',
  },
});
const title = ref('编辑检查类型');
const isEdit = computed(() => {
  return topUnitId == userInfo.unitId;
});

const modelForm: any = ref({
  delFlag: 0,
  // planTypeConfig: 'string', //权限配置
  planTypeId: '', //计划类型id
  planTypeName: '', //计划类型名称
  planTypeRemark: '', //备注
  planTypeSort: '', //排序
  planTypeStatus: '', //状态 1启用 2停用
  unitId: '', //单位id
  zhId: '',
  planTypeConfig: {
    area: false,
    device: false,
    pointer: false,
    checkEnd: false,
    clockIn: false,
  },
});
const message = useMessage();
const formRef = ref<any | null>(null);
const rules: any = {
  planTypeName: [
    {
      required: true,
      message: '请输入检查类型名称',
      trigger: ['blur'],
    },
  ],
  planTypeSort: [
    {
      type: 'number',
      required: true,
      trigger: ['blur', 'change'],
      // message: '请输入排序',
      validator(rule: FormItemRule, value: string[]) {
        if (!value) {
          return new Error('请输入排序');
        } else if (Number(value) < 1 || Number(value) > 99) {
          return new Error('序号限制输入最多两位数');
        } else if (value % 1 !== 0) {
          return new Error('请输入整数');
        } else {
          const index: any = tableData.value?.findIndex((item: any) => item.planTypeSort == Number(value));
          if (index > -1) {
            if (tableData.value[index].planTypeId != typeId.value) {
              return new Error('排序已存在');
            }
          }
        }
      },
    },
  ],
  planTypeConfig: [
    {
      type: 'number',
      required: true,
      trigger: ['blur', 'change'],
      // message: '请输入排序',
      validator(rule: FormItemRule, value: string[]) {
        console.log(value);
        if (value.area || value.pointer || value.device) {
        } else {
          return new Error('请选择权限配置');
        }
        // if (
        //   modelForm.value.planTypeConfig.area ||
        //   modelForm.value.planTypeConfig.pointer ||
        //   modelForm.value.planTypeConfig.device
        // ) {
        //   // return new Error('请选择权限配置');
        // } else {
        //   return new Error('请选择权限配置');
        // }
      },
    },
  ],
};
const [loading, search] = useAutoLoading(true);
const { pagination, updateTotal } = useNaivePagination(getTableData);
function handleInput(value) {
  // 过滤小数值
  const intValue = parseInt(value);
  if (!isNaN(intValue)) {
    modelForm.value.planTypeSort = intValue;
  }
}
const columns = ref<DataTableColumns>([]);
const planTypeName = ref('');
const tableData = ref<any[]>();
const showModal = ref(false);
const isDetail = ref(false);

watchEffect(() => {
  if (!showModal.value) {
    resetFrom();
  }
});
watch(
  () => modelForm.value.planTypeConfig.area,
  (newValue, oldValue) => {
    if (!newValue) {
      modelForm.value.planTypeConfig.checkEnd = false;
      modelForm.value.planTypeConfig.clockIn = false;
    }
    // 在此处添加针对变化的处理逻辑
  }
);
watch(
  () => isDetail.value,
  (newValue, oldValue) => {
    console.log(newValue, '================newValue');
    if (newValue) {
      title.value = '检查类型详情';
    } else {
      title.value = '编辑检查类型';
    }
    // 在此处添加针对变化的处理逻辑
  }
);

function addModal() {
  if (tableData.value?.length >= 10 && title.value == '新增检查类型') {
    message.error('最多只能添加10条数据');
    return;
  }
  title.value = '新增检查类型';
  showModal.value = true;
}

function handleValidateClick(e: Event) {
  e.preventDefault();
  formRef.value?.validate((errors: any) => {
    console.log(errors);
    if (!errors) {
      modelForm.value.planTypeConfig = JSON.stringify(modelForm.value.planTypeConfig);

      let _api = modelForm.value.planTypeId ? updateCheckSetting : addCheckSetting;

      _api({
        ...toRaw(modelForm.value),
        unitId: userInfo.topUnitId,
      }).then((res: any) => {
        if (res.code === 'success') {
          showModal.value = false;
          resetFrom();
        }
        getTableData();
      });

      // if (!modelForm.value.planTypeId) {
      //   addCheckSetting({ }).then((res: any) => {
      //     if (res.code === 'success') {
      //       showModal.value = false;
      //       resetFrom();
      //     }
      //     getTableData();
      //   });
      // } else {
      //   updateCheckSetting({ ...toRaw(modelForm.value), unitId: userInfo.topUnitId }).then((res: any) => {
      //     if (res.code === 'success') {
      //       showModal.value = false;
      //       resetFrom();
      //     }
      //     getTableData();
      //   });
      // }
    } else {
      console.log(errors);
    }
  });
}

function resetFrom() {
  modelForm.value = {
    planTypeId: null,
    planTypeName: null,
    planTypeSort: null,
    planTypeRemark: null,
    planTypeConfig: {
      area: false,
      device: false,
      pointer: false,
      checkEnd: false,
      clockIn: false,
    },
  };
  isDetail.value = false;
}

function setColumns() {
  columns.value.push(...cols);
  // 添加操作栏 action
  // if (isTopUnit())
  if (isTopUnit()) {
    columns.value.push({
      fixed: 'right',
      width: '230px',
      title: '操作',
      key: 'actions',
      align: 'left',
      render(row) {
        return getActionBtn(row);
      },
    });
  } else {
    // columns.value.push({
    //   fixed: 'right',
    //   width: '120px',
    //   title: '操作',
    //   key: 'actions',
    //   align: 'center',
    //   render(row) {
    //     return getActionBtn(row);
    //   },
    // });
  }
}
setColumns();

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    // [
    //   h(
    //     NButton,
    //     {
    //       size: 'small',
    //       type: 'primary',
    //       ghost: true,
    //       class: 'com-action-button',
    //       onClick: () => {
    //         getDetail(row, 'detail');
    //       },
    //     },
    //     { default: () => '详情' }
    //   ),
    // ],
  ];

  if (isTopUnit()) {
    acList.push([
      h(
        NButton,
        {
          size: 'small',
          type: 'primary',
          ghost: true,
          onClick: () => {
            getDetail(row, 'edit');
          },
        },
        { default: () => '编辑' }
      ),
    ]);

    acList.push([
      h(
        NButton,
        {
          size: 'small',
          type: 'warning',
          ghost: true,
          onClick: () => handleStop(row),
        },
        {
          default: () => {
            return row.planTypeStatus == '2' ? '启用' : '停用';
          },
        }
      ),
    ]);

    acList.push([
      h(
        NButton,
        {
          disabled: row.checkTypeInuse,
          size: 'small',
          type: 'error',
          ghost: true,
          onClick: () => handleDelete(row),
        },
        { default: () => '删除' }
      ),
    ]);
  }

  return useActionDivider(acList);
}

// 停用 or 启用
function handleStop(row: any) {
  $dialog.warning({
    title: '提示',
    content: `确定${row.planTypeStatus == '2' ? '启用' : '停用'}吗？`,
    positiveButtonProps: { type: 'primary', color: '#527cff' },
    positiveText: '确定',
    negativeText: '取消',
    transformOrigin: 'center',
    onPositiveClick: async () => {
      await stopCheckSetting({
        planTypeId: row.planTypeId,
        planTypeStatus: row.planTypeStatus == '1' ? '2' : '1',
      }).then((res: any) => {
        if (res.code === 'success') {
          message.success(row.planTypeStatus == '1' ? '停用成功' : '启用成功');
          getTableData();
        }
      });
    },
  });
}
const typeId = ref('');
function getDetail(row: any, type: string) {
  console.log('Row data:', row);
  resetFrom();
  // 初始化默认值
  typeId.value = row.planTypeId;
  modelForm.value.planTypeConfig = {
    area: false,
    device: false,
    pointer: false,
    checkEnd: false,
    clockIn: false,
  };
  try {
    if (row.planTypeConfig && typeof row.planTypeConfig === 'string') {
      const config = JSON.parse(row.planTypeConfig);
      console.log('Parsed config:', config);
      modelForm.value.planTypeConfig = {
        // area: config.area,
        // checkEnd: config.checkEnd,
        // clockIn: config.clockIn,
        // device: config.device,
        // pointer: config.pointer,
        ...modelForm.value.planTypeConfig,
        ...config,
      };
      console.log(modelForm.value.planTypeConfig);
    }
  } catch (error) {
    console.error('Parsing failed:', error);
  }

  let { planTypeId, planTypeName, planTypeSort, planTypeRemark } = toRaw(row);

  modelForm.value = {
    ...modelForm.value, // 保留其他属性
    planTypeId,
    planTypeSort: Number(planTypeSort),
    planTypeName,
    planTypeRemark,
  };
  console.log(modelForm.value);

  if (type == 'detail') {
    isDetail.value = true;
  } else {
    isDetail.value = false;
    title.value = '新增检查类型';
  }
  showModal.value = true;
}

// 删除检查项
function handleDelete(row: any) {
  $dialog.warning({
    title: '提示',
    content: '确定删除检查类型吗？删除后不可恢复！',
    positiveButtonProps: { type: 'primary', color: '#527cff' },
    positiveText: '确定',
    negativeText: '取消',
    transformOrigin: 'center',
    onPositiveClick: async () => {
      await deleteCheckSetting({ planTypeId: row.planTypeId });
      getTableData();
    },
  });
}

function getTableData() {
  const params = {
    // pageNo: pagination.page,
    // pageSize: pagination.pageSize,
    planTypeName: planTypeName.value,
    unitId: userInfo.topUnitId,
    planTypeStatus: '',
  };

  search(getCheckSettingList(params)).then((res: any) => {
    tableData.value = res.data || [];
    // tableData.value = [];

    // updateTotal(res.data.total || 0);
    // console.log(res.data.total);
  });
}

watch(
  () => planTypeName.value,
  () => {
    getTableData();
  }
);

function getTableDataWrap(data: any) {
  pagination.page = 1;
  getTableData();
}
getTableData();

defineOptions({ name: 'checkTampTableComp' });
defineExpose({ getTableDataWrap, getTableData });
</script>
<style module lang="scss">
.dialog_btn {
  --n-color: #527cff;
  --n-color-hover: #7aa0ff;
  --n-color-pressed: #3b5dd9;
  --n-color-focus: #7aa0ff;
  --n-color-disabled: #527cff;
  --n-ripple-color: #527cff;
  --n-text-color: #fff;
  --n-text-color-hover: #fff;
  --n-text-color-pressed: #fff;
  --n-text-color-focus: #fff;
  --n-text-color-disabled: #fff;
  --n-border: 1px solid #527cff;
  --n-border-hover: 1px solid #7aa0ff;
  --n-border-pressed: 1px solid #3b5dd9;
  --n-border-focus: 1px solid #7aa0ff;
  --n-border-disabled: 1px solid #527cff;
}
</style>
