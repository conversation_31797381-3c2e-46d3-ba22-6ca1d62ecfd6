import { IObj } from '@/types';
import { api } from '@/api';
import { $http } from '@tanzerfe/http';

// 检查；类型新增
export function addPlanType(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.sh.paramsConf.addPlanType);
  return $http.post<IObj<any>>(url, { data: { _cfg: { showTip: true }, ...query } });
}
export function getPalnTypeList(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.sh.paramsConf.getPalnTypeList, { ...query });
  return $http.post<IObj<any>>(url, { data: { _cfg: { showTip: true } } });
}
export function deletePlanType(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.sh.paramsConf.deletePlanType, { ...query });
  return $http.post<IObj<any>>(url, { data: { _cfg: { showTip: true } } });
}
export function updatePlanTypeStatus(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.sh.paramsConf.updatePlanTypeStatus, { ...query });
  return $http.post<IObj<any>>(url, { data: { _cfg: { showTip: true } } });
}
export function updatePlanType(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.sh.paramsConf.updatePlanType, {});
  return $http.post<IObj<any>>(url, { data: { _cfg: { showTip: true }, ...query } });
}
