<template>
  <AppHeader title="隐患超期时间设置" blueIcon class="mt-10 mb-5">
    <template #right v-if="isTopUnit()">
      <n-button type="primary" @click="showModal = true" :style="{ height: toVh(30), fontSize: toVh(16) }"
        >新增</n-button
      >
    </template>
  </AppHeader>
  <n-data-table
    class="com-table"
    remote
    striped
    :bordered="false"
    :columns="columns"
    :data="tableData"
    :pagination="false"
    :min-height="150"
    :max-height="530"
    :render-cell="useEmptyCell"
  />
  <n-modal
    v-model:show="showModal"
    :show-icon="false"
    preset="card"
    class="models w_500"
    :mask-closable="false"
    :autoFocus="false"
  >
    <template #header>
      <div class="flex flex-row items-center">
        <img class="w-[17px] h-[12px] mr-[20px]" src="@/components/header/assets/icon-title-arrow3.png" />
        <div class="text-[16px] text-[#222222] font-bold">
          {{ modelForm.id === null ? '新增隐患超期时间' : '编辑隐患超期时间' }}
        </div>
      </div>
    </template>
    <!-- <n-modal v-model:show="showModal" preset="dialog" title="Dialog" :mask-closable="false"> -->
    <!-- <template #header>
      <div>隐患超期时间设置</div>
    </template> -->
    <n-form ref="formRef" :model="modelForm" :rules="rules" class="p-[24px]">
      <n-form-item path="overdueDay" label="隐患超期时间(天)">
        <n-input-number class="w-full" v-model:value="modelForm.overdueDay" :min="1" :max="999999" clearable />
      </n-form-item>
      <n-form-item path="reminderObjList" label="提醒对象">
        <n-select
          placeholder="请选择"
          filterable
          max-tag-count="responsive"
          v-model:value="modelForm.reminderObjList"
          clearable
          :options="roleList"
          multiple
          label-field="roleName"
          value-field="id"
        />
      </n-form-item>
    </n-form>
    <template #action>
      <div class="flex justify-end items-center">
        <n-button type="primary" @click="showModal = false" ghost>取消</n-button>
        <n-button type="primary" @click="handleValidateClick" style="margin-left: 10px"> 保存 </n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { h, ref, VNode, watchEffect, toRaw } from 'vue';
import { DataTableColumns, NButton, FormInst, FormRules, FormItemRule, useMessage } from 'naive-ui';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { cols } from './columns';
import { toVw, toVh } from '@/utils/fit';
import {
  getOverdueSettingList,
  addOverdueSetting,
  deleteOverdueSetting,
  updateOverdueSetting,
  getRoleList,
} from '@/views/paramsConf/comp/fetchData';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { $dialog } from '@/common/shareContext/useDialogCtx.ts';
import { IObj } from '@/types';
import { isTopUnit } from '@/views/inspection-planning/planned-management/Details/utils';

const emits = defineEmits(['action']);
const props = defineProps({
  unitId: {
    type: String,
    default: '',
  },
});

const modelForm = ref({
  id: null,
  overdueDay: null,
  reminderObjList: <any[]>[],
});
const formRef = ref<FormInst | null>(null);
const onlyAllowNumber = ref((value: string) => !value || /^\d+$/.test(value));
const rules: FormRules = {
  overdueDay: [
    {
      required: true,
      message: '请输入超期时长',
    },
  ],
  reminderObjList: [
    {
      type: 'array',
      required: true,
      trigger: ['blur', 'change'],
      message: '请选择提醒对象',
    },
  ],
};
const [loading, search] = useAutoLoading(true);
const { pagination, updateTotal } = useNaivePagination(getTableData);

const columns = ref<DataTableColumns>([]);
const tableData = ref([]);
const showModal = ref(false);
const roleList = ref([]);

watchEffect(() => {
  if (!showModal.value) {
    resetFrom();
  }
});

function handleValidateClick(e: Event) {
  e.preventDefault();
  formRef.value?.validate((errors) => {
    if (!errors) {
      let { id, overdueDay, reminderObjList } = modelForm.value;
      let params = {
        id,
        overdueDay,
        reminderObjList,
        unitId: props.unitId,
        reminderObjNameList: roleList.value
          .map((item: any) => {
            if (reminderObjList.includes(item.id)) {
              return item.roleName;
            }
          })
          .filter((item: any) => item != null),
      };
      if (!modelForm.value.id) {
        addOverdueSetting(params).then((res: any) => {
          if (res.code === 'success') {
            showModal.value = false;
            resetFrom();
          }
          getTableData();
        });
      } else {
        updateOverdueSetting(params).then((res: any) => {
          if (res.code === 'success') {
            showModal.value = false;
            resetFrom();
          }
          getTableData();
        });
      }
    } else {
      console.log(errors);
    }
  });
}

function resetFrom() {
  modelForm.value = {
    id: null,
    overdueDay: null,
    reminderObjList: <any[]>[],
  };
}

function setColumns() {
  columns.value.push(...cols);
  // 添加操作栏 action
  if (isTopUnit())
    columns.value.push({
      fixed: 'right',
      width: '300px',
      title: '操作',
      key: 'actions',
      align: 'left',
      render(row) {
        return getActionBtn(row);
      },
    });
}
setColumns();

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          size: 'small',
          type: 'primary',
          ghost: true,
          onClick: () => {
            let { id, overdueDay, reminderObjList } = toRaw(row);
            modelForm.value.id = id;
            console.log(overdueDay, overdueDay.split('天')[0], 'overdueDay');
            modelForm.value.overdueDay = overdueDay ? Number(overdueDay.split('天')[0]) : Number(overdueDay);
            modelForm.value.reminderObjList = reminderObjList || [];
            showModal.value = true;
          },
        },
        { default: () => '编辑' }
      ),
    ],
    [
      h(
        NButton,
        {
          size: 'small',
          type: 'error',
          ghost: true,
          onClick: () => handleDelete(row),
        },
        { default: () => '删除' }
      ),
    ],
  ];
  return useActionDivider(acList);
}

// 删除
function handleDelete(row: any) {
  $dialog.warning({
    title: '提示',
    content: '确认要删除隐患超期时间吗？删除后不可恢复',
    positiveButtonProps: { type: 'primary', color: '#527cff' },
    positiveText: '确定',
    negativeText: '取消',
    transformOrigin: 'center',
    onPositiveClick: async () => {
      await deleteOverdueSetting({ id: row.id });
      getTableData();
    },
  });
}

function getTableData() {
  const params = {
    pageNo: 1,
    pageSize: -1,
    unitId: props.unitId,
  };

  search(getOverdueSettingList(params)).then((res: any) => {
    tableData.value = res.data?.rows || [];
    tableData.value?.forEach((item: any) => {
      item.overdueDay = item.overdueDay ? item.overdueDay + '天' : item.overdueDay;
    });
    updateTotal(res.data.total || 0);
  });
}

function getRoleListData() {
  getRoleList({ orgCode: props.unitId }).then((res: any) => {
    roleList.value = res.data;
  });
}

function getTableDataWrap(data: IObj<any>) {
  pagination.page = 1;
  getTableData();
}
getTableData();
getRoleListData();
defineOptions({ name: 'checkTampTableComp' });
defineExpose({ getTableDataWrap, getTableData });
</script>
<style module lang="scss"></style>
