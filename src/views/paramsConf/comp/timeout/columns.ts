import { DataTableColumn } from 'naive-ui';

export const cols: DataTableColumn[] = [
  {
    title: '序号',
    key: 'index',
    align: 'left',
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '超期时间',
    key: 'overdueDay',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },

  {
    title: '提醒对象',
    key: 'reminderObjNameList',
    align: 'left',
    render: (_: any) => {
      return _.reminderObjNameList && _.reminderObjNameList.length ? _.reminderObjNameList.join('，') : '';
    },
    ellipsis: {
      tooltip: true,
    },
  },
];
