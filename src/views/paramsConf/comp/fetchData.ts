import { $http } from '@tanzerfe/http';
import { api } from '@/api';
import { IObj } from '@/types';
import { ITaskExecutionStatusStatisticsData } from '@/views/inspection-planning/planned-management/type';

// ====================检查类型设定========================
export function getCheckSettingList(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.getCheckSettingList, data);
  return $http.post(url, {
    data: { _cfg: { showTip: true } },
  });
}

export function addCheckSetting(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.addCheckSetting);
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: true, okTipContent: '新增成功' }, ...data },
  });
}

export function deleteCheckSetting(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.deleteCheckSetting, { ...query });
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: true, okTipContent: '删除成功' } },
  });
}

export function stopCheckSetting(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.stopCheckSetting, { ...query });
  return $http.post(url, {
    data: { _cfg: {} },
  });
}

export function updateCheckSetting(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.updateCheckSetting);
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: true, okTipContent: '修改成功' }, ...data },
  });
}

// ====================隐患超期时间设置========================

export function getOverdueSettingList(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.getOverdueSettingList);
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}

export function addOverdueSetting(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.addOverdueSetting);
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: true, okTipContent: '新增成功' }, ...data },
  });
}

export function deleteOverdueSetting(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.deleteOverdueSetting, { ...query });
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: true, okTipContent: '删除成功' } },
  });
}

export function updateOverdueSetting(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.updateOverdueSetting);
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: true, okTipContent: '修改成功' }, ...data },
  });
}

// ====================物联网信号设置========================

export function iotSignalPageList(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.name.paramsConf.iotSignalPageList);
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}
export function addIotSignalCode(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.name.paramsConf.addIotSignalCode);
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}

// 信号设置下载列表
export function monitorEventConfigList(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.name.paramsConf.monitorEventConfigList);
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}
export function removeIotSignalCode(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.name.paramsConf.removeIotSignalCode, { ...query });
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: true, okTipContent: '删除成功' } },
  });
}

// ====================隐患级别设置========================

export function getGradeList(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.getGradeList);
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}

export function updateGrade(data: any) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.updateGrade);
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}

export function deleteGrade(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.deleteGrade);
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: true, okTipContent: '删除成功' }, ...data },
  });
}

// ====================默认检查项配置========================

export function getDefaultConfigList(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.getDefaultConfigList);
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}

export function saveOrUpdate(data: any) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.saveOrUpdate);
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}

export function deleteDefaultConfig(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.deleteDefaultConfig, { ...query });
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: true, okTipContent: '删除成功' } },
  });
}

export function getRoleList(data: any) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.getRoleList, data);
  return $http.post(url, {
    data: { _cfg: { showTip: true } },
  });
}

export function getPlanTaskCount(data: any) {
  const url = api.getUrl(api.type.hazard, api.sh.planTask.getPlanTaskCount, data);
  return $http.post<ITaskExecutionStatusStatisticsData>(url, {
    data: { _cfg: { showTip: true } },
  });
}

// ====================隐患分类========================
export function getTreeList(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.dyb.hazardClassify.getTreeList);
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}
// 删除隐患分类节点
export function getDeleteTreeList(data: any) {
  const url = api.getUrl(api.type.hazard, api.dyb.hazardClassify.getDeleteTreeList);
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: true, okTipContent: '删除成功' }, ...data },
  });
}
// 新增隐患库分类
export function getAddTreeList(data: any) {
  const url = api.getUrl(api.type.hazard, api.dyb.hazardClassify.getAddTreeList);
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}
// 编辑隐患库分类
export function getEditTreeList(data: any) {
  const url = api.getUrl(api.type.hazard, api.dyb.hazardClassify.getEditTreeList);
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: true, okTipContent: '修改成功' }, ...data },
  });
}
// 移动隐患库分类
export function getMoveTreeList(data: any) {
  const url = api.getUrl(api.type.hazard, api.dyb.hazardClassify.getMoveTreeList);
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}

// 获取单位列表
export function hazardCheckRangeGetOrgTree(data: any) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.hazardCheckRangeGetOrgTree, data);
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}

// 新增单位打卡设置
export function hazardCheckRangeSaveOrUpdate(data: any) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.hazardCheckRangeSaveOrUpdate);
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}

// 范围打卡分页
export function hazardCheckRangeList(data: any) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.hazardCheckRangeList);
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}

// 获取单个打卡详情
export function hazardCheckRangeDetail(data: any) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.hazardCheckRangeDetail, data);
  return $http.post(url, {
    data: { _cfg: { showTip: true } },
  });
}

// 获取单个打卡详情
export function hazardCheckRangeDelete(data: any) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.hazardCheckRangeDelete, data);
  return $http.post(url, {
    data: { _cfg: { showTip: true } },
  });
}

//
export function getHazardMisSetting(data: any) {
  const url = api.getUrl(api.type.hazard, api.hazardManagement.getHazardMisSetting);
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}

export function hazardMisSettingAdd(data: any) {
  const url = api.getUrl(api.type.hazard, api.hazardManagement.addHazardMisSetting);
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}

export function deleteHazardMisSetting(data: any) {
  const url = api.getUrl(api.type.hazard, api.hazardManagement.deleteHazardMisSetting);
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}
