<template>
  <div>
    <AppHeader title="一张图模块设置" blueIcon class="mt-10 mb-5"></AppHeader>
  </div>
  <n-data-table
    class="com-table"
    remote
    striped
    :bordered="false"
    :columns="columns"
    :data="tableData"
    :pagination="false"
    :min-height="150"
    :max-height="500"
    :render-cell="useEmptyCell"
    :loading="loading"
  ></n-data-table>
</template>

<script lang="ts" setup>
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { NCheckboxGroup, NCheckbox } from 'naive-ui';
import { render, h } from 'vue';
import { getHazardMisSetting, hazardMisSettingAdd, deleteHazardMisSetting } from '../fetchData';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { ref } from 'vue';
import { useStore } from '@/store';
import { isTopUnit } from '@/views/inspection-planning/planned-management/Details/utils';

const [loading, search] = useAutoLoading();
const userStore = useStore();
const checkedList = ref([]);

const getConfigData = async () => {
  const res: any = await search(
    getHazardMisSetting({
      createBy: userStore.userInfo.id,
      unitId: userStore.userInfo.topUnitId,
    })
  );
  checkedList.value = res.data;
  tableData.value.forEach((item: any) => {
    //遍历选中状态
    item.list.forEach((item2: any) => {
      item2.value = res.data.includes(item2.code);
      if (
        item2.checkedAll &&
        res.data.includes('101') &&
        res.data.includes('102') &&
        res.data.includes('103') &&
        res.data.includes('104')
      ) {
        item2.indeterminate = false;
      } else if (
        item2.checkedAll &&
        (res.data.includes('102') || res.data.includes('103') || res.data.includes('104'))
      ) {
        item2.indeterminate = true;
      }
    });
  });
};
getConfigData();

const columns = [
  {
    title: '模块名称',
    key: 'moduleName',
    align: 'left',
  },
  {
    title: '功能',
    key: 'value',
    titleAlign: 'left',
    render: (row: any) => {
      return row.list.map((item: any) => {
        return h(NCheckbox, {
          label: item.name,
          disabled: !isTopUnit(),
          value: item.code,
          checked: item.value,
          indeterminate: item.indeterminate,
          onUpdateChecked: async (value: any) => {
            item.value = value;
            if (item.checkedAll) {
              //全选标识
              checkedList.value = row.list.map((item2: any) => {
                item2.value = value;
                return item2.code;
              });
              item.indeterminate = false;
            }
            //如果有一项未勾选，则全选标识的那一项不勾选
            if (row.list.some((item2: any) => !item2.value)) {
              row.list.forEach((item2: any) => {
                if (item2.checkedAll) {
                  item2.value = false;
                  item2.indeterminate = true;
                }
              });
            }
            //全选标识剩余的三项都是选中状态，则全选标识的value为true
            if (row.list.filter((item2: any) => item2.value).length === 3) {
              row.list.forEach((item2: any) => {
                if (item2.checkedAll) {
                  item2.value = true;
                  item2.indeterminate = false;
                }
              });
            }
            //如果三项都未勾选，则全选标识的那一项不勾选
            if (row.list.every((item2: any) => !item2.value)) {
              row.list.forEach((item2: any) => {
                if (item2.checkedAll) {
                  item2.value = false;
                  item2.indeterminate = false;
                }
              });
            }
            //如果勾选，则添加，如果未勾选，则删除
            if (value) {
              let checkedCode = row.list.filter((item: any) => item.value).map((item: any) => item.code);

              await hazardMisSettingAdd({
                unitId: userStore.userInfo.topUnitId,
                createBy: userStore.userInfo.id,
                menuCodes: checkedCode.toString(),
              });
            } else {
              let checkedCode = row.list.filter((item: any) => !item.value).map((item: any) => item.code);
              await deleteHazardMisSetting({
                unitId: userStore.userInfo.topUnitId,
                createBy: userStore.userInfo.id,
                menuCodes: checkedCode.toString(),
              });
            }
            // getConfigData();
          },
        });
      });
    },
  },
];
const tableData = ref([
  {
    moduleName: '隐患智能监测',
    list: [
      {
        name: '整体模块功能',
        code: '101',
        checkedAll: true, //全选标识
        value: false,
        indeterminate: false,
      },
      {
        name: 'AI视频分析',
        code: '102',
        value: false,
      },
      {
        name: '视频智能巡检',
        code: '103',
        value: false,
      },
      {
        name: '物联网实时监测',
        code: '104',
        value: false,
      },
    ],
  },
  {
    moduleName: '隐患随手拍',
    list: [
      {
        name: '整体模块功能',
        code: '201',
        value: false,
      },
    ],
  },
  {
    moduleName: '近期排查任务',
    list: [
      {
        name: '整体模块功能',
        code: '301',
        value: false,
      },
    ],
  },
  {
    moduleName: '正在执行任务',
    list: [
      {
        name: '整体模块功能',
        code: '401',
        value: false,
      },
    ],
  },
  {
    moduleName: '待处置隐患',
    list: [
      {
        name: '整体模块功能',
        code: '501',
        value: false,
      },
    ],
  },
  {
    moduleName: '隐患位置分布',
    list: [
      {
        name: '整体模块功能',
        code: '601',
        value: false,
      },
    ],
  },
  {
    moduleName: '隐患分级分类',
    list: [
      {
        name: '整体模块功能',
        code: '701',
        value: false,
      },
    ],
  },
  {
    moduleName: '隐患整改时长',
    list: [
      {
        name: '整体模块功能',
        code: '801',
        value: false,
      },
    ],
  },
  {
    moduleName: '隐患点TOP',
    list: [
      {
        name: '整体模块功能',
        code: '901',
        value: false,
      },
    ],
  },
]);
defineOptions({ name: 'misGisConfig' });
</script>

<style scoped lang="scss"></style>
