<!--
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-10-12 14:04:02
 * @LastEditors: fangweiwei <EMAIL>
 * @LastEditTime: 2025-01-08 10:14:30
 * @FilePath: /隐患一张图/ehs-hazard/src/views/paramsConf/comp/hazardClassify/tree.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <AppHeader :title="props.title" blueIcon class="mt-10 mb-5">
    <template #right v-if="isTopUnit()">
      <n-button type="primary" @click="addPopup" :style="{ height: toVh(30), fontSize: toVh(16) }">新增</n-button>
    </template>
  </AppHeader>
  <div class="wrapTree">
    <!-- placement="top-start" -->
    <!-- :title="titleText" -->
    <n-tree
      class="tree-scroll mt-8"
      show-line
      selectable
      :data="treeData"
      key-field="id"
      label-field="label"
      children-field="children"
      :node-props="nodeProps"
      block-line
      :show-irrelevant-nodes="false"
      :render-label="nodelabel"
      :render-suffix="nodesuffix"
    />
  </div>
  <!-- :render-suffix="nodesuffix" -->
  <!-- 新增分类弹窗 -->
  <n-modal
    v-model:show="showModal"
    :mask-closable="false"
    class="models w_500"
    :show-icon="false"
    preset="card"
    :autoFocus="false"
  >
    <template #header>
      <div class="flex flex-row items-center">
        <img class="w-[17px] h-[12px] mr-[20px]" src="@/components/header/assets/icon-title-arrow3.png" />
        <div class="text-[16px] text-[#222222] font-bold">新增隐患分类</div>
      </div>
    </template>
    <n-form
      style="padding: 24px"
      ref="formRef"
      :rules="rules"
      layout="vertical"
      :label-col="{ style: { width: '250px' } }"
      :model="formState"
    >
      <div class="layout-wrap">
        <n-form-item label="分类名称" path="troubleFrom">
          <n-input placeholder="请输入分类名称" v-model:value="formState.troubleFrom" show-count :maxlength="100" />
        </n-form-item>
        <span v-if="nodeShow" class="ml-1" style="color: red; font-size: 12px">注：此功能为创建一级分类</span>
      </div>
    </n-form>
    <template #action>
      <div class="flex justify-end items-center">
        <n-button style="margin-right: 20px" type="primary" ghost @click="cancel"> 取消 </n-button>
        <n-button type="primary" :loading="submitLoading" @click="handleOk"> 保存 </n-button>
      </div>
    </template>
  </n-modal>
  <!-- <cationAdd
    v-model:isShowEdit="isShowEdit"
    @update="refreshUpdata"
    :rowMessage="rowMessage"
    :TreeData="treeData"
    @close="isShowEdit = false"
  ></cationAdd> -->
</template>
<script setup lang="ts">
import { h, ref, nextTick } from 'vue';
import { NButton, NInput, NIcon, useMessage, NTooltip } from 'naive-ui';
// import { AnOutlinedInfoCircle } from '@kalimahapps/vue-icons';
import { $dialog } from '@/common/shareContext/useDialogCtx.ts';
import type { TreeOption } from 'naive-ui';
import {
  CaAdd as IconAdd,
  AnOutlinedDelete as IconDelete,
  AnOutlinedEdit as IconEdit,
  AkArrowUp as IconUp,
  AkArrowDown as IconDown,
} from '@kalimahapps/vue-icons';
import {
  getAddTreeList,
  getTreeList,
  getDeleteTreeList,
  getEditTreeList,
  getMoveTreeList,
} from '@/views/paramsConf/comp/fetchData';
import { isTopUnit } from '@/views/inspection-planning/planned-management/Details/utils';
import { toVw, toVh } from '@/utils/fit';
const message = useMessage();

const props = defineProps({
  title: {
    type: String,
    default: '隐患分类',
  },
  unitId: {
    type: String,
    default: '',
  },
});
const formState: any = ref({
  // rootFlag: '2',
  troubleFrom: '',
});
const rules: any = {
  troubleFrom: [
    {
      required: true,
      message: '请输入分类名称',
      trigger: ['blur'],
    },
  ],
};

const showModal = ref<boolean>(false);
const nodeShow = ref<boolean>(false);
const treeData = ref([]);
const defaultIds = ref<any[]>([]);
const emits = defineEmits(['optionlabel']);
const submitLoading: any = ref(false);
const formRef = ref();
function cancel() {
  showModal.value = false;
  formState.value.troubleFrom = '';
}
function addPopup() {
  showModal.value = true;
  nodeShow.value = true;
  rowMessage.value.parentId = '';
}
// 提交
function handleOk() {
  let dataName;
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      submitLoading.value = true;
      dataName = {
        // parentId: formState.value.rootFlag == '1' ? rowMessage.value.parentId : '0',
        // rootFlag: formState.value.rootFlag == '1' ? 1 : 0,
        parentId: rowMessage.value.parentId ? rowMessage.value.parentId : '0',
        className: formState.value.troubleFrom,
        // classSort: treeData.value.length,
        classSort: 0,
        unitId: props.unitId,
      };
      console.log(dataName);
      getAddTreeList(dataName)
        .then((res: any) => {
          if (res.code == 'success') {
            submitLoading.value = false;
            showModal.value = false;
            formState.value.troubleFrom = '';
            refreshUpdata();
            message.success('保存成功');
            // formState.value.rootFlag = '2';
          }
        })
        .catch(() => {
          submitLoading.value = false;
        });
    } else {
      console.log(errors);
    }
  });
}

// 对树状图进行编辑
const inputRef = ref();
const nodelabel = ({ option }: { option: TreeOption }) => {
  // const list = [];
  // if (option.head != 'head' && option.head != 'first') {
  //   list.push(
  //     h(
  //       NIcon,
  //       {
  //         onClick: (e: any) => movenode(option, 'pre'), //向上移动节点函数
  //       },
  //       { default: () => h(IconUp) }
  //     )
  //   );
  // }
  // if (option.head != 'last' && option.head != 'first') {
  //   list.push(
  //     h(
  //       NIcon,
  //       {
  //         onClick: (e: any) => movenode(option, 'next'), //向下移动节点函数
  //       },
  //       { default: () => h(IconDown) }
  //     )
  //   );
  // }
  // if (option) {
  //   list.push(
  //     h(
  //       'div',
  //       { class: 'node', style: { width: '100%' } },
  //       option.isedit == true
  //         ? h(NInput, {
  //             autofocus: true,
  //             ref: inputRef,
  //             size: 'small',
  //             showCount: true,
  //             maxlength: 100,
  //             value: option.label,
  //             onUpdateValue: (v) => {
  //               option.label = v;
  //             },
  //             onChange: () => {
  //               option.isedit = false;
  //             },
  //             onBlur: () => {
  //               option.isedit = false;
  //               const list = [
  //                 {
  //                   id: option.id,
  //                   classSort: option.classSort,
  //                   className: option.label,
  //                 },
  //               ];
  //               getEditTreeList(list).then((res: any) => {
  //                 refreshUpdata();
  //               });
  //             },
  //           })
  //         : option.label.length > 20
  //           ? option.label.substring(0, 19) + '...'
  //           : option.label
  //     ),
  //     h(
  //       NIcon,
  //       {
  //         onClick: (e: any) => {
  //           deltree(option), e.stopPropagation(); //节点删除函数
  //         },
  //       },
  //       { default: () => h(IconDelete) }
  //     ),
  //     h(
  //       NIcon,
  //       {
  //         onClick: (e: any) => addnode(option), //自定义新增节点函数
  //       },
  //       { default: () => h(IconAdd) }
  //     ),
  //     h(
  //       NIcon,
  //       {
  //         onClick: (e: any) => editnode(option), //编辑节点函数
  //       },
  //       { default: () => h(IconEdit) }
  //     )
  //   );
  // }
  // return h('div', {}, list);
  // return `${option.label} ${h('div', {}, list)}`;

  return h(
    NTooltip,
    {
      disabled: option.label.length > 20 ? false : true,
    },
    {
      trigger: () =>
        option.isedit == true
          ? h(NInput, {
              autofocus: true,
              ref: inputRef,
              size: 'small',
              showCount: true,
              maxlength: 100,
              value: option.label,
              onUpdateValue: (v) => {
                option.label = v;
              },
              onChange: () => {
                option.isedit = false;
              },
              onBlur: () => {
                option.isedit = false;
                // const list = [
                //   {
                //     id: option.id,
                //     classSort: option.classSort,
                //     className: option.label,
                //     parentId: option.parentId,
                //   },
                // ];
                let data = {
                  id: option.id,
                  classSort: option.classSort,
                  className: option.label,
                  parentId: option.parentId,
                };
                getEditTreeList(data)
                  .then((res: any) => {
                    refreshUpdata();
                  })
                  .catch(() => {
                    refreshUpdata();
                  });
              },
            })
          : option.label.length > 20
            ? option.label.substring(0, 19) + '...'
            : option.label,
      default: () => option.label,
    }
  );
};

//节点后缀渲染
function nodesuffix({ option }: { option: TreeOption }) {
  const list = [];
  if (!isTopUnit()) return;
  if (option.head != 'head' && option.head != 'first') {
    list.push(
      h(
        NIcon,
        {
          onClick: (e: any) => movenode(option, 'pre'), //向上移动节点函数
        },
        { default: () => h(IconUp) }
      )
    );
  }
  if (option.head != 'last' && option.head != 'first') {
    list.push(
      h(
        NIcon,
        {
          onClick: (e: any) => movenode(option, 'next'), //向下移动节点函数
        },
        { default: () => h(IconDown) }
      )
    );
  }
  if (option) {
    list.push(
      h(
        NIcon,
        {
          onClick: (e: any) => {
            deltree(option), e.stopPropagation(); //节点删除函数
          },
        },
        { default: () => h(IconDelete) }
      ),
      h(
        NIcon,
        {
          onClick: (e: any) => addnode(option), //自定义新增节点函数
        },
        { default: () => h(IconAdd) }
      ),
      h(
        NIcon,
        {
          onClick: (e: any) => editnode(option), //编辑节点函数
        },
        { default: () => h(IconEdit) }
      )
    );
  }
  return h('div', {}, list);
}

const moveItem = (itemId: any, type: any) => {
  const findParent = (nodes: any, id: any) => {
    for (let i = 0; i < nodes.length; i++) {
      if (nodes[i].id === id) {
        const parentIndex = type == 'pre' ? i - 1 : i + 1;
        if (parentIndex >= 0 && parentIndex < nodes.length) {
          console.log(nodes[parentIndex]);
          return nodes[parentIndex];
        }
      }
      if (nodes[i].children && nodes[i].children.length > 0) {
        const childResult: any = findParent(nodes[i].children, id);
        if (childResult) {
          return childResult;
        }
      }
    }
    return null;
  };
  console.log(itemId, type, '=====================');
  return findParent(treeData.value, itemId);
};

// 移动节点
function movenode(option: any, type: any) {
  console.log('🚀 ~ movenode ~ 当前数据option:', option);
  console.log(moveItem(option.id, type), '==============================获取上/下数据');
  const getUporDownItem = moveItem(option.id, type);
  // console.log(getUporDownItem.classSort, '==============classSort');
  // treeData.value
  // type =pre 向下移 ： 向上移
  getMoveTreeList({
    unitId: props.unitId,
    id: option.id,
    // classSort: type == 'pre' ? option.classSort - 1 : option.classSort + 1,
    classSort: getUporDownItem.classSort,
  }).then((res: any) => {
    if (res && res.code == 'success') {
      refreshUpdata();
    }
  });
}

// 编辑子节点
function editnode(val: any) {
  val.isedit = true;
  nextTick(() => {
    inputRef.value.focus();
  });
}

//删除节点
function deltree(option: any) {
  if (option.children && option.children.length) {
    message.warning('当下根节点下存在子节点，无法删除');
  } else {
    $dialog.warning({
      title: '提示',
      content: '确定删除该节点？删除后不可恢复',
      positiveButtonProps: { type: 'primary', color: '#527cff' },
      positiveText: '确定',
      negativeText: '取消',
      transformOrigin: 'center',
      onPositiveClick: async () => {
        getDeleteTreeList({ id: option.id }).then((res: any) => {
          refreshUpdata();
        });
      },
    });
  }
}

const titleText = ref<string>('');
//节点点击事件
const key = ref();
const nodeProps = ({ option }: { option: TreeOption }) => {
  return {
    // onmouseover() {
    //   console.log(option.label);
    //   titleText.value = option.label as string;
    //   // $toast.success(`[Click] ${option[props.labelField]}`);
    // },
    onClick() {
      emits('optionlabel', option.label);
      key.value = option.id;
    },
    // ondblclick() {
    //   //双击事件
    //   option.isedit = true;
    //   nextTick(() => {
    //     inputRef.value.focus();
    //   });
    // },
  };
};

// 新增分类弹窗
// const isShowEdit = ref<boolean>(false);
let rowMessage: any = ref({});
function addnode(val: any) {
  nodeShow.value = false;
  if (val) {
    rowMessage.value.areaTreeDataLength = val.children && val.children.length;
    rowMessage.value.parentId = val.id;
    rowMessage.value.unitId = props.unitId;
  }
  console.log(rowMessage.value);
  showModal.value = true;
}
// 更新
function refreshUpdata() {
  getTreeData();
  // emits('refresh');
}

// 渲染树状图节点
function getTreeData() {
  getTreeList({ unitId: props.unitId, parentId: '0' }).then((res: any) => {
    res.data && res.data.length ? traverse(res.data) : [];
    treeData.value = createData(res.data);
    console.log('🚀 ~ getTreeList ~ treeData.value:', treeData.value);
  });
}

function createData(data: any) {
  return data.map((item: any, index: any) => {
    if (data.length > 1 && data[0].id == item.id) {
      item.head = 'head';
    }
    if (data.length > 1 && data[data.length - 1].id == item.id) {
      item.head = 'last';
    }
    if (data.length == 1) {
      item.head = 'first';
    }
    return {
      label: item.className,
      id: item.id,
      parentId: item.parentId,
      head: item.head,
      classSort: item.classSort,
      children: item.children && item.children.length ? createData(item.children) : null,
    };
  });
}

function traverse(items: any) {
  items.forEach((item: any) => {
    defaultIds.value.push(item.id); // 将当前项的id添加到ids数组中
    if (item.children && item.children.length > 0) {
      // 如果当前项有children，则递归遍历它们
      traverse(item.children);
    }
  });
  // console.log(defaultIds.value);
}

getTreeData();
</script>
<style lang="scss" scoped>
::v-deep .n-icon {
  margin: 0 4px;
  font-size: 17px;
  color: rgba(59, 130, 246);
}

// ::v-deep .n-base-icon {
// width: 80px;
// height: 110px;
// }
::v-deep .n-tree-node--selected {
  background: rgba(82, 124, 255, 0.1);
  padding: 4px 0;

  .n-tree-node-content__text {
    color: #527cff;
  }
}

.wrapTree {
  // background: #e4f2ff;
  .tree-scroll {
    min-height: calc(100vh - 372px);
    max-height: calc(100vh - 172px);
    overflow-y: scroll;
  }
}
</style>
