<template>
  <ComDrawerA
    title="新增分类"
    :autoFocus="false"
    :footerPaddingBottom="25"
    :maskNeedClosable="false"
    :show="props.isShowEdit"
    show-action
    @handleNegative="close"
    class="!w-[430px]"
  >
    <n-form
      style="padding: 24px"
      ref="formRef"
      :rules="rules"
      layout="vertical"
      :label-col="{ style: { width: '250px' } }"
      :model="formState"
    >
      <div class="layout-wrap">
        <n-form-item label="分类名称" path="troubleFrom">
          <n-input placeholder="请输入" v-model:value="formState.troubleFrom" show-count :maxlength="100" />
        </n-form-item>
        <n-form-item label="是否为子节点">
          <n-radio-group v-model:value="formState.rootFlag">
            <n-radio value="1">是</n-radio>
            <n-radio value="2">否</n-radio>
          </n-radio-group>
          <!-- <n-tooltip trigger="hover">
            <template #trigger>
              <label class="trigger-label"
                >是否为根节点&nbsp;&nbsp;&nbsp;<AnOutlinedInfoCircle
                  :style="{ color: '#0099ff', transform: 'scale(1.1)' }"
              /></label>
            </template>
            根节点无法添加子级
          </n-tooltip> -->
        </n-form-item>
      </div>
    </n-form>
    <template #footer>
      <div style="display: flex; justify-content: flex-end; padding: 0 24px">
        <n-button style="margin-right: 20px" type="info" @click="close"> 取消 </n-button>
        <n-button type="primary" :loading="submitLoading" @click="handleOk"> 保存 </n-button>
      </div>
    </template>
  </ComDrawerA>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { AnOutlinedInfoCircle } from '@kalimahapps/vue-icons';
import { getAddTreeList } from '@/views/paramsConf/comp/fetchData';
import ComDrawerA from '@/components/drawer/ComDrawerA.vue';
const props: any = defineProps({
  isShowEdit: {
    type: Boolean,
    default: false,
  },
  rowMessage: {
    type: Object,
    default: Function,
  },
  TreeData: {
    type: Array,
    default: () => [],
  },
});
const formState: any = ref({
  rootFlag: '2',
  troubleFrom: '',
});
const rules: any = {
  troubleFrom: [
    {
      required: true,
      message: '请输入分类名称',
      trigger: ['blur'],
    },
  ],
};
const emit = defineEmits(['update', 'close']);
const close = () => {
  emit('close', false);
};

const submitLoading: any = ref(false);
const formRef = ref();
// 提交
function handleOk() {
  let dataName;
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      submitLoading.value = true;
      dataName = {
        parentId: formState.value.rootFlag == '1' ? props.rowMessage.parentId : '0',
        className: formState.value.troubleFrom,
        classSort: props.TreeData.length,
        rootFlag: formState.value.rootFlag == '1' ? 1 : 0,
        unitId: props.rowMessage.unitId,
      };
      getAddTreeList(dataName)
        .then((res: any) => {
          if (res.code == 'success') {
            submitLoading.value = false;
            formState.value.troubleFrom = '';
            formState.value.rootFlag = '2';
            emit('update', true);
            emit('close', false);
          }
        })
        .catch(() => {
          submitLoading.value = false;
          formState.value.troubleFrom = '';
        });
    } else {
      console.log(errors);
    }
  });
}

onMounted(() => {});
</script>

<style lang="scss" scoped>
::v-deep .n-form-item .n-form-item-blank {
  display: block;
  .trigger-label {
    display: flex;
    align-items: center;
  }
}
.flexjust {
  display: flex;
  justify-content: space-between;
}

::v-deep .ant-image {
  border: 1px solid #ccc;
  margin-right: 5px;
  overflow: hidden;
}
.flex {
  display: flex;
  align-items: center;
}
</style>
