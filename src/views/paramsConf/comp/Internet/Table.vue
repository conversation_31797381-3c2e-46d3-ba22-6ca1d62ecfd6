<template>
  <AppHeader title="物联网信号设置" blueIcon class="mt-10 mb-5">
    <template #right v-if="isTopUnit()">
      <n-button type="primary" @click="handleAdd" :style="{ height: toVh(30), fontSize: toVh(16) }">新增</n-button>
    </template>
  </AppHeader>
  <n-flex justify="flex-end"> </n-flex>
  <n-data-table
    class="com-table"
    remote
    :bordered="false"
    :loading="loading"
    :columns="columns"
    :data="tableData"
    :pagination="false"
    :row-key="(row: any) => row.id"
    :min-height="150"
    :max-height="530"
  />
  <n-modal
    v-model:show="showModal"
    :show-icon="false"
    preset="card"
    class="models w_500"
    :mask-closable="false"
    :autoFocus="false"
  >
    <template #header>
      <div class="flex flex-row items-center">
        <img class="w-[17px] h-[12px] mr-[20px]" src="@/components/header/assets/icon-title-arrow3.png" />
        <div class="text-[16px] text-[#222222] font-bold">新增物联网信号</div>
      </div>
    </template>
    <n-form ref="formRef" :model="modelForm" :rules="rules" class="p-[24px]">
      <n-form-item path="iotSignalName" label="选择物联网信号：">
        <n-input
          :value="modelForm.iotSignalName.split(',').join('\n')"
          readonly
          type="textarea"
          :autosize="{ minRows: 1, maxRows: 5 }"
          placeholder="请选择"
          @click="iotSignalNameClick"
        />
      </n-form-item>
      <n-form-item path="essentialFactorId" label="选择隐患库：">
        <n-select
          placeholder="请选择隐患库"
          :render-label="renderLabel"
          v-model:value="modelForm.essentialFactorId"
          :options="hiddenDangeOptions"
          @update:value="selectChange"
          clearable
        />
      </n-form-item>

      <div class="search-title" @click="handleHiddenDange">隐患库查询</div>

      <n-form-item path="essentialFactorClassItemId" label="隐患库描述：">
        <n-input
          v-model:value="desc"
          placeholder="请选择"
          disabled
          type="textarea"
          :autosize="{
            minRows: 3,
            maxRows: 5,
          }"
        />
      </n-form-item>
    </n-form>
    <template #footer>
      <n-row :gutter="[0, 24]">
        <n-col :span="24">
          <div style="display: flex; justify-content: flex-end">
            <n-button @click="handleValidateCancel" style="margin-right: 10px" type="primary" ghost> 取消 </n-button>
            <n-button type="primary" :disabled="submitLoading" :loading="submitLoading" @click="handleValidateClick">
              保存
            </n-button>
          </div>
        </n-col>
      </n-row>
    </template>
  </n-modal>

  <n-modal v-model:show="showSignal" title="物联网信号" preset="card" style="width: 800px">
    <!-- @after-leave="handleSignalCancel" -->

    <n-layout style="height: 360px; border-bottom: 1px solid rgb(239, 239, 245)" has-sider>
      <!-- <n-layout-sider content-style="padding: 0 20px;" :native-scrollbar="false" bordered>
        <div
          class="sider-item"
          :class="activeLeft == item.eventType ? 'active-sider' : ''"
          v-for="(item, index) of signalLeftList"
          :key="index"
          @click="eventTypeClick(item.eventType)"
        >
          {{ item.name }}
        </div>
      </n-layout-sider> -->
      <n-layout content-style="padding:0 24px;" :native-scrollbar="false">
        <n-spin :show="signalLoading">
          <n-checkbox-group v-model:value="cities" style="min-height: 340px" @update:value="checkBoxUpdate">
            <div v-for="item of signalRightList" :key="item.signalCode" style="margin-bottom: 10px">
              <n-checkbox
                :disabled="item.checked == '1'"
                size="large"
                :value="item.signalCode + '_' + item.signalName + ' - ' + item.signalChildName"
                :label="item.signalName + ' - ' + item.signalChildName"
              />
            </div>
          </n-checkbox-group>
        </n-spin>
      </n-layout>
    </n-layout>

    <template #footer>
      <n-row :gutter="[0, 24]">
        <n-col :span="24">
          <div style="display: flex; justify-content: flex-end">
            <n-button @click="handleSignalCancel" style="margin-right: 20px"> 取消 </n-button>
            <n-button type="primary" @click="handleSignalSubmit"> 确定 </n-button>
          </div>
        </n-col>
      </n-row>
    </template>
  </n-modal>

  <n-modal
    class="models"
    v-model:show="showRiskLibrary"
    preset="card"
    style="width: 1070px"
    @negative-click="showRiskLibrary = false"
    :autoFocus="false"
  >
    <template #header>
      <div class="flex flex-row items-center">
        <img class="w-[17px] h-[12px] mr-[20px]" :src="arrow3Icon" />
        <div class="text-[16px] text-[#222222] font-bold">隐患选择</div>
      </div>
    </template>
    <Security :id="modelForm.essentialFactorId" @sendChooseId="getDesc" />
    <!-- <riskLibraryDetail isChoose :id="modelForm.essentialFactorId" @getDesc="getDesc" /> -->
  </n-modal>
</template>

<script setup lang="ts">
import { h, ref, VNode, watchEffect, toRaw } from 'vue';
import { DataTableColumns, NButton, FormInst, FormRules, FormItemRule, useMessage } from 'naive-ui';
import { cols } from './columns';
import {
  iotSignalPageList,
  monitorEventConfigList,
  addIotSignalCode,
  removeIotSignalCode,
} from '@/views/paramsConf/comp/fetchData';

import { getEssentialFactor } from '@/views/risk-database/risk-library/fetchData';
import riskLibraryDetail from '@/views/risk-database/risk-library/comp/detail/index.vue';
import Security from '@/views/hidden-dangers-photo/comp/security/Security.vue';
import arrow3Icon from '@/components/header/assets/icon-title-arrow3.png';

import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { $dialog } from '@/common/shareContext/useDialogCtx.ts';
import { toVw, toVh } from '@/utils/fit';
import { useStore } from '@/store/index.ts';
import { isTopUnit } from '@/views/inspection-planning/planned-management/Details/utils';
const { userInfo } = useStore();

const emits = defineEmits(['action']);

const modelForm = ref({
  id: '',
  essentialFactorClassId: '', //隐患分类ID
  essentialFactorClassItemId: '', //隐患分类检查项ID
  essentialFactorId: null, //隐患隐患库ID
  iotSignalCode: '', //物联网信号编码
  iotSignalName: '', //物联网信号名称
});
const desc = ref<string>(''); //隐患库描述

const message = useMessage();
const formRef = ref<FormInst | null>(null);
const rules: FormRules = {
  iotSignalName: [
    {
      type: 'string',
      required: true,
      message: '请输入检查类型名称',
      trigger: ['change'],
    },
  ],
  essentialFactorId: [
    {
      required: true,
      message: '请选择隐患库',
      trigger: ['change', 'blur'],
    },
  ],
  essentialFactorClassItemId: [
    {
      required: true,
      message: '请选择隐患库',
      trigger: ['change', 'blur'],
    },
  ],
};

const [loading, search] = useAutoLoading(true);
const { pagination, updateTotal } = useNaivePagination(getTableData);

const columns = ref<DataTableColumns>([]);
const tableData = ref<any[]>();

const showModal = ref(false);

const hiddenDangeOptions = ref([]);
const renderLabel = (option: any) => {
  return h(
    'div',
    {
      title: option.label,
    },
    option.label.length > 25 ? option.label.substring(0, 25) + '...' : option.label
  );
};

function selectChange() {
  modelForm.value.essentialFactorClassId = '';
  modelForm.value.essentialFactorClassItemId = '';
  desc.value = '';
}

function handleHiddenDange() {
  if (!modelForm.value.essentialFactorId) return message.warning('请选择隐患库');
  showRiskLibrary.value = true;
}

function handleAdd() {
  resetFrom();
  citiesValue.value = [];
  showModal.value = true;
}

// 物联网信号
const showSignal = ref<boolean>(false);
const showRiskLibrary = ref<boolean>(false);
const cities = ref<any[]>([]);
// 已选择值
const citiesValue = ref<any[]>([]);
watchEffect(() => {
  if (!showModal.value) {
    handleValidateCancel();
    // handleSignalCancel()
  }
});
function iotSignalNameClick() {
  cities.value = citiesValue.value;
  showSignal.value = true;
}
const submitLoading = ref<boolean>(false);

function handleValidateClick(e: Event) {
  e.preventDefault();
  console.log(modelForm.value);

  formRef.value?.validate((errors) => {
    if (!errors) {
      submitLoading.value = true;
      let data = {
        ...modelForm.value,
        unitId: userInfo.unitId,
      };
      addIotSignalCode(data)
        .then(() => {
          message.success('新增成功');
          showModal.value = false;
          submitLoading.value = false;
          resetFrom();
          getTableData();
          getMonitorEventConfigList();
        })
        .catch(() => {
          submitLoading.value = false;
        });
    }
  });
}
function handleValidateCancel() {
  cities.value = citiesValue.value = [];
  showModal.value = false;
  resetFrom();
}

function resetFrom() {
  modelForm.value = {
    id: '',
    essentialFactorClassId: '', //隐患分类ID
    essentialFactorClassItemId: '', //隐患分类检查项ID
    essentialFactorId: null, //隐患隐患库ID
    iotSignalCode: '', //物联网信号编码
    iotSignalName: '', //物联网信号名称
  };
  desc.value = '';
}

function setColumns() {
  columns.value.push(...cols);
  // 添加操作栏 action
  if (isTopUnit())
    columns.value.push({
      fixed: 'right',
      width: '120',
      title: '操作',
      key: 'actions',
      align: 'left',
      render(row) {
        return getActionBtn(row);
      },
    });
}
setColumns();

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    // [
    //   h(
    //     NButton,
    //     {
    //       size: 'small',
    //       type: 'success',
    //       ghost: true,
    //       onClick: () => {
    //         modelForm.value = JSON.parse(JSON.stringify(row));
    //         let arr = row.iotSignalCode.split(',');
    //         citiesValue.value = arr.map((item: any, index: number) => {
    //           return `${item}_${row.iotSignalName.split(',')[index]}`;
    //         });
    //         console.log(citiesValue.value, 'citiesValue.value ');
    //         showModal.value = true;
    //       },
    //     },
    //     { default: () => '编辑' }
    //   ),
    // ],
    [
      h(
        NButton,
        {
          size: 'small',
          type: 'error',
          ghost: true,
          onClick: () => handleDelete(row),
        },
        { default: () => '删除' }
      ),
    ],
  ];
  return useActionDivider(acList);
}

// 删除检查项
function handleDelete(row: any) {
  const d = $dialog.warning({
    title: '提示',
    content: '确定删除物联网信号吗？删除不可恢复',
    positiveButtonProps: { type: 'primary', color: '#527cff' },
    positiveText: '确定',
    negativeText: '取消',
    transformOrigin: 'center',
    onPositiveClick: async () => {
      d.loading = true;
      return new Promise((resolve) => {
        removeIotSignalCode({ id: row.id })
          .then((res) => {
            getTableData();
            getMonitorEventConfigList();
            resolve(res);
          })
          .catch(() => {
            d.loading = false;
          });
      });
    },
  });
}
const selectedIot = ref([]);
function getTableData() {
  const params = {
    pageNo: 1,
    pageSize: -1,
    unitId: userInfo.topUnitId,
  };

  search(iotSignalPageList(params)).then((res: any) => {
    tableData.value = res.data.rows || [];
    tableData.value?.forEach((item) => {
      selectedIot.value.push(item.iotSignalCode);
    });
    updateTotal(res.data.total || 0);
  });
}

function getTableDataWrap(data: any) {
  pagination.page = 1;
  getTableData();
}
//物联网信号选择
const activeLeft = ref<number>(4);
const signalLoading = ref<boolean>(false);
const signalLeftList = ref<any[]>([
  {
    name: '火警',
    eventType: 1,
  },
  {
    name: '故障',
    eventType: 3,
  },
  {
    name: '隐患',
    eventType: 4,
  },
  {
    name: '预警',
    eventType: 2,
  },
  // {
  //   name: '预警',
  //   eventType: 5,
  // },
]);
const signalRightList = ref<any[]>([]);

function checkBoxUpdate(params: object) {
  console.log(params, 'params');
}

function eventTypeClick(type: number) {
  activeLeft.value = type;
  getMonitorEventConfigList();
}

function getMonitorEventConfigList() {
  signalLoading.value = true;
  signalRightList.value = [];
  monitorEventConfigList({
    // groupFields: 'eventType',
    // eventName: '火警',
    status: 0,
    eventType: activeLeft.value,
    returnFields: 'signalCode,signalName,signalChildName',
    unitId: userInfo.unitId,
  })
    .then((res: any) => {
      signalLoading.value = false;
      signalRightList.value = res.data;
    })
    .catch(() => {
      signalLoading.value = false;
    });
}
function init() {
  getEssentialFactor({
    status: 0,
    pageSize: 10000,
    pageNum: 1,
    unitId: userInfo.unitId,
    valid: 1, // 隐患数据库有效，非空表
  }).then((res: any) => {
    hiddenDangeOptions.value = res.data.rows?.map((item: any) => {
      item.label = item.elementName;
      item.value = item.id;
      return item;
    });
  });
}

function handleSignalCancel() {
  showSignal.value = false;
  activeLeft.value = 1;
  cities.value = [];
}
function handleSignalSubmit() {
  console.log(cities.value);
  citiesValue.value = cities.value;
  // modelForm.value.iotSignalName = cities.value?.map((item) => item.split('_')[1]).join('\n');
  modelForm.value.iotSignalName = cities.value?.map((item) => item.split('_')[1]).join(',');
  modelForm.value.iotSignalCode = cities.value?.map((item) => item.split('_')[0]).join(',');
  // modelForm.value.iotSignalCode = cities.value?.map((item) => item.signalCode).join(',');
  showSignal.value = false;
}

function getDesc(row: any) {
  console.log('🚀 ~ getDesc ~ row:', row);
  modelForm.value.essentialFactorClassId = row.essentialFactorClassId;
  modelForm.value.essentialFactorClassItemId = row.id;
  desc.value = row.hazardDesc;
  showRiskLibrary.value = false;
}

init();
getMonitorEventConfigList();
getTableData();

defineOptions({ name: 'checkTampTableComp' });
defineExpose({ getTableDataWrap, getTableData });
</script>
<style scoped lang="scss">
.sider-item {
  background-color: rgb(245, 245, 245);
  padding: 18px 24px;
  margin: 10px 0;
  border-radius: 5px;
  cursor: pointer;
}

.active-sider {
  background-color: rgb(0, 153, 255);
  color: #fff;
}

.search-title {
  position: absolute;
  right: 24px;
  // margin-bottom: -20px;
  cursor: pointer;
  text-decoration: underline;
  color: #3e62eb;
}
</style>
