import { DataTableColumn } from 'naive-ui';

export const cols: DataTableColumn[] = [
  {
    title: '物联网信号',
    key: 'iotSignalName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '隐患库',
    key: 'essentialFactor',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
    render: (row: any) => {
      return row.essentialFactor.elementName;
    },
  },
  {
    title: '隐患分类',
    key: 'essentialFactorClass.className',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
    render: (row: any) => {
      return row.essentialFactorClass?.className || '-';
    },
  },
  {
    title: '隐患描述',
    key: 'hazardDescribe',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
    render: (row: any) => {
      return row.essentialFactorClassItem?.hazardDescribe || '-';
    },
  },
];
