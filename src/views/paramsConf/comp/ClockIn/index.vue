<template>
  <div>
    <AppHeader title="打卡位置设置" blueIcon class="mt-10 mb-5">
      <template #right>
        <n-button type="primary" v-if="showBtns" @click="createClock" :style="{ height: toVh(30), fontSize: toVh(16) }"
          >新增</n-button
        >
      </template>
    </AppHeader>

    <n-data-table
      class="com-table"
      remote
      :bordered="false"
      :loading="loading"
      :columns="columns"
      :data="tableData"
      :pagination="pagination"
      :row-key="(row: any) => row.id"
      :min-height="150"
      :max-height="500"
    />

    <n-drawer v-model:show="showModal" :style="{ width: toVw(700) }" class="models" :autoFocus="false">
      <n-drawer-content closable>
        <template #header>
          <div class="flex flex-row items-center">
            <img class="w-[17px] h-[12px] mr-[20px]" src="@/components/header/assets/icon-title-arrow3.png" />
            <div class="text-[16px] text-[#222222] font-bold">打卡位置设置</div>
          </div>
        </template>
        <div class="clockIn__content">
          <n-form ref="formRef" :model="params" :rules="rules" label-placement="left" label-width="80px" size="medium">
            <n-form-item label="单位" path="unitId">
              <n-cascader
                v-model:value="params.unitId"
                placeholder="请选择单位"
                :options="options"
                :show-path="false"
                value-field="id"
                label-field="text"
                check-strategy="child"
                children-field="children"
                @update:value="handleUpdate"
              />
            </n-form-item>
            <n-form-item label="打卡地点" path="unitAddress">
              <n-input v-model:value="params.unitAddress" disabled placeholder="请输入打卡地点" />
            </n-form-item>
            <n-form-item label="打卡范围" path="checkRange">
              <n-input-number
                v-model:value="params.checkRange"
                placeholder="请输入打卡范围"
                :min="100"
                :max="3000"
                clearable
              />
              <span class="clockIn__desc">m，员工可在画圈范围内正常打卡</span>
            </n-form-item>
          </n-form>
        </div>

        <template #footer>
          <n-space>
            <n-button @click="closeDrawer">取消</n-button>
            <n-button type="primary" @click="saveForm" :loading="canClick">保存</n-button>
          </n-space>
        </template>
      </n-drawer-content>
    </n-drawer>
  </div>
</template>

<script lang="ts" setup>
import { ref, h, reactive, onMounted } from 'vue';
import { cloneDeep } from 'lodash-es';
import { useStore } from '@/store';
import { NButton, useMessage } from 'naive-ui';
import type { SelectOption, FormInst } from 'naive-ui';
import { $dialog } from '@/common/shareContext/useDialogCtx.ts';
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { isTopUnit } from '@/views/inspection-planning/planned-management/Details/utils';
import {
  hazardCheckRangeGetOrgTree,
  hazardCheckRangeSaveOrUpdate,
  hazardCheckRangeList,
  hazardCheckRangeDetail,
  hazardCheckRangeDelete,
} from '@/views/paramsConf/comp/fetchData';
import { toVw, toVh } from '@/utils/fit';
const store = useStore();
const message = useMessage();
const params = reactive({
  id: '',
  zhId: store.userInfo.zhId || '',
  unitId: null,
  unitName: '',
  unitAddress: '',
  unitPointX: '',
  unitPointY: '',
  checkRange: '',
});
const showModal = ref<boolean>(false);
const loading = ref<boolean>(false);
const tableData: any[] = ref([]);
const canClick = ref<boolean>(false);
const showBtns = ref<boolean>(false);
let options: any[] = [];
const formRef = ref<FormInst | null>(null);
let columns = [
  {
    title: '单位名称',
    key: 'unitName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '打卡范围',
    key: 'checkRange',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
    render(row: any) {
      return `${row.checkRange}m`;
    },
  },
  {
    title: '打卡位置',
    key: 'unitAddress',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    fixed: 'right',
    width: '300px',
    title: '操作',
    key: 'actions',
    align: 'left',
    render(row: any) {
      return useActionDivider([
        [
          h(
            NButton,
            {
              ghost: true,
              type: 'primary',
              size: 'small',
              onClick: () => edit(row.id),
            },
            { default: () => '编辑' }
          ),
        ],
        [
          h(
            NButton,
            {
              ghost: true,
              type: 'error',
              size: 'small',
              onClick: () => deleteItem(row.id),
            },
            { default: () => '删除' }
          ),
        ],
      ]);
    },
  },
];
const rules = reactive({
  unitAddress: {
    required: true,
    trigger: ['blur'],
    message: '请输入打卡地点',
  },
  unitId: {
    required: true,
    trigger: ['blur'],
    message: '请选择单位',
  },
  checkRange: {
    required: true,
    type: 'number',
    trigger: ['blur', 'input'],
    message: '请输入打卡范围',
  },
});

const closeDrawer = () => {
  showModal.value = false;
};

const clearFormValue = () => {
  params.id = '';
  params.zhId = store.userInfo.zhId || '';
  params.unitId = null;
  params.unitName = '';
  params.unitAddress = '';
  params.unitPointX = '';
  params.unitPointY = '';
  params.checkRange = '';
};

const handleUpdate = (value: string, option: SelectOption) => {
  const { unitAddress, unitPointX, unitPointY } = option.erecordUnitInfo;
  params.unitAddress = unitAddress;
  params.unitPointX = unitPointX;
  params.unitPointY = unitPointY;
  params.unitName = option.text;
};

const getClockInList = async () => {
  let searchParams = {
    unitId: store.userInfo.unitId,
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
  };
  hazardCheckRangeList(searchParams).then((res: any) => {
    if (res.code === 'success') {
      tableData.value = res.data.rows || [];
      updateTotal(res.data.total || 0);
    }
  });
};

const { pagination, updateTotal } = useNaivePagination(getClockInList);
pagination.pageSize = 10;
const dealUnitTree = (dataArr: []) => {
  let treeArr = cloneDeep(dataArr);
  if (treeArr.length > 0) {
    treeArr.map((item: any) => {
      if (!item.isChecked && !item.hasChildren) {
        item.disabled = true;
      } else {
        item.disabled = false;
      }
      if (!item.hasChildren) {
        item.children = null;
      } else {
        deepDealArr(item.children);
      }
    });
  }

  return treeArr;
};

const deepDealArr = (arr: []) => {
  arr.map((item: any) => {
    if (!item.isChecked && !item.hasChildren) {
      item.disabled = true;
    } else {
      item.disabled = false;
    }
    if (!item.hasChildren) {
      item.children = null;
    } else {
      deepDealArr(item.children);
    }
  });
};

const getUnitList = async () => {
  const unitId = store.userInfo?.unitId || 10000;
  const res = await hazardCheckRangeGetOrgTree({ unitId });

  if (res.code === 'success') {
    options = dealUnitTree(res.data.data);
  }
  console.log(options, 'options.value');
};

const getClockInDetail = async (id: string) => {
  hazardCheckRangeDetail({ id }).then((res: any) => {
    if (res.code === 'success') {
      const { checkRange, id, unitAddress, unitId, unitName, zhId, unitPointX, unitPointY } = res.data;
      params.checkRange = checkRange;
      params.id = id;
      params.unitAddress = unitAddress;
      params.unitId = unitId;
      params.unitName = unitName;
      params.zhId = zhId;
      params.unitPointX = unitPointX;
      params.unitPointY = unitPointY;
    }
  });
};
const createClock = () => {
  // 置空
  clearFormValue();
  showModal.value = true;
};
const deleteItem = (id: string) => {
  $dialog.warning({
    title: '提示',
    content: `确认要删除打卡位置吗？删除后不可恢复`,
    positiveButtonProps: { type: 'primary', color: '#527cff' },
    positiveText: '确定',
    negativeText: '取消',
    transformOrigin: 'center',
    onPositiveClick: async () => {
      hazardCheckRangeDelete({ id }).then((res: any) => {
        if (res.code === 'success') {
          message.success('删除成功');
          getClockInList();
          // 重新调用打卡单位树，用户更改状态
          getUnitList();
        } else {
          message.error(res.message);
        }
      });
    },
    onEsc: () => {},
  });
};
const edit = (id: string) => {
  showModal.value = true;
  getClockInDetail(id);
};

const saveForm = (e: MouseEvent) => {
  e.preventDefault();
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      canClick.value = true;
      hazardCheckRangeSaveOrUpdate(params).then((res: any) => {
        if (res.code === 'success') {
          if (params.id) {
            message.success('编辑打卡位置成功');
          } else {
            message.success('新增打卡位置成功');
          }
          // 重新调用打卡单位树，用户更改状态
          getUnitList();
        }

        canClick.value = false;
        showModal.value = false;
        // 调用获取打卡列表接口
        getClockInList();
        getUnitList();
        // 置空
        clearFormValue();
      });
    }
  });
};

onMounted(() => {
  // 获取单位列表
  getUnitList();
  // 获取打卡分页列表
  getClockInList();

  const { unitId, topUnitId } = store.userInfo;
  if (unitId === topUnitId) {
    showBtns.value = true;
  } else {
    showBtns.value = false;
  }

  //是集团单位展示操作按钮(打卡位置设置)
  if (!isTopUnit()) {
    columns = columns.filter((item) => item.key !== 'actions');
  }
});

defineOptions({ name: 'checkClockIn' });
</script>

<style lang="scss" scoped>
.clockIn__desc {
  white-space: nowrap;
  font-size: 12px;
  margin-left: 10px;
}
</style>
