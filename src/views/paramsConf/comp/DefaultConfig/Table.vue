<template>
  <AppHeader :title="props.title" blueIcon class="mt-10 mb-5">
    <template #right>
      <n-button
        type="primary"
        v-if="isEdit && isTopUnit()"
        @click="showModal = true"
        :style="{ height: toVh(30), fontSize: toVh(16) }"
        >新增</n-button
      >
    </template>
  </AppHeader>
  <div v-if="tableData.length" style="min-height: 300px; max-height: 500px; overflow-y: auto" class="com-table">
    <n-flex justify="space-between" v-for="(row, index) in tableData" :key="row.id" class="mt-2">
      <div class="text-sm bg-sky-500/100 text-white w-8 h-8 flex justify-center items-center rounded-md">
        {{ index + 1 }}
      </div>
      <div
        class="bg-gray-200 flex justify-center items-center cursor-pointer"
        :style="{ width: isEdit && isTopUnit() ? '80%' : '95%', fontSize: toVh(14) }"
      >
        {{ row.checkContent }}
      </div>
      <n-flex justify="space-between" v-if="isEdit && isTopUnit()">
        <n-button size="small" type="success" ghost @click="handleEdit(row)">编辑</n-button>
        <n-button size="small" type="error" ghost @click="handleDelete(row)">删除</n-button>
      </n-flex>
    </n-flex>
  </div>
  <n-empty description="暂无数据" style="padding: 48px 0" size="large" v-else />

  <n-modal
    v-model:show="showModal"
    :show-icon="false"
    preset="card"
    class="models"
    :mask-closable="false"
    style="width: 500px"
    :autoFocus="false"
  >
    <template #header>
      <div class="flex flex-row items-center">
        <img class="w-[17px] h-[12px] mr-[20px]" src="@/components/header/assets/icon-title-arrow3.png" />
        <div class="text-[16px] text-[#222222] font-bold">{{ props.title }}</div>
      </div>
    </template>
    <n-form ref="formRef" :model="modelForm" :rules="rules" class="p-[24px]">
      <n-form-item path="checkContent">
        <n-input
          maxlength="50"
          show-count
          v-model:value="modelForm.checkContent"
          clearable
          :placeholder="placeholder"
        />
      </n-form-item>
    </n-form>
    <template #action>
      <div class="flex justify-end items-center">
        <n-button type="primary" @click="showModal = false" ghost>取消</n-button>
        <n-button type="primary" @click="handleValidateClick" style="margin-left: 10px"> 保存 </n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { h, ref, VNode, watchEffect, toRaw } from 'vue';
import { DataTableColumns, NButton, FormInst, FormRules, FormItemRule, useMessage } from 'naive-ui';
import { cols } from './columns';
import { getDefaultConfigList, saveOrUpdate, deleteDefaultConfig } from '@/views/paramsConf/comp/fetchData';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { $dialog } from '@/common/shareContext/useDialogCtx.ts';
import { IObj } from '@/types';
import { isTopUnit } from '@/views/inspection-planning/planned-management/Details/utils';
import { toVw, toVh } from '@/utils/fit';

const props = defineProps({
  checkTableType: {
    type: String,
    default: 'device',
  },
  title: {
    type: String,
    default: '设备默认检查项配置',
  },
  unitId: {
    type: String,
    default: '',
  },
  placeholder: {
    type: String,
    default: '请输入设备默认检查项',
  },
});

const message = useMessage();
const modelForm = ref({
  id: null,
  checkContent: null,
});
const formRef = ref<FormInst | null>(null);
const rules: FormRules = {
  checkContent: [
    {
      required: true,
      message: props.placeholder,
      trigger: ['blur'],
    },
  ],
};
const [loading, search] = useAutoLoading(true);
const { pagination, updateTotal } = useNaivePagination(getTableData);

const columns = ref<DataTableColumns>([]);
const tableData = ref<any[]>([]);
const showModal = ref(false);
const isEdit = ref(true);

watchEffect(() => {
  if (!showModal.value) {
    resetFrom();
  }
});

function handleValidateClick(e: Event) {
  e.preventDefault();
  formRef.value?.validate((errors) => {
    if (!errors) {
      let { id, checkContent } = modelForm.value;
      let params = {
        id: id || null,
        checkContent,
        checkTableType: props.checkTableType,
        unitId: props.unitId,
      };
      saveOrUpdate(params).then((res: any) => {
        if (res.code === 'success') {
          showModal.value = false;
          message.success(id ? '修改成功' : '新增成功');
          resetFrom();
        } else {
          message.error(res.message);
        }
        getTableData();
      });
    } else {
      console.log(errors);
    }
  });
}

function resetFrom() {
  modelForm.value = {
    id: null,
    checkContent: null,
  };
}

function setColumns() {
  columns.value.push(...cols);
  // 添加操作栏 action
  columns.value.push({
    title: '操作',
    key: 'actions',
    align: 'left',
    render(row) {
      return getActionBtn(row);
    },
  });
}
setColumns();

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          size: 'small',
          type: 'primary',
          ghost: true,
          onClick: () => {
            modelForm.value = row;
            showModal.value = true;
          },
        },
        { default: () => '编辑' }
      ),
    ],
    [
      h(
        NButton,
        {
          size: 'small',
          type: 'error',
          ghost: true,
          onClick: () => handleDelete(row),
        },
        { default: () => '删除' }
      ),
    ],
  ];
  return useActionDivider(acList);
}
// 编辑
function handleEdit(row: any) {
  modelForm.value.id = row.id;
  modelForm.value.checkContent = row.checkContent;
  showModal.value = true;
}
// 删除
function handleDelete(row: any) {
  $dialog.warning({
    title: '提示',
    content: '确定删除' + props.title + '吗？删除后不可恢复！',
    positiveButtonProps: { type: 'primary', color: '#527cff' },
    positiveText: '确定',
    negativeText: '取消',
    transformOrigin: 'center',
    onPositiveClick: async () => {
      await deleteDefaultConfig({ id: row.id });
      getTableData();
    },
  });
}

function getTableData() {
  const params = {
    pageNo: pagination.page,
    pageSize: 9999,
    checkTableType: props.checkTableType,
    unitId: props.unitId,
  };

  search(getDefaultConfigList(params)).then((res: any) => {
    tableData.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
}

function getTableDataWrap(data: IObj<any>) {
  pagination.page = 1;
  getTableData();
}
getTableData();
defineOptions({ name: 'checkTampTableComp' });
defineExpose({ getTableDataWrap, getTableData });
</script>
