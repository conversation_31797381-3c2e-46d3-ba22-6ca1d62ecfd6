interface IOption {
  itemCategoryId: string;
  keyOption: string;
  keyType: string;
  optionTitle: string;
}

export interface IFormData {
  categoryId: string;
  itemCategoryId: string;
  itemName: string;
  itemType: string;
  optionVoList: IOption[];
}

export interface ICategoryTree {
  id: string;
  text: string;
  state: string;
  checked: boolean;
  attributes: string;
  children?: ICategoryTree[];
  parentId: string;
  hasParent: boolean;
  hasChildren: boolean;
}

export interface ICategory {
  id: string;
  name: string;
  type: string;
  parentId: string | null;
  status: string;
  createTime: string;
  updateTime: string;
  createUser: string;
  updateUser: string;
  delFlag: string;
}

export interface IInspectItems {
  itemId: string;
  itemName: string;
  categoryId: string;
  itemCategoryId: string;
  itemType: string;
  itemTypeName: string;
  optionVoList: string;
}
