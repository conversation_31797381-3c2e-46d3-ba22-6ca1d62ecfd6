<template>
  <AppHeader title="隐患等级设置" blueIcon class="mt-10 mb-5">
    <template #right v-if="isTopUnit()">
      <n-button
        type="primary"
        :disabled="tableData.length >= 5"
        @click="showModal = true"
        :style="{ height: toVh(30), fontSize: toVh(16) }"
        >新增</n-button
      >
    </template>
  </AppHeader>
  <n-data-table
    class="com-table"
    remotet
    striped
    :bordered="false"
    :columns="columns"
    :data="tableData"
    :pagination="false"
    :min-height="150"
    :max-height="500"
    :render-cell="useEmptyCell"
  />
  <n-modal
    v-model:show="showModal"
    :show-icon="false"
    preset="card"
    class="models w_500"
    :mask-closable="false"
    :autoFocus="false"
  >
    <template #header>
      <div class="flex flex-row items-center">
        <img class="w-[17px] h-[12px] mr-[20px]" src="@/components/header/assets/icon-title-arrow3.png" />
        <div class="text-[16px] text-[#222222] font-bold">{{ modelForm.id ? '编辑隐患等级' : '新增隐患等级' }}</div>
      </div>
    </template>
    <n-form ref="formRef" :model="modelForm" :rules="rules" class="p-[24px]">
      <n-form-item path="gradeSort" label="隐患等级">
        <n-input
          maxlength="6"
          show-count
          type="text"
          :allow-input="onlyAllowNumber"
          v-model:value="modelForm.gradeSort"
          clearable
          placeholder="请输入"
        />
      </n-form-item>
      <n-form-item path="gradeName" label="隐患名称">
        <n-input maxlength="6" show-count v-model:value="modelForm.gradeName" clearable placeholder="请输入" />
      </n-form-item>
      <!-- <n-form-item path="gradeSort" label="隐患级别">
        <n-input-number v-model:value="Sort" :min="1" clearable />
      </n-form-item> -->
    </n-form>
    <template #action>
      <div class="flex justify-end items-center">
        <n-button type="primary" @click="showModal = false" ghost>取消</n-button>
        <n-button type="primary" @click="handleValidateClick" style="margin-left: 10px"> 保存 </n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { useStore } from '@/store/index';
import { h, ref, VNode, watchEffect, toRaw, computed } from 'vue';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { DataTableColumns, NButton, FormInst, FormRules, FormItemRule, useMessage } from 'naive-ui';
import { cols } from './columns';
import { getGradeList, updateGrade, deleteGrade } from '@/views/paramsConf/comp/fetchData';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { $dialog } from '@/common/shareContext/useDialogCtx.ts';
import { IObj } from '@/types';
import { isTopUnit } from '@/views/inspection-planning/planned-management/Details/utils';
import { number } from 'echarts';
import { toVw, toVh } from '@/utils/fit';
const { $state } = useStore();
const { topUnitId } = $state.userInfo;

const emits = defineEmits(['action']);

const props = defineProps({
  unitId: {
    type: String,
    default: '',
  },
});
const isEdit = computed(() => {
  return topUnitId == props.unitId;
});
const Sort = ref(0);
const modelForm = ref({
  id: null,
  gradeSort: null,
  gradeName: null,
});
const formRef = ref<FormInst | null>(null);

const rules: any = {
  gradeSort: [
    {
      required: true,
      message: '请输入隐患等级',
      trigger: ['blur'],
    },
  ],
  gradeName: [
    {
      required: true,
      message: '请输入隐患名称',
      trigger: ['blur'],
    },
  ],
};
const message = useMessage();
const [loading, search] = useAutoLoading(true);
const { pagination, updateTotal } = useNaivePagination(getTableData);

const columns = ref<DataTableColumns>([]);
const tableData = ref<any[]>([]);
const showModal = ref(false);
watchEffect(() => {
  if (!showModal.value) {
    resetFrom();
    Sort.value = null;
  }
  if (Sort.value) {
    modelForm.value.gradeSort = Sort.value.toString();
  } else {
    modelForm.value.gradeSort = null;
  }
});
const onlyAllowNumber = ref((value: string) => !value || /^\d+$/.test(value));
function handleValidateClick(e: Event) {
  // console.log(tableData.value.length, '================tableData.value.length');
  // if (tableData.value.length >= 5) {
  //   return message.error('最多只能添加5个');
  // }
  console.log(modelForm.value.gradeSort, 'modelForm.value');
  e.preventDefault();
  formRef.value?.validate((errors) => {
    if (!errors) {
      let { id, gradeSort, gradeName } = modelForm.value;
      let params = {
        id: id || null,
        gradeSort: gradeSort,
        gradeName,
        unitId: props.unitId,
      };
      updateGrade(params).then((res: any) => {
        if (res.code === 'success') {
          showModal.value = false;
          message.success(id ? '修改成功' : '新增成功');
          resetFrom();
        } else {
          message.error(res.message);
        }
        getTableData();
      });
    } else {
      console.log(errors);
    }
  });
}

function resetFrom() {
  modelForm.value.gradeSort = null;
  modelForm.value.id = null;
  modelForm.value.gradeName = null;
}

function setColumns() {
  columns.value.push(...cols);
  // 添加操作栏 action
  if (isTopUnit())
    columns.value.push({
      fixed: 'right',
      width: '300px',
      title: '操作',
      key: 'actions',
      align: 'left',
      render(row) {
        return getActionBtn(row);
      },
    });
}
setColumns();

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          size: 'small',
          type: 'primary',
          ghost: true,
          disabled: !isEdit.value,
          onClick: () => {
            // console.log(row, '======row');
            let { id, gradeSort, gradeName } = toRaw(row);
            // console.log(gradeSort, "========gradeSort")
            modelForm.value.id = id;
            // modelForm.value.gradeSort = +gradeSort;
            Sort.value = gradeSort;
            modelForm.value.gradeSort = Sort.value.toString();
            modelForm.value.gradeName = gradeName;
            console.log(modelForm.value);
            showModal.value = true;
          },
        },
        { default: () => '编辑' }
      ),
    ],
    [
      h(
        NButton,
        {
          size: 'small',
          type: 'error',
          ghost: true,
          disabled: !isEdit.value,
          onClick: () => handleDelete(row),
        },
        { default: () => '删除' }
      ),
    ],
  ];
  return useActionDivider(acList);
}

// 删除
function handleDelete(row: any) {
  $dialog.warning({
    title: '提示',
    content: '确定要删除隐患等级吗？删除后不可恢复',
    positiveButtonProps: { type: 'primary', color: '#527cff' },
    positiveText: '确定',
    negativeText: '取消',
    transformOrigin: 'center',
    onPositiveClick: async () => {
      await deleteGrade({ id: row.id });
      getTableData();
    },
  });
}

function getTableData() {
  const params = {
    pageNo: 1,
    pageSize: -1,
    unitId: props.unitId,
  };

  search(getGradeList(params)).then((res: any) => {
    tableData.value = res.data.rows || [];
    console.log(tableData.value.length, '================tableData.value.length');
    updateTotal(res.data.total || 0);
  });
}

function getTableDataWrap() {
  pagination.page = 1;
  getTableData();
}
getTableDataWrap();
defineOptions({ name: 'checkTampTableCompf' });
defineExpose({ getTableDataWrap, getTableData });
</script>
<style module lang="scss"></style>
