<template>
  <div class="risk-point-form">
    <n-form
      ref="formRef"
      :model="formValue"
      :rules="rules"
      label-placement="left"
      label-width="auto"
      require-mark-placement="right-hanging"
    >
      <n-grid :cols="24" :x-gap="12">
        <n-form-item-gi :span="12" label="风险点编码" path="riskPointCode">
          <n-input
            v-model:value="formValue.riskPointCode"
            placeholder="请输入风险点编码"
            :disabled="type === 2"
          />
        </n-form-item-gi>
        
        <n-form-item-gi :span="12" label="风险点名称" path="riskPointName">
          <n-input
            v-model:value="formValue.riskPointName"
            placeholder="请输入风险点名称"
          />
        </n-form-item-gi>
        
        <n-form-item-gi :span="12" label="风险等级" path="riskLevel">
          <n-select
            v-model:value="formValue.riskLevel"
            placeholder="请选择风险等级"
            :options="riskLevelOptions"
          />
        </n-form-item-gi>
        
        <n-form-item-gi :span="12" label="所属区域" path="areaName">
          <n-input
            v-model:value="formValue.areaName"
            placeholder="请输入所属区域"
          />
        </n-form-item-gi>
        
        <n-form-item-gi :span="12" label="管控状态" path="controlStatus">
          <n-select
            v-model:value="formValue.controlStatus"
            placeholder="请选择管控状态"
            :options="controlStatusOptions"
          />
        </n-form-item-gi>
        
        <n-form-item-gi :span="12" label="管控责任人" path="responsiblePerson">
          <n-input
            v-model:value="formValue.responsiblePerson"
            placeholder="请输入管控责任人"
          />
        </n-form-item-gi>
        
        <n-form-item-gi :span="12" label="联系电话" path="phone">
          <n-input
            v-model:value="formValue.phone"
            placeholder="请输入联系电话"
          />
        </n-form-item-gi>
        
        <n-form-item-gi :span="24" label="风险描述" path="riskDescription">
          <n-input
            v-model:value="formValue.riskDescription"
            type="textarea"
            placeholder="请输入风险描述"
            :rows="3"
          />
        </n-form-item-gi>
        
        <n-form-item-gi :span="24" label="管控措施" path="controlMeasures">
          <n-input
            v-model:value="formValue.controlMeasures"
            type="textarea"
            placeholder="请输入管控措施"
            :rows="3"
          />
        </n-form-item-gi>
      </n-grid>
    </n-form>
    
    <div class="mt-6 flex justify-end gap-3">
      <n-button @click="handleClose">取消</n-button>
      <n-button type="primary" @click="handleSubmit" :loading="submitLoading">
        {{ type === 1 ? '新增' : '保存' }}
      </n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue';
import { useMessage } from 'naive-ui';

interface Props {
  data?: any;
  type: number; // 1: 新增, 2: 编辑
}

const props = defineProps<Props>();
const emits = defineEmits(['success', 'close']);

const message = useMessage();
const formRef = ref();
const submitLoading = ref(false);

const formValue = reactive({
  riskPointCode: '',
  riskPointName: '',
  riskLevel: null,
  areaName: '',
  controlStatus: null,
  responsiblePerson: '',
  phone: '',
  riskDescription: '',
  controlMeasures: '',
});

// 表单验证规则
const rules = {
  riskPointCode: [
    { required: true, message: '请输入风险点编码', trigger: ['input', 'blur'] },
  ],
  riskPointName: [
    { required: true, message: '请输入风险点名称', trigger: ['input', 'blur'] },
  ],
  riskLevel: [
    { required: true, message: '请选择风险等级', trigger: ['change', 'blur'] },
  ],
  areaName: [
    { required: true, message: '请输入所属区域', trigger: ['input', 'blur'] },
  ],
  controlStatus: [
    { required: true, message: '请选择管控状态', trigger: ['change', 'blur'] },
  ],
  responsiblePerson: [
    { required: true, message: '请输入管控责任人', trigger: ['input', 'blur'] },
  ],
  phone: [
    { 
      pattern: /^1[3-9]\d{9}$/, 
      message: '请输入正确的手机号码', 
      trigger: ['input', 'blur'] 
    },
  ],
};

// 风险等级选项
const riskLevelOptions = [
  { label: '重大风险', value: '1' },
  { label: '较大风险', value: '2' },
  { label: '一般风险', value: '3' },
  { label: '低风险', value: '4' },
];

// 管控状态选项
const controlStatusOptions = [
  { label: '正常管控', value: '1' },
  { label: '异常', value: '2' },
  { label: '停用', value: '3' },
];

// 监听数据变化，初始化表单
watch(
  () => props.data,
  (newData) => {
    if (newData && props.type === 2) {
      Object.keys(formValue).forEach(key => {
        if (newData[key] !== undefined) {
          formValue[key] = newData[key];
        }
      });
    }
  },
  { immediate: true }
);

const handleSubmit = async () => {
  try {
    await formRef.value?.validate();
    submitLoading.value = true;
    
    // TODO: 调用实际API
    // if (props.type === 1) {
    //   await addRiskPoint(formValue);
    // } else {
    //   await updateRiskPoint({ ...formValue, id: props.data.id });
    // }
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    emits('success');
  } catch (error) {
    if (error?.errors) {
      message.error('请检查表单填写');
    } else {
      message.error('操作失败');
    }
  } finally {
    submitLoading.value = false;
  }
};

const handleClose = () => {
  emits('close');
};

defineOptions({ name: 'RiskPointForm' });
</script>

<style scoped lang="scss">
.risk-point-form {
  padding: 20px 0;
}
</style>
