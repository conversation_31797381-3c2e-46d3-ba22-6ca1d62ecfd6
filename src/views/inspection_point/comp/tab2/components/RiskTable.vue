<template>
  <div class="table-container">
    <n-data-table
      class="h-full com-table com-table-container !p-[0]"
      remote
      striped
      :columns="columns"
      :data="tableData"
      :bordered="false"
      :flex-height="true"
      :row-key="(row: any) => row.id"
      :loading="loading"
      :pagination="pagination"
      :scroll-x="1200"
      @update:checked-row-keys="handleCheck"
    />

    <!-- 风险点位详情弹窗 -->
    <n-modal
      v-model:show="showDetailModal"
      :mask-closable="false"
      preset="card"
      style="width: 800px"
      title="风险点位详情"
    >
      <RiskPointDetail v-if="showDetailModal" :data="currentRowData" @close="showDetailModal = false" />
    </n-modal>

    <!-- 新增/编辑风险点位弹窗 -->
    <n-modal
      v-model:show="showFormModal"
      :mask-closable="false"
      preset="card"
      style="width: 600px"
      :title="formModalTitle"
    >
      <RiskPointForm
        v-if="showFormModal"
        :data="currentRowData"
        :type="formType"
        @success="handleFormSuccess"
        @close="showFormModal = false"
      />
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { h, ref, reactive, onMounted } from 'vue';
import { DataTableColumns, NButton, NTag, useMessage } from 'naive-ui';
import { ACTION } from '../../../constant';
import { useAutoLoading } from '@/common/hooks/useAutoLoading';
import { useNaivePagination } from '@/common/hooks/useNaivePagination';
import { useEmptyCell } from '@/common/hooks/useEmptyCell';
import RiskPointDetail from './RiskPointDetail.vue';
import RiskPointForm from './RiskPointForm.vue';

interface Props {
  unitId: string;
  unitName: string;
}

const props = defineProps<Props>();
const emits = defineEmits(['action']);

const message = useMessage();
const tableData = ref([]);
const loading = ref(false);
const checkedRowKeys = ref([]);
const showDetailModal = ref(false);
const showFormModal = ref(false);
const currentRowData = ref({});
const formType = ref(1); // 1: 新增, 2: 编辑
const formModalTitle = ref('新增风险点位');

// 表格列配置
const columns: DataTableColumns = [
  {
    type: 'selection',
    width: 50,
  },
  {
    title: '风险点编码',
    key: 'riskPointCode',
    width: 120,
  },
  {
    title: '风险点名称',
    key: 'riskPointName',
    width: 150,
  },
  {
    title: '风险等级',
    key: 'riskLevel',
    width: 100,
    render: (row: any) => {
      const levelMap = {
        '1': { label: '重大风险', color: 'error' },
        '2': { label: '较大风险', color: 'warning' },
        '3': { label: '一般风险', color: 'info' },
        '4': { label: '低风险', color: 'success' },
      };
      const level = levelMap[row.riskLevel];
      return level ? h(NTag, { type: level.color }, { default: () => level.label }) : useEmptyCell(row, 'riskLevel');
    },
  },
  {
    title: '所属区域',
    key: 'areaName',
    width: 120,
  },
  {
    title: '管控状态',
    key: 'controlStatus',
    width: 100,
    render: (row: any) => {
      const statusMap = {
        '1': { label: '正常管控', color: 'success' },
        '2': { label: '异常', color: 'error' },
        '3': { label: '停用', color: 'default' },
      };
      const status = statusMap[row.controlStatus];
      return status
        ? h(NTag, { type: status.color }, { default: () => status.label })
        : useEmptyCell(row, 'controlStatus');
    },
  },
  {
    title: '管控责任人',
    key: 'responsiblePerson',
    width: 120,
  },
  {
    title: '创建时间',
    key: 'createTime',
    width: 160,
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right',
    render: (row: any) => {
      return h('div', { class: 'flex gap-2' }, [
        h(
          NButton,
          {
            size: 'small',
            type: 'primary',
            text: true,
            onClick: () => handleDetail(row),
          },
          { default: () => '详情' }
        ),
        h(
          NButton,
          {
            size: 'small',
            type: 'primary',
            text: true,
            onClick: () => handleEdit(row),
          },
          { default: () => '编辑' }
        ),
        h(
          NButton,
          {
            size: 'small',
            type: 'error',
            text: true,
            onClick: () => handleDelete(row),
          },
          { default: () => '删除' }
        ),
      ]);
    },
  },
];

// 模拟数据
const mockData = [
  {
    id: '1',
    riskPointCode: 'FX001',
    riskPointName: '危险化学品储存区',
    riskLevel: '1',
    areaName: '生产区A',
    controlStatus: '1',
    responsiblePerson: '张三',
    createTime: '2024-01-15 10:30:00',
  },
  {
    id: '2',
    riskPointCode: 'FX002',
    riskPointName: '高温作业区',
    riskLevel: '2',
    areaName: '生产区B',
    controlStatus: '1',
    responsiblePerson: '李四',
    createTime: '2024-01-16 14:20:00',
  },
  {
    id: '3',
    riskPointCode: 'FX003',
    riskPointName: '电气设备间',
    riskLevel: '3',
    areaName: '设备区',
    controlStatus: '2',
    responsiblePerson: '王五',
    createTime: '2024-01-17 09:15:00',
  },
];

// 获取表格数据
const getTableDataWrap = async (params?: any) => {
  loading.value = true;
  try {
    // TODO: 调用实际API
    // const res = await getRiskPointList({
    //   ...params,
    //   pageNo: pagination.page,
    //   pageSize: pagination.pageSize,
    // });

    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 500));
    tableData.value = mockData;
    pagination.itemCount = mockData.length;
  } catch (error) {
    message.error('获取数据失败');
  } finally {
    loading.value = false;
  }
};
const { pagination, updateTotal } = useNaivePagination(getTableDataWrap);

// 处理选择
const handleCheck = (keys: any[]) => {
  checkedRowKeys.value = keys;
};

// 查看详情
const handleDetail = (row: any) => {
  currentRowData.value = row;
  showDetailModal.value = true;
};

// 编辑
const handleEdit = (row: any) => {
  currentRowData.value = row;
  formType.value = 2;
  formModalTitle.value = '编辑风险点位';
  showFormModal.value = true;
};

// 删除
const handleDelete = (row: any) => {
  // TODO: 实现删除逻辑
  message.info('删除功能开发中');
};

// 显示新增弹窗
const showRiskPointDialog = (options: { rowId: string; type: number }) => {
  if (options.type === 1) {
    currentRowData.value = {};
    formType.value = 1;
    formModalTitle.value = '新增风险点位';
  }
  showFormModal.value = true;
};

// 表单操作成功
const handleFormSuccess = () => {
  showFormModal.value = false;
  getTableDataWrap();
  message.success(formType.value === 1 ? '新增成功' : '编辑成功');
};

// 重置查询
const resetQuery = () => {
  pagination.page = 1;
  getTableDataWrap();
};

// 暴露方法
defineExpose({
  getTableDataWrap,
  showRiskPointDialog,
  resetQuery,
  pagination,
});

onMounted(() => {
  getTableDataWrap();
});

defineOptions({ name: 'RiskTable' });
</script>

<style scoped lang="scss">
.table-container {
  background: #fff;
  border-radius: 8px;
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
