<template>
  <div class="filter-container">
    <n-form ref="formRef" :model="formValue" label-placement="left" :show-feedback="false" class="filter-form">
      <n-grid :cols="24" :x-gap="12">
        <n-form-item-gi :span="6" label="风险点名称" path="riskPointName">
          <n-input
            v-model:value="formValue.riskPointName"
            placeholder="请输入风险点名称"
            clearable
            @keydown.enter="handleSearch"
          />
        </n-form-item-gi>
        
        <n-form-item-gi :span="6" label="风险等级" path="riskLevel">
          <n-select
            v-model:value="formValue.riskLevel"
            placeholder="请选择风险等级"
            clearable
            :options="riskLevelOptions"
          />
        </n-form-item-gi>
        
        <n-form-item-gi :span="6" label="管控状态" path="controlStatus">
          <n-select
            v-model:value="formValue.controlStatus"
            placeholder="请选择管控状态"
            clearable
            :options="controlStatusOptions"
          />
        </n-form-item-gi>
        
        <n-form-item-gi :span="6" class="flex justify-end">
          <n-space>
            <n-button @click="handleReset">重置</n-button>
            <n-button type="primary" @click="handleSearch">查询</n-button>
          </n-space>
        </n-form-item-gi>
      </n-grid>
    </n-form>
    
    <div class="action-bar mt-4">
      <n-space>
        <n-button type="primary" @click="handleAddRiskPoint">
          <template #icon>
            <n-icon><AddOutline /></n-icon>
          </template>
          新增风险点位
        </n-button>
        
        <n-button @click="handleExportRiskPoint" :loading="exportLoading">
          <template #icon>
            <n-icon><DownloadOutline /></n-icon>
          </template>
          导出
        </n-button>
      </n-space>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { AddOutline, DownloadOutline } from '@vicons/ionicons5';
import { ACTION } from '../../../constant';

const emits = defineEmits(['action', 'exportRiskPoint', 'addRiskPoint']);

const formRef = ref();
const exportLoading = ref(false);

const formValue = reactive({
  riskPointName: '',
  riskLevel: null,
  controlStatus: null,
});

// 风险等级选项
const riskLevelOptions = [
  { label: '重大风险', value: '1' },
  { label: '较大风险', value: '2' },
  { label: '一般风险', value: '3' },
  { label: '低风险', value: '4' },
];

// 管控状态选项
const controlStatusOptions = [
  { label: '正常管控', value: '1' },
  { label: '异常', value: '2' },
  { label: '停用', value: '3' },
];

const handleSearch = () => {
  emits('action', {
    action: ACTION.SEARCH,
    data: { ...formValue },
  });
};

const handleReset = () => {
  formRef.value?.restoreValidation();
  Object.keys(formValue).forEach(key => {
    formValue[key] = key === 'riskPointName' ? '' : null;
  });
  handleSearch();
};

const handleAddRiskPoint = () => {
  emits('addRiskPoint');
};

const handleExportRiskPoint = () => {
  emits('exportRiskPoint');
};

// 获取表单值
const getVal = () => {
  return { ...formValue };
};

// 清空表单
const clearFrom = () => {
  Object.keys(formValue).forEach(key => {
    formValue[key] = key === 'riskPointName' ? '' : null;
  });
};

const getValFool = () => {
  // 兼容原有逻辑
};

// 暴露方法
defineExpose({
  getVal,
  clearFrom,
  getValFool,
  exportLoading,
});

defineOptions({ name: 'RiskFilter' });
</script>

<style scoped lang="scss">
.filter-container {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filter-form {
  .n-form-item {
    margin-bottom: 0;
  }
}

.action-bar {
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
}
</style>
