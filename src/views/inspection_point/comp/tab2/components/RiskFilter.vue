<template>
  <div class="filter-container">
    <n-form ref="formRef" :model="formValue" label-placement="left" :show-feedback="false">
      <n-grid :x-gap="12" :y-gap="8" :cols="3">
        <n-gi>
          <n-form-item label="风险点等级:" path="riskPointName">
            <n-select v-model:value="formValue.riskLevel" placeholder="请选择" clearable :options="riskLevelOptions" />
          </n-form-item>
        </n-gi>

        <n-gi>
          <n-form-item label="管控层级:" path="contorlLevel">
            <n-select
              v-model:value="formValue.contorlLevel"
              placeholder="请选择"
              clearable
              :options="controlStatusOptions"
            />
          </n-form-item>
        </n-gi>

        <n-gi>
          <n-form-item>
            <n-input
              placeholder="请输入风险点编码/名称/位置/管控责任人模糊搜索"
              v-model:value="formValue.keyword"
              maxlength="50"
              show-word-limit
              clearable
            >
              <template #suffix>
                <BsSearch />
              </template>
            </n-input>
          </n-form-item>
        </n-gi>
      </n-grid>
    </n-form>

    <div class="action-bar">
      <n-space>
        <n-button type="primary" @click="handleAddRiskPoint">
          <template #icon>
            <n-icon><AddOutline /></n-icon>
          </template>
          新增风险点位
        </n-button>
      </n-space>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { AddOutline, DownloadOutline } from '@vicons/ionicons5';
import { ACTION } from '../../../constant';

interface formData {
  contorlLevel: string | ;
  keyword: string;
  riskLevel: string;
}
const emits = defineEmits(['action', 'exportRiskPoint', 'addRiskPoint']);

const formRef = ref();
const exportLoading = ref(false);

const formValue = reactive<formData>({
  contorlLevel: null,
  keyword: '',
  riskLevel: '',
});

// 风险等级选项
const riskLevelOptions = [
  { label: '重大风险', value: '1' },
  { label: '较大风险', value: '2' },
  { label: '一般风险', value: '3' },
  { label: '低风险', value: '4' },
];

// 管控状态选项
const controlStatusOptions = [
  { label: '正常管控', value: '1' },
  { label: '异常', value: '2' },
  { label: '停用', value: '3' },
];

const handleSearch = () => {
  emits('action', {
    action: ACTION.SEARCH,
    data: { ...formValue },
  });
};

const handleAddRiskPoint = () => {
  emits('addRiskPoint');
};

const handleExportRiskPoint = () => {
  emits('exportRiskPoint');
};

// 获取表单值
const getVal = () => {
  return { ...formValue };
};

const getValFool = () => {
  // 兼容原有逻辑
};

// 暴露方法
defineExpose({
  getVal,
  getValFool,
  exportLoading,
});

defineOptions({ name: 'RiskFilter' });
</script>

<style scoped lang="scss">
.filter-container {
  border-radius: 8px;
}

.filter-form {
  .n-form-item {
    margin-bottom: 0;
  }
}

.action-bar {
  padding-top: 16px;
  display: flex;
  justify-content: flex-end;
}
</style>
