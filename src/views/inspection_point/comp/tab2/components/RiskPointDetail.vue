<template>
  <div class="risk-point-detail">
    <n-descriptions :column="2" bordered>
      <n-descriptions-item label="风险点编码">
        {{ data.riskPointCode || '--' }}
      </n-descriptions-item>
      <n-descriptions-item label="风险点名称">
        {{ data.riskPointName || '--' }}
      </n-descriptions-item>
      <n-descriptions-item label="风险等级">
        <n-tag v-if="data.riskLevel" :type="getRiskLevelColor(data.riskLevel)">
          {{ getRiskLevelLabel(data.riskLevel) }}
        </n-tag>
        <span v-else>--</span>
      </n-descriptions-item>
      <n-descriptions-item label="所属区域">
        {{ data.areaName || '--' }}
      </n-descriptions-item>
      <n-descriptions-item label="管控状态">
        <n-tag v-if="data.controlStatus" :type="getControlStatusColor(data.controlStatus)">
          {{ getControlStatusLabel(data.controlStatus) }}
        </n-tag>
        <span v-else>--</span>
      </n-descriptions-item>
      <n-descriptions-item label="管控责任人">
        {{ data.responsiblePerson || '--' }}
      </n-descriptions-item>
      <n-descriptions-item label="联系电话">
        {{ data.phone || '--' }}
      </n-descriptions-item>
      <n-descriptions-item label="创建时间">
        {{ data.createTime || '--' }}
      </n-descriptions-item>
      <n-descriptions-item label="风险描述" :span="2">
        {{ data.riskDescription || '--' }}
      </n-descriptions-item>
      <n-descriptions-item label="管控措施" :span="2">
        {{ data.controlMeasures || '--' }}
      </n-descriptions-item>
    </n-descriptions>
    
    <div class="mt-6 flex justify-end">
      <n-button @click="handleClose">关闭</n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  data: any;
}

const props = defineProps<Props>();
const emits = defineEmits(['close']);

// 获取风险等级标签
const getRiskLevelLabel = (level: string) => {
  const levelMap = {
    '1': '重大风险',
    '2': '较大风险',
    '3': '一般风险',
    '4': '低风险',
  };
  return levelMap[level] || '--';
};

// 获取风险等级颜色
const getRiskLevelColor = (level: string) => {
  const colorMap = {
    '1': 'error',
    '2': 'warning',
    '3': 'info',
    '4': 'success',
  };
  return colorMap[level] || 'default';
};

// 获取管控状态标签
const getControlStatusLabel = (status: string) => {
  const statusMap = {
    '1': '正常管控',
    '2': '异常',
    '3': '停用',
  };
  return statusMap[status] || '--';
};

// 获取管控状态颜色
const getControlStatusColor = (status: string) => {
  const colorMap = {
    '1': 'success',
    '2': 'error',
    '3': 'default',
  };
  return colorMap[status] || 'default';
};

const handleClose = () => {
  emits('close');
};

defineOptions({ name: 'RiskPointDetail' });
</script>

<style scoped lang="scss">
.risk-point-detail {
  padding: 20px 0;
}
</style>
