<!--
 * @Description: 自定义点位表格
-->
<template>
  <div class="right-table !p-[0]">
    <div ref="tableRef" class="table-wrap">
      <!-- 表格内容 -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, defineExpose } from "vue";
import { useStore } from "@/store";
import { ElMessage } from "element-plus";

interface Props {
  unitId?: string;
  unitName?: string;
  optTreeData?: any[];
  isBusinessUnit?: boolean;
}

const props = defineProps<Props>();
const { userInfo } = useStore();
const tableRef = ref();
const loading = ref(false);
const tableData = ref([]);
const pagination = reactive({
  pageNo: 1,
  pageSize: 10,
  total: 0
});

// 暴露的属性
const exLoading = ref(false);
const epLoading = ref(false);
const exportLoading = ref(false);

// 暴露给父组件的方法
const resetSearch = () => {
  pagination.pageNo = 1;
  getTableDataWrap();
};

const updateUnit = (unitId: string, unitName: string) => {
  pagination.pageNo = 1;
  getTableDataWrap({ unitId });
};

const getTableDataWrap = async (params?: any) => {
  loading.value = true;
  try {
    // 这里实现获取表格数据的逻辑
    loading.value = false;
  } catch (error) {
    console.error(error);
    loading.value = false;
    ElMessage.error("获取数据失败");
  }
};

const getVal = () => {
  // 实现获取筛选值的逻辑
  return {};
};

onMounted(() => {
  getTableDataWrap();
});

defineExpose({
  resetSearch,
  updateUnit,
  getTableDataWrap,
  getVal,
  exLoading,
  epLoading,
  exportLoading
});
</script>

<style scoped>
.table-wrap {
  height: 100%;
}
</style>
