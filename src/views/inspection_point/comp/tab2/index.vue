<template>
  <div class="risk-point-tab">
    <RiskFilter
      @action="actionFn"
      ref="filterRef"
      @exportRiskPoint="exportRiskPoint()"
      @addRiskPoint="addRiskPoint()"
    />
    <RiskTable class="mt-4" ref="tableCompRef" @action="actionFn" :unitId="filterVal.unitId" :unitName="unitName" />
  </div>
</template>

<script setup lang="ts">
import { ref, watchEffect } from 'vue';
import RiskFilter from './components/RiskFilter.vue';
import RiskTable from './components/RiskTable.vue';
import { ACTION } from '../../constant';
import { useStore } from '@/store';
import { ElMessage } from 'element-plus';
import { fileDownloader } from '@/utils/fileDownloader';

interface Props {
  unitId: string;
  unitName: string;
  isBusinessUnit: boolean;
}

const props = defineProps<Props>();
const emits = defineEmits(['action']);

const { userInfo } = useStore();
const currentAction = ref({ action: ACTION.ADD, data: {} });
const filterRef = ref();
const tableCompRef = ref();
let filterVal = ref({ unitId: props.unitId });

// 监听props变化
watchEffect(() => {
  filterVal.value.unitId = props.unitId;
});

function actionFn(val: any) {
  currentAction.value = val;
  if (val.action === ACTION.SEARCH) {
    filterVal.value = { unitId: filterVal.value.unitId, ...val.data };
    return handleSearch(filterVal.value);
  }
  emits('action', val);
}

// 导出风险点位
const exportRiskPoint = () => {
  filterVal.value = { ...filterVal.value, ...filterRef.value?.getVal() };
  filterRef.value.exportLoading = true;
  
  // TODO: 实现风险点位导出API
  // fileDownloader(exportRiskPointApi(), {
  //   method: 'POST',
  //   contentType: 'application/json',
  //   body: JSON.stringify(filterVal.value),
  // })
  //   .then((res) => {
  //     filterRef.value.exportLoading = false;
  //   })
  //   .catch((err) => {
  //     filterRef.value.exportLoading = false;
  //   });
  
  // 临时实现
  setTimeout(() => {
    filterRef.value.exportLoading = false;
    ElMessage.success('导出功能开发中');
  }, 1000);
};

// 新增风险点位
const addRiskPoint = () => {
  if (!props.isBusinessUnit)
    return ElMessage({
      message: '请选择具体业务单位后再进行操作',
      type: 'warning',
    });
  tableCompRef.value?.showRiskPointDialog({ rowId: '', type: 1 });
};

function handleSearch(data: any) {
  tableCompRef.value?.getTableDataWrap(data);
}

// 重置查询
const resetQuery = () => {
  filterRef.value?.clearFrom();
  filterVal.value = { unitId: props.unitId };
  tableCompRef.value.pagination.page = 1;
  filterRef.value?.getValFool();
  handleSearch(filterVal.value);
};

// 暴露方法给父组件
defineExpose({
  resetQuery,
  handleSearch,
});

defineOptions({ name: 'RiskPointTab' });
</script>

<style scoped lang="scss">
.risk-point-tab {
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
