<template>
  <div class="flex flex-col">
    <com-bread :data="breadData"></com-bread>
    <div class="item_content notVw flex-1">
      <UnitSelect class="unit-tree" style="min-width: 323px" @updateVal="updateVal" v-show="showTree" />
      <div class="right-table" :style="{ width: showTree ? '62vw' : '100%' }">
        <img
          src="@/assets/select_tree.png"
          class="select-tree"
          :style="{ width: toVw(30), left: `-${toVw(15)}` }"
          v-show="userInfo.unitOrgType == '2'"
          @click="showTree = !showTree"
          alt=""
        />
        <Filter
          @action="actionFn"
          :optTreeData="optTreeData"
          ref="filterRef"
          @downloadErCodes="downloadErCodes()"
          @relevanceTable="relevanceTable()"
          @showPointDialog="showPointDialog()"
          @importPoint="importPoint()"
          @importPoint2="importPoint2()"
          @exportPoint="exportPoint()"
          @deleteBatch="deleteBatch()"
          @pointTable="pointTable()"
          @uppointTable="uppointTable()"
        />
        <TableList class="mt-4" ref="tableCompRef" @action="actionFn" :unitId="filterVal.unitId" :unitName="unitName" />
      </div>
    </div>
    <!-- 点位更新规则弹窗 -->
    <n-modal
      v-model:show="showModalCenter"
      :mask-closable="false"
      class="models"
      :show-icon="false"
      preset="card"
      style="width: 560px"
      :autoFocus="false"
    >
      <template #header>
        <div class="flex flex-row items-center">
          <img class="w-[17px] h-[12px] mr-[20px]" src="@/components/header/assets/icon-title-arrow3.png" />
          <div class="text-[16px] text-[#222222] font-bold">更新点位ID规则</div>
        </div>
      </template>
      <div class="flex flex-col items-center pt-[20px]">
        <img width="246px" src="@/assets/bgid.png" />
        <div class="text-[15px] text-[#303133] mt-[16px] pb-[8px]">点位ID规则更新成功</div>
        <div class="text-[15px] text-[#969799]">（可通过点击下载点位ID规则，查看最新的规则）</div>
      </div>
      <div class="flex justify-end items-center p-[20px]">
        <n-button style="width: 88px" type="primary" color="#527CFF" @click="showModalCenter = false"> 确定 </n-button>
      </div>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, watchEffect } from 'vue';
import UnitSelect from '@/components/unitSelect/index.vue';
import Filter from './components/Filter.vue';
import TableList from './components/Table.vue';
import ComBread from '@/components/breadcrumb/ComBread.vue';
import { ACTION } from './constant';
import { useStore } from '@/store';
import { toVw } from '@/utils/fit';
import { ElMessage, ElMessageBox } from 'element-plus';
import { exportPointManage, deleteBatchApi, exportPointCodeRule, addPointCodeRule } from './fetchData';
import { getBuildingTreeByUnitId } from '@/views/inspection_point/fetchData';
import { fileDownloader } from '@/utils/fileDownloader';

const { userInfo } = useStore();
const currentAction = ref({ action: ACTION.ADD, data: {} });
const breadData = [{ name: '隐患治理系统' }, { name: '巡查点位管理' }];
const filterRef = ref();
const tableCompRef = ref();
let filterVal = ref({ unitId: userInfo.unitId });
const isBusinessUnit = ref(); //true:业务单位 false:非业务单位
const unitName = ref(); //选中的树节点name
const showTree = ref(true);
const showModalCenter = ref(false);
const optTreeData = ref([]);
/** 获取楼栋楼层树数据 */
const getBuildingTreeByUnitIdApi = () => {
  let unitId = filterVal.value.unitId;
  return new Promise(async (resolve, reject) => {
    const res: any = await getBuildingTreeByUnitId({ unitId });
    optTreeData.value = res.data.options;
    resolve(true);
  });
};

// 初始化判断当前用户是否是业务单位
isBusinessUnit.value = userInfo.unitOrgType == '1';

// 组织结构切换
function updateVal(unitId: any, _isBusinessUnit: boolean, { treeName }: any) {
  isBusinessUnit.value = _isBusinessUnit || false;
  unitName.value = treeName;
  filterRef.value?.clearFrom();
  filterVal.value = { unitId, ...filterRef.value?.getVal() };
  tableCompRef.value.pagination.page = 1;
  // tableCompRef.value.pagination.pageSize = 10;
  filterRef.value?.getValFool();
  handleSearch(filterVal.value);
  getBuildingTreeByUnitIdApi();
}

watchEffect(() => {
  if (filterVal.value.unitId) {
    console.log('当前选择的单位id', filterVal.value.unitId);
  }
});

function actionFn(val: any) {
  currentAction.value = val;
  if (val.action === ACTION.SEARCH) {
    filterVal.value = { unitId: filterVal.value.unitId, ...val.data };
    return handleSearch(filterVal.value);
  }
}

// 批量删除
const deleteBatch = async () => {
  let ids = tableCompRef.value?.idList || [];
  if (ids.length == 0)
    return ElMessage({
      message: '请选择要删除的数据',
      type: 'warning',
    });
  ElMessageBox.confirm('确认删除选中数据?', '系统提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    let { code }: any = await deleteBatchApi({ ids });
    if (code === 'success') {
      // tableCompRef.value.pagination.page = 1;
      // tableCompRef.value.pagination.pageSize = 10;
      tableCompRef.value?.resetQuery();
    }
  });
};
// 下载点位
const pointTable = async () => {
  const parms = {
    topUnitId: userInfo.topUnitId,
  };
  filterRef.value.exLoading = true;
  fileDownloader(exportPointCodeRule(), {
    method: 'POST',
    contentType: 'application/json',
    body: JSON.stringify(parms),
  })
    .then((res) => {
      filterRef.value.exLoading = false;
    })
    .catch((err) => {
      filterRef.value.exLoading = false;
    });
};
// 更新点位
const uppointTable = async () => {
  const addForm = {
    topUnitId: userInfo.topUnitId,
    // unitId: filterVal.value.unitId,
  };
  filterRef.value.epLoading = true;
  let { code }: any = await addPointCodeRule(addForm);
  if (code === 'success') {
    showModalCenter.value = true;
  }
  filterRef.value.epLoading = false;
};
// 批量导出
const exportPoint = () => {
  filterVal.value = { ...filterVal.value, ...filterRef.value?.getVal() };
  filterRef.value.exportLoading = true;
  fileDownloader(exportPointManage(), {
    method: 'POST',
    contentType: 'application/json',
    body: JSON.stringify(filterVal.value),
  })
    .then((res) => {
      filterRef.value.exportLoading = false;
    })
    .catch((err) => {
      filterRef.value.exportLoading = false;
    });
};

// 批量下载二维码
const downloadErCodes = () => {
  tableCompRef.value?.downloadErCodes();
};
// 批量关联检查表
const relevanceTable = () => {
  if (!isBusinessUnit.value)
    return ElMessage({
      message: '请选择具体业务单位后再进行关联',
      type: 'warning',
    });
  tableCompRef.value?.relevanceTable();
};
// 新增巡查点位
const showPointDialog = () => {
  // type 1 新增 2 编辑
  tableCompRef.value?.showPointDialog({ rowId: '', type: 1 });
};

//批量导入巡查点位
const importPoint = () => {
  if (!isBusinessUnit.value)
    return ElMessage({
      message: '请选择具体业务单位后再进行导入',
      type: 'warning',
    });
  tableCompRef.value?.importPoint();
};

//批量导入
const importPoint2 = () => {
  if (!isBusinessUnit.value)
    return ElMessage({
      message: '请选择具体业务单位后再进行导入',
      type: 'warning',
    });
  tableCompRef.value?.importPoint2();
};

function handleSearch(data: any) {
  tableCompRef.value?.getTableDataWrap(data);
}

defineOptions({ name: 'inspection_point' });
</script>

<style module lang="scss"></style>
