<!--
 * @Description: 巡查点位管理
-->
<template>
  <div class="flex flex-col">
    <com-bread :data="breadData"></com-bread>

    <!-- 内容区域 -->
    <div class="item_content notVw flex-1">
      <transition name="slide-fade">
        <UnitSelect
          v-show="showTree"
          class="unit-tree"
          style="min-width: 323px"
          @updateVal="updateVal"
        />
      </transition>
      <div
        style="flex: 6; position: relative"
        :style="{ width: showTree ? '62vw' : '100%' }"
      >
        <img
          src="@/assets/select_tree.png"
          class="select-tree"
          style="
            cursor: pointer;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            z-index: 10;
          "
          :style="{ width: toVw(30), left: `-${toVw(15)}` }"
          v-show="userInfo.unitOrgType == '2'"
          @click="showTree = !showTree"
          alt=""
        />
        <radio-tab
          class="!pt-[0]"
          :tab-list="tabList"
          :tab="activeTab"
          @change="handleTabChange"
        ></radio-tab>
        <TableRisk
          v-show="activeTab === 'risk'"
          ref="riskPointsRef"
          :unitId="filterVal.unitId"
          :unitName="unitName"
          :optTreeData="optTreeData"
        />
        <TableCustom
          v-show="activeTab === 'custom'"
          ref="customPointsRef"
          :unitId="filterVal.unitId"
          :unitName="unitName"
          :optTreeData="optTreeData"
          :isBusinessUnit="isBusinessUnit"
        />
      </div>
    </div>
    <!-- 点位更新规则弹窗 -->
    <n-modal
      v-model:show="showModalCenter"
      :mask-closable="false"
      class="models"
      :show-icon="false"
      preset="card"
      style="width: 560px"
      :autoFocus="false"
    >
      <template #header>
        <div class="flex flex-row items-center">
          <img
            class="w-[17px] h-[12px] mr-[20px]"
            src="@/components/header/assets/icon-title-arrow3.png"
          />
          <div class="text-[16px] text-[#222222] font-bold">更新点位ID规则</div>
        </div>
      </template>
      <div class="flex flex-col items-center pt-[20px]">
        <img width="246px" src="@/assets/bgid.png" />
        <div class="text-[15px] text-[#303133] mt-[16px] pb-[8px]">
          点位ID规则更新成功
        </div>
        <div class="text-[15px] text-[#969799]">
          （可通过点击下载点位ID规则，查看最新的规则）
        </div>
      </div>
      <div class="flex justify-end items-center p-[20px]">
        <n-button
          style="width: 88px"
          type="primary"
          color="#527CFF"
          @click="showModalCenter = false"
        >
          确定
        </n-button>
      </div>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, watchEffect } from "vue";
import UnitSelect from "@/components/unitSelect/index.vue";
import TableRisk from "./comp/tab1/Table.vue";
import TableCustom from "./comp/tab2/Table.vue";
import ComBread from "@/components/breadcrumb/ComBread.vue";
import RadioTab from "@/components/tab/ComRadioTabE.vue";
import { ACTION } from "./constant";
import { useStore } from "@/store";
import { toVw } from "@/utils/fit";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  exportPointManage,
  deleteBatchApi,
  exportPointCodeRule,
  addPointCodeRule
} from "./fetchData";
import { getBuildingTreeByUnitId } from "@/views/inspection_point/fetchData";
import { fileDownloader } from "@/utils/fileDownloader";

const { userInfo } = useStore();
const breadData = [{ name: "隐患治理系统" }, { name: "巡查点位管理" }];
const riskPointsRef = ref();
const customPointsRef = ref();
let filterVal = ref({ unitId: userInfo.unitId });
const isBusinessUnit = ref(); //true:业务单位 false:非业务单位
const unitName = ref(); //选中的树节点name
const showTree = ref(true);
const showModalCenter = ref(false);
const optTreeData = ref([]);

// Tab 相关
const activeTab = ref("risk"); // 默认显示风险点位
const tabList = [
  { name: "risk", label: "风险点位" },
  { name: "custom", label: "自定义点位" }
];

const handleTabChange = (value: string) => {
  activeTab.value = value;
  if (value === "risk") {
    riskPointsRef.value?.resetSearch();
  } else if (value === "custom") {
    customPointsRef.value?.resetSearch();
  }
};

/** 获取楼栋楼层树数据 */
const getBuildingTreeByUnitIdApi = () => {
  let unitId = filterVal.value.unitId;
  return new Promise(async (resolve, reject) => {
    const res: any = await getBuildingTreeByUnitId({ unitId });
    optTreeData.value = res.data.options;
    resolve(true);
  });
};

// 初始化判断当前用户是否是业务单位
isBusinessUnit.value = userInfo.unitOrgType == "1";

// 组织结构切换
function updateVal(unitId: any, _isBusinessUnit: boolean, { treeName }: any) {
  isBusinessUnit.value = _isBusinessUnit || false;
  unitName.value = treeName;
  filterVal.value.unitId = unitId;
  getBuildingTreeByUnitIdApi();

  // 更新当前活动tab的数据
  if (activeTab.value === "risk") {
    riskPointsRef.value?.updateUnit(unitId, treeName);
  } else if (activeTab.value === "custom") {
    customPointsRef.value?.updateUnit(unitId, treeName);
  }
}

watchEffect(() => {
  if (filterVal.value.unitId) {
    console.log("当前选择的单位id", filterVal.value.unitId);
  }
});

function actionFn(val: any) {
  if (val.action === ACTION.SEARCH) {
    filterVal.value = { unitId: filterVal.value.unitId, ...val.data };
    return handleSearch(filterVal.value);
  }
}

// 批量删除
const deleteBatch = async () => {
  let ids = riskPointsRef.value?.idList || [];
  if (ids.length == 0)
    return ElMessage({
      message: "请选择要删除的数据",
      type: "warning"
    });
  ElMessageBox.confirm("确认删除选中数据?", "系统提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(async () => {
    let { code }: any = await deleteBatchApi({ ids });
    if (code === "success") {
      riskPointsRef.value?.resetQuery();
    }
  });
};

// 下载点位
const pointTable = async () => {
  const parms = {
    topUnitId: userInfo.topUnitId
  };
  if (customPointsRef.value) {
    customPointsRef.value.exLoading = true;
  }
  fileDownloader(exportPointCodeRule(), {
    method: "POST",
    contentType: "application/json",
    body: JSON.stringify(parms)
  })
    .then((res) => {
      if (customPointsRef.value) {
        customPointsRef.value.exLoading = false;
      }
    })
    .catch((err) => {
      if (customPointsRef.value) {
        customPointsRef.value.exLoading = false;
      }
    });
};

// 更新点位
const uppointTable = async () => {
  const addForm = {
    topUnitId: userInfo.topUnitId
  };
  customPointsRef.value?.epLoading = true;
  let { code }: any = await addPointCodeRule(addForm);
  if (code === "success") {
    showModalCenter.value = true;
  }
  customPointsRef.value?.epLoading = false;
};

// 批量导出
const exportPoint = () => {
  filterVal.value = { ...filterVal.value, ...customPointsRef.value?.getVal() };
  customPointsRef.value?.exportLoading = true;
  fileDownloader(exportPointManage(), {
    method: "POST",
    contentType: "application/json",
    body: JSON.stringify(filterVal.value)
  })
    .then((res) => {
      customPointsRef.value?.exportLoading = false;
    })
    .catch((err) => {
      customPointsRef.value?.exportLoading = false;
    });
};

// 批量下载二维码
const downloadErCodes = () => {
  riskPointsRef.value?.downloadErCodes();
};

// 批量关联检查表
const relevanceTable = () => {
  if (!isBusinessUnit.value)
    return ElMessage({
      message: "请选择具体业务单位后再进行关联",
      type: "warning"
    });
  riskPointsRef.value?.relevanceTable();
};

// 新增巡查点位
const showPointDialog = () => {
  // type 1 新增 2 编辑
  riskPointsRef.value?.showPointDialog({ rowId: "", type: 1 });
};

//批量导入巡查点位
const importPoint = () => {
  if (!isBusinessUnit.value)
    return ElMessage({
      message: "请选择具体业务单位后再进行导入",
      type: "warning"
    });
  riskPointsRef.value?.importPoint();
};

//批量导入
const importPoint2 = () => {
  if (!isBusinessUnit.value)
    return ElMessage({
      message: "请选择具体业务单位后再进行导入",
      type: "warning"
    });
  riskPointsRef.value?.importPoint2();
};

function handleSearch(data: any) {
  riskPointsRef.value?.getTableDataWrap(data);
}

defineOptions({ name: "inspection_point" });
</script>

<style module lang="scss">
.radio-tab {
  @apply bg-[#F4F9FF] h-[48px] border-b-[1px] border-[#EBEEF5] rounded-t-[4px];

  :global(.n-tabs) {
    @apply pl-[36px];
  }
  :global(.n-tabs-nav-scroll-content) {
    @apply h-[48px];
  }
  :global(.n-tab-pane) {
    display: none;
  }
}

.tab-content {
  background: #fff;
  border-radius: 0 0 4px 4px;
  border: 1px solid #ebeef5;
  border-top: none;
  min-height: calc(100vh - 200px);
  padding: 24px;
}

.select-tree {
  cursor: pointer;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
}
</style>
