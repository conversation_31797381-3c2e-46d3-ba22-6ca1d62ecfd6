<!-- eslint-disable vue/no-dupe-keys -->
<template>
  <el-drawer
    v-model="dialogVisible"
    size="30%"
    direction="rtl"
    :show-close="false"
    :close-on-click-modal="+formDialogType === 3"
  >
    <template #header="{ close }">
      <div class="flex justify-between items-center">
        <div class="flex justify-start items-center">
          <img src="@/assets/title_ico.png" style="width: 17px" alt="" />
          <div style="font-size: 16px" class="ml-2 text-black">
            {{ getTitle }}
          </div>
        </div>
        <img src="@/assets/dialog_close.png" style="width: 42px" class="cursor-pointer" alt="" @click="close" />
      </div>
    </template>
    <div class="bg-white rounded p-4 h-full">
      <el-form
        ref="formRef"
        :model="form"
        class="mt-5"
        :rules="rules"
        status-icon
        label-width="150px"
        v-loading="formLoading"
        label-position="left"
        :disabled="props.formDialogType == 3"
      >
        <el-form-item label="所属单位:" prop="unitId">
          <el-select v-model="form.unitId" placeholder="请选择" filterable @change="unitChange">
            <el-option :label="item.unitName" :value="item.id" v-for="item in unitList" :key="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="点位名称:" prop="pointName">
          <el-input v-model="form.pointName" clearable placeholder="请输入点位名称" maxlength="20" show-word-limit />
        </el-form-item>
        <el-form-item label="位置:" prop="location">
          <el-input v-model="form.location" clearable placeholder="请输入位置信息" maxlength="20" show-word-limit />
        </el-form-item>
        <el-form-item label="楼栋楼层:" prop="floorId">
          <el-tree-select
            v-model="form.floorId"
            :data="treeData"
            placeholder="请选择楼栋楼层"
            @change="floorChange"
            filterable
            :render-after-expand="false"
            clearable
            no-match-text="无匹配数据"
          />
        </el-form-item>
        <el-form-item label="是否重点部位:" prop="isKeyArea">
          <el-radio-group v-model="form.isKeyArea">
            <el-radio :value="1">是</el-radio>
            <el-radio :value="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="资产编码:" prop="assetCode">
          <el-input
            v-model="form.assetCode"
            clearable
            placeholder="请输入资产编码（选填）"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="关联检查表:" prop="checkTableName">
          <el-row class="w-full">
            <el-col :span="form.checkTableId || props.formDialogType != 3 ? 22 : 24" class="w-full">
              <el-input
                v-model="form.checkTableName"
                class="relevance-check-table"
                placeholder="请关联检查表"
                style="width: 100%"
                :suffix-icon="ArrowRight"
                @click="showCheckTableDialog"
              />
            </el-col>
            <el-col :span="2" v-if="props.formDialogType != 3 && form.checkTableId">
              <el-button class="ml-1" type="danger" :icon="Delete" circle size="small" @click="deleteCheckTable" />
            </el-col>
          </el-row>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="close" :loading="formLoading">{{ props.formDialogType == 3 ? '关闭' : '取消' }}</el-button>
        <el-button type="primary" @click="submitForm" v-if="props.formDialogType != 3" :loading="formLoading"
          >保存</el-button
        >
      </div>
    </template>
  </el-drawer>
  <!-- 选择检查表 -->
  <el-drawer v-model="checkTableDialog" size="50%" direction="rtl" :show-close="false" :close-on-click-modal="false">
    <template #header>
      <div class="flex justify-between items-center">
        <div class="flex justify-start items-center">
          <img src="@/assets/title_ico.png" style="width: 17px" alt="" />
          <div style="font-size: 16px" class="ml-2 text-black">选择检查表</div>
        </div>
        <img
          src="@/assets/dialog_close.png"
          style="width: 42px"
          class="cursor-pointer"
          alt=""
          @click="closeCheckTableDialog"
        />
      </div>
    </template>
    <div class="bg-white rounded p-4 h-full">
      <n-form label-placement="left" inline :show-feedback="false" class="mb-[12px]">
        <n-grid :x-gap="12" :y-gap="12" :cols="2">
          <n-gi class="flex items-center">
            <n-form-item label="编制单位:" class="flex-1">
              <el-select
                :disabled="standard"
                v-model="checkTableunitId"
                placeholder="全部"
                clearable
                @change="getCheckTableDataApi()"
              >
                <el-option v-for="item in unitOpts" :key="item.orgCode" :label="item.orgName" :value="item.orgCode" />
              </el-select>
            </n-form-item>
            <n-checkbox v-model:checked="standard" :on-update:checked="handleCheckbox">仅看本单位</n-checkbox>
          </n-gi>
          <n-gi>
            <n-form-item>
              <n-input
                v-model:value="checkTableName"
                @update:value="getCheckTableDataApi()"
                clearable
                class="w-full flex items-center"
                placeholder="请输入检查表名称"
              />
            </n-form-item>
          </n-gi>
        </n-grid>
      </n-form>
      <el-table
        empty-text="暂无数据"
        :data="checkTableData"
        height="74vh"
        :row-key="(row: any) => row.id"
        v-loading="loading"
        stripe
        @selection-change="selectChange"
        ref="singleTable"
      >
        <el-table-column type="selection" width="55" reserve-selection />
        <el-table-column type="index" label="序号" width="80" />
        <el-table-column prop="checkTableName" label="检查表名称" width="270" show-overflow-tooltip />
        <el-table-column prop="unitName" label="编制单位" show-overflow-tooltip />
        <el-table-column prop="createTime" label="编制时间" show-overflow-tooltip />
        <el-table-column fixed="right" label="操作" width="90">
          <template #default="{ row }">
            <n-button size="small" type="primary" ghost @click="goTableDetail(row)">详情</n-button>
            <n-divider vertical />
          </template>
        </el-table-column>
      </el-table>
    </div>
    <template #footer>
      <div class="flex justify-end items-center">
        <el-button @click="closeCheckTableDialog">取消</el-button>
        <el-button type="primary" @click="confirmCheck">确定选择</el-button>
      </div>
    </template>
  </el-drawer>
  <!-- 检查表详情 -->
  <CheckTableDetail :show="checkItemDialog" :row="checkItemDetail" @close="checkItemDialog = false" />

  <!-- 点位更新规则弹窗 -->
  <n-modal
    v-model:show="showModalCenter"
    :mask-closable="false"
    class="models"
    :z-index="2200"
    :show-icon="false"
    preset="card"
    style="width: 560px"
    :autoFocus="false"
  >
    <template #header>
      <div class="flex flex-row items-center">
        <img class="w-[17px] h-[12px] mr-[20px]" src="@/components/header/assets/icon-title-arrow3.png" />
        <div class="text-[16px] text-[#222222] font-bold">提示</div>
      </div>
    </template>
    <div class="flex flex-col items-center pt-[20px]">
      <img width="246px" src="@/assets/bgnone.png" />
      <div class="text-[15px] text-[#303133] mx-[16px] px-[50px]">
        您添加的楼栋楼层信息，无法生成点位ID，请联系管理员更新点位ID规则后，再尝试添加该点位
      </div>
    </div>
    <div class="flex justify-end items-center p-[20px]">
      <n-button style="width: 88px" type="primary" color="#527CFF" @click="showModalCenter = false">
        我知道了
      </n-button>
    </div>
  </n-modal>
</template>
<script lang="ts" setup>
import { useStore } from '@/store';
import CheckTableDetail from '@/views/inspect-mgr/inspect/comp/CheckTableDetail.vue';
import { getAllUnit, getBuildingTreeByUnitId } from '@/views/inspection_point/fetchData.ts';
import { ArrowRight, Delete } from '@element-plus/icons-vue';
import type { FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';
import { computed, nextTick, reactive, ref } from 'vue';
import {
  bindCheckTableByBatch,
  getCheckList,
  getCheckPointDetail,
  getUpAndDownUnitInfo,
  saveOrUpdateCheckPoint,
} from '../fetchData';
const { userInfo } = useStore();
const props = defineProps({
  id: {
    type: String,
    default: '',
  },
  formDialogType: {
    //1新增  2编辑  3详情  null单独配置检查表
    type: Number,
    default: null,
  },
  unitId: {
    type: String,
    default: '',
  },
  checkTableId: {
    type: String,
    default: '',
  },
});
interface RuleForm {
  unitId: string;
  pointName: string;
  location: string;
  floorId: string;
  isKeyArea: number;
  assetCode: string;
  checkTableId: string;
}
const emit = defineEmits(['success', 'getCheckTableData']);
const dialogVisible = ref(false);
const getTitle = computed(() => {
  let titles = ['新增巡查点位', '编辑巡查点位', '巡查点位详情'];
  return titles[props.formDialogType - 1];
});
const formLoading = ref(false);
const treeData = ref([]);
const unitList = ref();
const formRef = ref();
const loading = ref(false);
const form = ref<any>({
  id: null,
  pointName: '',
  location: '',
  floor: '',
  floorId: '',
  building: '',
  buildingId: '',
  isKeyArea: 0,
  assetCode: '',
  checkTableId: '',
  checkTableName: '',
  pointRefType: 1, //手动创建的巡查点位
  unitId: null,
  zhId: userInfo.zhId,
  createBy: userInfo.id,
});
const showModalCenter = ref(false);
const rules = reactive<FormRules<RuleForm>>({
  unitId: [{ required: true, message: '请选择所属单位', trigger: 'blur' }],
  pointName: [{ required: true, message: '请输入点位名称', trigger: 'blur' }],
  location: [{ required: true, message: '请输入位置信息', trigger: 'blur' }],
  floorId: [
    {
      required: true,
      message: '请选择楼栋楼层',
      trigger: 'change',
    },
  ],
});
const standard = ref(false);
const handleCheckbox = (val: any) => {
  standard.value = val;
  checkTableunitId.value = null;
  getCheckTableDataApi();
};

/** 获取楼栋楼层树数据 */
const getBuildingTreeByUnitIdApi = (rowUnitId: any) => {
  console.log(rowUnitId);
  let unitId = rowUnitId || form.value.unitId;
  return new Promise(async (resolve, reject) => {
    const res: any = await getBuildingTreeByUnitId({ unitId });
    treeData.value = res.data.options;
    resolve(true);
  });
};
// 获取业务单位
const getAllUnitApi = async () => {
  const res: any = await getAllUnit({ orgCode: props.unitId, pageNo: 1, pageSize: -1, orgType: 1 });
  unitList.value = res.data.rows || [];
  if (props.formDialogType == 1) {
    form.value.unitId = unitList.value[0].id || ''; // 默认选择第一个实体单位
  }
};

// 获取巡查点位详情
const getCheckPointDetailApi = async (id: any) => {
  const res: any = await getCheckPointDetail({ id });
  form.value = res.data;
  // 查找检查表id 对应的检查表勾选项  用于单选回显
  let index = checkTableData.value.findIndex((item: any) => item.id == res.data.checkTableId);
  selectArr.value = index > -1 ? [checkTableData.value[index]] : [];
  formLoading.value = false;
};

const floorChange = (floorId: any) => {
  if (floorId) {
    // 通过floorId 查找关联项
    let { label, value, parentId, parentName } = findItemByFloorId(treeData.value, floorId);
    form.value.floor = label;
    form.value.floorId = value;
    form.value.building = parentName || null;
    form.value.buildingId = parentId;
  } else {
    form.value.floor = '';
    form.value.floorId = '';
    form.value.building = '';
    form.value.buildingId = '';
  }
};

function findItemByFloorId(array: any, value: any): any {
  for (let item of array) {
    if (item.value === value) {
      return item;
    } else if (item.children && item.children.length > 0) {
      let found = findItemByFloorId(item.children, value);
      if (found) {
        return found;
      }
    }
  }
  return null;
}

// 关闭新增/编辑/详情  巡查点位弹窗
const close = () => {
  selectArr.value = [];
  dialogVisible.value = false;
};

// 关闭选检查表弹窗
const closeCheckTableDialog = () => {
  selectArr.value = [];
  checkTableunitId.value = null;
  singleTable.value && singleTable.value.clearSelection();
  checkTableDialog.value = false;
};

// 提交表单
const submitForm = () => {
  formRef.value.validate(async (valid: any) => {
    if (valid) {
      formLoading.value = true;
      try {
        saveOrUpdateCheckPoint(form.value)
          .then((res: any) => {
            if (res.code == 'success') {
              formLoading.value = false;
              props.formDialogType == 1
                ? ElMessage({
                    message: '新增成功',
                    type: 'success',
                  })
                : ElMessage({
                    message: '编辑成功',
                    type: 'success',
                  });
              closeDialog();
              // 触发列表刷新 true-重置分页 false-不重置分页
              emit('success', props.formDialogType != 1);
            }
          })
          .catch((res) => {
            const error = res.message && JSON.parse(res.message);
            formLoading.value = false;
            if (error.message == 'POINT_ID_ERROR_CODE') {
              showModalCenter.value = true;
            } else {
              ElMessage({
                message: error.message,
                type: 'error',
              });
            }
          });
      } catch (error) {
        formLoading.value = false;
      }
    }
  });
};

// 单位切换
const unitChange = async () => {
  form.value.floor = '';
  form.value.floorId = '';
  form.value.building = '';
  form.value.buildingId = '';
  // 获取楼栋楼层数据
  await getBuildingTreeByUnitIdApi(null);
};

// 新建/编辑/详情  巡查点位弹窗
const showFormDialog = (id: any, rowUnitId: any) => {
  dialogVisible.value = true;
  nextTick(async () => {
    formRef.value && formRef.value.resetFields();
    // 编辑或详情
    if (id) {
      // 获取检查表信息用于回显数据
      getOrgCodeUpListApi().then(async () => {
        formLoading.value = true;
        // 获取检查表表列表
        await getCheckTableDataApi();
        await getCheckPointDetailApi(id);
      });
    } else {
      //新增
      form.value.id = null;
      // 清空检查表选中
      props.formDialogType == 1 && singleTable.value && singleTable.value.clearSelection();
    }

    // 获取下级业务单位枚举
    await getAllUnitApi();
    // 获取楼栋楼层数据
    getBuildingTreeByUnitIdApi(rowUnitId);
  });
};

// showFormDialog()

// 选择检查表变量
const checkTableDialog = ref(false);
const selectArr = ref<any>([]);
const checkItemDetail = ref<any>({});
const checkItemDialog = ref(false);
const checkTableunitId = ref<any>(null);
const checkTableName = ref('');
const unitOpts = ref();
const singleTable = ref();
// 检查表数据
const checkTableData = ref();
// 选择检查表
const selectChange = (selection: any) => {
  if (selection.length > 1) {
    const del_row = selection.shift();
    singleTable.value.toggleRowSelection(del_row, false);
  }
  selectArr.value = selection;
};

// 显示检查表列表
const showCheckTableDialog = async () => {
  checkTableName.value = '';
  // 新增并且没有选择检查表时，清空检查表选中
  selectArr.value = [];
  // 详情页不允许选择检查表
  if (props.formDialogType && props.formDialogType == 3) return;
  checkTableDialog.value = true;
  // 获取编制单位
  getOrgCodeUpListApi().then(() => {
    // 获取检查表表列表
    getCheckTableDataApi();
  });
};

// 获取检查表列表
const getCheckTableDataApi = async () => {
  return new Promise(async (resolve, reject) => {
    loading.value = true;
    const parms = {
      unitId: checkTableunitId.value || unitOpts.value.map((item: any) => item.orgCode).join(','),
      topUnitId: userInfo.topUnitId,
      checkTableType: 1, //类型为综合检查表
      checkStatus: 0, //状态为启用
      pageNo: 1,
      pageSize: -1, //查所有
      checkTableName: checkTableName.value, //检查表名称
      flag: 1,
    };
    if (standard.value) parms.unitId = userInfo.unitId;
    const res: any = await getCheckList(parms);
    loading.value = false;
    checkTableData.value = res.data.rows || [];
    // 新增编辑并且有检查表的回显勾选检查表
    if (form.value.checkTableId) {
      let checkedItem = checkTableData.value.find((item: any) => item.id == form.value.checkTableId);
      checkedItem &&
        checkTableData.value.length &&
        singleTable.value &&
        singleTable.value.toggleRowSelection(checkedItem);
      // 直接从列表点检查表的回显勾选检查表
    } else if (props.checkTableId) {
      let checkedItem = checkTableData.value.find((item: any) => item.id == props.checkTableId);
      checkedItem &&
        checkTableData.value.length &&
        singleTable.value &&
        singleTable.value.toggleRowSelection(checkedItem);
    } else {
      // 上面两种情况都是有检查表情况 如未选择检查表 清空选中
      singleTable.value && singleTable.value.clearSelection();
    }
    resolve(true);
  }).catch(() => {
    loading.value = false;
  });
};

// 关闭新增/编辑/详情  巡查点位弹窗
const closeDialog = () => {
  selectArr.value = [];
  form.value = {
    id: null,
    pointName: '',
    location: '',
    checkTableId: '',
    checkTableName: '',
    floor: '',
    floorId: '',
    building: '',
    buildingId: '',
    pointRefType: 1, //手动创建的巡查点位
    unitId: null,
    isKeyArea: 0,
    zhId: userInfo.zhId,
    createBy: userInfo.id,
  };
  dialogVisible.value = false;
};

// 获取编制单位
const getOrgCodeUpListApi = () => {
  return new Promise<void>(async (resolve, reject) => {
    let { data } = await getUpAndDownUnitInfo({ unitId: userInfo.unitId });
    unitOpts.value = data || [];
    resolve();
  });
};

// 删除检查表
const deleteCheckTable = () => {
  form.value.checkTableId = '';
  form.value.checkTableName = '';
};
// 确认选择检查表
const confirmCheck = () => {
  if (selectArr.value.length === 0) {
    checkTableDialog.value = false;
  }
  // 从列表过来的  单独配置检查表(作为组件使用 抛出选中的检查表数据)
  if (props.formDialogType == null) {
    // emit('getCheckTableData', selectArr.value[0]);
    if (selectArr.value.length && selectArr.value[0].id) {
      bindCheckTableByBatchApi();
    } else {
      checkTableDialog.value = false;
      emit('success');
    }
  } else {
    //从新增编辑过来的
    form.value.checkTableId = selectArr.value.length ? selectArr.value[0].id : '';
    form.value.checkTableName = selectArr.value.length ? selectArr.value[0].checkTableName : '';
    checkTableDialog.value = false;
  }
};

// 单独配置检查表(作为组件使用)
const bindCheckTableByBatchApi = async () => {
  let bindList = [
    {
      checkPointId: props.id,
      checkTableId: selectArr.value[0].id,
    },
  ];
  let { code }: any = await bindCheckTableByBatch({ bindList });
  if (code == 'success') {
    checkTableDialog.value = false;
    emit('success');
  }
};

// 检查表详情
const goTableDetail = async (row: any) => {
  checkItemDetail.value = row;
  checkItemDialog.value = true;
};

defineExpose({ showFormDialog, showCheckTableDialog });
defineOptions({ name: 'formDialogs' });
</script>
<style lang="scss" scoped>
::v-deep .el-table__header-wrapper:not(.custom-class) .el-checkbox {
  display: none !important;
}

::v-deep .relevance-check-table {
  .el-input__wrapper.is-disabled {
    cursor: pointer;
  }
}
</style>
