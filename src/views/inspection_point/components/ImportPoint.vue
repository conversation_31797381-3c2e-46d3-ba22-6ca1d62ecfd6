<template>
  <!-- 选择导入方式 -->
  <el-dialog v-model="dialogVisible" width="800px" :show-close="false" :close-on-click-modal="false">
    <template #header>
      <div class="flex justify-between items-center">
        <div class="flex justify-start items-center">
          <img src="@/assets/title_ico.png" style="width: 17px" alt="" />
          <div style="font-size: 16px" class="ml-2 text-black">请选择导入方式</div>
        </div>
        <img
          src="@/assets/dialog_close.png"
          style="width: 42px"
          class="cursor-pointer"
          alt=""
          @click="dialogVisible = false"
        />
      </div>
    </template>
    <div class="flex justify-between items-center" style="height: 400px">
      <div
        class="flex justify-center items-center bg-white h-full rounded box-border cursor-pointer text-[#333]"
        style="width: 47%"
        @click="pointRefTypeChange(2)"
        :class="{ selected: pointRefType == 2 }"
      >
        <div class="flex justify-center items-center flex-col">
          <img src=" ../img/device_ico.png" style="width: 130px" alt="" />
          <div class="mt-14 text-2xl">从设备管理中导入</div>
        </div>
      </div>
      <div
        class="flex justify-center items-center bg-white h-full rounded box-border cursor-pointer text-[#333]"
        style="width: 47%"
        @click="pointRefTypeChange(3)"
        :class="{ selected: pointRefType == 3 }"
      >
        <div class="flex justify-center items-center flex-col">
          <img src="../img/import_point.png" style="width: 116px" alt="" />
          <div class="mt-14 text-2xl">从重点部位管理中导入</div>
        </div>
      </div>
    </div>
    <template #footer>
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="confirmImportType">确定</el-button>
    </template>
  </el-dialog>

  <!-- 导入设备/重点部位弹窗 -->
  <el-drawer v-model="checkDeviceDialog" size="80%" direction="rtl" :show-close="false" :close-on-click-modal="false">
    <template #header>
      <div class="flex justify-between items-center">
        <div class="flex justify-start items-center">
          <img src="@/assets/title_ico.png" style="width: 17px" alt="" />
          <div style="font-size: 16px" class="ml-2 text-black">
            {{ pointRefType == 2 ? '从设备管理中导入' : '从重点部位中导入' }}
          </div>
        </div>
        <img
          src="@/assets/dialog_close.png"
          style="width: 42px"
          class="cursor-pointer"
          alt=""
          @click="closeDeviceDialog"
        />
      </div>
    </template>
    <el-row :gutter="20" class="h-full">
      <!-- 巡查点位 -->
      <el-col :span="10">
        <div class="bg-white rounded p-4 h-full">
          <div class="flex justify-start items-center mb-4 text-base">
            <div class="title-ico"></div>
            <div>
              {{ pointRefType == 2 ? '已选择导入的设备位置' : '已选择导入的重点部位位置' }}
            </div>
          </div>
          <el-table :data="importDeviceData" height="74vh" empty-text="暂无数据" stripe v-if="pointRefType == 2">
            <el-table-column label="导入设备" show-overflow-tooltip>
              <template #default="{ row }">
                {{ row.deviceTypeName && row.deviceAddress ? row.deviceTypeName + '/' + row.deviceAddress : '--' }}
              </template>
            </el-table-column>
            <el-table-column label="设备类型" show-overflow-tooltip>
              <template #default="{ row }">
                {{ row.deviceTypeName || '--' }}
              </template>
            </el-table-column>
            <el-table-column label="安装/使用位置" show-overflow-tooltip>
              <template #default="{ row }">
                {{ row.deviceAddress || '--' }}
              </template>
            </el-table-column>
            <el-table-column fixed="right" width="80" align="center" label="操作">
              <template #default="{ row }">
                <n-button size="small" type="error" ghost @click="delCheckTable(row)">删除</n-button>
              </template>
            </el-table-column>
          </el-table>
          <el-table :data="importDeviceData" height="74vh" empty-text="暂无数据" stripe v-else style="width: 100%">
            <el-table-column label="导入重点部位" show-overflow-tooltip>
              <template #default="{ row }">
                {{ row.keyPartName && row.position ? row.keyPartName + '/' + row.position : '--' }}
              </template>
            </el-table-column>
            <el-table-column label="重点部位名称" show-overflow-tooltip>
              <template #default="{ row }">
                {{ row.keyPartName || '--' }}
              </template>
            </el-table-column>
            <el-table-column label="位置" show-overflow-tooltip>
              <template #default="{ row }">
                {{ row.position || '--' }}
              </template>
            </el-table-column>
            <el-table-column fixed="right" width="80" align="center" label="操作">
              <template #default="{ row }">
                <n-button size="small" type="error" ghost @click="delCheckTable(row)">删除</n-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-col>
      <el-col :span="14">
        <div class="bg-white rounded p-4 h-full">
          <div class="flex justify-start items-center mb-4 text-base">
            <div class="title-ico"></div>
            <div>
              {{ pointRefType == 2 ? '从设备管理中选择' : '从重点部位中选择' }}
            </div>
          </div>
          <!-- 导入设备模块 -->
          <RadioTab
            :tabList="tabList"
            :tab="curTab"
            @change="tabChange"
            style="padding-top: 0"
            v-if="tabList && tabList.length && curTab && pointRefType == 2"
          />
          <el-form :model="deviceSearchForm" class="mt-5" ref="deviceFormRef" v-if="pointRefType == 2">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="关键词:" prop="deviceName">
                  <el-input
                    v-model="deviceSearchForm.deviceName"
                    @input="getDeviceList()"
                    clearable
                    placeholder="搜索设备安装位置"
                    :suffix-icon="Search"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="设备类型:" prop="deviceType">
                  <el-tree-select
                    v-model="deviceSearchForm.deviceType"
                    :data="treeData"
                    collapse-tags
                    @change="getDeviceList()"
                    multiple
                    node-key="deviceTypeId"
                    :props="treeProps"
                    placeholder="全部"
                    :render-after-expand="false"
                    show-checkbox
                    no-match-text="无匹配数据"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="设备状态:" prop="deviceStatus">
                  <el-select
                    v-model="deviceSearchForm.deviceStatus"
                    placeholder="全部"
                    @change="getDeviceList()"
                    clearable
                  >
                    <el-option
                      v-for="item in statusOpts"
                      :key="item.dictValue"
                      :label="item.dictLabel"
                      :value="item.dictValue"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <!-- 注释下 接口不支持 -->
              <el-col :span="8">
                <el-form-item label="楼栋楼层:" prop="floorId">
                  <el-tree-select
                    v-model="deviceSearchForm.floorId"
                    :data="optTreeData"
                    placeholder="请选择楼栋楼层"
                    @change="changeSelect"
                    filterable
                    :render-after-expand="false"
                    clearable
                    no-match-text="无匹配数据"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <el-table
            empty-text="暂无数据"
            :data="deviceList"
            v-loading="loading"
            height="58vh"
            stripe
            highlight-current-row
            @row-click="handleCurrentChange"
            class="cursor-pointer"
            v-if="pointRefType == 2"
          >
            <el-table-column prop="unitName" label="所属单位" show-overflow-tooltip />
            <el-table-column prop="deviceCode" label="资产编码" show-overflow-tooltip>
              <template #default="{ row }">
                {{ row.deviceCode || '--' }}
              </template>
            </el-table-column>
            <el-table-column prop="deviceTypeName" label="设备类型" show-overflow-tooltip>
              <template #default="{ row }">
                {{ row.deviceTypeName || '--' }}
              </template>
            </el-table-column>
            <el-table-column prop="deviceAddress" label="安装/使用位置" show-overflow-tooltip>
              <template #default="{ row }">
                {{ row.deviceAddress || '--' }}
              </template>
            </el-table-column>
            <el-table-column prop="deviceStatusName" label="设备状态" show-overflow-tooltip>
              <template #default="{ row }">
                {{ row.deviceStatusName || '--' }}
              </template>
            </el-table-column>
            <el-table-column prop="lastMaintainDate" label="上次维护日期" show-overflow-tooltip>
              <template #default="{ row }">
                {{ row.lastMaintainDate || '--' }}
              </template>
            </el-table-column>
          </el-table>
          <!-- 导入重点部位模块 -->
          <el-row :gutter="20" v-if="pointRefType == 3">
            <el-col :span="8">
              <el-form-item label="关键词:">
                <el-input
                  v-model="keyPartName"
                  @input="getKeyPartList()"
                  clearable
                  placeholder="搜索重点部位名称"
                  :suffix-icon="Search"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="楼栋楼层:">
                <el-tree-select
                  v-model="keyfloorId"
                  :data="optTreeData"
                  placeholder="请选择楼栋楼层"
                  @change="changeSelectZD"
                  filterable
                  :render-after-expand="false"
                  clearable
                  no-match-text="无匹配数据"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-table
            empty-text="暂无数据"
            :data="KeyPartPageList"
            v-loading="loading"
            height="65vh"
            stripe
            highlight-current-row
            @row-click="handleCurrentChange"
            class="cursor-pointer"
            v-if="pointRefType == 3"
          >
            <el-table-column label="所属单位" show-overflow-tooltip>
              <template #default="{ row }">
                {{ row.unitName || '--' }}
              </template>
            </el-table-column>
            <el-table-column label="重点部位名称" show-overflow-tooltip>
              <template #default="{ row }">
                {{ row.keyPartName || '--' }}
              </template>
            </el-table-column>
            <el-table-column label="部位码" show-overflow-tooltip>
              <template #default="{ row }">
                {{ row.partCode || '--' }}
              </template>
            </el-table-column>
            <el-table-column label="位置" show-overflow-tooltip>
              <template #default="{ row }">
                {{ row.position || '--' }}
              </template>
            </el-table-column>
            <el-table-column label="检查频率" show-overflow-tooltip>
              <template #default="{ row }">
                {{ row.checkPeriod + '天' || '--' }}
              </template>
            </el-table-column>
            <el-table-column label="最近检查日期" show-overflow-tooltip>
              <template #default="{ row }">
                {{ row.checkTime || '--' }}
              </template>
            </el-table-column>
          </el-table>
          <!-- <div class="flex justify-end items-center mt-4">
            <n-pagination
              v-model:page="pagination.pageNo"
              :item-count="pagination.total"
              size="medium"
              show-quick-jumper
              :page-size="pagination.pageSize"
              :page-sizes="pageSizes"
              show-size-picker
              :on-update:page="pageUpdate"
              :on-update:pageSize="pagesizeUpdate"
            >
              <template #prefix> 共 {{ pagination.total }} 条 </template>
            </n-pagination>
          </div> -->
          <div class="flex justify-end items-end mt-4">
            <div class="mr-3">共 {{ pagination.total }} 条</div>
            <el-pagination
              v-model:current-page="pagination.pageNo"
              v-model:page-size="pagination.pageSize"
              :total="pagination.total"
              @size-change="pagesizeUpdate"
              @current-change="pageUpdate"
              layout="prev, pager, next, sizes, jumper"
              :page-sizes="[10, 20, 30, 40, 50, 100]"
              background
              small
            />
          </div>
        </div>
      </el-col>
    </el-row>
    <template #footer>
      <el-button @click="closeDeviceDialog" :loading="submitLoading">取消</el-button>
      <el-button type="primary" @click="importCheckPointByBatchApi" :loading="submitLoading">确定导入</el-button>
    </template>
  </el-drawer>
</template>
<script lang="ts" setup>
import { ref, nextTick } from 'vue';
import {
  getDeviceMainTypePageList,
  getDevicePageList,
  getDeviceTypeList,
  getTagList,
  queryDictDataByType,
  importCheckPointByBatch,
  keyPartManageList,
} from '../fetchData';
import { getBuildingTreeByUnitId } from '@/views/inspection_point/fetchData';
import { Search } from '@element-plus/icons-vue';
import RadioTab from '@/components/tab/ComRadioTabE.vue';
import { useStore } from '@/store';
import { ElMessage } from 'element-plus';

const emit = defineEmits(['success']);
const { userInfo } = useStore();
const props = defineProps({
  unitId: {
    type: String,
    default: '',
  },
});

const dialogVisible = ref(false);
const pointRefType = ref(2); //导入类型 2设备、3重点部位
const submitLoading = ref(false);
const curTab = ref();
const tabList = ref();

const deviceSearchForm = ref({
  deviceName: null,
  orgCode: props.unitId,
  mainTypeId: '',
  deviceType: null, //设备类型
  flag: null, //标签
  deviceStatus: null, //设备状态
  floorId: null,
});
const optTreeData = ref([]);
const keyPartName = ref();
const keyfloorId = ref();
const KeyPartPageList = ref();
const pagination = ref({
  pageNo: 1,
  pageSize: 10,
  total: 0,
});
const pageSizes = [10, 20, 30, 40, 50];
const deviceFormRef = ref();
const treeData = ref();
const laegipstOpts = ref();
const statusOpts = ref();
const deviceList = ref([]);
const importDeviceData = ref<any>([]);
const loading = ref(false);
const checkDeviceDialog = ref(false);

// 切换导入方式
const pointRefTypeChange = (type: number) => {
  pointRefType.value = type;
};

// 开启选择导入方式的弹窗
const showImportTypeDialog = () => {
  pointRefType.value = 2;
  dialogVisible.value = true;
};

const closeDeviceDialog = () => {
  checkDeviceDialog.value = false;
  deviceSearchForm.value.deviceName = null;
  deviceSearchForm.value.mainTypeId = '';
  deviceSearchForm.value.deviceType = null;
  deviceSearchForm.value.flag = null;
  deviceSearchForm.value.deviceStatus = null;
  deviceSearchForm.value.floorId = null;
  keyPartName.value = '';
  keyfloorId.value = '';
};

// 确定导入方式并关闭弹窗
const confirmImportType = async () => {
  dialogVisible.value = false;
  // 清空之前选择的点位数据
  importDeviceData.value = [];
  checkDeviceDialog.value = true;

  if (pointRefType.value == 2) {
    //导入类型为设备
    // 重置设备列表搜索条件
    nextTick(() => {
      deviceFormRef.value && deviceFormRef.value.resetFields();
      pagination.value = {
        pageNo: 1,
        pageSize: 10,
        total: 0,
      };
    });
    // 获取设备类型tab
    getTabTypeList();
  } else {
    getBuildingTreeByUnitIdApi();
    //导入类型为点位
    keyPartName.value = '';
    // 获取重点部位列表
    getKeyPartList();
  }
};

// 获取重点部位列表
const getKeyPartList = async () => {
  loading.value = true;
  let { data, code }: any = await keyPartManageList(
    {
      keyPartName: keyPartName.value,
      floorId: keyfloorId.value,
      orgCode: props.unitId,
      ...pagination.value,
    },
    userInfo.zhId
  );
  KeyPartPageList.value = data.rows;
  pagination.value.total = data.total;
  if (code) loading.value = false;
};

const pageUpdate = (page: number) => {
  pagination.value.pageNo = page;
  pointRefType.value == 2 ? getDeviceList() : getKeyPartList();
};

const pagesizeUpdate = (pagesize: number) => {
  pagination.value.pageSize = pagesize;
  pointRefType.value == 2 ? getDeviceList() : getKeyPartList();
};

// 获取设备类型tab
const getTabTypeList = async () => {
  let { data }: any = await getDeviceMainTypePageList(
    {
      pageNo: 1,
      pageSize: 10,
    },
    userInfo.zhId
  );
  tabList.value = data.rows.map((item: any) => {
    return {
      label: item.deviceTypeName,
      name: item.deviceTypeId + '',
    };
  });
  // 获取标签枚举
  getLaegipstOpts();
  // 获取设备状态枚举
  queryDictDataByTypeOpts();
  // 默认选中第一个tab
  tabChange(tabList.value[0].name);
};

// 配置设备类型参数配置
const treeProps = {
  label: 'deviceTypeName',
  children: 'children',
};

// 获取设备列表
const getDeviceList = async () => {
  loading.value = true;
  let { data, code }: any = await getDevicePageList(
    {
      ...deviceSearchForm.value,
      ...pagination.value,
      orgCode: props.unitId,
      deviceType: deviceSearchForm.value.deviceType ? deviceSearchForm.value.deviceType.join(',') : null,
    },
    userInfo.zhId
  );
  pagination.value.total = data.total;
  deviceList.value = data.rows.map((item: any) => {
    return {
      ...item,
      // https://agjp.tanzervas.com/aqsc/v1//ycsy/ycsy/420000DW1862378631284654080/ces_202502071955016917555.png 如果ycsy出现两次，则只保留一个 确保链接能够访问  (临时使用）
      qrcodeUrl: (item.qrcodeUrl.match(/ycsy/g) || []).length == 2 ? normalizeURL(item.qrcodeUrl) : item.qrcodeUrl,
    };
  });
  if (code) loading.value = false;
};

/**
 * 修复URL路径中的重复目录和多余斜杠
 * @param {string} url - 原始URL
 * @returns {string} 规范化后的URL
 */
function normalizeURL(url: string) {
  // 分解URL组成部分
  const [protocol, ...rest] = url.split('://');
  const domainParts = rest.join('://').split('/');
  const host = domainParts.shift();

  // 处理路径部分
  const pathSegments = domainParts
    .join('/') // 重新拼接路径
    .replace(/\/+/g, '/') // 合并连续斜杠
    .split('/')
    .filter(Boolean); // 移除空段

  // 移除相邻重复目录
  const deduplicated = pathSegments.reduce((acc, cur) => {
    if (acc[acc.length - 1] !== cur) acc.push(cur);
    return acc;
  }, []);

  // 重构完整URL
  return [protocol + '://' + host, ...deduplicated].join('/').replace(/([^:]\/)\/+/g, '$1'); // 最后清理斜杠
}

// 获取标签枚举
const getLaegipstOpts = async () => {
  let { data }: any = await getTagList({
    pageNo: 1,
    pageSize: -1,
  });
  laegipstOpts.value = data.rows;
};

// 获取设备状态枚举
const queryDictDataByTypeOpts = async () => {
  let { data }: any = await queryDictDataByType({
    type: 'deviceStatus',
    filterValues: '',
  });
  statusOpts.value = data;
};

// 获取设备类型树
const getDeviceTypeTree = async (deviceTypeId: any) => {
  let { data }: any = await getDeviceTypeList({ deviceTypeId });
  treeData.value = data;
};

// tab切换
function tabChange(name: string) {
  curTab.value = name;
  deviceSearchForm.value.mainTypeId = name;
  getBuildingTreeByUnitIdApi();
  // 获取设备类型树
  getDeviceTypeTree(name);
  // 获取设备列表
  getDeviceList();
}

// 删除已选数据
const delCheckTable = (row: any) => {
  let index = importDeviceData.value.findIndex((item: any) => item.deviceId == row.deviceId);
  if (index != -1) {
    importDeviceData.value.splice(index, 1);
  }
};

// 选择table数据
const handleCurrentChange = (row: any) => {
  if (!row) return;
  let deviceIds = importDeviceData.value.map((item: any) => item.deviceId);
  let KeyPartIds = importDeviceData.value.map((item: any) => item.id);
  if (pointRefType.value == 2 && deviceIds.includes(row.deviceId)) {
    return ElMessage({
      message: '该设备已存在，请勿重复添加',
      type: 'warning',
    });
  }
  if (pointRefType.value == 3 && KeyPartIds.includes(row.id)) {
    return ElMessage({
      message: '该重点部位已存在，请勿重复添加',
      type: 'warning',
    });
  }
  if (!row.buildingId || !row.floorId) {
    return ElMessage({
      message: '未设置楼栋楼层信息，不可选中',
      type: 'warning',
    });
  }

  if (!row || loading.value) return;
  importDeviceData.value.unshift({
    ...row,
    pointRefId: pointRefType.value == 2 ? row.deviceId : row.id,
    location: pointRefType.value == 2 ? row.deviceAddress : row.position,
    checkTableId: row.checkFormId,
    pointName: pointRefType.value == 2 ? row.deviceTypeName || row.deviceAddress : row.keyPartName,
    isKeyArea: pointRefType.value == 2 ? '0' : '1', //设备重点部位默认为否  重点部位默认为是
    unitId: props.unitId,
    zhId: userInfo.zhId,
    createBy: userInfo.id,
    pointRefType: pointRefType.value, //导入类型
    qrCodeName: row.keyPartName,
    qrCodeUrl: row.qrcodeUrl,
  });
};

// 确定导入
const importCheckPointByBatchApi = async () => {
  submitLoading.value = true;
  let importList = importDeviceData.value.map((item: any) => {
    return {
      ...item,
      id: null,
    };
  });
  importCheckPointByBatch({ importList })
    .then((res: any) => {
      submitLoading.value = false;
      if (res.code == 'success') {
        checkDeviceDialog.value = false;
        emit('success');
      }
    })
    .catch((res) => {
      submitLoading.value = false;
    });
};

/** 获取楼栋楼层树数据 */
const getBuildingTreeByUnitIdApi = () => {
  let unitId = props.unitId;
  return new Promise(async (resolve, reject) => {
    const res: any = await getBuildingTreeByUnitId({ unitId });
    optTreeData.value = res.data.options;
    resolve(true);
  });
};
const changeSelect = (e: any) => {
  deviceSearchForm.value.floorId = e;
  getDeviceList();
};
const changeSelectZD = (e: any) => {
  keyfloorId.value = e;
  getKeyPartList();
};

defineExpose({ showImportTypeDialog });
defineOptions({ name: 'ImportPoint' });
</script>
<style scoped lang="scss">
.relevance-check-table {
  .el-select__wrapper.is-disabled,
  .el-select__wrapper.is-disabled:hover {
    cursor: pointer !important;
  }
}

::v-deep .el-table__header-wrapper:not(.custom-class) .el-checkbox {
  display: none !important;
}

.selected {
  box-shadow: inset 0px 1px 3px 0px rgba(0, 0, 0, 0.5);
  border: 2px solid #527cff;
  color: #527cff;
}

.title-ico {
  width: 4px;
  height: 12px;
  background: #4070ff;
  border-radius: 2px;
  margin-right: 10px;
}

::v-deep .highlight-row {
  .el-table__cell {
    background: rgba(82, 124, 255, 0.4) !important;
    color: #fff;
  }
}

::v-deep .el-table__body tr.current-row > td {
  background: rgba(82, 124, 255, 0.4) !important;
  color: #fff;
}

::v-deep .el-table--enable-row-hover .el-table__body tr:hover > td {
  background: rgba(82, 124, 255, 0.4);
  color: #fff;
}

::v-deep .el-pagination {
  .el-pagination__sizes {
    margin-left: 15px;
  }

  .el-pagination__jump {
    margin-left: 15px;
  }

  .el-input__inner {
    height: 26px;
    line-height: 26px;
  }

  button {
    min-width: 24px;
    height: 24px;
  }

  .el-pager li {
    min-width: 24px;
    height: 24px;
    line-height: 24px;
  }
}
</style>
