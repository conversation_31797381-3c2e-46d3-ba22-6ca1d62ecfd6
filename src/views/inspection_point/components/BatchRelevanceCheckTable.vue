<template>
  <!-- 选择检查表 -->
  <el-drawer v-model="checkTableDialog" size="80%" direction="rtl" :show-close="false" :close-on-click-modal="false">
    <template #header>
      <div class="flex justify-between items-center">
        <div class="flex justify-start items-center">
          <img src="@/assets/title_ico.png" style="width: 17px" alt="" />
          <div style="font-size: 16px" class="ml-2 text-black">批量关联检查表</div>
        </div>
        <img src="@/assets/dialog_close.png" style="width: 42px" class="cursor-pointer" alt="" @click="closeDrow" />
      </div>
    </template>
    <el-row :gutter="25" class="h-full">
      <!-- 巡查点位 -->
      <el-col :span="11">
        <div class="bg-white rounded p-4 h-full">
          <el-row :gutter="20">
            <el-col :span="24" class="text-[red] opacity-0 py-[10px]">*请选择要关联的检查表</el-col>
            <el-col :span="12">
              <el-form-item label="检查表：">
                <el-select v-model="checkSelects" placeholder="请选择" clearable @change="changeSelect">
                  <el-option label="全部" value="" />
                  <el-option
                    v-for="item in checkOpts"
                    :key="item.checkTableName"
                    :label="item.checkTableName"
                    :value="item.checkTableId"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="">
                <el-input
                  v-model.trim="keyword"
                  placeholder="请输入位置名称"
                  maxlength="50"
                  show-word-limit
                  @input="multipleSelectionSearch"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-table
            empty-text="暂无数据"
            :data="keyword || checkSelects ? filterSelection : multipleSelection"
            height="74vh"
            ref="singleTables"
            row-key="id"
            stripe
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" reserve-selection></el-table-column>
            <el-table-column type="index" label="序号" width="70" />
            <el-table-column prop="pointFullName" label="位置名称" show-overflow-tooltip />
            <el-table-column prop="checkTableName" label="关联检查表" show-overflow-tooltip>
              <template #default="{ row }">
                <div class="cursor-pointer flex justify-between items-center">
                  <div class="w-[200px] truncate">{{ row.checkTableName }}</div>
                  <el-button
                    v-if="row.checkTableId"
                    class="ml-1"
                    type="danger"
                    :icon="Delete"
                    circle
                    size="small"
                    @click.stop="delCheckTable(row)"
                  />
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-col>
      <el-col :span="1" class="justify-center items-center">
        <img src="@/assets/select_tree.png" :style="{ width: toVw(50) }" alt="" />
      </el-col>
      <!-- 检查表 -->
      <el-col :span="12">
        <div class="bg-white rounded p-4 h-full">
          <el-row :gutter="20">
            <el-col :span="24" class="text-[red] py-[10px]">*请选择要关联的检查表</el-col>
            <el-col :span="14" class="flex items-center flex-row">
              <el-form-item label="编制单位：" class="flex-1">
                <el-select
                  :disabled="standard"
                  v-model="checkTableunitId"
                  placeholder="全部"
                  clearable
                  @change="getCheckTableDataApi()"
                >
                  <el-option v-for="item in unitOpts" :key="item.orgCode" :label="item.orgName" :value="item.orgCode" />
                </el-select>
              </el-form-item>
              <n-checkbox class="mb-[20px] ml-[12px]" v-model:checked="standard" :on-update:checked="handleCheckbox"
                >仅看本单位</n-checkbox
              >
            </el-col>
            <el-col :span="10">
              <el-form-item label="">
                <el-input
                  v-model="checkTableName"
                  @input="getCheckTableDataApi()"
                  clearable
                  placeholder="请输入检查表名称"
                  :suffix-icon="Search"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-table
            empty-text="暂无数据"
            :data="checkTableData"
            height="74vh"
            ref="singleTable"
            v-loading="loading"
            highlight-current-row
            @row-click="(row: any) => handleCurrentChange(row)"
            class="cursor-pointer"
          >
            <el-table-column type="index" label="序号" width="70" />
            <el-table-column prop="checkTableName" label="检查表名称" show-overflow-tooltip />
            <el-table-column prop="unitName" label="编制单位" show-overflow-tooltip />
            <el-table-column prop="createTime" label="编制时间" show-overflow-tooltip />
            <el-table-column label="操作" width="90">
              <template #default="{ row }">
                <n-button size="small" type="primary" ghost @click="goTableDetail(row)">详情</n-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-col>
    </el-row>

    <template #footer>
      <div class="flex justify-end items-center">
        <el-button @click="closeDrow">取消</el-button>
        <el-button type="primary" @click="bindCheckTableByBatchApi">确定选择</el-button>
      </div>
    </template>
  </el-drawer>
  <CheckTableDetail v-model:show="checkItemDialog" :row="checkItemDetail" @close="checkItemDialog = false" />
</template>
<script setup lang="ts">
import { ref } from 'vue';
import { getCheckList, getUpAndDownUnitInfo, getCheckTableDetail, bindCheckTableByBatch } from '../fetchData';
import CheckTableDetail from '@/views/inspect-mgr/inspect/comp/CheckTableDetail.vue';
import { Search } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { useStore } from '@/store';
import { Delete } from '@element-plus/icons-vue';
import { toVw } from '@/utils/fit';

const props = defineProps({
  _multipleSelection: {
    type: Array,
    default: () => [],
  },
  unitId: {
    type: String,
    default: '',
  },
});
const emit = defineEmits(['success']);
const { userInfo } = useStore();

const multipleSelection = ref();
const filterSelection = ref(); // 用于位置条件过滤

// 选择检查表变量
const checkTableDialog = ref(false);
const checkItemDetail = ref<any>({});
const checkItemDialog = ref(false);
const checkTableunitId = ref(null);
const checkTableName = ref('');
const unitOpts = ref();
const singleTable = ref();
const singleTables = ref();
const checkSelects = ref();
const checkOpts = ref<any>([]);
const currentId = ref(-1);
const keyword = ref('');
// 检查表数据
const checkTableData = ref([]);
const loading = ref(false);
const standard = ref(false);
const handleCheckbox = (val: any) => {
  standard.value = val;
  checkTableunitId.value = null;
  getCheckTableDataApi();
};

// 显示检查表列表
const showCheckTableDialog = async () => {
  multipleSelection.value = props._multipleSelection ? JSON.parse(JSON.stringify(props._multipleSelection)) : [];
  console.log(multipleSelection.value);
  getOption(multipleSelection.value);
  // 重置检查表搜索条件
  checkTableunitId.value = null;
  checkTableName.value = '';
  getCurrentId(multipleSelection.value[0]);
  // 详情页不允许选择检查表
  checkTableDialog.value = true;
  // 获取编制单位
  getOrgCodeUpListApi().then(() => {
    // 获取检查表表列表
    getCheckTableDataApi();
  });
};

const multiple = ref([]);
// 选择检查表
const handleSelectionChange = (val: any) => {
  multiple.value = val;
};
const changeSelect = (e: any) => {
  filterSelection.value = e ? multipleSelection.value.filter((item: any) => item.checkTableId == e) : [];
};
const getOption = (arrayOpt: any) => {
  if (!arrayOpt.length) return [];
  const _opt = arrayOpt;
  let _arr = [];
  for (let index = 0; index < _opt.length; index++) {
    const element = _opt[index];
    if (_opt[index].checkTableName) {
      _arr.push({
        checkTableName: _opt[index].checkTableName,
        checkTableId: _opt[index].checkTableId,
      });
    }
  }
  checkOpts.value = _arr?.filter(
    (item, index, self) =>
      index ===
      self.findIndex((obj) => obj.checkTableId === item.checkTableId && obj.checkTableName === item.checkTableName)
  );
};

// 获取检查表列表
const getCheckTableDataApi = async () => {
  loading.value = true;
  const parms = {
    unitId: checkTableunitId.value || unitOpts.value.map((item: any) => item.orgCode).join(','),
    topUnitId: userInfo.topUnitId,
    checkTableType: 1, //类型为综合检查表
    checkStatus: 0, //状态为启用
    pageNo: 1,
    pageSize: -1, //查所有
    checkTableName: checkTableName.value, //检查表名称
    flag: 1,
  };
  if (standard.value) parms.unitId = userInfo.unitId;
  const res: any = await getCheckList(parms);
  loading.value = false;
  checkTableData.value = res.data.rows || [];
};

// 获取编制单位
const getOrgCodeUpListApi = () => {
  return new Promise<void>(async (resolve, reject) => {
    let { data } = await getUpAndDownUnitInfo({ unitId: userInfo.unitId });
    unitOpts.value = data || [];
    resolve();
  });
};

// 检查表被点击
const delCheckTable = (row: any) => {
  multipleSelection.value.forEach((item: any, index: number) => {
    if (item.id == row.id) {
      multipleSelection.value[index] = {
        ...row,
        checkTableName: '',
        checkTableId: '',
      };
    }
  });
};

// 检查表被点击
const handleCurrentChange = (row: any) => {
  if (!row) return;
  let { checkTableName, id } = row;
  if (!multiple.value.length) {
    return ElMessage({
      message: '请选择要添加的巡查点位',
      type: 'warning',
    });
  }
  // 关键字搜索的结果
  if (keyword.value || checkSelects.value) {
    multiple.value.forEach((kk: any, _index: number) => {
      filterSelection.value.forEach((item: any, index: number) => {
        if (kk.id == item.id) {
          filterSelection.value[index] = {
            ...item,
            checkTableName,
            checkTableId: id,
          };
        }
      });
    });
    multipleSelection.value.forEach((item: any, index: number) => {
      filterSelection.value.forEach((filterItem: any) => {
        if (item.id == filterItem.id) {
          multipleSelection.value[index] = {
            ...item,
            ...filterItem,
          };
        }
      });
    });
  } else {
    multiple.value.forEach((kk: any, _index: number) => {
      multipleSelection.value.forEach((item: any, index: number) => {
        if (kk.id == item.id) {
          multipleSelection.value[index] = {
            ...item,
            checkTableName,
            checkTableId: id,
          };
        }
      });
    });
  }
  getOption(multipleSelection.value);
};

// 获取点击的下标 作为添加检查表的位置
const getCurrentId = ({ id }: any) => {
  currentId.value = id;
};

const getRowClassName = ({ row }: any) => {
  if (currentId.value == row.id) {
    return 'highlight-row';
  }
  return '';
};

// 根据位置关键字筛选
const multipleSelectionSearch = (val: any) => {
  filterSelection.value = val ? multipleSelection.value.filter((item: any) => item.pointName.includes(val)) : [];
  if (filterSelection.value.length) {
    getCurrentId(filterSelection.value[0]);
  }
};

// 关闭弹窗
const closeDrow = () => {
  singleTables.value && singleTables.value.clearSelection();
  multiple.value = [];
  standard.value = false;
  checkSelects.value = '';
  keyword.value = '';
  checkTableDialog.value = false;
};

// 单独配置检查表(作为组件使用)
const bindCheckTableByBatchApi = async () => {
  let bindList = multipleSelection.value.map((item: any) => {
    return {
      checkPointId: item.id,
      checkTableId: item.checkTableId,
    };
  });
  let { code }: any = await bindCheckTableByBatch({ bindList });
  if (code == 'success') {
    closeDrow();
    emit('success');
  }
};

// 检查表详情
const goTableDetail = async (row: any) => {
  checkItemDetail.value = row;
  checkItemDialog.value = true;
};

defineExpose({ showCheckTableDialog });
defineOptions({ name: 'BatchRelevanceCheckTable' });
</script>
<style scoped lang="scss">
.relevance-check-table {
  .el-select__wrapper.is-disabled,
  .el-select__wrapper.is-disabled:hover {
    cursor: pointer !important;
  }
}

// ::v-deep .el-table__header-wrapper:not(.custom-class) .el-checkbox {
//   display: none !important;
// }

::v-deep .highlight-row {
  .el-table__cell {
    background: rgba(82, 124, 255, 0.4) !important;
    color: #fff;
  }
}

::v-deep .el-table__body tr.current-row > td {
  background: rgba(82, 124, 255, 0.4) !important;
  color: #fff;
}

::v-deep .el-table--enable-row-hover .el-table__body tr:hover > td {
  background: rgba(82, 124, 255, 0.4);
  color: #fff;
}

.el-col-1,
.el-col-1.is-guttered {
  display: flex;
  padding: 0;
}

.flex {
  display: flex;
}
</style>
