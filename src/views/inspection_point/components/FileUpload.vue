<template>
  <!-- 选择导入方式 -->
  <el-dialog
    v-model="dialogVisiblefile"
    width="800px"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <template #header>
      <div class="flex justify-between items-center">
        <div class="flex justify-start items-center">
          <img src="@/assets/title_ico.png" style="width: 17px" alt="" />
          <div style="font-size: 16px" class="ml-2 text-black">{{ title }}</div>
        </div>
        <img src="@/assets/dialog_close.png" style="width: 42px" class="cursor-pointer" alt="" @click="closeDialog" />
      </div>
    </template>
    <div class="flex flex-col justify-center items-end" style="height: 350px">
      <n-upload
        ref="uploadRef"
        directory-dnd
        :action="actionUrl"
        :data="params"
        show-retry-button
        with-credentials
        :max="1"
        accept=".xlsx"
        :is-error-state="isErrorState"
      >
        <n-upload-dragger>
          <div style="margin-bottom: 12px">
            <n-icon size="48" :depth="3">
              <ArchiveIcon />
            </n-icon>
          </div>
          <n-text style="font-size: 16px"> 点击或者拖动文件到该区域来上传 </n-text>
        </n-upload-dragger>
      </n-upload>
      <div class="flex justify-end template-url mt-[60px]" @click="downloadTemplate" v-if="templateUrl">
        下载导入模版
      </div>
    </div>
  </el-dialog>
</template>
<script lang="ts" setup>
import { ref } from 'vue';
import { ArchiveOutline as ArchiveIcon } from '@vicons/ionicons5';
import { ElMessage } from 'element-plus';
import { fileDownloader } from '@/utils/fileDownloader';

const emit = defineEmits(['success']);
const props = defineProps({
  title: {
    type: String,
    default: '导入',
  },
  params: {
    type: Object,
    default: () => {},
  },
  actionUrl: {
    type: String,
    default: '',
  },
  fileName: {
    type: String,
    default: '',
  },
  templateUrl: {
    type: String,
    default: '',
  },
});

const dialogVisiblefile = ref(false);
const uploadRef = ref();

function isErrorState(e: any) {
  let { code, message } = JSON.parse(e.response);
  if (code == 'success') {
    ElMessage({
      message: '导入成功',
      type: code,
    });
    dialogVisiblefile.value = false;
    uploadRef.value.clear();
    emit('success');
  } else {
    ElMessage({
      message,
      type: code,
    });
  }
}

function downloadTemplate() {
  fileDownloader(props.templateUrl, {
    filename: props.fileName + '导入模板.xlsx',
    method: 'POST',
    contentType: 'application/json',
  });
}

function closeDialog() {
  dialogVisiblefile.value = false;
  uploadRef.value.clear();
}

// 开启选择导入方式的弹窗
const showImportTypeDialogfile = () => {
  dialogVisiblefile.value = true;
};

defineExpose({ showImportTypeDialogfile });
defineOptions({ name: 'ImportPoint' });
</script>
<style scoped lang="scss">
.template-url {
  color: #3e62eb;
  text-decoration: underline;
  cursor: pointer;
}

.n-upload {
  height: 80%;

  .n-upload-dragger {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
}

:deep(.n-upload-trigger) {
  height: 100%;
}
</style>
