<template>
  <div class="flex flex-col justify-between" style="height: 100%">
    <n-data-table
      class="h-full com-table"
      remote
      :columns="columns"
      :data="tableData"
      :bordered="false"
      v-model:checked-row-keys="idList"
      :pagination="pagination"
      :flex-height="true"
      v-loading="loading"
      :render-cell="useEmptyCell"
      :scroll-x="1500"
      :row-key="(row: any) => row.id"
    />

    <!-- 新建编辑巡查点位 + 单独配置检查表 -->
    <FormDialog
      ref="formDialogRef"
      @success="resetQuery"
      :id="id"
      :checkTableId="checkTableId"
      :formDialogType="formDialogType"
      :unitId="props.unitId"
    />
    <!-- 批量关联检查表 -->
    <BatchRelevanceCheckTable
      ref="batchRelevanceCheckTableRef"
      @success="resetQuery"
      :unitId="props.unitId"
      :_multipleSelection="_multipleSelection"
    />
    <!-- 设备或重点部位导入 -->
    <ImportPoint
      ref="importPointRef"
      @success="resetQuery"
      :unitId="props.unitId"
      :multipleSelection="_multipleSelection"
    />

    <!-- 批量导入 -->
    <FileUpload
      ref="FileUploadRef"
      @success="resetQuery"
      :params="{ operatorUnitId: userInfo.topUnitId, unitId, zhId: userInfo.zhId, createBy: userInfo.id }"
      :actionUrl="getActionUrl()"
      :templateUrl="getTemplateUrl({ unitId, unitName })"
      fileName="巡查点位"
      title="批量导入"
    />
    <!-- 查看二维码 -->
    <ErCode ref="erCodeRef" />
  </div>
</template>

<script setup lang="ts">
import { ref, h, computed } from 'vue';
import { NImage, NButton } from 'naive-ui';
import { IObj } from '@/types';
import { getCheckPointList, deleteCheckPointById, exportBatchQrCode, getTemplateUrl, getActionUrl } from '../fetchData';
import { useStore } from '@/store';
import { fileDownloader } from '@/utils/fileDownloader';
import { ElMessage, ElMessageBox } from 'element-plus';
import FormDialog from './FormDialog.vue';
import BatchRelevanceCheckTable from './BatchRelevanceCheckTable.vue';
import ImportPoint from './ImportPoint.vue';
import getFileURL from '@/utils/getFileURL';
import FileUpload from './FileUpload.vue';
import ErCode from './ErCode.vue';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';

const props = defineProps({
  unitId: {
    type: String,
    default: '',
  },
  unitName: {
    type: String,
    default: '',
  },
});

const { userInfo } = useStore();
const emits = defineEmits(['action']);
const idList = ref<any[]>([]);
const { pagination, updateTotal } = useNaivePagination(getTableData);
const queryParams = ref(); // 搜索条件
const tableData = ref();
const loading = ref(false);
const id = ref('');
const checkTableId = ref('');
const formDialogRef = ref();
const batchRelevanceCheckTableRef = ref();
const FileUploadRef = ref();
const erCodeRef = ref();
const formDialogType = ref<any>(1); //1新增  2编辑  3详情
const importPointRef = ref();
const _multipleSelection = computed(() => {
  let arr: any = [];
  tableData.value?.forEach((item: any) => {
    if (idList.value.includes(item.id)) {
      arr.push(item);
    }
  });
  return arr;
});

let columns = [
  {
    type: 'selection',
  },
  {
    title: '序号',
    key: 'index',
    width: 80,
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
    render(row: any, index: number) {
      let { page, pageSize }: any = pagination;
      return (page - 1) * pageSize + index + 1;
    },
  },
  {
    title: '巡查点位名称',
    key: 'pointFullName',
    align: 'left',
    width: 200,
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '点位ID',
    key: 'pointId',
    align: 'left',
    width: 110,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '点位来源',
    key: 'pointRefType',
    align: 'left',
    width: 100,
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
    render: (row: any) => {
      return row.pointRefType ? ['手动新增', '设备导入', '重点部位导入', 'Excel导入'][row.pointRefType - 1] : '--';
    },
  },
  {
    title: '位置',
    key: 'location',
    align: 'left',
    width: 100,
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '楼栋楼层',
    key: 'building',
    align: 'left',
    width: 170,
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
    render: (row: any) => {
      return row.building && row.floor ? row.building + row.floor : '--';
    },
  },
  {
    title: '是否重点部位',
    key: 'isKeyArea',
    align: 'left',
    width: 120,
    ellipsis: {
      tooltip: true,
    },
    render: (row: any) => {
      return row.isKeyArea != null ? (row.isKeyArea == 1 ? '是' : '否') : '--';
    },
  },
  {
    title: '检查表名称',
    key: 'checkTableName',
    align: 'left',
    width: 170,
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
    render: (row: any) => {
      return h(
        'span',
        {
          style: {
            color: '#409eff',
          },
          class: 'cursor-pointer',
          onClick: () => {
            singleConfigCheckTable({ _id: row.id, _checkTableId: row.checkTableId });
          },
        },
        row.checkTableName ? row.checkTableName : '去配置>'
      );
    },
  },
  {
    title: '点位二维码',
    width: 120,
    ellipsis: {
      tooltip: true,
    },
    render(row: any) {
      let img = h(
        NImage,
        {
          style: 'width: 50px; height: 30px',
          src: getFileURL(row.qrCodeUrl),
        },
        () => row.hazardGradeName
      );
      return row.qrCodeUrl ? img : '--';
    },
  },
  {
    title: '操作',
    fixed: 'right',
    width: 220,
    render(row: any) {
      return getActionBtn(row);
    },
  },
];

function getActionBtn(row: any) {
  const btns: any = [
    [
      h(
        NButton,
        {
          size: 'small',
          type: 'primary',
          ghost: true,
          onClick: () => {
            showPointDialog({ rowId: row.id, rowUnitId: row.unitId, type: 3 });
          },
        },
        { default: () => '详情' }
      ),
    ],
    [
      h(
        NButton,
        {
          size: 'small',
          type: 'primary',
          ghost: true,
          onClick: () => {
            showPointDialog({ rowId: row.id, rowUnitId: row.unitId, type: 2 });
          },
        },
        { default: () => '编辑' }
      ),
    ],
    [
      h(
        NButton,
        {
          size: 'small',
          type: 'error',
          ghost: true,
          onClick: () => {
            delPoint(row.id);
          },
        },
        { default: () => '删除' }
      ),
    ],
  ];
  return useActionDivider(btns);
}

async function getTableData() {
  loading.value = true;
  let { data }: any = await getCheckPointList({
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    ...queryParams.value,
    zhId: userInfo.zhId,
  });
  idList.value = [];
  tableData.value = data.rows || [];
  updateTotal(data.total ?? 0);
  loading.value = false;
}

// 批量下载二维码
const downloadErCodes = () => {
  if (idList.value.length == 0) {
    return ElMessage({
      message: tableData.value && tableData.value.length ? '请选择要下载的二维码' : '暂无数据',
      type: 'warning',
    });
  }
  fileDownloader(exportBatchQrCode(), {
    filename: '巡查点位二维码',
    method: 'POST',
    contentType: 'application/json',
    body: JSON.stringify({ unitName: userInfo.unitName, idList: idList.value }),
  });
};

// 批量关联检查表
const relevanceTable = () => {
  if (idList.value.length == 0 || _multipleSelection.value.length == 0) {
    return ElMessage({
      message: tableData.value && tableData.value.length ? '请选择要关联的巡查点位' : '暂无数据',
      type: 'warning',
    });
  }
  batchRelevanceCheckTableRef.value.showCheckTableDialog();
};
// 删除巡查点位
const delPoint = (id: string) => {
  if (id) {
    ElMessageBox.confirm('确认删除本条数据?', '系统提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(async () => {
      let { code }: any = await deleteCheckPointById({ id });
      if (code == 'success') {
        getTableData();
      }
    });
  }
};

// 批量导入
const importPoint2 = (type: string) => {
  FileUploadRef.value.showImportTypeDialogfile(type);
};

// 设备/重点部位 批量导入
const importPoint = (type: string) => {
  importPointRef.value.showImportTypeDialog(type);
};

// 查看二维码
const showErCodeDialog = (row: any) => {
  erCodeRef.value.showErCodeDialog(row);
};

// 单独配置检查表
const singleConfigCheckTable = ({ _id, _checkTableId }: any) => {
  formDialogType.value = null;
  checkTableId.value = _checkTableId;
  id.value = _id;
  formDialogRef.value.showCheckTableDialog();
};

// 新增编辑巡查点位
const showPointDialog = ({ rowId, rowUnitId, type }: any) => {
  formDialogType.value = type;
  id.value = rowId || '';
  formDialogRef.value.showFormDialog(id.value, rowUnitId);
};

// 新增编辑巡查点位成功
const resetQuery = () => {
  checkTableId.value = '';
  id.value = '';
  formDialogType.value = null;
  getTableData();
};

function getTableDataWrap(data: IObj<any>) {
  queryParams.value = data;
  if (data) pagination.page = 1;
  resetQuery();
}

defineExpose({
  getTableDataWrap,
  downloadErCodes,
  relevanceTable,
  showPointDialog,
  importPoint,
  importPoint2,
  pagination,
  idList,
  resetQuery,
});
defineOptions({ name: 'myTable' });
</script>
<style lang="scss" scoped></style>
