<template>
  <n-collapse :on-item-header-click="collapseClick" :trigger-areas="['main', 'arrow']" arrow-placement="right">
    <n-form :show-feedback="false" label-placement="left" label-width="120px" ref="ruleFormRef">
      <n-collapse-item>
        <template #header>
          <n-button text type="primary" width="120">
            {{ collapse ? '收起' : '展开' }}
          </n-button>
        </template>
        <template #header-extra>
          <n-grid :x-gap="12" :y-gap="8" :cols="3">
            <n-gi>
              <n-form-item label="是否重点部位:">
                <n-select v-model:value="form.isKeyArea" placeholder="全部" :options="comOpts" clearable />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="楼栋楼层:">
                <n-cascader
                  v-model:value="form.floorId"
                  class="w-full flex items-center"
                  :options="optTreeData"
                  expand-trigger="click"
                  check-strategy="child"
                  clearable
                />
              </n-form-item>
            </n-gi>
            <n-gi v-if="collapse">
              <n-form-item>
                <n-input
                  placeholder="请输入点位名称/点位ID/位置模糊搜索"
                  v-model:value="form.keyword"
                  maxlength="50"
                  show-word-limit
                  clearable
                >
                  <template #suffix>
                    <BsSearch />
                  </template>
                </n-input>
              </n-form-item>
            </n-gi>
            <n-gi v-if="!collapse">
              <div class="flex justify-end">
                <el-dropdown split-button type="primary">
                  批量操作
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item>
                        <el-button class="min-w-[190px]" type="primary" plain @click="emits('deleteBatch')"
                          >批量删除</el-button
                        >
                      </el-dropdown-item>
                      <el-dropdown-item>
                        <el-button
                          class="min-w-[190px]"
                          type="primary"
                          plain
                          @click="emits('exportPoint')"
                          :loading="exportLoading"
                          >批量导出</el-button
                        >
                      </el-dropdown-item>
                      <el-dropdown-item>
                        <el-button class="min-w-[190px]" type="primary" plain @click="emits('importPoint2')"
                          >批量导入</el-button
                        >
                      </el-dropdown-item>
                      <el-dropdown-item>
                        <el-button class="min-w-[190px]" type="primary" plain @click="emits('downloadErCodes')"
                          >批量下载二维码</el-button
                        >
                      </el-dropdown-item>
                      <el-dropdown-item>
                        <el-button class="min-w-[190px]" type="primary" plain @click="emits('relevanceTable')"
                          >批量关联检查表</el-button
                        >
                      </el-dropdown-item>
                      <el-dropdown-item>
                        <el-button class="min-w-[190px]" type="primary" plain @click="emits('importPoint')"
                          >从设备/重点部位批量导入</el-button
                        >
                      </el-dropdown-item>
                      <!-- 可配置的按钮  v-if="btnAuths.includes('下载点位ID规则')"  v-if="btnAuths.includes('更新点位ID规则')"-->
                      <el-dropdown-item v-if="btnAuths.includes('下载点位ID规则')">
                        <el-button
                          class="min-w-[190px]"
                          type="primary"
                          plain
                          @click="emits('pointTable')"
                          :loading="exLoading"
                          >下载点位ID规则</el-button
                        >
                      </el-dropdown-item>
                      <el-dropdown-item v-if="btnAuths.includes('更新点位ID规则')">
                        <el-button
                          class="min-w-[190px]"
                          type="primary"
                          plain
                          @click="emits('uppointTable')"
                          :loading="epLoading"
                          >更新点位ID规则</el-button
                        >
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
                <el-button type="primary" :icon="Plus" @click="emits('showPointDialog')" class="ml-5"
                  >新增巡查点位</el-button
                >
              </div>
            </n-gi>
          </n-grid>
        </template>
        <n-grid :x-gap="12" :y-gap="8" :cols="3" v-if="collapse">
          <n-gi>
            <n-form-item label="是否关联检查表:">
              <n-select v-model:value="form.checkTableIdRefFlag" placeholder="全部" :options="comOpts" clearable />
            </n-form-item>
          </n-gi>
          <n-gi :span="2">
            <div class="flex justify-end">
              <el-dropdown split-button type="primary">
                批量操作
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item>
                      <el-button class="min-w-[190px]" type="primary" plain @click="emits('deleteBatch')"
                        >批量删除</el-button
                      >
                    </el-dropdown-item>
                    <el-dropdown-item>
                      <el-button
                        class="min-w-[190px]"
                        type="primary"
                        plain
                        @click="emits('exportPoint')"
                        :loading="exportLoading"
                        >批量导出</el-button
                      >
                    </el-dropdown-item>
                    <el-dropdown-item>
                      <el-button class="min-w-[190px]" type="primary" plain @click="emits('importPoint')"
                        >批量导入</el-button
                      >
                    </el-dropdown-item>
                    <el-dropdown-item>
                      <el-button class="min-w-[190px]" type="primary" plain @click="emits('downloadErCodes')"
                        >批量下载二维码</el-button
                      >
                    </el-dropdown-item>
                    <el-dropdown-item>
                      <el-button class="min-w-[190px]" type="primary" plain @click="emits('relevanceTable')"
                        >批量关联检查表</el-button
                      >
                    </el-dropdown-item>
                    <el-dropdown-item>
                      <el-button class="min-w-[190px]" type="primary" plain @click="emits('importPoint')"
                        >从设备/重点部位批量导入</el-button
                      >
                    </el-dropdown-item>
                    <!-- 可配置的按钮  v-if="btnAuths.includes('下载点位ID规则')"  v-if="btnAuths.includes('更新点位ID规则')"-->
                    <el-dropdown-item>
                      <el-button
                        class="min-w-[190px]"
                        type="primary"
                        plain
                        @click="emits('pointTable')"
                        :loading="exLoading"
                        >下载点位ID规则</el-button
                      >
                    </el-dropdown-item>
                    <el-dropdown-item>
                      <el-button
                        class="min-w-[190px]"
                        type="primary"
                        plain
                        @click="emits('uppointTable')"
                        :loading="epLoading"
                        >更新点位ID规则</el-button
                      >
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
              <el-button type="primary" :icon="Plus" @click="emits('showPointDialog')" class="ml-5"
                >新增巡查点位</el-button
              >
            </div>
          </n-gi>
        </n-grid>
      </n-collapse-item>
    </n-form>
  </n-collapse>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { ACTION } from '../constant';
import type { FormInstance } from 'element-plus';
import { Plus } from '@element-plus/icons-vue';
import { BsSearch } from '@kalimahapps/vue-icons';
import { throttle } from 'lodash-es';
import { useStore } from '@/store';
const { userInfo } = useStore();
let btnAuths = userInfo.resourceVoList
  .find((item: any) => item.resUrl === 'inspectionPoint')
  ?.childrens.filter((item: any) => item.resType == '2')
  .map((item: any) => item.resName);
console.log(btnAuths);

const props = defineProps({
  optTreeData: {
    type: Array,
    default: () => [],
  },
});

const emits = defineEmits([
  'action',
  'downloadErCodes',
  'relevanceTable',
  'showPointDialog',
  'importPoint',
  'importPoint2',
  'exportPoint',
  'deleteBatch',
  'pointTable',
  'uppointTable',
]);
const form = ref<any>({
  isKeyArea: null, //默认全部
  checkTableIdRefFlag: null, //默认全部
  keyword: null,
  floorId: null,
});

const comOpts = [
  { label: '是', value: 1 },
  { label: '否', value: 0 },
];

const collapse = ref(false);
const exportLoading = ref(false);
const exLoading = ref(false);
const epLoading = ref(false);
const collapseClick = (e: any) => {
  collapse.value = e.expanded;
};

function getVal() {
  return form.value;
}

function getValFool() {
  form.value.floorId = null;
  return;
}

// const changeSelect = (e: string) => {
//   form.value.floorId = e;
//   doHandle();
// };

const clearFrom = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
};

const doHandle = throttle(() => {
  emits('action', {
    action: ACTION.SEARCH,
    data: form.value,
  });
}, 1000);

watch(form.value, () => {
  doHandle();
});

defineOptions({ name: 'checkTempFilterComp' });
defineExpose({ getVal, getValFool, clearFrom });
</script>
<style lang="scss" scoped>
:deep(.el-form-item) {
  margin-bottom: 0;
}

:deep(.n-collapse-item__header-extra) {
  width: calc(100% - 60px);
}

:deep(.n-collapse-item__content-inner) {
  padding-left: 60px;
}

.n-date-picker {
  width: 100%;
}
.ml_12px {
  margin-left: 12px;
}
</style>
