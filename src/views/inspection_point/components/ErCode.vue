<template>
  <el-dialog
    v-model="dialogVisible"
    width="524px"
    height="500px"
    top="3%"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <template #header="{ close }">
      <div class="flex justify-between items-center">
        <div class="flex justify-start items-center">
          <img src="@/assets/title_ico.png" style="width: 17px" alt="" />
          <div style="font-size: 16px" class="ml-2 text-black font-bold">查看二维码</div>
        </div>
        <img src="@/assets/dialog_close.png" style="width: 48px" class="cursor-pointer" alt="" @click="close" />
      </div>
    </template>
    <div>
      <img :src="getFileURL(formData.qrCodeUrl)" style="width: 100%" />
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import getFileURL from '@/utils/getFileURL';

const dialogVisible = ref(false);
const formData = ref();
const showErCodeDialog = (row: any) => {
  console.log(row);
  dialogVisible.value = true;
  formData.value = row;
};
defineOptions({ name: 'Er-Code' });
defineExpose({ showErCodeDialog });
</script>

<style scoped lang="scss">
.img-box {
  height: 600px;
  background: url('../img/ercodebg.png') no-repeat;
  background-size: 100%;
  border: 1px solid #e3e3e3;
  position: relative;
  overflow: hidden;
  text-align: center;

  .triangle-up {
    position: absolute;
    top: -40px;
    left: -40px;
    overflow: hidden;
    height: 80px;
    width: 80px;
    background-color: rgb(163, 33, 36);
    color: #fff;
    font-size: 18px;
    line-height: 136px;
    transform: rotate(315deg);
  }

  .img-box-title {
    font-size: 32px;
    margin: 20px auto;
    text-align: center;
    color: rgb(163, 33, 36);
    font-weight: bold;
  }

  .img-src {
    margin-top: 138px;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      border-radius: 20px;
      width: 300px;
      height: 300px;
      transform: scale(0.8);
    }
  }

  .img-address {
    margin-top: 15px;
    font-size: 16px;
    color: #fff;
  }

  .img-pointId {
    margin-top: 15px;
    font-size: 16px;
    color: #fff;
  }
}
</style>
