{"name": "ehs-hazard-mgr", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "test:dev": "vite --mode test", "pro:dev": "vite --mode pro", "build": "vite build", "test:build": "vite build --mode test", "pro:build": "vite build  --mode pro", "tsc": "vue-tsc --noEmit", "lint": "eslint . --ext .js,.ts,.vue --ignore-path .eslint<PERSON>ore", "lint:fix": "eslint . --ext .js,.ts,.vue --fix", "prepare": "husky install", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@plugin-web-update-notification/vite": "^1.7.1", "@tanzerfe/ifm-child": "^1.0.5", "@vicons/ionicons5": "^0.12.0", "@vueuse/core": "^10.9.0", "artplayer": "5.1.0", "big.js": "^6.2.2", "colord": "^2.9.3", "dayjs": "^1.11.11", "easytimer.js": "^4.6.0", "echarts": "^5.5.1", "echarts-gl": "^2.0.9", "echarts-liquidfill": "^3.1.0", "element-plus": "^2.7.6", "flv.js": "1.6.2", "hel-iso": "^4.3.2", "hel-micro": "^4.9.10", "hls.js": "1.5.3", "image-compressorionjs": "^1.0.2", "install": "^0.13.0", "js-cookie": "^3.0.5", "js-file-downloader": "^1.1.25", "lodash-es": "^4.17.21", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.1", "postcss-px-to-viewport": "1.1.1", "rxjs": "^7.8.1", "seemly": "^0.3.8", "sysend": "^1.17.4", "vue": "^3.4.21", "vue-router": "^4.3.2", "vue-uuid": "^3.0.0"}, "devDependencies": {"@commitlint/cli": "17.8.1", "@commitlint/config-angular": "17.8.1", "@kalimahapps/vue-icons": "^1.4.1", "@tanzerfe/eslint-config-lint": "^0.0.7", "@tanzerfe/http": "^1.0.1", "@types/js-cookie": "^3.0.6", "@types/lodash-es": "^4.17.12", "@types/node": "^20.12.10", "@types/querystringify": "^2.0.2", "@vitejs/plugin-vue": "^5.0.4", "@vitejs/plugin-vue-jsx": "^4.0.1", "autoprefixer": "^10.4.19", "eslint": "8.57.0", "husky": "8.0.3", "lint-staged": "^15.2.2", "naive-ui": "^2.38.2", "postcss": "^8.4.38", "querystringify": "^2.2.0", "sass": "^1.79.3", "sass-embedded": "1.79.3", "svgson": "5.2.1", "tailwind-config": "^0.1.2", "tailwindcss": "^3.4.3", "typescript": "^5.5.4", "unplugin-vue-components": "^0.27.0", "vite": "^5.2.0", "vue-tsc": "^2.1.4"}, "husky": {"hooks": {"pre-commit": "lint-staged", "post-commit": "git update-index --again", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "lint-staged": {"*.{vue,ts,tsx,js,jsx}": "eslint --ignore-path .eslint<PERSON>ore --fix"}}