import { defineConfig, loadEnv } from 'vite';
import vue from '@vitejs/plugin-vue';
import Components from 'unplugin-vue-components/vite';
import { NaiveUiResolver } from 'unplugin-vue-components/resolvers';
import path, { resolve } from 'path';
import vueJsx from '@vitejs/plugin-vue-jsx';
import { webUpdateNotice } from '@plugin-web-update-notification/vite';

export default defineConfig(({ mode, command }) => {
  return {
    base: './',

    server: {
      port: 8385,
      open: false,
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@import "src/css/variables.scss";`,
          api: 'modern-compiler',
        },
      },
    },
    plugins: [
      vue(),
      webUpdateNotice({
        logVersion: true,
        versionType: 'build_timestamp',
        checkInterval: 20 * 1000, // 设置检查间隔，单位毫秒，20秒
        notificationProps: {
          title: '系统更新',
          description: '系统已更新，请刷新页面',
          buttonText: '刷新',
          dismissButtonText: '忽略',
        },
      }),
      vueJsx(),
      Components({
        resolvers: [NaiveUiResolver()],
        directoryAsNamespace: true,
      }),
    ],
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'),
        src: resolve(__dirname, 'src'),
      },
    },
    build: {
      outDir: 'ehs-hazard-web',
    },
  };
});
