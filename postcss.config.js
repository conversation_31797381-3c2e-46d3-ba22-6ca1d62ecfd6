export default {
  plugins: {
    tailwindcss: {},
    // autoprefixer: {},
    autoprefixer: {
      overrideBrowserslist: ['> 1%', 'last 2 versions', 'not dead'],
      grid: true,
    },
    'postcss-px-to-viewport': {
      viewportWidth: 1920,
      unitPrecision: 5,
      propList: ['*'], // 处理所有属性
      // (String) 希望使用的视口单位
      viewportUnit: 'vw',
      // (String) 字体使用的视口单位
      fontViewportUnit: 'vw',
      minPixelValue: 1,
      selectorBlackList: ['notVw'], //对css选择器进行过滤的数组，比如你设置为['ignore']，那所有ignore类名里面有关px的样式将不被转换，
    },
  },
};
